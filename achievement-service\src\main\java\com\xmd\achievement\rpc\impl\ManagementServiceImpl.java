package com.xmd.achievement.rpc.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.xmd.achievement.rpc.ManagementService;
import com.xmd.achievement.rpc.entity.dto.OrgInfoDTO;
import com.xmd.achievement.rpc.entity.dto.UserLoginInfoDTO;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.util.http.HttpResult;
import com.xmd.achievement.util.http.HttpResultEnum;
import com.xmd.achievement.util.http.OKHttpUtil;
import com.xmd.achievement.web.config.ThirdHttpUrlConfiguration;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/15
 */
@Service
@Slf4j
public class ManagementServiceImpl implements ManagementService {

    @Resource
    private ThirdHttpUrlConfiguration urlConfiguration;

    @Override
    public WebResult<UserLoginInfoDTO> getLoginUserInfo(Map<String, String> paramMap) {
        if (MapUtil.isEmpty(paramMap)) {
            log.error("[management RpcServiceImpl.getLoginUserInfo] paramMap is null");
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        String url = urlConfiguration.getBspManagement().getVerifyUserInfo();
        HttpResult<String> result = OKHttpUtil.getSyncWithHeader(url, paramMap);
        log.info("拦截器中调用用户服务 校验用户信息返回结果={}", JSON.toJSONString(result));
        if (Objects.nonNull(result) && result.checkSuccess() && Objects.nonNull(result.getData())) {
            String data = result.getData();
            return JSON.parseObject(data, new TypeReference<WebResult<UserLoginInfoDTO>>() {
            });
        }
        return WebResult.error(WebCodeMessageEnum.RPC_SERVER_EXCEPTION);
    }

    @Override
    public List<OrgInfoDTO> getAllBranchOffice() {
        String url = urlConfiguration.getBspManagement().getPageQueryKjListOrg();
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("pageIndex", NumberConstants.INTEGER_VALUE_1.toString());
        paramMap.put("pageSize", NumberConstants.INTEGER_VALUE_50.toString());
        HttpResult<String> result = OKHttpUtil.postSync(url, paramMap);
        if (Objects.nonNull(result) && result.checkSuccess() && Objects.nonNull(result.getData())) {
            String data = result.getData();
            JSONObject jsonObj = JSON.parseObject(data);
            if (HttpResultEnum.REQUEST_SUCCESS.getCode().equals(jsonObj.getInteger("code"))) {
                JSONObject dataObj = jsonObj.getJSONObject("data");
                if (Objects.nonNull(dataObj)) {
                    return dataObj.getObject("records", new TypeReference<List<OrgInfoDTO>>(){});
                }
            }
        }
        return Lists.newArrayList();
    }
}
