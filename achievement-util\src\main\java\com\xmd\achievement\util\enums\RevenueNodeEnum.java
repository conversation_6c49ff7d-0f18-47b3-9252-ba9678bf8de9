package com.xmd.achievement.util.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/18 10:52
 * @version: 1.0.0
 * @return {@link }
 */
@Getter
public enum RevenueNodeEnum {
    /**
     * 1 支付完成
     */
    PAYMENT_COMPLETED(1),
    /**
     * 2 生产完成
     */
    PRODUCTION_COMPLETED(2);

    private final Integer node;

    RevenueNodeEnum(Integer node) {
        this.node = node;
    }

    public static List<Integer> getNodeList() {
        return Arrays.stream(RevenueNodeEnum.values()).map(RevenueNodeEnum::getNode).collect(Collectors.toList());
    }
}
