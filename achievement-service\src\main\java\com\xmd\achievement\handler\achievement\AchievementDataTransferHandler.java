package com.xmd.achievement.handler.achievement;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.dao.repository.*;
import com.xmd.achievement.dao.repository.impl.*;
import com.xmd.achievement.service.entity.request.ThirdPerformanceRequest;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.SaasEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class AchievementDataTransferHandler {

    @Autowired
    private IAchievementProductDetailRepository achievementProductDetailRepository;
    @Autowired
    private IAchievementProductDetailSelectRepository achievementProductDetailSelectRepository;
    @Autowired
    private IAchievementCategoryDetailRepository achievementCategoryDetailRepository;
    @Autowired
    private AchievementCategoryDetailSelectRepositoryImpl achievementCategoryDetailSelectRepository;
    @Autowired
    private IAchievementSpecDetailRepository achievementSpecDetailRepository;
    @Autowired
    private IAchievementSpecDetailSelectRepository achievementSpecDetailSelectRepository;

    @Transactional
    public void process(Date startTime) {
        Date onHourAge = ObjectUtil.isNotEmpty(startTime) ? startTime : getOneHourAgo();
        log.info("当前任务执行时间：{}", onHourAge);
        List<AchievementCategoryDetailModel> categoryModels = achievementCategoryDetailRepository.listByUpdateTime(onHourAge);
        List<AchievementCategoryDetailSelectModel> categorySelectModels = BeanUtil.copyToList(categoryModels, AchievementCategoryDetailSelectModel.class);
        achievementCategoryDetailSelectRepository.insertOrUpdate(categorySelectModels);
        productDetailHandler(onHourAge);
        SpecDetailHandler(onHourAge);
        log.info("本次更新规格分类业绩{}条", categoryModels.size());
    }

    private void productDetailHandler(Date onHourAge) {
        int pageSize = 500;
        Long lastId = 0L;
        int count=0;
        while (true) {
            LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(BaseModel::getUpdateTime,onHourAge)
                .eq(AchievementProductDetailModel::getDisplayed, 0)
                .orderByAsc(AchievementProductDetailModel::getId)
                .gt(AchievementProductDetailModel::getId, lastId)
                .last("LIMIT " + pageSize);
            List<AchievementProductDetailModel> productList = achievementProductDetailRepository.list(queryWrapper);
            if (CollectionUtils.isEmpty(productList)) {
                break;
            }
            List<AchievementProductDetailSelectModel> productSelectModels = BeanUtil.copyToList(productList, AchievementProductDetailSelectModel.class);
            achievementProductDetailSelectRepository.insertOrUpdate(productSelectModels);
            // 更新 lastId 为当前批次最后一条记录的 ID
            lastId = productList.get(productList.size() - 1).getId();
            count+=productList.size();
        }
        log.info("本次更新商品业绩{}条",count);
    }

    private void SpecDetailHandler(Date onHourAge) {
        int pageSize = 500;
        Long lastId = 0L;
        int count=0;
        while (true) {
            LambdaQueryWrapper<AchievementSpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(BaseModel::getUpdateTime,onHourAge)
                    .orderByAsc(AchievementSpecDetailModel::getId)
                    .gt(AchievementSpecDetailModel::getId, lastId)
                    .last("LIMIT " + pageSize);
            List<AchievementSpecDetailModel> specModels = achievementSpecDetailRepository.list(queryWrapper);
            if (CollectionUtils.isEmpty(specModels)) {
                break;
            }
            List<AchievementSpecDetailSelectModel> specSelectModels = BeanUtil.copyToList(specModels, AchievementSpecDetailSelectModel.class);
            achievementSpecDetailSelectRepository.insertOrUpdate(specSelectModels);
            // 更新 lastId 为当前批次最后一条记录的 ID
            lastId = specModels.get(specModels.size() - 1).getId();
            count+=specModels.size();
        }
        log.info("本次更新商品业绩{}条",count);
    }
    
    private Date getOneHourAgo() {
        // 获取当前时间前1小时
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(NumberConstants.INTEGER_VALUE_1);
        // 转换为 Date 类型
        return Date.from(oneHourAgo.atZone(ZoneId.systemDefault()).toInstant());
    }
}
