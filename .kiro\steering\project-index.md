# BSP Achievement System - Project Index

## 项目概览
BSP Achievement System 是一个基于 Spring Boot 的业绩管理平台，采用多模块 Maven 架构。

## 模块结构

### 1. achievement-util (工具模块)
**路径**: `achievement-util/`
**职责**: 通用工具类和帮助方法
**主要包**: 
- 工具类、常量定义
- HTTP 客户端封装
- 通用数据处理

### 2. achievement-dao (数据访问层)
**路径**: `achievement-dao/`
**职责**: 数据库操作和 ORM 映射
**主要包**:
- Entity 实体类
- Mapper 接口
- 数据库配置

### 3. achievement-cache (缓存层)
**路径**: `achievement-cache/`
**职责**: Redis 缓存抽象
**主要包**:
- 缓存服务接口
- 缓存配置

### 4. achievement-support (基础设施层)
**路径**: `achievement-support/`
**职责**: 基础设施和外部服务集成
**主要包**:
- 配置类
- 外部服务集成
- 消息队列处理

### 5. achievement-service (主应用模块)
**路径**: `achievement-service/`
**职责**: 业务逻辑和 REST API
**主要包**:
- `com.xmd.achievement.web.controller` - REST 控制器
- `com.xmd.achievement.service` - 业务服务
- `com.xmd.achievement.handler` - 业务处理器
- `com.xmd.achievement.async` - 异步处理
- `com.xmd.achievement.rpc` - RPC 接口

## 核心业务模块

### 业绩处理 (Achievement Processing)
- **AchievementHandler**: 主要业绩处理逻辑
- **AchievementRefundHandler**: 退款业绩处理
- **BusinessAchHandler**: 商务业绩统计

### 外部服务集成 (External Services)
- **InnerServiceImpl**: 内部服务调用实现
- 集成订单、客户、商品、合同等外部系统

### 异步处理 (Async Processing)
- **MQ 消息处理**: 订单支付、退款、服务完成等消息
- **定时任务**: 数据同步、报表生成等

### 数据模型 (Data Models)
- **Achievement**: 业绩主表
- **AchievementProductDetail**: 业绩商品明细
- **AchievementCategoryDetail**: 业绩分类明细
- **AchievementSpecDetail**: 业绩规格明细

## 配置文件位置

### 应用配置
- `achievement-service/src/main/resources/application.yml` - 主配置
- `achievement-service/src/main/resources/application-{env}.yml` - 环境配置

### 日志配置
- `achievement-service/src/main/resources/logback-{env}.xml` - 日志配置

## 测试文件
- `achievement-service/src/test/java/ServiceTest.java` - 主要测试类
- `achievement-service/src/test/java/InnerServiceTest.java` - 内部服务测试

## 构建配置
- 根目录 `pom.xml` - 父 POM
- 各模块 `pom.xml` - 模块特定配置
- `achievement-service/src/main/assembly/assembly.xml` - 打包配置

## 关键依赖
- Spring Boot 2.3.6
- MyBatis Plus 3.5.2
- Redis/Redisson
- RocketMQ
- MySQL 8.0.29