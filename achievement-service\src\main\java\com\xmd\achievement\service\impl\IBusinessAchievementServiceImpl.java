package com.xmd.achievement.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.BusinessAchievementModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IBusinessAchievementRepository;
import com.xmd.achievement.handler.statistics.BusinessAchHandler;
import com.xmd.achievement.service.IBusinessAchievementService;
import com.xmd.achievement.service.entity.request.RebuildBusinessAchStatInfoRequest;
import com.xmd.achievement.support.constant.enums.AchievementSourceEnum;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.util.enums.BusinessAchievementUpdateTypeEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class IBusinessAchievementServiceImpl implements IBusinessAchievementService {
    private static final Logger log = LogManager.getLogger(IBusinessAchievementServiceImpl.class);
    @Resource
    private IBusinessAchievementRepository businessAchRepository;

    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    private BusinessAchHandler businessAchHandler;

    @Override
    public BusinessAchievementModel getByMonthIdAndEmployeeId(Long monthId, String employeeId) {
        return (null != monthId && null != employeeId) ? businessAchRepository.getOne(new LambdaQueryWrapper<BusinessAchievementModel>()
                .eq(BusinessAchievementModel::getBusinessMonthId, monthId)
                .eq(BusinessAchievementModel::getEmployeeId, employeeId)
                .eq(BusinessAchievementModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .last("limit 1")) : null;
    }

    @Override
    public void saveOrUpdateBatch(List<BusinessAchievementModel> models) {
        if (CollectionUtils.isNotEmpty(models)) {
            businessAchRepository.saveOrUpdateBatch(models, models.size());
        }
    }

    @Override
    public BusinessAchievementModel findByBusinessMonthIdAndBusinessId(Long businessMonthId, String businessId) {
        if (null == businessId || StringUtils.isEmpty(businessId)) {
            return null;
        }
        return businessAchRepository.getOne(new LambdaQueryWrapper<BusinessAchievementModel>().
                eq(BusinessAchievementModel::getBusinessMonthId, businessMonthId)
                .eq(BusinessAchievementModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                 .eq(BusinessAchievementModel::getEmployeeId, businessId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rebuildBusinessAchStatInfo(RebuildBusinessAchStatInfoRequest request) {
        //查询出所有商品业绩信息
        List<AchievementProductDetailModel> achList = achievementProductDetailRepository.list(
                new LambdaQueryWrapper<AchievementProductDetailModel>()
                        .eq(AchievementProductDetailModel::getBusinessMonth, request.getBusinessMonth())
                        .eq(AchievementProductDetailModel::getAchievementSource, AchievementSourceEnum.KUAJINFG.getCode())
                        .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                        .orderByAsc(AchievementProductDetailModel::getCreateTime));
        if (ObjectUtil.isEmpty(achList)) {
            return;
        }

        //删除已有的商务月人员业绩信息
        businessAchRepository.remove(new LambdaUpdateWrapper<BusinessAchievementModel>().eq(BusinessAchievementModel::getBusinessMonth, request.getBusinessMonth()));

        //重新计算
        for (AchievementProductDetailModel ach : achList) {
            businessAchHandler.achStatistics(Collections.singletonList(ach), ach.getStatus(), BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
        }
        log.info("重新计算业绩数据完成");
    }

    @Override
    public List<BusinessAchievementModel> selectByBusinessMonthList(String businessMonth) {
        LambdaQueryWrapper<BusinessAchievementModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessAchievementModel::getBusinessMonth,businessMonth);
        queryWrapper.eq(BusinessAchievementModel::getDeleteFlag,DeleteFlagEnum.NOT_DELETE.getCode());
        queryWrapper.groupBy(BusinessAchievementModel::getEmployeeId);
        return businessAchRepository.list(queryWrapper);
    }

    @Override
    public void deleteByIdList(List<Long> unList) {
        LambdaUpdateWrapper<BusinessAchievementModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BusinessAchievementModel::getId,unList);
        updateWrapper.set(BusinessAchievementModel::getDeleteFlag,DeleteFlagEnum.DELETE.getCode());
        businessAchRepository.update(updateWrapper);
    }

    @Override
    public List<BusinessAchievementModel> queryBusinessAchievements(List<String> businessMonths, List<String> employeeIds) {
        if (CollectionUtils.isEmpty(businessMonths) || CollectionUtils.isEmpty(employeeIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BusinessAchievementModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BusinessAchievementModel::getBusinessMonth, businessMonths)
                    .in(BusinessAchievementModel::getEmployeeId, employeeIds)
                    .eq(BusinessAchievementModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        return businessAchRepository.list(queryWrapper);
    }
}
