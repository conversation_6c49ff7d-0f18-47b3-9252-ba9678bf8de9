package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;

import java.util.List;

/**
 * 业绩规格分类详情接口
 * <AUTHOR>
 */
public interface IAchievementCategoryDetailService {
    /**
     * 保存规格分类明细
     * @param category 分类实体
     * <AUTHOR>
     * @date: 2024/12/19 10:01
     * @version: 1.0.0
     */
    void save(AchievementCategoryDetailModel category);

    /**
     *  批量保存规格分类明细
     * @param models 分类实体
     */
    void saveBatch(List<AchievementCategoryDetailModel> models);

    /**
     *  批量更新规格分类
     * @param models 分类实体
     * <AUTHOR>
     * @date: 2024/12/24 14:42
     * @version: 1.0.0
     */
    void updateBatch(List<AchievementCategoryDetailModel> models);

    List<AchievementCategoryDetailModel> listByAchievementId(Long achievementId);

    void saveOrUpdateBatch(List<AchievementCategoryDetailModel> categoryList);

    List<AchievementCategoryDetailModel> getCategoryByProductCategoryId(Long orderId, Long productId, Long productCategoryId,Integer mainSplitPerson,Integer installmentNum);
}
