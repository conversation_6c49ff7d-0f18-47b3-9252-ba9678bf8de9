package com.xmd.achievement.web.controller;

import com.xmd.achievement.dao.entity.AchievementBlacklistModel;
import com.xmd.achievement.service.AchievementBlacklistService;
import com.xmd.achievement.service.entity.request.AchievementBlacklistRequest;
import com.xmd.achievement.service.entity.response.AchievementBlacklistVO;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Tag(name = "业绩黑名单")
@Slf4j
@RestController
@RequestMapping("achievement")
public class AchievementBlacklistController {

    @Resource
    private AchievementBlacklistService achievementBlacklistService;

    /**
     * 查询黑名单列表
     **/
    @Operation(summary = "查询黑名单列表")
    @PostMapping("selectBlacklist")
    public WebResult<PageResponse<AchievementBlacklistVO>> selectBlacklist(@RequestBody @Valid AchievementBlacklistRequest request) {
        try {
            return achievementBlacklistService.selectBlacklist(request);
        } catch (Exception e) {
            log.error("查询绩效配置失败", e);
        }
        return null;
    }

    /**
     * 删除黑名单
     **/
    @Operation(summary = "删除黑名单")
    @PostMapping("deleteBlacklist")
    public WebResult<Boolean> deleteBlacklist(@RequestBody @Valid AchievementBlacklistRequest request) {
        try {
            return achievementBlacklistService.deleteBlacklist(request);
        } catch (Exception e) {
            log.error("删除黑名单失败", e);
        }
        return null;
    }

    @Operation(summary = "导入业绩黑名单")
    @PostMapping("/importBlacklist")
    public WebResult<Void> importBlacklist(@RequestParam("file") MultipartFile file) {
        try {
            checkParam(file);
            achievementBlacklistService.importBlacklist(file);
            return WebResult.success();
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
            return WebResult.error(WebCodeMessageEnum.FILE_PARSING_ABNORMAL);
        } catch (Exception e) {
            log.error("校验导入业绩文件格式异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
    }

    private void checkParam(MultipartFile file) {
        if (null == file) {
            throw new IllegalArgumentException("文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
            throw new IllegalArgumentException("只支持 Excel 文件");
        }
    }

    /**
     * 导入订单明细黑名单
     */
    @Operation(summary = "导入订单明细黑名单")
    @PostMapping("/import-order-info-blacklist")
    public WebResult<Void> importOrderInfoBlacklist(@RequestParam("file") MultipartFile file) {
        try {
            checkParam(file);
            achievementBlacklistService.importBlacklist(file);
            return WebResult.success();
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
            return WebResult.error(WebCodeMessageEnum.FILE_PARSING_ABNORMAL);
        } catch (Exception e) {
            log.error("校验导入业绩文件格式异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
    }
}
