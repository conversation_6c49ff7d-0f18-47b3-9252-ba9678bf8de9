package com.xmd.achievement.web.controller;

import com.xmd.achievement.dao.entity.AchievementBlacklistModel;
import com.xmd.achievement.service.AchievementBlacklistService;
import com.xmd.achievement.service.IExcelImportExceptionService;
import com.xmd.achievement.service.IOrganizationReportService;
import com.xmd.achievement.service.entity.request.AchievementBlacklistRequest;
import com.xmd.achievement.service.entity.response.AchievementBlacklistVO;
import com.xmd.achievement.service.entity.response.MonthlyReportImportResultResponse;
import com.xmd.achievement.support.constant.enums.ExcelImportExceptionEnum;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

import static com.xmd.achievement.web.entity.response.WebCodeMessageEnum.FILE_PARSING_ABNORMAL;
import static com.xmd.achievement.web.entity.response.WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL;

/**
 * <AUTHOR>
 */
@Tag(name = "业绩黑名单")
@Slf4j
@RestController
@RequestMapping("achievement")
public class AchievementBlacklistController {

    @Resource
    private AchievementBlacklistService achievementBlacklistService;
    @Autowired
    private IOrganizationReportService organizationReportService;
    @Resource
    private IExcelImportExceptionService excelImportExceptionService;
    /**
     * 查询黑名单列表
     **/
    @Operation(summary = "查询黑名单列表")
    @PostMapping("selectBlacklist")
    public WebResult<PageResponse<AchievementBlacklistVO>> selectBlacklist(@RequestBody @Valid AchievementBlacklistRequest request) {
        try {
            return achievementBlacklistService.selectBlacklist(request);
        } catch (Exception e) {
            log.error("查询绩效配置失败", e);
        }
        return null;
    }

    /**
     * 删除黑名单
     **/
    @Operation(summary = "删除黑名单")
    @PostMapping("deleteBlacklist")
    public WebResult<Boolean> deleteBlacklist(@RequestBody @Valid AchievementBlacklistRequest request) {
        try {
            return achievementBlacklistService.deleteBlacklist(request);
        } catch (Exception e) {
            log.error("删除黑名单失败", e);
        }
        return null;
    }

    @Operation(summary = "导入业绩黑名单")
    @PostMapping("/importBlacklist")
    public WebResult<Void> importBlacklist(@RequestParam("file") MultipartFile file) {
        try {
            checkParam(file);
            achievementBlacklistService.importBlacklist(file);
            return WebResult.success();
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
            return WebResult.error(WebCodeMessageEnum.FILE_PARSING_ABNORMAL);
        } catch (Exception e) {
            log.error("校验导入业绩文件格式异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
    }

    private void checkParam(MultipartFile file) {
        if (null == file) {
            throw new IllegalArgumentException("文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
            throw new IllegalArgumentException("只支持 Excel 文件");
        }
    }

    /**
     * 导入订单明细黑名单
     */
    @Operation(summary = "导入订单明细黑名单")
    @PostMapping("/import-order-info-blacklist")
    public WebResult<Void> importOrderInfoBlacklist(@RequestParam("file") MultipartFile file) {
        try {
            checkParam(file);
            achievementBlacklistService.importBlacklist(file);
            return WebResult.success();
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
            return WebResult.error(WebCodeMessageEnum.FILE_PARSING_ABNORMAL);
        } catch (Exception e) {
            log.error("校验导入业绩文件格式异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
    }

    @Operation(summary = "月报导入")
    @PostMapping("/monthly/importMonthlyReport")
    public WebResult<MonthlyReportImportResultResponse> importMonthlyReport(@RequestParam("file") MultipartFile file) {
        MonthlyReportImportResultResponse response = new MonthlyReportImportResultResponse();
        response.setCode(ExcelImportExceptionEnum.MONTH_REPORT.getCode());
        try {
            if (null == file) {
                WebResult.error(REQUEST_PARAM_NOT_NULL, "文件不能为空");
            }
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                WebResult.error(REQUEST_PARAM_NOT_NULL, "文件名不能为空");
            }
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                response = organizationReportService.excelImport(file);
                return WebResult.success(response);
            } else {
                WebResult.error(FILE_PARSING_ABNORMAL, "只支持 Excel 文件");
            }
        } catch (Exception e) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
        return WebResult.success(response);
    }

    @Data
    static class ExceptionDescRequest {

        @Schema(description = "code")
        private String code;
    }

    @Operation(summary = "月报导入结果下载")
    @PostMapping("/monthly/exportReportResult")
    public void exportReportResult(@RequestBody ExceptionDescRequest request, HttpServletResponse response) {
        try {
            excelImportExceptionService.exportExcel(request.getCode(), response);
        } catch (IOException e) {
            log.error("导出失败", e);
        }
    }
}
