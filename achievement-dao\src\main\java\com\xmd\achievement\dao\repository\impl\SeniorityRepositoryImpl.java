package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.SeniorityModel;
import com.xmd.achievement.dao.mapper.SeniorityMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.ISeniorityRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 司龄表（入职公司年限） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@Slf4j
public class SeniorityRepositoryImpl extends ServiceImpl<SeniorityMapper, SeniorityModel> implements ISeniorityRepository {

    @Resource
    private SeniorityMapper seniorityMapper;

}