package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 是否异常枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AdnormalEnum {

    /**
     * 0 正常
     */
    NORMAL(0, "正常"),

    /**
     * 1 异常
     */
    ABNORMAL(1, "异常");

    private final Integer code;
    private final String desc;

    AdnormalEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举项
     */
    public static AdnormalEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AdnormalEnum value : AdnormalEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     *
     * @param code 状态码
     * @return 描述信息
     */
    public static String getDescByCode(Integer code) {
        AdnormalEnum enumValue = fromCode(code);
        return enumValue != null ? enumValue.getDesc() : null;
    }
}
