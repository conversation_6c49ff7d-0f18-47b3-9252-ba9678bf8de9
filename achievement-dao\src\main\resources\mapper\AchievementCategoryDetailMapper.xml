<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.AchievementCategoryDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.AchievementCategoryDetailModel">
        <id column="id" property="id" />
        <result column="achievement_category_id" property="achievementCategoryId" />
        <result column="achievement_id" property="achievementId" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="standard_price" property="standardPrice" />
        <result column="payable_amount" property="payableAmount" />
        <result column="paid_amount" property="paidAmount"/>
        <result column="first_year_quote" property="firstYearQuote" />
        <result column="first_year_income" property="firstYearIncome" />
        <result column="renewal_quote" property="renewalQuote" />
        <result column="renewal_income" property="renewalIncome" />
        <result column="net_cash" property="netCash" />
        <result column="agent_comm_achv" property="agentCommAchv" />
        <result column="agent_act_comm_achv" property="agentActCommAchv" />
        <result column="agent_def_comm_achv" property="agentDefCommAchv" />
        <result column="dept_comm_achv" property="deptCommAchv" />
        <result column="bu_comm_achv" property="buCommAchv" />
        <result column="branch_comm_achv" property="branchCommAchv" />
        <result column="installment_num" property="installmentNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , achievement_category_id, achievement_id, category_id, category_name, standard_price, payable_amount, paid_amount, first_year_quote, first_year_income, renewal_quote, renewal_income, net_cash, agent_comm_achv, agent_act_comm_achv, agent_def_comm_achv, dept_comm_achv, bu_comm_achv, branch_comm_achv,installment_num
    </sql>

</mapper>
