package com.xmd.achievement.rpc.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ServiceDelivery {
    private String serveId;          // 服务ID
    private String serveNo;          // 服务单号
    private Integer serveType;       // 服务类型（数值）
    private String serveTypeName;    // 服务类型名称
    private String productId;        // 产品ID
    private String serveName;        // 服务名称
    private String customerId;       // 客户ID
    private String customerName;     // 客户名称
    private Integer serveStatus;     // 服务状态（数值）
    private String serveStatusName;  // 服务状态名称
    private String serveNodeName;    // 服务节点名称（可能为null）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 日期格式化
    private LocalDateTime beginTime; // 开始时间（可能为null）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;   // 结束时间（可能为null）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;// 创建时间
    private Integer delayOpenDays;   // 延迟开通天数（可能为null）
    private String delayOpenDaysTime;// 延迟开通时间（可能为null）
    private List<String> orderIds;   // 订单ID列表
    private List<String> orderNos;   // 订单号列表
    private List<Order> orderList;   // 订单列表（嵌套对象）
    private List<Object> deliveryList;// 交付列表（可能为空，未展开）
    private List<Item> itemList;     // 服务项列表（嵌套对象）
    private Boolean operateStatus;   // 操作状态
    private Integer siteFlag;        // 站点标识
    private Integer adFlag;          // 广告标识
    private Integer afterServeStatus;// 服务后状态
    private String thirdInstanceId;  // 第三方实例ID
    private String levelOneCategoryId;// 一级分类ID
    private String levelOneCategoryName;// 一级分类名称
    private String levelTwoCategoryId;// 二级分类ID
    private String levelTwoCategoryName;// 二级分类名称
    private String levelThreeCategoryId;// 三级分类ID
    private String levelThreeCategoryName;// 三级分类名称
    private Boolean domainStatus;    // 域名状态
    private Boolean emailStatus;     // 邮箱状态
    private String remark;           // 备注（可能为null）
}
