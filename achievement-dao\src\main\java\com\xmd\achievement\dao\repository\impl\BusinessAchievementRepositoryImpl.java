package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.BusinessAchievementModel;
import com.xmd.achievement.dao.mapper.BusinessAchievementMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IBusinessAchievementRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 商务业绩表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
@Slf4j
public class BusinessAchievementRepositoryImpl extends ServiceImpl<BusinessAchievementMapper, BusinessAchievementModel> implements IBusinessAchievementRepository {

    @Resource
    private BusinessAchievementMapper businessAchievementMapper;

}