package com.xmd.achievement.rpc.entity.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class ProductInfoForOrderResponse {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品售卖类型")
    private List<Integer> saleTypes;

    @Schema(description = "一级商品分类id")
    private Long levelOneCategoryId;

    @Schema(description = "一级商品分类名称")
    private String levelOneCategoryName;

    @Schema(description = "二级商品分类id")
    private Long levelTwoCategoryId;

    @Schema(description = "二级商品分类名称")
    private String levelTwoCategoryName;

    @Schema(description = "三级商品分类id")
    private Long levelThreeCategoryId;

    @Schema(description = "三级商品分类名称")
    private String levelThreeCategoryName;

    @Schema(description = "商品状态 1-上架, 2-下架 3-待上架")
    private Integer productStatus;

    @Schema(description = "展示 LOGO")
    private String displayLogo;

    @Schema(description = "协议ID")
    private Long agreementId;

    @Schema(description = "协议名称")
    private String agreementName;

    @Schema(description = "是否网站 0-否 1-是")
    private Integer siteFlag;

    @Schema(description = "是否电商 0-否 1-是")
    private Integer commerceFlag;

    @Schema(description = "广告标识: 0-否, 1-是")
    private Integer adFlag;

    @Schema(description = "分期标识")
    private Integer installmentFlag;

    @Schema(description = "分期顺序")
    private Integer installmentOrder;

    @Schema(description = "复购标识")
    private Integer reBuyFlag;

    @Schema(description = "规格列表")
    private List<ProductSpecForOrderResponse> specList;

    @Schema(description = "规格分类列表")
    private List<ProductSpecCategoryForOrderResponse> specCateList;
}
