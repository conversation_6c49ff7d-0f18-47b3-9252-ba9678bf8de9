package com.xmd.achievement.async.job.entity;

import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import lombok.Data;

import java.util.List;

@Data
public class AchievementRepairEntity {

    List<AchievementProductDetailModel> repairProductList ;

    List<AchievementCategoryDetailModel> repairCategoryList ;

    List<AchievementSpecDetailModel> repairSpecList ;
}
