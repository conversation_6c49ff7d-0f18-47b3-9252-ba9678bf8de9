package com.xmd.achievement.util.thread.core;

import cn.hutool.core.map.MapUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.xmd.achievement.util.constant.UtilConstant;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务管理类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/21 上午11:32
 */
public class TaskManager implements Runnable {

    /**
     * 任务名称
     */
    private final String taskName;

    /**
     * 任务
     */
    private final Runnable runnable;

    private final TransmittableThreadLocal<Map<String, String>> transmittableThreadLocal = new TransmittableThreadLocal<>();

    public TaskManager(String taskName, Runnable runnable) {
        Map<String, String> mdcMap = new HashMap<>();
        UtilConstant.Mdc.MDC_LIST.forEach((key, value) -> mdcMap.put(key, MDC.get(key)));
        if (MapUtil.isNotEmpty(mdcMap)) {
            this.transmittableThreadLocal.set(mdcMap);
        }
        this.taskName = taskName;
        this.runnable = runnable;
    }

    public String getTaskName() {
        return taskName;
    }

    @Override
    public void run() {
        Map<String, String> mdcMap = transmittableThreadLocal.get();
        if (MapUtil.isNotEmpty(mdcMap)) {
            mdcMap.forEach(MDC::put);
        }
        runnable.run();
        transmittableThreadLocal.remove();
    }
}
