package com.xmd.achievement.rpc.entity.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TieredBillingPriceResponse {

    @Schema(description = "新开阶梯价格列表")
    private List<TieredPriceItemResponse> newOpenTieredPriceList;

    @Schema(description = "续费阶梯价格列表")
    private List<TieredPriceItemResponse> renewalTieredPriceList;

    @Schema(description = "另购阶梯价格列表")
    private List<TieredPriceItemResponse> additionalTieredPriceList;
}
