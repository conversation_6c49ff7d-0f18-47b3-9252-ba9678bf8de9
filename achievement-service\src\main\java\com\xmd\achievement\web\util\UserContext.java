package com.xmd.achievement.web.util;

import com.xmd.achievement.rpc.entity.dto.UserLoginInfoDTO;
import org.springframework.stereotype.Component;

/**
 * 用户上下文
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/6/12 11:20 上午
 */
@Component
public class UserContext {
    private static ThreadLocal<UserLoginInfoDTO> UserInfoRespThreadLocal = new ThreadLocal<UserLoginInfoDTO>();

    public static void setUserInfo(UserLoginInfoDTO UserLoginInfoDto) {
        UserInfoRespThreadLocal.set(UserLoginInfoDto);
    }

    public static UserLoginInfoDTO getCurrentUserInfo() {
        return UserInfoRespThreadLocal.get();
    }

    public static void removeUserInfo() {
        UserInfoRespThreadLocal.remove();
    }

}
