package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 组合支付关联响应
 * 
 * <AUTHOR> Generated
 * @date 2025-01-30
 */
@Data
@Schema(description = "组合支付关联响应")
public class CombinationPayRelResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组合支付关联id
     */
    @Schema(description = "组合支付关联id")
    private Long combinationPayRelId;

    /**
     * 支付详情id
     */
    @Schema(description = "支付详情id")
    private String payDetailId;

    /**
     * 支付类型
     */
    @Schema(description = "支付类型")
    private Integer payType;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 交易id
     */
    @Schema(description = "交易id")
    private Long transactionId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
}
