package com.xmd.achievement.service.entity.response;

import com.xmd.achievement.support.constant.enums.CustomerChurnStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/10:47
 * @since 1.0
 */
@Data
public class CustomerChurnResponse {

    // CHURN_CUSTOMER-流失客户  NEW_CUSTOMER-新客户  NO_CHURN_CUSTOMER-未流失客户
    private CustomerChurnStatusEnum churnStatus;

    //流失时间
    private Date churnTime;
}
