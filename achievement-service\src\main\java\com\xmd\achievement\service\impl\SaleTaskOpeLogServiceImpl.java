package com.xmd.achievement.service.impl;

import com.google.common.collect.Lists;
import com.xmd.achievement.dao.entity.SaleTaskModel;
import com.xmd.achievement.dao.entity.SaleTaskOpeLogModel;
import com.xmd.achievement.dao.repository.ISaleTaskOpeLogRepository;
import com.xmd.achievement.service.SaleTaskOpeLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class SaleTaskOpeLogServiceImpl implements SaleTaskOpeLogService {

    @Autowired
    private ISaleTaskOpeLogRepository opeLogRepository;

    @Override
    public void saveBatch(List<SaleTaskModel> taskModels, int opeType) {
        List<SaleTaskOpeLogModel> logModels = Lists.newArrayList();
        for (SaleTaskModel model : taskModels) {
            SaleTaskOpeLogModel saleTaskOpeLogModel = new SaleTaskOpeLogModel();
            saleTaskOpeLogModel.setTaskId(model.getTaskId());
            saleTaskOpeLogModel.setPreviousBasicTask(new BigDecimal("0.0"));
            saleTaskOpeLogModel.setModifiedBasicTask(model.getBasicTask());
            saleTaskOpeLogModel.setOpeType(SaleTaskOpeLogModel.LOG_OPE_TYPE_ADD);

            logModels.add(saleTaskOpeLogModel);
        }
        opeLogRepository.saveBatch(logModels);
    }

    @Override
    public void saveBatch(List<SaleTaskOpeLogModel> models) {
        opeLogRepository.saveBatch(models);
    }

    @Override
    public void saveLog(Long taskId, BigDecimal previous, BigDecimal modify, int opeType) {
        SaleTaskOpeLogModel saleTaskOpeLogModel = new SaleTaskOpeLogModel();
        saleTaskOpeLogModel.setTaskId(taskId);
        saleTaskOpeLogModel.setPreviousBasicTask(previous);
        saleTaskOpeLogModel.setModifiedBasicTask(modify);
        saleTaskOpeLogModel.setOpeType(opeType);
        opeLogRepository.save(saleTaskOpeLogModel);
    }
}
