package com.xmd.achievement.util.translateBaidu;

/**
 * http返回结果枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/1/24 9:34 上午
 */
public enum ResultEnum {

    REQUEST_SUCCESS(200, "请求成功"),
    REQUEST_ERROR(-1, "请求失败"),
    LAN_DETECT_EXCEPTION(-1, "语言检测异常"),
    TXT_TRANSLATE_EXCEPTION(-1, "翻译异常"),
    ;

    private Integer code;

    private String message;

    ResultEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
