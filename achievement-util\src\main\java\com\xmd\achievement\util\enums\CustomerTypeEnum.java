package com.xmd.achievement.util.enums;

import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/18 10:58
 * @version: 1.0.0
 * @return {@link }
 */
@Getter
public enum CustomerTypeEnum {
    /**
     * 1 新客户
     */
    NEW(1, "新客户"),
    /**
     * 2 老客户
     */
    OLD(2, "老客户"),
    /**
     * 3 非新老
     */
    NON_NEW_OLD(3, "非新老");

    private final Integer customerType;
    private final String message;

    CustomerTypeEnum(Integer customerType, String message) {
        this.customerType = customerType;
        this.message = message;
    }

    public Integer getCode() {
        return customerType;
    }

    public String getMessage() {
        return message;
    }

    public static CustomerTypeEnum fromCode(int code) {
        for (CustomerTypeEnum value : CustomerTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("CustomerTypeEnum code: " + code);
    }

    public static void validate(Integer customerType) {
        if (ObjectUtils.isEmpty(customerType)) {
            return;
        }
        for (CustomerTypeEnum type : values()) {
            if (type.customerType.equals(customerType)) {
                return;
            }
        }
        throw new IllegalArgumentException("Invalid customerType: " + customerType + ". Must be 1, 2, or 3.");
    }

}