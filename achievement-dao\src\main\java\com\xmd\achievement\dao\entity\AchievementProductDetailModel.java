package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 业绩商品明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("achievement_product_detail")
public class AchievementProductDetailModel extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业绩流水ID
     */
    @TableField("achievement_id")
    private Long achievementId;
    /**
     * 商务月ID
     */
    @TableField("business_month_id")
    private Long businessMonthId;
    /**
     * 商务月
     */
    @TableField("business_month")
    private String businessMonth;
    /**
     * 订单明细编号
     */
    @TableField("order_product_id")
    private String orderProductId;
    /**
     * 服务编号
     */
    @TableField("serve_no")
    private String serveNo;
    /**
     * 商品id
     */
    @TableField("product_id")
    private Long productId;
    /**
     * 是否网站：0=否 1=是
     */
    @TableField("site_flag")
    private Integer siteFlag;
    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;
    /**
     * 商品类型
     */
    @TableField("product_type")
    private String productType;
    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购
     */
    @TableField("sale_type")
    private Integer saleType;
    /**
     * 业绩状态 1=有效，2=已完成，3=失效，4=退款
     */
    @TableField("status")
    private Integer status;
    /**
     * 订单id
     */
    @TableField("order_id")
    private Long orderId;
    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;
    /**
     * 订单来源：1=商务签单，2=官网，3=驾驶舱-PC,4=驾驶舱移动端，5优化师工作台，6商务工作台，7优化师录单
     */
    @TableField("order_source")
    private Integer orderSource;
    /**
     * 客户id
     */
    @TableField("customer_id")
    private String customerId;
    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;
    /**
     * 客户类型 1=新客户，2=老客户，3=非新老
     */
    @TableField("customer_type")
    private Integer customerType;
    /**
     * 省id
     */
    @TableField("province_code")
    private String provinceCode;
    /**
     * 省名称
     */
    @TableField("province_name")
    private String provinceName;
    /**
     * 城市id
     */
    @TableField("city_code")
    private String cityCode;
    /**
     * 城市名称
     */
    @TableField("city_name")
    private String cityName;
    /**
     * 区县id
     */
    @TableField("district_code")
    private String districtCode;
    /**
     * 区县名称
     */
    @TableField("district_name")
    private String districtName;
    /**
     * 客户所在区
     */
    @TableField("customer_region")
    private String customerRegion;
    /**
     * 合同编号
     */
    @TableField("contract_no")
    private String contractNo;
    /**
     * 商务ID 注释
     */
    @TableField("business_id")
    private String businessId;
    /**
     * 商务代表
     */
    @TableField("business_representative")
    private String businessRepresentative;
    /**
     * 主分单人 1=主，2=辅
     */
    @TableField("main_split_person")
    private Integer mainSplitPerson;
    /**
     * 公司ID
     */
    @TableField("company_id")
    private Long companyId;

    @TableField("region_id")
    private Long regionId;

    /**
     * 公司
     */
    @TableField("company")
    private String company;
    /**
     * 事业部ID
     */
    @TableField("division_id")
    private Long divisionId;
    /**
     * 事业部
     */
    @TableField("division")
    private String division;
    /**
     * 部门ID
     */
    @TableField("dept_id")
    private Long deptId;
    /**
     * 部门
     */
    @TableField("department")
    private String department;
    /**
     * 标准价
     */
    @TableField("standard_price")
    private BigDecimal standardPrice;
    /**
     * 应付金额
     */
    @TableField("payable_amount")
    private BigDecimal payableAmount;
    /**
     * 实付金额
     */
    @TableField("paid_amount")
    private BigDecimal paidAmount;
    /**
     * 折扣比例
     */
    @TableField("discount_rate")
    private BigDecimal discountRate;
    /**
     * 交付方式: 1-软件交付, 2-服务交付
     */
    @TableField("delivery_method")
    private Integer deliveryMethod;
    /**
     * 订单类型：1=普通订单，2=折扣订单
     */
    @TableField("order_type")
    private Integer orderType;
    /**
     * 业绩生成时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 业绩统计时间
     */
    @TableField("statistics_time")
    private Date statisticsTime;
    /**
     * 签单时间
     */
    @TableField("signed_time")
    private Date signedTime;
    /**
     * 付款时间
     */
    @TableField("payment_time")
    private Date paymentTime;
    /**
     * 首年报价
     */
    @TableField("first_year_quote")
    private BigDecimal firstYearQuote;
    /**
     * 首年到账金额
     */
    @TableField("first_year_revenue")
    private BigDecimal firstYearRevenue;
    /**
     * 续费报价
     */
    @TableField("renewal_quote")
    private BigDecimal renewalQuote;
    /**
     * 续费到账金额
     */
    @TableField("renewal_revenue")
    private BigDecimal renewalRevenue;
    /**
     * 净现金
     */
    @TableField("net_cash")
    private BigDecimal netCash;
    /**
     * 商代提成业绩
     */
    @TableField("agent_commission_achievement")
    private BigDecimal agentCommissionAchievement;
    /**
     * 商代实发提成业绩
     */
    @TableField("agent_actual_commission")
    private BigDecimal agentActualCommission;
    /**
     * 商代缓发提成业绩
     */
    @TableField("agent_deferred_commission")
    private BigDecimal agentDeferredCommission;
    /**
     * 部门提成业绩
     */
    @TableField("dept_commission")
    private BigDecimal deptCommission;
    /**
     * 事业部提成业绩
     */
    @TableField("div_commission")
    private BigDecimal divCommission;
    /**
     * 分司提成业绩
     */
    @TableField("branch_commission")
    private BigDecimal branchCommission;
    /**
     * 服务完成时间
     */
    @TableField("serve_finish_time")
    private Date serveFinishTime;
    /**
     * 业绩来源：1-跨境 2-中小
     */
    @TableField("achievement_source")
    private Integer achievementSource;

    /**
     * 三方业绩流水ID
     */
    @TableField("third_achievement_id")
    private String thirdAchievementId;

    /**
     * 付款类型：ALL-全款 分期·STAGES
     */
    @TableField("pay_type")
    private String payType;

    /**
     * 付款期数:
     */
    @TableField("pay_cycle_count")
    private Integer payCycleCount;


    /**
     * 当前付款周期
     */
    @TableField("current_cycle_count")
    private String currentCycleCount;

    /**
     * 数据改动标识  1 - 正常数据、2 - 人工新增、3 - 人工修改
     */
    @TableField("data_change_type")
    private Integer dataChangeType;

    /**
     * 历史修改备注
     */
    @TableField("remark_history")
    private String remarkHistory;

    /**
     * 最后一次修改备注
     */
    @TableField("latest_remark")
    private String latestRemark;

    /**
     * 业绩是否100%核算 0是 1否
     */
    @TableField("calculate_all")
    private Integer calculateAll;

    /**
     * 分期期数
     */
    @TableField("installment_num")
    private Integer installmentNum;

    /**
     * 是否展示
     */
    @TableField("displayed")
    private Integer displayed;

    @TableField("is_saas")
    private Integer isSaas;

    /**
     * 售后订单id
     */
    @TableField("aftersale_order_id")
    private Long aftersaleOrderId;

    /**
     * 售后订单编号
     */
    @TableField("aftersale_order_no")
    private String aftersaleOrderNo;

    /**
     * 售后合同编号
     */
    @TableField("aftersale_contract_no")
    private String aftersaleContractNo;

    /**
     * 是否异常，0-正常，1-异常
     */
    @TableField("is_abnormal")
    private Integer isAbnormal;
}