package com.xmd.achievement.service.entity.dto;

import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvDate;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * easyExcel
 */
@Data
public class ThirdExcelAchievementDTO implements Serializable {

    private static final long serialVersionUID = 2387356132678099748L;

    @CsvBindByName(column = "thirdId")
    private String thirdId;

    @CsvBindByName(column = "singingDate")
    @CsvDate("yyyy-MM-dd HH:mm:ss")
    private Date singingDate; // 签单日期

    @CsvBindByName(column = "toAccountDate")
    @CsvDate("yyyy-MM-dd HH:mm:ss")
    private Date toAccountDate; // 到账日期

    @CsvBindByName(column = "dbInsertTime")
    @CsvDate("yyyy-MM-dd HH:mm:ss")
    private Date dbInsertTime; // 数据库插入时间
}
