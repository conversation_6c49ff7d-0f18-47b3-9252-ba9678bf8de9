package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.AchievementProductDetailSelectModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 业绩商品明细表(查询) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IAchievementProductDetailSelectRepository extends IService<AchievementProductDetailSelectModel> {

    void insertOrUpdate(List<AchievementProductDetailSelectModel> models);

    List<AchievementProductDetailSelectModel> selectAchievement(List<Long> companyIds, List<Long> deptIds, List<Long> businessMonthIds, List<String> businessIds);

    void logicDeleteByAchievementId(List<Long> achivevementIdList);
}
