package com.xmd.achievement.web.controller;

import com.xmd.achievement.service.ICustomerSaasService;
import com.xmd.achievement.service.entity.response.CustomerChurnResponse;
import com.xmd.achievement.web.entity.response.WebResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(tags = "客户SaaS重算相关接口")
@RestController
@RequestMapping("/customerSaas")
public class CustomerSaasController {

    @Resource
    private ICustomerSaasService customerSaasService;

    @ApiOperation("重算客户SaaS历史数据")
    @PostMapping("/recalculateHistory")
    public void recalculateHistory() {
        customerSaasService.recalculateHistory();
    }

    @ApiOperation("重算客户SaaS状态")
    @PostMapping("/recalculateAllSaasStatus")
    public void recalculateAllSaasStatus() {
        customerSaasService.recalculateAllSaasStatus();
    }

    @ApiOperation("客户流失批量检查")
    @PostMapping("/customerSaasJob")
    public void customerSaasJob() {
        customerSaasService.customerSaasJob();
    }

    @ApiOperation("客户流失批量检查")
    @PostMapping("/count")
    public WebResult<Integer> count() {
        return WebResult.success(
            customerSaasService.searchCount("aa1b82df919842a4a83f3e9f96aae737", 1906525403712933222L));
    }

    @ApiOperation("获取客户流失日期列表（带缓存）")
    @GetMapping("/getCustomerLossDateList")
    public WebResult<List<String>> getCustomerLossDateList(
            @ApiParam(value = "客户ID", required = true) @RequestParam String customerId) {
        return WebResult.success(customerSaasService.getCustomerLossDateListWithCache(customerId));
    }

    @ApiOperation("获取客户流失信息（带缓存）")
    @GetMapping("/getCustomerChurnInfo")
    public WebResult<CustomerChurnResponse> getCustomerChurnInfo(
            @ApiParam(value = "客户ID", required = true) @RequestParam String customerId) {
        return WebResult.success(customerSaasService.getCustomerChurnInfoWithCache(customerId));
    }

    @ApiOperation("获取SaaS相关Redis值")
    @GetMapping("/redis/getSaasValues")
    public WebResult<Map<String, String>> getSaasRedisValues() {
        return WebResult.success(customerSaasService.getSaasRedisValues());
    }

    @ApiOperation("清除客户SaaS Redis值")
    @PostMapping("/redis/clearCustomerSaasValue")
    public WebResult<Boolean> clearCustomerSaasRedisValue() {
        return WebResult.success(customerSaasService.clearCustomerSaasRedisValue());
    }

    @ApiOperation("清除SaaS状态重算 Redis值")
    @PostMapping("/redis/clearSaasStatusRecalcValue")
    public WebResult<Boolean> clearSaasStatusRecalcRedisValue() {
        return WebResult.success(customerSaasService.clearSaasStatusRecalcRedisValue());
    }

        @ApiOperation("清除重试 Redis值")
        @PostMapping("/redis/clearRetryValue")
        public WebResult<Boolean> clearRetryRedisValue() {
            return WebResult.success(customerSaasService.clearRetryRedisValue());
        }
}
