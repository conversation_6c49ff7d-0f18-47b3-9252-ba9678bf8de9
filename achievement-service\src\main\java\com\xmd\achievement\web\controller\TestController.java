package com.xmd.achievement.web.controller;

import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.ThirdAchievementModel;
import com.xmd.achievement.handler.achievement.AchievementHandler;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContext;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContextV4;
import com.xmd.achievement.service.*;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.service.IAchievementService;
import com.xmd.achievement.service.MqOrderPaymentInfoService;
import com.xmd.achievement.service.ThirdAchievementService;

import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/16/14:13
 * @since 1.0
 */

@Tag(name = "TEST-测试")
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {
    @Resource
    ThirdAchievementService thirdAchievementService;

    @Resource
    IAchievementService achievementService;

    @Resource
    CalculateCustomerContext calculateCustomerContext;
/*    @Resource
    MqServeInProgressInfoService mqServeInProgressInfoService;*/
    @Resource
    MqOrderPaymentInfoService mqOrderPaymentInfoService;
    @Resource
    AchievementHandler achievementHandler;
    @Resource
    CalculateCustomerContextV4 calculateCustomerContextV4;
    @Resource
    private IBusinessAchievementService businessAchievementService;
    @Resource
    InnerService innerService;
    @Autowired
    IAchievementProductDetailService achievementProductDetailService;


    @Operation(summary = "TEST-01-业绩流水录入")
    @GetMapping("thirdAchievementJob")
    public WebResult<Boolean> thirdAchievementJob() {
        List<ThirdAchievementModel> models = thirdAchievementService.queryExecuteTask();
        for (ThirdAchievementModel model : models) {
            try {
                achievementService.handelThirdAchievement(model, null);
            } catch (Exception e) {
                log.error("ThirdAchievementJob任务失败，TaskId:{},失败原因：", model.getTaskId(), e);
            }
        }
        log.info("执行ThirdAchievementJob任务End...");
        return WebResult.success(true);
    }

/*    @Operation(summary = "TEST-03-洗服务中数据到服务完成中")
    @GetMapping("serveDataToPayment")
    public WebResult<Boolean> serveDataToPayment() {
        mqServeInProgressInfoService.serveDataToPayment();
        return WebResult.success(true);
    }*/

    @Operation(summary = "TEST-04 检查服务中的数据成业绩否是正确")
    @PostMapping("checkInServeData")
    public WebResult<Boolean> checkInServeData(@RequestBody @Valid CheckInServeDataIsRightRequest request) {
        MqOrderPaymentInfoModel model = mqOrderPaymentInfoService.queryExcutTaskByTaskId(request.getTaskId());
        achievementHandler.checkInServeData(model);
        return WebResult.success(true);
    }

    @Operation(summary = "TEST-05-修改支付完成状态")
    @PostMapping("updatePayCompleteTest")
    public WebResult<Boolean> updatePayCompleteTest(@RequestBody @Valid UpdatePaycompleteTestRequest request) {
        mqOrderPaymentInfoService.updatePayCompleteTest(request);
        return WebResult.success(true);
    }

    @Operation(summary = "TEST-06-支付完成xxljob")
    @GetMapping("payCompleteExecute")
    public WebResult<Boolean> payCompleteExecute() {
        List<MqOrderPaymentInfoModel> models = mqOrderPaymentInfoService.queryExcutTask();
        for (MqOrderPaymentInfoModel model : models) {
            try {
                achievementHandler.processAchievement(model);
            } catch (Exception e) {
                log.error("MqOrderPaymentInfoModel任务失败，TaskId:{},失败原因：", model.getTaskId(), e);
            }
        }
        log.info("执行MqOrderPaymentInfoModel任务End...");

        return WebResult.success(true);
    }

    @Operation(summary = "TEST-07-重新计算商务人员业绩按照商务月")
    @PostMapping("rebuildBusinessAchStatInfo")
    public WebResult<Boolean> rebuildBusinessAchStatInfo(@RequestBody @Valid RebuildBusinessAchStatInfoRequest request) {
        businessAchievementService.rebuildBusinessAchStatInfo(request);
        return WebResult.success(true);
    }

    @Operation(summary = "TEST-06-获取CRM服务系统客户流失状态")
    @GetMapping("getCrmCustomerStatus")
    public WebResult<Boolean> getCrmCustomerStatus() {
        innerService.getGetCustomerInfoByCondition("23oH6kKYV8h86k1HStz9vw");
        return WebResult.success(true);
    }

    @Operation(summary = "TEST-04-支付完成xxljob测试")
    @GetMapping("paycompleteTest")
    public WebResult<Boolean> paycompleteTest() {
        List<MqOrderPaymentInfoModel> models = mqOrderPaymentInfoService.queryExcutTask();
        for (MqOrderPaymentInfoModel model : models) {
            try {
                achievementHandler.processAchievement(model);
            } catch (Exception e) {
                log.error("MqOrderPaymentInfoModel任务失败，TaskId:{},失败原因：", model.getTaskId(), e);
            }
        }
        log.info("执行MqOrderPaymentInfoModel任务End...");

        return WebResult.success(true);
    }

    @Operation(summary = "TEST-07-商务人员组织信息修复")
    @GetMapping("businessOrgInfoRepair")
    public WebResult<Boolean> businessOrgInfoRepair() {
        achievementProductDetailService.businessOrgInfoRepair();
        return WebResult.success(true);
    }

}
