package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * saas标签分页查询请求参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaasTabPageRequest extends PageRequest {

    @Schema(description = "关联ID", example = "1")
    private Long associationId;

    @Schema(description = "关联名称", example = "示例名称")
    private String associationName;
} 