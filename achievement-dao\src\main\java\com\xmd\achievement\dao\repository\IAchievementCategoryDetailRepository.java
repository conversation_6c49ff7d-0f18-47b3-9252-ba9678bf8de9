package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业绩规格分类明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IAchievementCategoryDetailRepository extends IService<AchievementCategoryDetailModel> {

    List<AchievementCategoryDetailModel> selectAchievementCategoryByCategoryIds(List<Long> achievementCategoryIdList);

    List<AchievementCategoryDetailModel> selectAchievementCategoryByAchievementIds(List<Long> achievementIdList);

    void updateChangeTypeByAchievementId(Long achievementId, Integer changeType);

    List<AchievementCategoryDetailModel> listByUpdateTime(Date startTime);

    void logicDeleteByAchievementId(List<Long> achievementId);

    List<AchievementCategoryDetailModel> selectCategoryByAchievementIdsAndProductCategoryId(List<Long> achievementIdList,Long productCategoryId);
}
