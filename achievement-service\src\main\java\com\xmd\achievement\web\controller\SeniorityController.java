package com.xmd.achievement.web.controller;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.ISeniorityService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PageQuerySeniorityListResponse;
import com.xmd.achievement.service.entity.response.QuerySeniorityDetailResponse;
import com.xmd.achievement.service.entity.response.QuerySeniorityListResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 司龄
 *
 * <AUTHOR>
 * @date: 2024/12/19 11:16
 * @version: 1.0.0
 * @return {@link }
 */
@Tag(name = "SC-司龄接口")
@Slf4j
@RestController
@RequestMapping("/seniority")
public class SeniorityController {
    @Resource
    ISeniorityService seniorityService;


    /**
     * 保存
     **/
    @Operation(summary = "SC-01-司龄保存")
    @PostMapping("saveSeniority")
    public WebResult<Boolean> saveSeniority(@RequestBody @Valid SaveSeniorityRequest request) {
        log.info("SC-01-司龄保存,请求参数:{}", JSONUtil.toJsonStr(request));
        return seniorityService.saveSeniority(request);
    }

    @Operation(summary = "SC-02-分页查询司龄列表")
    @PostMapping("pageQuerySeniorityList")
    public WebResult<PageResponse<PageQuerySeniorityListResponse>> pageQuerySeniorityList(@RequestBody @Valid PageRequest request) {
        log.info("SC-02-分页查询司龄列表,请求参数:{}", JSONUtil.toJsonStr(request));
        return seniorityService.pageQuerySeniorityList(request);
    }

    @Operation(summary = "SC-03-查询司龄详情")
    @PostMapping("querySeniorityDetail")
    public WebResult<QuerySeniorityDetailResponse> querySeniorityDetail(@RequestBody @Valid QuerySeniorityDetailRequest request) {
        log.info("SC-03-查询司龄详情,请求参数:{}", JSONUtil.toJsonStr(request));
        return seniorityService.querySeniorityDetail(request);
    }

    @Operation(summary = "SC-04-查询司龄列表")
    @GetMapping("querySeniorityList")
    public WebResult<List<QuerySeniorityListResponse>> querySeniorityList() {
        log.info("SC-04-查询司龄列表");
        return seniorityService.querySeniorityList();
    }

    @Operation(summary = "SC-05-修改司龄列表")
    @PostMapping("updateSeniority")
    public WebResult<Boolean> updateSeniority(@RequestBody @Valid UpdateSeniorityRequest request) {
        log.info("SC-05-修改司龄列表,请求参数:{}", JSONUtil.toJsonStr(request));
        return seniorityService.updateSeniority(request);
    }
}