package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgInfoNodeResp {

    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long orgId;


    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String name;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Long parentId;

    /**
     * 级别
     */
    @Schema(description = "级别")
    private Integer level;

    /**
     * 1总部；2区域；3分公司；4部门
     */
    @Schema(description = "机构类型1总部；2区域；3分公司；4部门")
    private Integer type;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "子部门树")
    private List<OrgInfoNodeResp> childOrgInfoNodeRespList;

    public List<OrgInfoNodeResp> getChildren() {
        return childOrgInfoNodeRespList != null ? childOrgInfoNodeRespList : Collections.emptyList();
    }

}
