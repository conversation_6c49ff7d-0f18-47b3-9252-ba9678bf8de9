package com.xmd.achievement.support.lengthLimit;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class ByteSizeValidator implements ConstraintValidator<ByteSize, String> {
    private int min;
    private int max;

    @Override
    public void initialize(ByteSize constraintAnnotation) {
        min = constraintAnnotation.min();
        max = constraintAnnotation.max();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        int byteCount = value.getBytes().length;
        return byteCount >= min && byteCount <= max;
    }
}
