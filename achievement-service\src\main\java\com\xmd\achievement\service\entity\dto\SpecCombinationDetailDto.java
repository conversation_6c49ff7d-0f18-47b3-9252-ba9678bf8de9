/*
package com.xmd.achievement.service.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

*/
/**
 * <p>
 * 规格组合明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 *//*

@Data
public class SpecCombinationDetailDto implements Serializable {
    private static final long serialVersionUID = -9164238856182793076L;

    @Schema(description = "规格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格id不能为空")
    private Long specId;

    @Schema(description = "规格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格id不能为空")
    private Long specId;

    @Schema(description = "规格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格id不能为空")
    private Long specId;

    @Schema(description = "规格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格id不能为空")
    private Long specId;

    @Schema(description = "规格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格id不能为空")
    private Long specId;


    @Schema(description = "规格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格id不能为空")
    private Long specId;


    @Schema(description = "规格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格id不能为空")
    private Long specId;
}*/
