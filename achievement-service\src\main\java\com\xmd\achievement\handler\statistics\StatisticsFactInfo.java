package com.xmd.achievement.handler.statistics;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSegmentModel;
import com.xmd.achievement.dao.entity.PositionModel;
import com.xmd.achievement.dao.entity.SeniorityModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 统计因子
 * <AUTHOR>
 * @date: 2024/12/25 11:03
 */
@Data
@Accessors(chain = true)
public class StatisticsFactInfo implements Serializable {
    /**
     * 商务id
     */
    private String businessId;
    /**
     * 当前商务业绩流水集合
     */
    private List<AchievementProductDetailModel> achList;
    /**
     * 职级类型
     */
    private PositionModel positionType;
    /**
     * 职级
     */
    private String position;
    /**
     * 在岗时长
     */
    private Integer tenure;
    /**
     * 司龄
     */
    private SeniorityModel seniority;
    /**
     * 净现金到账，
     */
    private BigDecimal netCashReceipt;
    /**
     * 业绩段
     */
    private AchievementSegmentModel segment;
    /**
     * 实发业绩提成
     */
    private BigDecimal actualCommission;
    /**
     * 新客户数
     */
    private Integer newCustomerCount;
    /**
     *老客户数
     */
    private Integer oldCustomerCount;
    /**
     * 非续费单
     */
    private Integer nonRenewalOrders;
    /**
     * 新开网站个数
     */
    private Integer newWebsiteCount;
    /**
     * 网站非续费客户数
     */
    private Integer websiteNonRenewalCustomers;
    /**
     * 商代提成业绩
     */
    private BigDecimal agentCommissionAchievement;
    /**
     * 网站净现金到账
     */
    private BigDecimal siteNetCashReceipt;
    /**
     * 当前状态 1=支付完成，2=服务完成
     */
    private Integer status;
    /**
     * saas净现金
     */
    private BigDecimal saasNetCash;
}
