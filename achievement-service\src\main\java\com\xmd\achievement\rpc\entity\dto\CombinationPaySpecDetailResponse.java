package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 组合支付规格详情响应
 * 
 * <AUTHOR> Generated
 * @date 2025-01-30
 */
@Data
@Schema(description = "组合支付规格详情响应")
public class CombinationPaySpecDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组合支付关联id
     */
    @Schema(description = "组合支付关联id")
    private Long combinationPayRelId;

    /**
     * 订单支付详情id
     */
    @Schema(description = "订单支付详情id")
    private String orderPayDetailId;

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 交易id
     */
    @Schema(description = "交易id")
    private Long transactionId;

    /**
     * 分期状态
     */
    @Schema(description = "分期状态")
    private Integer installmentStatus;

    /**
     * 分期详情id
     */
    @Schema(description = "分期详情id")
    private Long installmentDetailId;

    /**
     * 分期期数
     */
    @Schema(description = "分期期数")
    private Integer installmentNum;

    /**
     * 订单商品id
     */
    @Schema(description = "订单商品id")
    private Long orderProductId;

    /**
     * 订单规格id
     */
    @Schema(description = "订单规格id")
    private Long orderSpecId;

    /**
     * 商品id
     */
    @Schema(description = "商品id")
    private Long productId;

    /**
     * 商品规格id
     */
    @Schema(description = "商品规格id")
    private Long productSpecId;

    /**
     * 规格名称
     */
    @Schema(description = "规格名称")
    private String specName;

    /**
     * 规格总金额
     */
    @Schema(description = "规格总金额")
    private BigDecimal specTotalAmount;

    /**
     * 应付金额
     */
    @Schema(description = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额")
    private BigDecimal paidAmount;

    /**
     * 渠道实付金额
     */
    @Schema(description = "渠道实付金额")
    private BigDecimal channelPaidAmount;

    /**
     * 渠道支付类型
     */
    @Schema(description = "渠道支付类型")
    private Integer channelPaidType;

    /**
     * 规格分类id
     */
    @Schema(description = "规格分类id")
    private Long specCategoryId;

    /**
     * 规格分类名称
     */
    @Schema(description = "规格分类名称")
    private String specCategoryName;
}
