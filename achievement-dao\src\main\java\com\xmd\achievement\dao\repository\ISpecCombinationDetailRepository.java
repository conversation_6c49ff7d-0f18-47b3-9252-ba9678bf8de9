package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.SpecCombinationDetailModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规格组合明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface ISpecCombinationDetailRepository extends IService<SpecCombinationDetailModel> {

    List<SpecCombinationDetailModel> selectSpecCombinationDetailList(List<Long> combinationIdList);
}
