package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.BaseModel;
import com.xmd.achievement.dao.mapper.AchievementSpecDetailMapper;
import com.xmd.achievement.dao.repository.IAchievementSpecDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业绩规格明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
@Slf4j
public class AchievementSpecDetailRepositoryImpl extends ServiceImpl<AchievementSpecDetailMapper, AchievementSpecDetailModel> implements IAchievementSpecDetailRepository {

    @Resource
    private AchievementSpecDetailMapper achievementSpecDetailMapper;

    @Override
    public List<AchievementSpecDetailModel> selectAllAchievementSpecList() {
        LambdaQueryWrapper<AchievementSpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementSpecDetailModel::getDeleteFlag, 0);
        return achievementSpecDetailMapper.selectList(queryWrapper);
    }

    @Override
    public List<AchievementSpecDetailModel> selectAchievementSpecListByCategoryIds(List<Long> achievementCategoryIdList) {
        LambdaQueryWrapper<AchievementSpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AchievementSpecDetailModel::getAchievementCategoryId, achievementCategoryIdList);
        queryWrapper.eq(AchievementSpecDetailModel::getDeleteFlag, 0);
        return achievementSpecDetailMapper.selectList(queryWrapper);
    }

    @Override
    public void updateChangeTypeByAchievementCategoryId(List<Long> achievementCategoryIds, Integer changeType) {
        LambdaUpdateWrapper<AchievementSpecDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AchievementSpecDetailModel::getAchievementCategoryId, achievementCategoryIds);
        updateWrapper.eq(BaseModel::getDeleteFlag, 0);
        updateWrapper.set(AchievementSpecDetailModel::getDataChangeType, changeType);
        achievementSpecDetailMapper.update(null, updateWrapper);
    }

    @Override
    public List<AchievementSpecDetailModel> selectSpecDetailListByBetweenDate(Date startDate, Date endDate) {
        LambdaQueryWrapper<AchievementSpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(AchievementSpecDetailModel::getCreateTime, startDate,endDate);
        queryWrapper.eq(BaseModel::getDeleteFlag, 0);
        return achievementSpecDetailMapper.selectList(queryWrapper);
    }

    @Override
    public List<AchievementSpecDetailModel> listByUpdateTime(Date startTime) {
        LambdaQueryWrapper<AchievementSpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(BaseModel::getUpdateTime,startTime);
        return this.list(queryWrapper);
    }

    @Override
    public void logicDeleteByOrderIdAndOrderProductId(Long orderId, List<String> orderProductId) {
        LambdaUpdateWrapper<AchievementSpecDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AchievementSpecDetailModel::getOrderId, orderId);
        updateWrapper.in(AchievementSpecDetailModel::getOrderProductId, orderProductId);
        updateWrapper.eq(BaseModel::getDeleteFlag, 0);
        updateWrapper.set(BaseModel::getDeleteFlag, 1);
        achievementSpecDetailMapper.update(null, updateWrapper);
    }

    /**
     * 根据商品业绩Id查询对应所有规格业绩流水
     *
     * @param achievementId 商品业绩Id
     * @return {@link List }<{@link AchievementSpecDetailModel }>
     * <AUTHOR>
     * @since 1.0
     */
    @Override
    public List<AchievementSpecDetailModel> listByAchievementId(Long achievementId) {
        return this.baseMapper.listByAchievementId(achievementId);
    }

    @Override
    public List<AchievementSpecDetailModel> selectByOrderIdAndSpecId(Long orderId, Long specId) {
        LambdaQueryWrapper<AchievementSpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementSpecDetailModel::getOrderId, orderId);
        queryWrapper.eq(AchievementSpecDetailModel::getSpecId, specId);
        queryWrapper.eq(BaseModel::getDeleteFlag, 0);
        queryWrapper.eq(AchievementSpecDetailModel::getStatus, 4);
        return this.list(queryWrapper);
    }
}