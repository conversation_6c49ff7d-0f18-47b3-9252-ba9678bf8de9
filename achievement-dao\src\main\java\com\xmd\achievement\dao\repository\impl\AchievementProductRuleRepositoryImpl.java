package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.AchievementProductRuleModel;
import com.xmd.achievement.dao.mapper.AchievementProductRuleMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IAchievementProductRuleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
@Slf4j
public class AchievementProductRuleRepositoryImpl extends ServiceImpl<AchievementProductRuleMapper,AchievementProductRuleModel> implements IAchievementProductRuleRepository {

@Resource
private AchievementProductRuleMapper achievementProductRuleMapper;

}