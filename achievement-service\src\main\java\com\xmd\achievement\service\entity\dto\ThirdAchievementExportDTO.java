package com.xmd.achievement.service.entity.dto;


import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

@Data
public class ThirdAchievementExportDTO implements Serializable {

    @ExcelProperty("id")
    private String thirdId;

    @ExcelProperty("businessMonth")
    private String businessMonth; // 商务月

    @ExcelProperty("productType")
    private Integer productType; // 产品类型

    @ExcelProperty("productId")
    private Integer productId; // 商品ID

    @ExcelProperty("productName")
    private String productName; // 商品名称

    @ExcelProperty("isWeb")
    private Integer webInfo; // 是否网站

    @ExcelProperty("businessType")
    private String businessType; //业务类型

    @ExcelProperty("state")
    private Integer state; // 状态

    @ExcelProperty("orderRecordCode")
    private String orderRecordCode; // 订单记录编号

    @ExcelProperty("dataSource")
    private Integer dataSource; // 数据来源

    @ExcelProperty("custId")
    private String custId; // 客户ID

    @ExcelProperty("custName")
    private String custName; // 客户名称

    @ExcelProperty("custType")
    private Integer custType; // 客户类型

    @ExcelProperty("custCity")
    private String custCity; // 客户所在城市

    @ExcelProperty("contractCode")
    private String contractCode; // 合同编号

    @ExcelProperty("salerId")
    private String salerId; // 商务ID

    @ExcelProperty("salerName")
    private String salerName; // 商务名称

    @ExcelProperty("shareType")
    private Integer shareType; // 主副类型

    @ExcelProperty("orgId")
    private String orgId; // 分司ID

    @ExcelProperty("areaId")
    private String areaId; // 区域ID

    @ExcelProperty("buId")
    private String buId; // BU ID

    @ExcelProperty("deptId")
    private String deptId; // 部门ID

    @ExcelProperty("singingAmount")
    private Double singingAmount; // 签单金额

    @ExcelProperty("actualAccount")
    private Double actualAccount; // 实际到账

    @ExcelProperty("discountAccount")
    private Double discountAccount; // 折扣金额

    @ExcelProperty("singingDate")
    private Date singingDate; // 签单日期

    @ExcelProperty("toAccountDate")
    private Date toAccountDate; // 到账日期

    @ExcelProperty("firstStandardAccount")
    private Double firstStandardAccount; // 首年标准价

    @ExcelProperty("firstActualAccount")
    private Double firstActualAccount; // 首年到账

    @ExcelProperty("renewStandardAccount")
    private Double renewStandardAccount; // 续费标准价

    @ExcelProperty("renewActualAccount")
    private Double renewActualAccount; // 续费到账

    @ExcelProperty("netCashAccount")
    private Double netCashAccount; // 实发业绩

    @ExcelProperty("saleHiredMoney")
    private Double saleHiredMoney; // 商务提成业绩

    @ExcelProperty("relaySaleHiredMoney")
    private Double relaySaleHiredMoney; // 实发商务提成业绩

    @ExcelProperty("delaySaleHiredMoney")
    private Double delaySaleHiredMoney; // 缓发商务提成业绩

    @ExcelProperty("managerHiredMoney")
    private Double managerHiredMoney; // 经理提成业绩

    @ExcelProperty("currentPrice")
    private Double currentPrice; // 当前价格

    @ExcelProperty("subManagerHiredMoney")
    private Double subManagerHiredMoney; // 分总提成业绩

    @ExcelProperty("dataState")
    private Integer dataState; // 数据状态

    @ExcelProperty("dbInsertTime")
    private Date dbInsertTime; // 数据库插入时间

    @ExcelProperty("creater")
    private String creater; // 创建人

    @ExcelProperty("dbUpdateTime")
    private Date dbUpdateTime; // 数据库更新时间

    @ExcelProperty("updater")
    private String updater; // 修改人
}
