package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/10:40
 * @since 1.0f
 */
@Data
public class QueryPositionListResponse implements Serializable {

    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "职级ID")
    private Long positionId;

    @Schema(description = "职级")
    private String positionName;

    @Schema(description = "职级code")
    private String positionCode;

    @Schema(description = "是否转正 0=未转正，1=已转正")
    private Integer confirmed;

    @Schema(description = "职级选择字符串")
    private String positionCodes;

}
