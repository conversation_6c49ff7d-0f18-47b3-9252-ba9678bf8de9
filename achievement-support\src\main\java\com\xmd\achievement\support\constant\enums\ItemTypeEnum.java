package com.xmd.achievement.support.constant.enums;


import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date: 2024/12/20 11:22
 */
@Getter
public enum ItemTypeEnum {
    /**
     * 时长
     */
    TIME_LENGTH(1,"时长"),
    /**
     * 数量
     */
    NUMBER(2,"数量"),
    /**
     * 额外计费项
     */
    BILLING_ITEM(3,"额外计费项");

    private final Integer type;
    private final String description;

    ItemTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public static String getDescriptionByType(int type) {
        for (ItemTypeEnum flag : ItemTypeEnum.values()) {
            if (flag.getType().equals(type)) {
                return flag.getDescription();
            }
        }
        throw new IllegalArgumentException("No enum constant with type " + type);
    }
}
