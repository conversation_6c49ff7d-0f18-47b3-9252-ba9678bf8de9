package com.xmd.achievement.service;


import com.xmd.achievement.dao.entity.SeniorityModel;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PageQuerySeniorityListResponse;
import com.xmd.achievement.service.entity.response.QuerySeniorityDetailResponse;
import com.xmd.achievement.service.entity.response.QuerySeniorityListResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;

import java.util.List;

/**
 * 司龄服务接口
 * <AUTHOR>
 */
public interface ISeniorityService {

    /**
     * 司龄保存
     *
     * @param request 司龄数据传输对象
     * @return Boolean 操作结果
     */
    WebResult<Boolean> saveSeniority(SaveSeniorityRequest request);

    /**
     * 分页查询司龄列表
     *
     * @param request 司龄数据传输对象
     * @return PageResponse<PageQuerySeniorityListResponse> 操作结果
     */
    WebResult<PageResponse<PageQuerySeniorityListResponse>> pageQuerySeniorityList(PageRequest request);

    /**
     * 查询司龄详情
     *
     * @param request 请求参数
     * @return WebResult<QuerySeniorityListResponse> 操作结果
     */
    WebResult<QuerySeniorityDetailResponse> querySeniorityDetail(QuerySeniorityDetailRequest request);

    /**
     * 查询司龄列表
     *
     * @param request 请求参数
     * @return WebResult<QuerySeniorityListResponse> 操作结果
     */
    WebResult<List<QuerySeniorityListResponse>> querySeniorityList();

    /**
     * 修改司龄列表
     *
     * @param request 请求参数
     * @return Boolean 操作结果
     */
    WebResult<Boolean> updateSeniority(UpdateSeniorityRequest request);

    /**
     * 内部使用
     * 根据在岗时长获取司龄细分
     * @param tenure  在岗时长
     * <AUTHOR>
     * @date: 2024/12/26 09:52
     * @version: 1.0.0
     * @return {@link }
     */
    SeniorityModel getSeniority(Integer tenure);

    /**
     * 查询所有司龄段全表数据
     * @return List<SeniorityModel>
     */
    List<SeniorityModel> getAllSeniorities();
}
