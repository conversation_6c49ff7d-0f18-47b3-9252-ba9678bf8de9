package com.xmd.achievement.handler.statistics;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.enums.SiteFlagEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 网站净现金到账处理器
 * <AUTHOR>
 * @date: 2024/12/25 11:54
 */
@Service
public class SiteNetCashReceiptHandler implements StatisticsHandler {
    @Override
    public void statistics(StatisticsFactInfo factInfo) {
        List<AchievementProductDetailModel> achList = factInfo.getAchList();
        BigDecimal siteNetCashReceipt = achList.stream()
                .filter(ach -> SiteFlagEnum.YES.getType().equals(ach.getSiteFlag()))
                .map(AchievementProductDetailModel::getNetCash)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        factInfo.setSiteNetCashReceipt(siteNetCashReceipt);
    }
}
