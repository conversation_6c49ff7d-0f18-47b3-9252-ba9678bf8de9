package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售任务审核提交参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
public class SaleTaskAuditRequest implements
        Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "任务主键集合")
    @NotEmpty(message = "任务主键集合不能为空")
    @Size(message = "任务主键集合不能为空")
    private List<Long> ids;

    @Schema(description = "审核意见")
    @NotNull(message = "审核意见不能为空")
    private Integer auditResult;

    @Schema(description = "审核备注")
    private String auditRemark;

}