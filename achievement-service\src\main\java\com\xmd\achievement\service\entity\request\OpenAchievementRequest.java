package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 业绩查询开放接口 查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Schema(description = "业绩查询开放接口 查询参数")
public class OpenAchievementRequest implements Serializable {

    private static final long serialVersionUID = -684987758321756898L;

    @Schema(description = "公司ID")
    private List<Long> companyId;

    @Schema(description = "部门ID")
    private List<Long> deptId;

    @Schema(description = "商务月ID")
    private List<Long> businessMonthId;

    @Schema(description = "商务ID")
    private List<String> businessId;
}
