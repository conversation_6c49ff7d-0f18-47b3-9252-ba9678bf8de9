package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 政策规格明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class PolicySpecDetailRequest implements Serializable {
    private static final long serialVersionUID = 1039900543509743363L;

    @Schema(description = "业绩计收节点 1=支付完成，2=生产完成\"", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业绩计收节点不能为空")
    private Integer revenueNode;

    @Schema(description = "业绩核算比例", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业绩核算比例不能为空")
    private BigDecimal achievementRatio;

    @Schema(description = "实发业绩提成比例", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "实发业绩提成比例不能为空")
    private BigDecimal commissionRatio;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "商品名称")
    private String productName;

    @Schema(description = "规格分类ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格分类ID不能为空")
    private Long specCategoryId;

    @Schema(description = "规格分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "规格分类名称")
    @NotBlank(message = "规格分类名称不能为空")
    private String specCategoryName;

    @Schema(description = "规格ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格ID不能为空")
    private Long specId;

    @Schema(description = "规格名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "规格名称")
    @NotBlank(message = "规格名称不能为空")
    private String specName;

    @Schema(description = "商代政策性成本新开", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商代政策性成本新开不能为空")
    private BigDecimal policyCostOpen;

    @Schema(description = "商代政策性成本续费", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商代政策性成本续费不能为空")
    private BigDecimal policyCostRenew;

    @Schema(description = "商代政策性成本另购", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商代政策性成本另购不能为空")
    private BigDecimal policyCostAdd;

    @Schema(description = "商代政策性成本升级", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商代政策性成本升级不能为空")
    private BigDecimal policyCostUpgrade;
}
