package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 根据机构id查询机构商务信息返回信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class OrgBusinessResponse implements Serializable {
    private static final long serialVersionUID = 6864734240174716302L;

    @Schema(description = "机构id")
    private Long orgId;

    @Schema(description = "父机构id")
    private Long parentId;

    @Schema(description = "机构全名")
    private String shortName;

    @Schema(description = "机构类别（1-集团 3-区域 4-分公司 5-部门）")
    private Integer orgType;

    @Schema(description = "市场分类")
    private Integer marketCategoryId;

    @Schema(description = "调整期（是：Y，“”为否或无效属性）")
    private String isTzq;

    @Schema(description = "月末在岗商务代表人数")
    private String salesCounts;

    @Schema(description = "实际商务部门数量")
    private String deptCounts;

    @Schema(description = "开启/关闭时间")
    private String effectDate;

    @Schema(description = "是否新开0-否 1-是")
    private String isXk;

    @Schema(description = "新开部门数")
    private String xkCounts;

    @Schema(description = "调整期部门数")
    private String tzCounts;

    @Schema(description = "干部输出")
    private String outCounts;

    @Schema(description = "正式骨干数量")
    private String ggCounts;

    @Schema(description = "部均正式骨干")
    private String ggAvg;

    @Schema(description = "在岗人数")
    private String zgCounts;

    @Schema(description = "出单人数")
    private String cdCounts;

    @Schema(description = "出单率")
    private String cdLv;

    @Schema(description = "职能体系ID")
    private Long orgFunction;

    @Schema(description = "机构领导ID")
    private Long eid;

    @Schema(description = "机构领导名称")
    private String leader;

    @Schema(description = "任职本机构日期")
    private Date leaderDate;

    @Schema(description = "考核部门数")
    private BigDecimal tcSubOrgCounts;

    @Schema(description = "部门数")
    private Integer subOrgCounts;

    @Schema(description = "成立时长(月)")
    private Integer monthCounts;

    @Schema(description = "机构全体在岗人数")
    private Integer allCounts;

    @Schema(description = "机构编制数")
    private Integer selfPreCounts;

    @Schema(description = "商务标签")
    private String orgClass2;

    @Schema(description = "商务组类型")
    private String orgClass3;

    @Schema(description = "商务组类型")
    private String salesCounts2;
}
