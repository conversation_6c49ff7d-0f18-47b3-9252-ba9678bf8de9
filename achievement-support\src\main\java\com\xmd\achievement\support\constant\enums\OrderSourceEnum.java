package com.xmd.achievement.support.constant.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public enum OrderSourceEnum {

    /**
     * 订单来源：1=商务签单，2=官网，3=驾驶舱-PC,4=驾驶舱移动端，5优化师工作台，6商务工作台，7优化师录单
     */
    UNKNOWN_ZERO(0, "未知来源_中企"),
    UNKNOWN_TWENTY_TWO(22, "未知来源_中企"),
    PROXY_ORDER(1, "商务签单"),
    PORTAL(2, "官网"),
    DRIVE_DOCKER_PC(3, "驾驶舱-PC"),
    DRIVE_DOCKER_MOBILE(4, "驾驶舱移动端"),
    SOP_PLATFORM(5, "优化师工作台"),
    SALER_PLATFORM(6, "商务工作台"),
    OFF_LINE_ORDER(7, "优化师录单"),
    BOSS(17, "BOSS"),
    NEW_PL_ON_LINE(18, "新平台线上,"),
    NEW_PL_OFF_LINE(19, "新平台线下"),
    NEW_PL_SIGINA(20, "新平台电子签单"),
    NEW_COLUND(21, "新云市场"),
    ;
    private Integer type;

    private String visitorCode;

    OrderSourceEnum(Integer type, String visitorCode) {
        this.type = type;
        this.visitorCode = visitorCode;
    }

    public String getVisitorCode() {
        return visitorCode;
    }

    public Integer getType() {
        return type;
    }

    public static OrderSourceEnum getOrderSourceByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        OrderSourceEnum[] values = OrderSourceEnum.values();
        for (int i = 0; i < values.length; i++) {
            OrderSourceEnum value = values[i];
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static OrderSourceEnum getOrderSourceByVisitorCode(String visitorCode) {
        if (StringUtils.isEmpty(visitorCode)) {
            return null;
        }
        OrderSourceEnum[] values = OrderSourceEnum.values();
        for (int i = 0; i < values.length; i++) {
            OrderSourceEnum value = values[i];
            if (StringUtils.equals(value.getVisitorCode(), visitorCode)) {
                return value;
            }
        }
        return null;
    }

}
