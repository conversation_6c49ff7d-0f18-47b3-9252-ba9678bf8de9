package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/11:29
 * @since 1.0
 */
@Getter
public enum ProductChurnStatusEnum {
    /**
     * 流失
     */
    CHURN(1, "流失"),
    /**
     * 未流失
     */
    NO_CHURN(2, "未流失");

    private final Integer code;
    private final String msg;

    ProductChurnStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
