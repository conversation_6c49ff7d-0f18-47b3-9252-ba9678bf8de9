package com.xmd.achievement.web.controller;

import com.xmd.achievement.service.ISaasTabService;
import com.xmd.achievement.service.entity.request.SaasTabAddRequest;
import com.xmd.achievement.service.entity.request.SaasTabDeleteRequest;
import com.xmd.achievement.service.entity.request.SaasTabPageRequest;
import com.xmd.achievement.service.entity.request.SaasTabUpdateRequest;
import com.xmd.achievement.service.entity.response.SaasTabResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * saas标签表控制器
 */
@Slf4j
@RestController
@RequestMapping("/saasTab")
@Tag(name = "saas标签管理")
public class SaasTabController {

    @Resource
    private ISaasTabService saasTabService;

    @Operation(summary = "分页查询saas标签列表")
    @PostMapping("/page")
    public WebResult<PageResponse<SaasTabResponse>> pageQuery(@RequestBody @Valid SaasTabPageRequest request) {
        log.info("分页查询saas标签列表");
        PageResponse<SaasTabResponse> pageResponse = saasTabService.pageQuery(request);
        return WebResult.success(pageResponse);
    }

    @Operation(summary = "新增saas标签")
    @PostMapping("/add")
    public WebResult<Boolean> add(@RequestBody @Valid SaasTabAddRequest request) {
        log.info("新增saas标签");
        boolean result = saasTabService.add(request);
        return WebResult.success(result);
    }

    @Operation(summary = "修改saas标签")
    @PostMapping("/update")
    public WebResult<Boolean> update(@RequestBody @Valid SaasTabUpdateRequest request) {
        log.info("修改saas标签");
        boolean result = saasTabService.update(request);
        return WebResult.success(result);
    }

    @Operation(summary = "删除saas标签")
    @PostMapping("/delete")
    public WebResult<Boolean> delete(@RequestBody @Valid SaasTabDeleteRequest request) {
        log.info("删除saas标签");
        boolean result = saasTabService.delete(request.getId());
        return WebResult.success(result);
    }

    @Operation(summary = "批量刷新saas标签数据")
    @PostMapping("/batchRefresh")
    public WebResult<Boolean> batchRefresh() {
        log.info("开始执行saas标签数据批量刷新");
        boolean result = saasTabService.batchRefresh();
        return WebResult.success(result);
    }
}