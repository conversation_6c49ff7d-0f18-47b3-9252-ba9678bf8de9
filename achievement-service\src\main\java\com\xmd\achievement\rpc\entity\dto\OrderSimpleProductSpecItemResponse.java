package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *订单商品规格条目信息
 * <AUTHOR>
 * @date 2024年11月11日 下午1:43
 */
@Data
public class OrderSimpleProductSpecItemResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;

    /**
     * 订单商品规格计费项id
     */

    private Long orderProductSpecItemId;

    /**
     * 订单商品规格id
     */

    private Long orderProductSpecId;

    /**
     * 订单商品id
     */

    private String orderProductId;

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 商品id
     */

    private Long productId;

    /**
     * 商品规格id
     */

    private Long productSpecId;

    /**
     * 商品规格计费项id
     */

    private Long productSpecItemId;

    /**
     * 项目类型-对应商品计费项类型：数量1，时长-2，自定义：3
     */

    private Integer itemType;

    /**
     * 计费单位
     */

    private String itemUnit;

    /**
     * 计费个数
     */

    private Integer itemNum;

    /**
     * 计费项单价
     */

    private BigDecimal unitPrice;

    /**
     * 标准价
     */

    private BigDecimal basePrice;

    /**
     * 折扣优惠金额
     */

    private BigDecimal discountAmount;

    /**
     * 应付金额
     */

    private BigDecimal payableAmount;

    /**
     * 实付金额
     */

    private BigDecimal paidAmount;

    /**
     * 删除标记: 0-未删除|1-删除
     */

    private Integer deleteFlag;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 创建人id
     */

    private String createUserId;

    /**
     * 创建人名称
     */

    private String createUserName;

    /**
     * 更新时间
     */

    private Date updateTime;

    /**
     * 更新人id
     */

    private String updateUserId;

    /**
     * 更新人名称
     */

    private String updateUserName;
}
