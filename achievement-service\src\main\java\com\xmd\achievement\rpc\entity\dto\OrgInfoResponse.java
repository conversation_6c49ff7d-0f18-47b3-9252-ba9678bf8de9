package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * orgInfo返回信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class OrgInfoResponse implements Serializable {
    private static final long serialVersionUID = -3342279408937285039L;
    /**
     * 机构id
     */
    private Long id;

    /**
     * 机构名称
     */
    private String name;

    /**
     * 机构上级id
     */
    private Long parentId;

    /**
     * 来源
     */
    private String source;

    /**
     * 可用状态（0否；1是）
     */
    private Integer state;

    /**
     * 机构层级类型（HQ总部；AREA区域；SUB分公司；DEPT部门）
     */
    private String type;

    /**
     * 机构业务类型（0综合；1商务；2技术；3服务；4生产；5财务；6管家；9其他）
     */
    private Integer businessType;

    /**
     * 商务标签（0-通用 ，1-电商）
     */
    private Integer orgClass2;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 是否商务机构（0-否 1-是）
     */
    private Integer isCommerce;

    /**
     * n级市场分类
     */
    private Integer marketCategoryId;

    /**
     * 机构ID路径
     */
    private String orgPath;

    /**
     * 全路径名称
     */
    private String shortName;
}
