package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 客户类型枚举 ( 新客户/老客户/非新老)
 * <AUTHOR>
 * @date: 2024/12/18 10:07
 * @version: 1.0.0
 */
@Getter
public enum CustomerType {

    NEW(1, "新客户"), OLD(2, "老客户"), NON_EXISTENT(3, "非新老");

    private final Integer type;
    private final String description;

    CustomerType(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public static String getDescriptionByType(int type) {
        for (CustomerType flag : CustomerType.values()) {
            if (flag.getType().equals(type)) {
                return flag.getDescription();
            }
        }
        throw new IllegalArgumentException("No enum constant with type " + type);
    }
}
