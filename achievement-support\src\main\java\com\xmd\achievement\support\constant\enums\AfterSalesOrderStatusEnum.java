package com.xmd.achievement.support.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 售后单状态枚举类
 */
@Getter
@AllArgsConstructor
public enum AfterSalesOrderStatusEnum {

    AMOUNT_CALCULATING(1, "金额核算中", "金额核算中"),
    AMOUNT_PENDING_CONFIRMATION(2, "金额待确认", "金额确认中"),
    PENDING_COMPANY_APPROVAL(3, "待分司/总部审核", "分司/总部审核中"),
    CONTRACT_PENDING_UPLOAD(4, "合同待上传", "待上传终止合同"),
    CONTRACT_PENDING_REVIEW(5, "合同待审核", "待审核终止合同"),
    REFUND_PENDING(6, "待退款", "已关闭服务，待财务操作退款"),
    TRANSFER_PENDING(7, "待转款", "已关闭服务，待财务操作转款"),
    AFTER_SALES_COMPLETED(8, "售后完成", "已完成退/转款，售后已完成"),
    AFTER_SALES_CANCELLED(9, "售后取消", "售后流程已终止，服务已重新开启");

    private final int status;
    private final String description;
    private final String remark;

    /**
     * 根据状态码获取枚举值
     *
     * @param status 状态码
     * @return 对应的枚举项
     * @throws IllegalArgumentException 如果找不到对应的状态码
     */
    public static AfterSalesOrderStatusEnum fromStatus(int status) {
        return Arrays.stream(values())
                .filter(e -> e.getStatus() == status)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未知的售后状态码: " + status));
    }

    public static String getDescriptionByStatus(int status) {
        return Arrays.stream(values())
                .filter(e -> e.getStatus() == status)
                .map(AfterSalesOrderStatusEnum::getDescription)
                .findFirst()
                .orElse("未知状态");
    }

    public static String getRemarkByStatus(int status) {
        return Arrays.stream(values())
                .filter(e -> e.getStatus() == status)
                .map(AfterSalesOrderStatusEnum::getRemark)
                .findFirst()
                .orElse("");
    }
}
