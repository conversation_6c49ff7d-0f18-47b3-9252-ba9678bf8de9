package com.xmd.achievement.web.util;

import cn.hutool.core.util.ObjectUtil;
import com.xmd.achievement.support.constant.enums.TimeOperationEnum;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import static com.xmd.achievement.async.constant.ServiceConstant.MothConstant.ZONE_ID;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/31 14:33
 * @version: 1.0.0
 * @return {@link }
 */
public class DateUtils {


    public static LocalDateTime convertToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        return LocalDateTime.ofInstant(instant, ZONE_ID);
    }

    public static Date convertToDate(LocalDateTime localDateTime) {
        Instant instant = localDateTime.atZone(ZONE_ID).toInstant();
        return Date.from(instant);
    }

    /**
     * 根据枚举类型格式化时间
     *
     * @param date   时间对象
     * @param format 时间格式枚举
     * @return 格式化后的时间字符串
     */
    public static String format(Date date, DateTimeFormatStyleEnum format) {
        if (date == null || format == null) {
            throw new IllegalArgumentException("Date and format must not be null");
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format.getCode());
        return sdf.format(date);
    }

    /**
     * 根据传入的日期、操作类型和天数，计算并返回新的日期
     *
     * @param date      原始日期
     * @param operation 操作类型（增加或减少）
     * @param days      天数
     * @return 计算后的日期
     */
    public static Date calculateDate(Date date, TimeOperationEnum operation, int days) {
        if (date == null) {
            throw new IllegalArgumentException("日期不能为空");
        }
        if (days < 0) {
            throw new IllegalArgumentException("天数必须为非负数");
        }

        // 使用 Calendar 进行日期计算
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 根据操作类型增加或减少天数
        if (operation == TimeOperationEnum.ADD) {
            calendar.add(Calendar.DAY_OF_YEAR, days);
        } else if (operation == TimeOperationEnum.SUBTRACT) {
            calendar.add(Calendar.DAY_OF_YEAR, -days);
        } else {
            throw new IllegalArgumentException("无效的操作类型");
        }

        // 返回计算后的日期
        return calendar.getTime();
    }

    /**
     * 计算两个日期的时间差值
     *
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 时间差值对象（包含天、小时、分钟、秒）
     */
    public static DateDifference calculateDifference(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            throw new IllegalArgumentException("日期不能为空");
        }

        // 计算时间差值（毫秒）
        long diffInMillis = date1.getTime() - date2.getTime();

        // 将毫秒转换为天、小时、分钟、秒
        long diffInSeconds = Math.abs(diffInMillis) / 1000;
        long diffInMinutes = diffInSeconds / 60;
        long diffInHours = diffInMinutes / 60;
        long diffInDays = diffInHours / 24;

        // 返回时间差值对象
        return new DateDifference(
                diffInDays,
                diffInHours % 24,
                diffInMinutes % 60,
                diffInSeconds % 60
        );
    }

    /**
     * 时间差值对象
     */
    public static class DateDifference {
        private final long days;
        private final long hours;
        private final long minutes;
        private final long seconds;

        public DateDifference(long days, long hours, long minutes, long seconds) {
            this.days = days;
            this.hours = hours;
            this.minutes = minutes;
            this.seconds = seconds;
        }

        public long getDays() {
            return days;
        }

        public long getHours() {
            return hours;
        }

        public long getMinutes() {
            return minutes;
        }

        public long getSeconds() {
            return seconds;
        }

        @Override
        public String toString() {
            return String.format("%d 天 %d 小时 %d 分钟 %d 秒", days, hours, minutes, seconds);
        }
    }

    /**
     * 获取当天的最小时间（00:00:00）
     *
     * @param date 日期
     * @return 当天的最小时间
     */
    public static Date getStartOfDay(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("日期不能为空");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置为 0 点
        calendar.set(Calendar.MINUTE, 0);      // 设置为 0 分
        calendar.set(Calendar.SECOND, 0);      // 设置为 0 秒
        calendar.set(Calendar.MILLISECOND, 0); // 设置为 0 毫秒

        return calendar.getTime();
    }

    /**
     * 获取当天的最大时间（23:59:59）
     *
     * @param date 日期
     * @return 当天的最大时间
     */
    public static Date getEndOfDay(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("日期不能为空");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23); // 设置为 23 点
        calendar.set(Calendar.MINUTE, 59);      // 设置为 59 分
        calendar.set(Calendar.SECOND, 59);      // 设置为 59 秒
        //calendar.set(Calendar.MILLISECOND, 999); // 设置为 999 毫秒

        return calendar.getTime();
    }

    /**
     * 格式化日期为字符串
     *
     * @param date   日期
     * @param format 日期格式
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 将字符串时间转换为 Date 类型
     *
     * @param dateStr 时间字符串（格式：yyyy-M-d HH:mm:ss.S）
     * @return Date 对象
     */
    public static Date stringToDate(String dateStr, DateTimeFormatStyleEnum format) {
        if (ObjectUtil.isEmpty(dateStr)) {
            return null;
        }
        // 定义输入时间格式
        SimpleDateFormat inputFormat = new SimpleDateFormat(format.getCode());
        // 解析字符串为 Date 对象
        try {
            return inputFormat.parse(dateStr);
        } catch (Exception e) {
            throw new BusinessException("stringToDate 时间转换错误");
        }

    }

    public static Date stringToDate(String dateStr, String pattern) {
        if (ObjectUtil.isEmpty(dateStr)) {
            return null;
        }
        // 指定日期格式和 Locale
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern, Locale.ENGLISH);
        // 解析字符串为 ZonedDateTime 对象
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateStr, formatter);
        // 如果需要转换为 Date 类型
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 比较两个Date类型的日期，如果date1大于date2，返回true；否则返回false。
     *
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 如果date1大于date2，返回true；否则返回false。
     */
    public static boolean isDateAfter(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            throw new IllegalArgumentException("日期参数不能为null");
        }
        return date1.after(date2);
    }

    public static void main(String[] args) {
        // 示例用法
        Date date1 = new Date(); // 当前时间
        Date date2 = new Date(System.currentTimeMillis() - 1000 * 60 * 60); // 一小时前的时间

        System.out.println("date1是否大于date2: " + isDateAfter(date1, date2));
    }
}
