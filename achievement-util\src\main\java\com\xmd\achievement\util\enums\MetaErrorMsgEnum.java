package com.xmd.achievement.util.enums;

import lombok.Getter;

/**
 * meta全局错误码.
 * 参考文档：<a href="https://work.weixin.qq.com/api/doc/90000/90139/90313">meta全局错误码</a>
 */
@Getter
public enum MetaErrorMsgEnum {
    /**
     * 系统繁忙；服务器暂不可用，建议稍候重试。建议重试次数不超过3次.
     */
    CODE_1(-1, "系统繁忙；服务器暂不可用，建议稍候重试。建议重试次数不超过3次。"),
    /**
     * 请求成功；接口调用成功.
     */
    CODE_0(0, "ok"),
    /**
     * 数据版本冲突；可能有多个调用端同时修改数据，稍后重试.
     */
    CODE_6000(6000, "数据版本冲突；可能有多个调用端同时修改数据，稍后重试"),
    /**
     * 不合法的secret参数；secret在应用详情/通讯录管理助手可查看.
     */
    CODE_40001(40001, "不合法的secret参数；secret在应用详情/通讯录管理助手可查看"),
    /**
     * 无效的UserID.
     */
    CODE_40003(40003, "无效的UserID"),
    /**
     * 不合法的媒体文件类型；不满足系统文件要求。参考：上传的媒体文件限制.
     */
    CODE_40004(40004, "不合法的媒体文件类型；不满足系统文件要求。参考：上传的媒体文件限制"),
    /**
     * 不合法的type参数；合法的type取值，参考：上传临时素材.
     */
    CODE_40005(40005, "不合法的type参数；合法的type取值，参考：上传临时素材"),
    /**
     * 不合法的文件大小；系统文件要求，参考：上传的媒体文件限制.
     */
    CODE_40006(40006, "不合法的文件大小；系统文件要求，参考：上传的媒体文件限制"),
    /**
     * 不合法的media_id参数.
     */
    CODE_40007(40007, "不合法的media_id参数"),
    /**
     * 不合法的msgtype参数；合法的msgtype取值，参考：消息类型.
     */
    CODE_40008(40008, "不合法的msgtype参数；合法的msgtype取值，参考：消息类型"),
    /**
     * 上传图片大小不是有效值；图片大小的系统限制，参考上传的媒体文件限制.
     */
    CODE_40009(40009, "上传图片大小不是有效值；图片大小的系统限制，参考上传的媒体文件限制"),
    /**
     * 上传视频大小不是有效值；视频大小的系统限制，参考上传的媒体文件限制.
     */
    CODE_40011(40011, "上传视频大小不是有效值；视频大小的系统限制，参考上传的媒体文件限制"),
    /**
     * 不合法的CorpID；需确认CorpID是否填写正确，在 web管理端-设置 可查看.
     */
    CODE_40013(40013, "不合法的CorpID；需确认CorpID是否填写正确，在 web管理端-设置 可查看"),
    /**
     * 不合法的access_token.
     */
    CODE_40014(40014, "不合法的access_token"),
    /**
     * 不合法的按钮个数；菜单按钮1-3个.
     */
    CODE_40016(40016, "不合法的按钮个数；菜单按钮1-3个"),
    /**
     * 不合法的按钮类型；支持的类型，参考：按钮类型.
     */
    CODE_40017(40017, "不合法的按钮类型；支持的类型，参考：按钮类型"),
    /**
     * 不合法的按钮名字长度；长度应不超过16个字节.
     */
    CODE_40018(40018, "不合法的按钮名字长度；长度应不超过16个字节"),
    /**
     * 不合法的按钮KEY长度；长度应不超过128字节.
     */
    CODE_40019(40019, "不合法的按钮KEY长度；长度应不超过128字节"),
    /**
     * 不合法的按钮URL长度；长度应不超过1024字节.
     */
    CODE_40020(40020, "不合法的按钮URL长度；长度应不超过1024字节"),
    /**
     * 不合法的子菜单级数；只能包含一级菜单和二级菜单.
     */
    CODE_40022(40022, "不合法的子菜单级数；只能包含一级菜单和二级菜单"),
    /**
     * 不合法的子菜单按钮个数；子菜单按钮1-5个.
     */
    CODE_40023(40023, "不合法的子菜单按钮个数；子菜单按钮1-5个"),
    /**
     * 不合法的子菜单按钮类型；支持的类型，参考：按钮类型.
     */
    CODE_40024(40024, "不合法的子菜单按钮类型；支持的类型，参考：按钮类型"),
    /**
     * 不合法的子菜单按钮名字长度；支持的类型，参考：按钮类型.
     */
    CODE_40025(40025, "不合法的子菜单按钮名字长度；支持的类型，参考：按钮类型"),
    /**
     * 不合法的子菜单按钮KEY长度；长度应不超过60个字节.
     */
    CODE_40026(40026, "不合法的子菜单按钮KEY长度；长度应不超过60个字节"),
    /**
     * 不合法的子菜单按钮URL长度；长度应不超过1024字节.
     */
    CODE_40027(40027, "不合法的子菜单按钮URL长度；长度应不超过1024字节"),
    /**
     * 不合法的oauth_code.
     */
    CODE_40029(40029, "不合法的oauth_code"),
    /**
     * 不合法的UserID列表；指定的UserID列表，至少存在一个UserID不在通讯录中.
     */
    CODE_40031(40031, "不合法的UserID列表；指定的UserID列表，至少存在一个UserID不在通讯录中"),
    /**
     * 不合法的UserID列表长度.
     */
    CODE_40032(40032, "不合法的UserID列表长度"),
    /**
     * 不合法的请求字符；不能包含\\uxxxx格式的字符.
     */
    CODE_40033(40033, "不合法的请求字符；不能包含\\uxxxx格式的字符"),
    /**
     * 不合法的参数.
     */
    CODE_40035(40035, "不合法的参数"),
    /**
     * 不合法的模板id长度
     */
    CODE_40036(40036, "不合法的模板id长度"),
    /**
     * 无效的模板id
     */
    CODE_40037(40037, "无效的模板id"),
    /**
     * 不合法的url长度
     */
    CODE_40039(40039, "不合法的url长度"),
    /**
     * chatid不存在；会话需要先创建后，才可修改会话详情或者发起聊天.
     */
    CODE_40050(40050, "chatid不存在；会话需要先创建后，才可修改会话详情或者发起聊天"),
    /**
     * 不合法的子菜单url域名.
     */
    CODE_40054(40054, "不合法的子菜单url域名"),
    /**
     * 不合法的菜单url域名.
     */
    CODE_40055(40055, "不合法的菜单url域名"),
    /**
     * 不合法的agentid.
     */
    CODE_40056(40056, "不合法的agentid"),
    /**
     * 不合法的callbackurl或者callbackurl验证失败；可自助到开发调试工具重现.
     */
    CODE_40057(40057, "不合法的callbackurl或者callbackurl验证失败；可自助到开发调试工具重现"),
    /**
     * 不合法的参数；传递参数不符合系统要求，需要参照具体API接口说明.
     */
    CODE_40058(40058, "不合法的参数；传递参数不符合系统要求，需要参照具体API接口说明"),
    /**
     * 不合法的上报地理位置标志位；开关标志位只能填 0 或者 1.
     */
    CODE_40059(40059, "不合法的上报地理位置标志位；开关标志位只能填 0 或者 1"),
    /**
     * 参数为空.
     */
    CODE_40063(40063, "参数为空"),
    /**
     * 不合法的部门列表；部门列表为空，或者至少存在一个部门ID不存在于通讯录中.
     */
    CODE_40066(40066, "不合法的部门列表；部门列表为空，或者至少存在一个部门ID不存在于通讯录中"),
    /**
     * 不合法的标签ID；标签ID未指定，或者指定的标签ID不存在.
     */
    CODE_40068(40068, "不合法的标签ID；标签ID未指定，或者指定的标签ID不存在"),
    /**
     * 指定的标签范围结点全部无效.
     */
    CODE_40070(40070, "指定的标签范围结点全部无效"),
    /**
     * 不合法的标签名字；标签名字已经存在.
     */
    CODE_40071(40071, "不合法的标签名字；标签名字已经存在"),
    /**
     * 不合法的标签名字长度；不允许为空，最大长度限制为32个字（汉字或英文字母）.
     */
    CODE_40072(40072, "不合法的标签名字长度；不允许为空，最大长度限制为32个字（汉字或英文字母）"),
    /**
     * 不合法的openid；openid不存在，需确认获取来源.
     */
    CODE_40073(40073, "不合法的openid；openid不存在，需确认获取来源"),
    /**
     * news消息不支持保密消息类型；图文消息支持保密类型需改用mpnews.
     */
    CODE_40074(40074, "news消息不支持保密消息类型；图文消息支持保密类型需改用mpnews"),
    /**
     * 不合法的pre_auth_code参数；预授权码不存在，参考：获取预授权码.
     */
    CODE_40077(40077, "不合法的pre_auth_code参数；预授权码不存在，参考：获取预授权码"),
    /**
     * 不合法的auth_code参数；需确认获取来源，并且只能消费一次.
     */
    CODE_40078(40078, "不合法的auth_code参数；需确认获取来源，并且只能消费一次"),
    /**
     * 不合法的suite_secret；套件secret可在第三方管理端套件详情查看.
     */
    CODE_40080(40080, "不合法的suite_secret；套件secret可在第三方管理端套件详情查看"),
    /**
     * 不合法的suite_token.
     */
    CODE_40082(40082, "不合法的suite_token"),
    /**
     * 不合法的suite_id；suite_id不存在.
     */
    CODE_40083(40083, "不合法的suite_id；suite_id不存在"),
    /**
     * 不合法的permanent_code参数.
     */
    CODE_40084(40084, "不合法的permanent_code参数"),
    /**
     * 不合法的的suite_ticket参数；suite_ticket不存在或者已失效.
     */
    CODE_40085(40085, "不合法的的suite_ticket参数；suite_ticket不存在或者已失效"),
    /**
     * 不合法的第三方应用appid；至少有一个不存在应用id.
     */
    CODE_40086(40086, "不合法的第三方应用appid；至少有一个不存在应用id"),
    /**
     * jobid不存在；请检查 jobid 来源.
     */
    CODE_40088(40088, "jobid不存在；请检查 jobid 来源"),
    /**
     * 批量任务的结果已清理；系统仅保存最近5次批量任务的结果。可在通讯录查看实际导入情况.
     */
    CODE_40089(40089, "批量任务的结果已清理；系统仅保存最近5次批量任务的结果。可在通讯录查看实际导入情况"),
    /**
     * secret不合法；可能用了别的企业的secret.
     */
    CODE_40091(40091, "secret不合法；可能用了别的企业的secret"),
    /**
     * 导入文件存在不合法的内容.
     */
    CODE_40092(40092, "导入文件存在不合法的内容"),
    /**
     * 不合法的jsapi_ticket参数；ticket已失效，或者拼写错误.
     */
    CODE_40093(40093, "不合法的jsapi_ticket参数；ticket已失效，或者拼写错误"),
    /**
     * 不合法的URL；缺少主页URL参数，或者URL不合法（链接需要带上协议头，以 http:// 或者 https:// 开头）.
     */
    CODE_40094(40094, "不合法的URL；缺少主页URL参数，或者URL不合法（链接需要带上协议头，以 http:// 或者 https:// 开头）"),
    /**
     * 不合法的外部联系人userid
     */
    CODE_40096(40096, "不合法的外部联系人userid"),
    /**
     * 该成员尚未离职；离职成员外部联系人转移接口要求转出用户必须已经离职
     */
    CODE_40097(40097, "该成员尚未离职；离职成员外部联系人转移接口要求转出用户必须已经离职"),
    /**
     * 成员尚未实名认证；确认传入的userid是已经过实名认证成员的
     */
    CODE_40098(40098, "成员尚未实名认证；确认传入的userid是已经过实名认证成员的"),
    /**
     * 外部联系人的数量已达上限
     */
    CODE_40099(40099, "外部联系人的数量已达上限"),
    /**
     * 此用户的外部联系人已经在转移流程中
     */
    CODE_40100(40100, "此用户的外部联系人已经在转移流程中"),
    /**
     * 域名或IP不可与应用市场上架应用重复
     */
    CODE_40102(40102, "域名或IP不可与应用市场上架应用重复"),
    /**
     * 上传临时图片素材，图片格式非法；请确认上传的内容是否为合法的图片内容
     */
    CODE_40123(40123, "上传临时图片素材，图片格式非法；请确认上传的内容是否为合法的图片内容"),
    /**
     * 推广活动里的sn禁止绑定
     */
    CODE_40124(40124, "推广活动里的sn禁止绑定"),
    /**
     * 无效的openuserid参数
     */
    CODE_40125(40125, "无效的openuserid参数"),
    /**
     * 企业标签个数达到上限，最多为3000个
     */
    CODE_40126(40126, "企业标签个数达到上限，最多为3000个"),
    /**
     * 不支持的uri schema；检查uri链接的schema是否符合参数要求
     */
    CODE_40127(40127, "不支持的uri schema；检查uri链接的schema是否符合参数要求"),
    /**
     * 客户转接过于频繁（90天内只允许转接一次，同一个客户最多只能转接两次）
     */
    CODE_40128(40128, "客户转接过于频繁（90天内只允许转接一次，同一个客户最多只能转接两次）"),
    /**
     * 当前客户正在转接中
     */
    CODE_40129(40129, "当前客户正在转接中"),
    /**
     * 原跟进人与接手人一样，不可继承
     */
    CODE_40130(40130, "原跟进人与接手人一样，不可继承"),
    /**
     * handover_userid 并不是外部联系人的跟进人
     */
    CODE_40131(40131, "handover_userid 并不是外部联系人的跟进人"),
    /**
     * 缺少access_token参数.
     */
    CODE_41001(41001, "缺少access_token参数"),
    /**
     * 缺少corpid参数.
     */
    CODE_41002(41002, "缺少corpid参数"),
    /**
     * 缺少secret参数.
     */
    CODE_41004(41004, "缺少secret参数"),
    /**
     * 缺少media_id参数；media_id为调用接口必填参数，请确认是否有传递.
     */
    CODE_41006(41006, "缺少media_id参数；media_id为调用接口必填参数，请确认是否有传递"),
    /**
     * 缺少auth code参数.
     */
    CODE_41008(41008, "缺少auth code参数"),
    /**
     * 缺少userid参数.
     */
    CODE_41009(41009, "缺少userid参数"),
    /**
     * 缺少url参数.
     */
    CODE_41010(41010, "缺少url参数"),
    /**
     * 缺少agentid参数.
     */
    CODE_41011(41011, "缺少agentid参数"),
    /**
     * 缺少title参数；发送图文消息，标题是必填参数。请确认参数是否有传递.
     */
    CODE_41016(41016, "缺少title参数；发送图文消息，标题是必填参数。请确认参数是否有传递。"),
    /**
     * 缺少 department 参数.
     */
    CODE_41019(41019, "缺少 department 参数"),
    /**
     * 缺少tagid参数.
     */
    CODE_41017(41017, "缺少tagid参数"),
    /**
     * 缺少suite_id参数.
     */
    CODE_41021(41021, "缺少suite_id参数"),
    /**
     * 缺少suite_access_token参数.
     */
    CODE_41022(41022, "缺少suite_access_token参数"),
    /**
     * 缺少suite_ticket参数.
     */
    CODE_41023(41023, "缺少suite_ticket参数"),
    /**
     * 缺少secret参数.
     */
    CODE_41024(41024, "缺少secret参数"),
    /**
     * 缺少permanent_code参数.
     */
    CODE_41025(41025, "缺少permanent_code参数"),
    /**
     * 缺少 description 参数；发送文本卡片消息接口，description 是必填字段.
     */
    CODE_41033(41033, "缺少 description 参数；发送文本卡片消息接口，description 是必填字段"),
    /**
     * 缺少外部联系人userid参数.
     */
    CODE_41035(41035, "缺少外部联系人userid参数"),
    /**
     * 不合法的企业对外简称；企业对外简称必须是认证过的，如果要改回默认简称，传空字符串把对外简称清除就可以了.
     */
    CODE_41036(41036, "不合法的企业对外简称；企业对外简称必须是认证过的，如果要改回默认简称，传空字符串把对外简称清除就可以了"),
    /**
     * 缺少「联系我」type参数.
     */
    CODE_41037(41037, "缺少「联系我」type参数"),
    /**
     * 缺少「联系我」scene参数.
     */
    CODE_41038(41038, "缺少「联系我」scene参数"),
    /**
     * 无效的「联系我」type参数.
     */
    CODE_41039(41039, "无效的「联系我」type参数"),
    /**
     * 无效的「联系我」scene参数.
     */
    CODE_41040(41040, "无效的「联系我」scene参数"),
    /**
     * 「联系我」使用人数超过限制；默认限制不超过100人(包括部门展开后的人数).
     */
    CODE_41041(41041, "「联系我」使用人数超过限制；默认限制不超过100人(包括部门展开后的人数)"),
    /**
     * 无效的「联系我」style参数.
     */
    CODE_41042(41042, "无效的「联系我」style参数"),
    /**
     * 无效的「联系我」style参数.
     */
    CODE_41043(41043, "缺少「联系我」config_id参数"),
    /**
     * 无效的「联系我」config_id参数.
     */
    CODE_41044(41044, "无效的「联系我」config_id参数"),
    /**
     * API添加「联系我」达到数量上限.
     */
    CODE_41045(41045, "API添加「联系我」达到数量上限"),
    /**
     * 缺少企业群发消息id.
     */
    CODE_41046(41046, "缺少企业群发消息id"),
    /**
     * 无效的企业群发消息id.
     */
    CODE_41047(41047, "无效的企业群发消息id"),
    /**
     * 无可发送的客户
     */
    CODE_41048(41048, "无可发送的客户"),
    /**
     * 缺少欢迎语code参数.
     */
    CODE_41049(41049, "缺少欢迎语code参数"),
    /**
     * 无效的欢迎语code；欢迎语code(welcome_code)具有时效性，须在添加好友后20秒内使用
     */
    CODE_41050(41050, "无效的欢迎语code；欢迎语code(welcome_code)具有时效性，须在添加好友后20秒内使用"),
    /**
     * 客户和服务人员已经开始聊天了；已经开始的聊天的客户不能发送欢迎语
     */
    CODE_41051(41051, "客户和服务人员已经开始聊天了；已经开始的聊天的客户不能发送欢迎语"),
    /**
     * 无效的发送时间
     */
    CODE_41052(41052, "无效的发送时间"),
    /**
     * 客户未同意聊天存档；须外部联系人同意服务须知后，成员才可发送欢迎语
     */
    CODE_41053(41053, "客户未同意聊天存档；须外部联系人同意服务须知后，成员才可发送欢迎语"),
    /**
     * 该用户尚未激活
     */
    CODE_41054(41054, "该用户尚未激活"),
    /**
     * 群欢迎语模板数量达到上限.
     */
    CODE_41055(41055, "群欢迎语模板数量达到上限"),
    /**
     * 外部联系人id类型不正确.
     */
    CODE_41056(41056, "外部联系人id类型不正确"),
    /**
     * 企业或服务商未绑定微信开发者账号	.
     */
    CODE_41057(41057, "企业或服务商未绑定微信开发者账号\t"),
    /**
     * 缺少moment_id参数.
     */
    CODE_41059(41059, "缺少moment_id参数"),
    /**
     * 不合法的moment_id参数.
     */
    CODE_41060(41060, "不合法的moment_id参数"),
    /**
     * 不合法朋友圈发送成员userid，当前朋友圈并非此用户发表.
     */
    CODE_41061(41061, "不合法朋友圈发送成员userid，当前朋友圈并非此用户发表"),
    /**
     * 企业创建的朋友圈尚未被成员userid发表.
     */
    CODE_41062(41062, "企业创建的朋友圈尚未被成员userid发表"),
    /**
     * 群发消息正在被派发中，请稍后再试；创建企业群发后，立刻调用获取企业的全部群发记录的相关接口，将可能出现该错误.
     */
    CODE_41063(41063, "群发消息正在被派发中，请稍后再试；创建企业群发后，立刻调用获取企业的全部群发记录的相关接口，将可能出现该错误"),
    /**
     * 附件大小超过限制.
     */
    CODE_41064(41064, "附件大小超过限制"),
    /**
     * 无效的附件类型.
     */
    CODE_41065(41065, "无效的附件类型"),
    /**
     * 用户视频号名称错误.
     */
    CODE_41066(41066, "用户视频号名称错误"),
    /**
     * 朋友圈moment_id类型错误；判断该朋友圈创建类型是否符合接口调用标准.
     */
    CODE_41067(41067, "朋友圈moment_id类型错误；判断该朋友圈创建类型是否符合接口调用标准"),
    /**
     * 聊天敏感词列表超过了限制
     */
    CODE_41068(41068, "聊天敏感词列表超过了限制"),
    /**
     * 聊天敏感词规则总数超过了限制
     */
    CODE_41069(41069, "聊天敏感词规则总数超过了限制"),
    /**
     * 无效的聊天敏感词规则id
     */
    CODE_41070(41070, "无效的聊天敏感词规则id"),
    /**
     * 无效的聊天敏感词规则id
     */
    CODE_41071(41071, "聊天敏感词规则已经被删除"),
    /**
     * 资源附件场景使用错误
     */
    CODE_41072(41072, "资源附件场景使用错误"),
    /**
     * 商品图册描述不符合标准
     */
    CODE_41073(41073, "商品图册描述不符合标准"),
    /**
     * 商品图册数据已经被删除
     */
    CODE_41074(41074, "商品图册数据已经被删除"),
    /**
     * 无效的商品图册id或者数据不存在
     */
    CODE_41075(41075, "无效的商品图册id或者数据不存在"),
    /**
     * 聊天敏感词规则适用范围超过限制,建议使用部门id
     */
    CODE_41076(41076, "聊天敏感词规则适用范围超过限制,建议使用部门id"),
    /**
     * 聊天敏感词规则名称不唯一
     */
    CODE_41077(41077, "聊天敏感词规则名称不唯一"),
    /**
     * 创建朋友圈正在进行的异步任务总数超过了限制
     */
    CODE_41078(41078, "创建朋友圈正在进行的异步任务总数超过了限制"),
    /**
     * 朋友圈正在被派发中，请稍后再试
     */
    CODE_41079(41079, "朋友圈正在被派发中，请稍后再试"),
    /**
     * 附件资源大小超过限制
     */
    CODE_41080(41080, "附件资源大小超过限制"),
    /**
     * 附件资源大小超过限制
     */
    CODE_41081(41081, "附件资源的图片分辨率超过限制"),
    /**
     * 附件资源的视频时长超过限制
     */
    CODE_41082(41082, "附件资源的视频时长超过限制"),
    /**
     * 敏感词关键字错误
     */
    CODE_41083(41083, "敏感词关键字错误"),
    /**
     * 敏感词关键字错误
     */
    CODE_41084(41084, "敏感词拦截语义规则错误"),
    /**
     * 缺少菜单名.
     */
    CODE_41102(41102, "缺少菜单名"),
    /**
     * access_token已过期；access_token有时效性，需要重新获取一次.
     */
    CODE_42001(42001, "access_token已过期；access_token有时效性，需要重新获取一次"),
    /**
     * pre_auth_code已过期；pre_auth_code有时效性，需要重新获取一次.
     */
    CODE_42007(42007, "pre_auth_code已过期；pre_auth_code有时效性，需要重新获取一次"),
    /**
     * suite_access_token已过期；suite_access_token有时效性，需要重新获取一次.
     */
    CODE_42009(42009, "suite_access_token已过期；suite_access_token有时效性，需要重新获取一次"),
    /**
     * jsapi_ticket不可用，一般是没有正确调用接口来创建jsapi_ticket；如果是agentConfig使用，请特别注意是否是使用”获取应用身份的ticket“来获取jsapi_ticket.
     */
    CODE_42012(42012, "jsapi_ticket不可用，一般是没有正确调用接口来创建jsapi_ticket；如果是agentConfig使用，请特别注意是否是使用”获取应用身份的ticket“来获取jsapi_ticket"),
    /**
     * 小程序未登陆或登录态已经过期；需要重新走登陆流程.
     */
    CODE_42013(42013, "小程序未登陆或登录态已经过期；需要重新走登陆流程"),
    /**
     * 任务卡片消息的task_id不合法.
     */
    CODE_42014(42014, "任务卡片消息的task_id不合法"),
    /**
     * 更新的消息的应用与发送消息的应用不匹配.
     */
    CODE_42015(42015, "更新的消息的应用与发送消息的应用不匹配"),
    /**
     * 更新的task_id不存在.
     */
    CODE_42016(42016, "更新的task_id不存在"),
    /**
     * 按钮key值不存在.
     */
    CODE_42017(42017, "按钮key值不存在"),
    /**
     * 按钮key值不合法.
     */
    CODE_42018(42018, "按钮key值不合法"),
    /**
     * 小程序未登陆或登录态已经过期；需要重新走登陆流程.
     */
    CODE_42019(42019, "缺少按钮key值不合法"),
    /**
     * 缺少按钮名称.
     */
    CODE_42020(42020, "缺少按钮名称"),
    /**
     * device_access_token 过期.
     */
    CODE_42021(42021, "device_access_token 过期"),
    /**
     * dcode已经被使用过。只能使用一次.
     */
    CODE_42022(42022, "code已经被使用过。只能使用一次"),
    /**
     * Template_Card.horizontal_content_list.keyname 字段缺失
     */
    CODE_42027(42027, "Template_Card.horizontal_content_list.keyname 字段缺失"),
    /**
     * Template_Card 缺失 Url，card_action、horizontal_content_list、jump_list缺失该字段都可能报此错误
     */
    CODE_42028(42028, "Template_Card 缺失 Url，card_action、horizontal_content_list、jump_list缺失该字段都可能报此错误"),
    /**
     * Template_Card 缺失 mediaid，Template_Card.horizontal_content_list.mediaid 字段缺失
     */
    CODE_42029(42029, "Template_Card 缺失 mediaid，Template_Card.horizontal_content_list.mediaid 字段缺失"),
    /**
     * Template_Card 缺失 appid，card_action、jump_list 缺失该字段都可能报此错误
     */
    CODE_42030(42030, "Template_Card 缺失 appid，card_action、jump_list 缺失该字段都可能报此错误"),
    /**
     * Template_Card.CardType 字段不合法
     */
    CODE_42031(42031, "Template_Card.CardType 字段不合法"),
    /**
     * Template_Card.缺失 Title，vertical_content_list、jump_list缺失该字段都可能报此错误
     */
    CODE_42033(42033, "Template_Card.缺失 Title，vertical_content_list、jump_list缺失该字段都可能报此错误"),
    /**
     * Template_Card.horizontal_content_list 数组长度不合法
     */
    CODE_42035(42035, "Template_Card.horizontal_content_list 数组长度不合法"),
    /**
     * Template_Card.vertical_content_list 数组长度不合法
     */
    CODE_42036(42036, "Template_Card.vertical_content_list 数组长度不合法"),
    /**
     * Template_Card.option_list 数组长度不合法
     */
    CODE_42037(42037, "Template_Card.option_list 数组长度不合法"),
    /**
     * Template_Card.button_list.text 缺失或不合法
     */
    CODE_42038(42038, "Template_Card.button_list.text 缺失或不合法"),
    /**
     * Template_Card.button_list.key 缺失或不合法
     */
    CODE_42039(42039, "Template_Card.button_list.key 缺失或不合法"),
    /**
     * Template_Card.option_list.id 缺失或不合法
     */
    CODE_42040(42040, "Template_Card.option_list.id 缺失或不合法"),
    /**
     * Template_Card.option_list.text 缺失或不合法
     */
    CODE_42041(42041, "Template_Card.option_list.text 缺失或不合法"),
    /**
     * Template_Card.jump_list 数组长度不合法
     */
    CODE_42042(42042, "Template_Card.jump_list 数组长度不合法"),
    /**
     * Template_Card.question_Key 缺失或不合法
     */
    CODE_42043(42043, "Template_Card.question_Key 缺失或不合法"),
    /**
     * Template_Card.card_image.url 缺失或不合法
     */
    CODE_42044(42044, "Template_Card.card_image.url 缺失或不合法"),
    /**
     * Template_Card.card_action 缺失或不合法
     */
    CODE_42045(42045, "Template_Card.card_action 缺失或不合法"),
    /**
     * Template_Card.submit_button.key 缺失或不合法
     */
    CODE_42046(42046, "Template_Card.submit_button.key 缺失或不合法"),
    /**
     * Template_Card.select_list 数组长度不合法
     */
    CODE_42047(42047, "Template_Card.select_list 数组长度不合法"),
    /**
     * Template_Card.submit_button.text 缺失或不合法
     */
    CODE_42049(42049, "Template_Card.submit_button.text 缺失或不合法"),
    /**
     * Template_Card.horizontal_content_list.userid 缺失或不合法
     */
    CODE_42050(42050, "Template_Card.horizontal_content_list.userid 缺失或不合法"),
    /**
     * Template_Card.action_menu.action_list.key key冲突
     */
    CODE_42051(42051, "Template_Card.action_menu.action_list.key key冲突"),
    /**
     * msgid已过期 仅可撤回24小时内的消息
     */
    CODE_42052(42052, "msgid已过期,仅可撤回24小时内的消息"),
    /**
     * 指定的userid未绑定微信或未关注微信插件；需要成员使用微信登录企业微信或者关注微信插件才能获取openid.
     */
    CODE_43004(43004, "指定的userid未绑定微信或未关注微信插件；需要成员使用微信登录企业微信或者关注微信插件才能获取openid"),
    /**
     * 企业未验证主体.
     */
    CODE_43009(43009, "企业未验证主体"),
    /**
     * 应用需配置回调url.
     */
    CODE_43012(43012, "应用需配置回调url"),
    /**
     * 多媒体文件为空；上传格式参考：上传临时素材，确认header和body的内容正确.
     */
    CODE_44001(44001, "多媒体文件为空；上传格式参考：上传临时素材，确认header和body的内容正确。"),
    /**
     * 文本消息content参数为空；发文本消息content为必填参数，且不能为空.
     */
    CODE_44004(44004, "文本消息content参数为空；发文本消息content为必填参数，且不能为空"),
    /**
     * 多媒体文件大小超过限制；图片不可超过5M；音频不可超过5M；文件不可超过20M.
     */
    CODE_45001(45001, "多媒体文件大小超过限制；图片不可超过5M；音频不可超过5M；文件不可超过20M"),
    /**
     * 消息内容大小超过限制.
     */
    CODE_45002(45002, "消息内容大小超过限制"),
    /**
     * 应用description参数长度不符合系统限制；设置应用若带有description参数，则长度必须为4至120个字符.
     */
    CODE_45004(45004, "应用description参数长度不符合系统限制；设置应用若带有description参数，则长度必须为4至120个字符"),
    /**
     * 语音播放时间超过限制；语音播放时长不能超过60秒.
     */
    CODE_45007(45007, "语音播放时间超过限制；语音播放时长不能超过60秒"),
    /**
     * 图文消息的文章数量不符合系统限制；图文消息的文章数量不能超过8条.
     */
    CODE_45008(45008, "图文消息的文章数量不符合系统限制；图文消息的文章数量不能超过8条"),
    /**
     * 接口调用超过限制.
     */
    CODE_45009(45009, "接口调用超过限制"),
    /**
     * 应用name参数长度不符合系统限制；设置应用若带有name参数，则不允许为空，且不超过32个字符.
     */
    CODE_45022(45022, "应用name参数长度不符合系统限制；设置应用若带有name参数，则不允许为空，且不超过32个字符"),
    /**
     * 帐号数量超过上限.
     */
    CODE_45024(45024, "帐号数量超过上限"),
    /**
     * 触发删除用户数的保护；限制参考：全量覆盖成员.
     */
    CODE_45026(45026, "触发删除用户数的保护；限制参考：全量覆盖成员"),
    /**
     * 图文消息author参数长度超过限制；最长64个字节.
     */
    CODE_45032(45032, "图文消息author参数长度超过限制；最长64个字节"),
    /**
     * 接口并发调用超过限制.
     */
    CODE_45033(45033, "接口并发调用超过限制"),
    /**
     * url必须有协议头；在url前面加上协议头 http:// 或 https://.
     */
    CODE_45034(45034, "url必须有协议头；在url前面加上协议头 http:// 或 https://"),
    /**
     * 菜单未设置；菜单需发布后才能获取到数据.
     */
    CODE_46003(46003, "菜单未设置；菜单需发布后才能获取到数据"),
    /**
     * 指定的用户不存在；需要确认指定的用户存在于通讯录中.
     */
    CODE_46004(46004, "指定的用户不存在；需要确认指定的用户存在于通讯录中"),
    /**
     * API接口无权限调用.
     */
    CODE_48002(48002, "API接口无权限调用"),
    /**
     * 不合法的suite_id；确认suite_access_token由指定的suite_id生成.
     */
    CODE_48003(48003, "不合法的suite_id；确认suite_access_token由指定的suite_id生成"),
    /**
     * 授权关系无效；可能是无授权或授权已被取消.
     */
    CODE_48004(48004, "授权关系无效；可能是无授权或授权已被取消"),
    /**
     * API接口已废弃；接口已不再支持，建议改用新接口或者新方案.
     */
    CODE_48005(48005, "API接口已废弃；接口已不再支持，建议改用新接口或者新方案"),
    /**
     * 接口权限被收回；由于企业长时间未使用应用，接口权限被收回，需企业管理员重新启用.
     */
    CODE_48006(48006, "接口权限被收回；由于企业长时间未使用应用，接口权限被收回，需企业管理员重新启用"),
    /**
     * redirect_url未登记可信域名.
     */
    CODE_50001(50001, "redirect_url未登记可信域名"),
    /**
     * 成员不在权限范围；请检查应用或管理组的权限范围.
     */
    CODE_50002(50002, "成员不在权限范围；请检查应用或管理组的权限范围"),
    /**
     * 应用已禁用；禁用的应用无法使用API接口。可在”管理端-企业应用”启用应用.
     */
    CODE_50003(50003, "应用已禁用；禁用的应用无法使用API接口。可在”管理端-企业应用”启用应用"),
    /**
     * 分页查询的游标无效.
     */
    CODE_50100(50100, "分页查询的游标无效"),
    /**
     * 部门长度不符合限制；部门名称不能为空且长度不能超过32个字.
     */
    CODE_60001(60001, "部门长度不符合限制；部门名称不能为空且长度不能超过32个字"),
    /**
     * 部门ID不存在；需要确认部门ID是否有带，并且存在通讯录中.
     */
    CODE_60003(60003, "部门ID不存在；需要确认部门ID是否有带，并且存在通讯录中"),
    /**
     * 父部门不存在；需要确认父亲部门ID是否有带，并且存在通讯录中.
     */
    CODE_60004(60004, "父部门不存在；需要确认父亲部门ID是否有带，并且存在通讯录中"),
    /**
     * 部门下存在成员；不允许删除有成员的部门.
     */
    CODE_60005(60005, "部门下存在成员；不允许删除有成员的部门"),
    /**
     * 部门下存在子部门；不允许删除有子部门的部门.
     */
    CODE_60006(60006, "部门下存在子部门；不允许删除有子部门的部门"),
    /**
     * 不允许删除根部门.
     */
    CODE_60007(60007, "不允许删除根部门"),
    /**
     * 部门已存在；部门ID或者部门名称已存在.
     */
    CODE_60008(60008, "部门已存在；部门ID或者部门名称已存在"),
    /**
     * 部门名称含有非法字符；不能含有 \\:?*“< >| 等字符.
     */
    CODE_60009(60009, "部门名称含有非法字符；不能含有 \\ :?*“< >| 等字符"),
    /**
     * 部门存在循环关系.
     */
    CODE_60010(60010, "部门存在循环关系"),
    /**
     * 指定的成员/部门/标签参数无权限.
     */
    CODE_60011(60011, "指定的成员/部门/标签参数无权限"),
    /**
     * 不允许删除默认应用；默认应用的id为0.
     */
    CODE_60012(60012, "不允许删除默认应用；默认应用的id为0"),
    /**
     * 访问ip不在白名单之中；请确认访问ip是否在服务商白名单IP列表.
     */
    CODE_60020(60020, "访问ip不在白名单之中；请确认访问ip是否在服务商白名单IP列表"),
    /**
     * userid不在应用可见范围内.
     */
    CODE_60021(60021, "userid不在应用可见范围内"),
    /**
     * 不允许修改第三方应用的主页 URL；第三方应用类型，不允许通过接口修改该应用的主页 URL.
     */
    CODE_60028(60028, "不允许修改第三方应用的主页 URL；第三方应用类型，不允许通过接口修改该应用的主页 URL"),
    /**
     * UserID已存在.
     */
    CODE_60102(60102, "UserID已存在"),
    /**
     * 手机号码不合法；长度不超过32位，字符仅支持数字，加号和减号.
     */
    CODE_60103(60103, "手机号码不合法；长度不超过32位，字符仅支持数字，加号和减号"),
    /**
     * 手机号码已存在；同一个企业内，成员的手机号不能重复。建议更换手机号，或者更新已有的手机记录.
     */
    CODE_60104(60104, "手机号码已存在；同一个企业内，成员的手机号不能重复。建议更换手机号，或者更新已有的手机记录。"),
    /**
     * 邮箱不合法；长度不超过64位，且为有效的email格式.
     */
    CODE_60105(60105, "邮箱不合法；长度不超过64位，且为有效的email格式"),
    /**
     * 邮箱已存在；同一个企业内，成员的邮箱不能重复。建议更换邮箱，或者更新已有的邮箱记录.
     */
    CODE_60106(60106, "邮箱已存在；同一个企业内，成员的邮箱不能重复。建议更换邮箱，或者更新已有的邮箱记录。"),
    /**
     * 微信号不合法；微信号格式由字母、数字、”-“、”_“组成，长度为 3-20 字节，首字符必须是字母或”-“或”_“.
     */
    CODE_60107(60107, "微信号不合法；微信号格式由字母、数字、”-“、”_“组成，长度为 3-20 字节，首字符必须是字母或”-“或”_“"),
    /**
     * 用户所属部门数量超过限制；用户同时归属部门不超过20个.
     */
    CODE_60110(60110, "用户所属部门数量超过限制；用户同时归属部门不超过20个"),
    /**
     * UserID不存在；UserID参数为空，或者不存在通讯录中.
     */
    CODE_60111(60111, "UserID不存在；UserID参数为空，或者不存在通讯录中"),
    /**
     * 成员name参数不合法；不能为空，且不能超过64字符.
     */
    CODE_60112(60112, "成员name参数不合法；不能为空，且不能超过64字符"),
    /**
     * 无效的部门id；部门不存在通讯录中.
     */
    CODE_60123(60123, "无效的部门id；部门不存在通讯录中"),
    /**
     * 无效的父部门id；父部门不存在通讯录中.
     */
    CODE_60124(60124, "无效的父部门id；父部门不存在通讯录中"),
    /**
     * 非法部门名字；不能为空，且不能超过64字节，且不能含有\\:*?”< >|等字符.
     */
    CODE_60125(60125, "非法部门名字；不能为空，且不能超过64字节，且不能含有\\:*?”< >|等字符"),
    /**
     * 缺少department参数.
     */
    CODE_60127(60127, "缺少department参数"),
    /**
     * 成员手机和邮箱都为空；成员手机和邮箱至少有个非空.
     */
    CODE_60129(60129, "成员手机和邮箱都为空；成员手机和邮箱至少有个非空"),
    /**
     * is_leader_in_dept和department的元素个数不一致.
     */
    CODE_60132(60132, "is_leader_in_dept和department的元素个数不一致"),
    /**
     * 记录不存在.
     */
    CODE_60136(60136, "记录不存在"),
    /**
     * 家长手机号重复；同一个家校通讯录中，家长的手机号不能重复。建议更换手机号，或者更新已有的手机记录.
     */
    CODE_60137(60137, "家长手机号重复；同一个家校通讯录中，家长的手机号不能重复。建议更换手机号，或者更新已有的手机记录"),
    /**
     * 不合法的模版ID.
     */
    CODE_60203(60203, "不合法的模版ID"),
    /**
     * 模版状态不可用.
     */
    CODE_60204(60204, "模版状态不可用"),
    /**
     * 模版关键词不匹配.
     */
    CODE_60205(60205, "模版关键词不匹配"),
    /**
     * 该种类型的消息只支持第三方独立应用使用
     */
    CODE_60206(60206, "该种类型的消息只支持第三方独立应用使用"),
    /**
     * 第三方独立应用只允许发送模板消息
     */
    CODE_60207(60207, "第三方独立应用只允许发送模板消息"),
    /**
     * 第三方独立应用不支持指定@all，不支持参数toparty和totag
     */
    CODE_60208(60208, "第三方独立应用不支持指定@all，不支持参数toparty和totag"),
    /**
     * 缺少操作者vid
     */
    CODE_60209(60209, "缺少操作者vid"),
    /**
     * 选择成员列表为空
     */
    CODE_60210(60210, "选择成员列表为空"),
    /**
     * SelectedTicket为空
     */
    CODE_60211(60211, "SelectedTicket为空"),
    /**
     * 仅支持第三方应用调用
     */
    CODE_60214(60214, "仅支持第三方应用调用"),
    /**
     * 传入SelectedTicket数量超过最大限制（10个）
     */
    CODE_60215(60215, "传入SelectedTicket数量超过最大限制（10个）"),
    /**
     * 当前操作者无权限，操作者需要授权或者在可见范围内
     */
    CODE_60217(60217, "当前操作者无权限，操作者需要授权或者在可见范围内"),
    /**
     * 消费SelectedTicket和创建SelectedTicket的应用appid不匹配
     */
    CODE_60218(60218, "消费SelectedTicket和创建SelectedTicket的应用appid不匹配"),
    /**
     * 缺少corpappid
     */
    CODE_60220(60220, "缺少corpappid"),
    /**
     * open_userid对应的服务商不是当前服务商
     */
    CODE_60221(60221, "open_userid对应的服务商不是当前服务商"),
    /**
     * 非法SelectedTicket
     */
    CODE_60222(60222, "非法SelectedTicket"),
    /**
     * 非法BundleId
     */
    CODE_60223(60223, "非法BundleId"),
    /**
     * 非法PackageName
     */
    CODE_60224(60224, "非法PackageName"),
    /**
     * 当前操作者并非SelectedTicket相关人，不能创建群聊
     */
    CODE_60225(60225, "当前操作者并非SelectedTicket相关人，不能创建群聊"),
    /**
     * 选人数量超过最大限制（2000）
     */
    CODE_60226(60226, "选人数量超过最大限制（2000）"),
    /**
     * 缺少ServiceCorpid
     */
    CODE_60227(60227, "缺少ServiceCorpid"),
    /**
     * 缺少bind_corpid字段
     */
    CODE_60228(60228, "缺少bind_corpid字段"),
    /**
     * 成员或者部门id不正确
     */
    CODE_60229(60229, "成员或者部门id不正确"),
    /**
     * 缺少shareticket
     */
    CODE_60230(60230, "缺少shareticket"),
    /**
     * 缺少shareticket；检查shareticket是否拼写错误
     */
    CODE_60231(60231, "缺少shareticket；检查shareticket是否拼写错误"),
    /**
     * shareticket非法；检查shareticket是否误用或者拼写错误
     */
    CODE_60233(60233, "shareticket非法；检查shareticket是否误用或者拼写错误"),
    /**
     * shareticket非法；检查是否误用其他应用生成的shareticket或者拼写错误
     */
    CODE_60234(60234, "shareticket非法；检查是否误用其他应用生成的shareticket或者拼写错误"),
    /**
     * 缺少payment_id字段
     */
    CODE_60235(60235, "缺少payment_id字段"),
    /**
     * 缺少trade_no字段
     */
    CODE_60236(60236, "缺少trade_no字段"),
    /**
     * 传入的payment_id对应的收款项目不是由当前应用发起的；检查payment_id是否误用或者拼写错误
     */
    CODE_60237(60237, "传入的payment_id对应的收款项目不是由当前应用发起的；检查payment_id是否误用或者拼写错误"),
    /**
     * 收款人未实名
     */
    CODE_60239(60239, "收款人未实名"),
    /**
     * 收款企业尚未验证或者认证
     */
    CODE_60240(60240, "收款企业尚未验证或者认证"),
    /**
     * 付款学生或者部门id不正确
     */
    CODE_60241(60241, "付款学生或者部门id不正确"),
    /**
     * shareticket不能跨域名使用；检查分享出去的页面与使用shareticket的页面域名是否一致
     */
    CODE_60242(60242, "shareticket不能跨域名使用；检查分享出去的页面与使用shareticket的页面域名是否一致"),
    /**
     * trade_no不合法；检查trade_no是否拼写错误或者误用其他收款项目的trade_no
     */
    CODE_60243(60243, "trade_no不合法；检查trade_no是否拼写错误或者误用其他收款项目的trade_no"),
    /**
     * shareticket不能跨APP使用；检查分享出去的小程序与使用shareticket的小程序appid是否一致
     */
    CODE_60244(60244, "shareticket不能跨APP使用；检查分享出去的小程序与使用shareticket的小程序appid是否一致"),
    /**
     * 学校已经迁移
     */
    CODE_65000(65000, "学校已经迁移"),
    /**
     * 无效的关注模式.
     */
    CODE_65001(65001, "无效的关注模式"),
    /**
     * 导入家长信息数量过多；批量导入家长每次最多1000个.
     */
    CODE_65002(65002, "导入家长信息数量过多；批量导入家长每次最多1000个"),
    /**
     * 学校尚未迁移.
     */
    CODE_65003(65003, "学校尚未迁移"),
    /**
     * 组织架构不存在.
     */
    CODE_65004(65004, "组织架构不存在"),
    /**
     * 无效的同步模式.
     */
    CODE_65005(65005, "无效的同步模式"),
    /**
     * 无效的管理员类型.
     */
    CODE_65006(65006, "无效的管理员类型"),
    /**
     * 无效的家校部门类型.
     */
    CODE_65007(65007, "无效的家校部门类型"),
    /**
     * 无效的入学年份.
     */
    CODE_65008(65008, "无效的入学年份"),
    /**
     * 无效的标准年级类型.
     */
    CODE_65009(65009, "无效的标准年级类型"),
    /**
     * 此userid并不是学生.
     */
    CODE_65010(65010, "此userid并不是学生"),
    /**
     * 家长userid数量超过限制；每次最多批量处理100个家长.
     */
    CODE_65011(65011, "家长userid数量超过限制；每次最多批量处理100个家长"),
    /**
     * 学生userid数量超过限制；每次最多批量处理10个学生.
     */
    CODE_65012(65012, "学生userid数量超过限制；每次最多批量处理10个学生"),
    /**
     * 学生已有家长.
     */
    CODE_65013(65013, "学生已有家长"),
    /**
     * 非学校企业.
     */
    CODE_65014(65014, "非学校企业"),
    /**
     * 父部门类型不匹配；添加学校部门，需满足层级关机，班级需要以年级为父部门.
     */
    CODE_65015(65015, "父部门类型不匹配；添加学校部门，需满足层级关机，班级需要以年级为父部门"),
    /**
     * 家长人数达到上限；未验证的学校\企业最多可添加2000名家长，验证过的学校\企业最多可添加20000名家长.
     */
    CODE_65018(65018, "家长人数达到上限；未验证的学校\\企业最多可添加2000名家长，验证过的学校\\企业最多可添加20000名家长"),
    /**
     * 家校通迅录无权限；无权限获取家校通迅录.
     */
    CODE_65022(65022, "家校通迅录无权限；无权限获取家校通迅录"),
    /**
     * 无效的商户号；请检查商户号是否正确.
     */
    CODE_660001(660001, "无效的商户号；请检查商户号是否正确"),
    /**
     * 无效的企业收款人id；请检查payee_userid是否正确.
     */
    CODE_660002(660002, "无效的企业收款人id；请检查payee_userid是否正确"),
    /**
     * userid不在应用的可见范围.
     */
    CODE_660003(660003, "userid不在应用的可见范围"),
    /**
     * partyid不在应用的可见范围.
     */
    CODE_660004(660004, "partyid不在应用的可见范围"),
    /**
     * 找不到该商户号.
     */
    CODE_660006(660006, "找不到该商户号"),
    /**
     * 申请已经存在；不需要重复申请.
     */
    CODE_660007(660007, "申请已经存在；不需要重复申请"),
    /**
     * 商户号已经绑定；不需要重新提交申请.
     */
    CODE_660008(660008, "商户号已经绑定；不需要重新提交申请"),
    /**
     * 商户号主体和商户主体不一致.
     */
    CODE_660009(660009, "商户号主体和商户主体不一致"),
    /**
     * 超过商户号绑定数量限制.
     */
    CODE_660010(660010, "超过商户号绑定数量限制"),
    /**
     * 商户号未绑定.
     */
    CODE_660011(660011, "商户号未绑定"),
    /**
     * 应用不在共享范围.
     */
    CODE_670001(670001, "应用不在共享范围"),
    /**
     * 发票已被其他公众号锁定.
     */
    CODE_72023(72023, "发票已被其他公众号锁定"),
    /**
     * 发票状态错误；reimburse_status状态错误.
     */
    CODE_72024(72024, "发票状态错误；reimburse_status状态错误"),
    /**
     * 存在发票不属于该用户；只能批量更新该openid的发票.
     */
    CODE_72037(72037, "存在发票不属于该用户；只能批量更新该openid的发票"),
    /**
     * 可信域名不正确，或者无ICP备案.
     */
    CODE_80001(80001, "可信域名不正确，或者无ICP备案"),
    /**
     * 部门下的结点数超过限制（3W）.
     */
    CODE_81001(81001, "部门下的结点数超过限制（3W）"),
    /**
     * 部门最多15层.
     */
    CODE_81002(81002, "部门最多15层"),
    /**
     * 标签下节点个数超过30000个.
     */
    CODE_81003(81003, "标签下节点个数超过30000个"),
    /**
     * 无权限操作标签.
     */
    CODE_81011(81011, "无权限操作标签"),
    /**
     * 缺失可见范围；请求没有填写UserID、部门ID、标签ID.
     */
    CODE_81012(81012, "缺失可见范围；请求没有填写UserID、部门ID、标签ID"),
    /**
     * UserID、部门ID、标签ID全部非法或无权限.
     */
    CODE_81013(81013, "UserID、部门ID、标签ID全部非法或无权限"),
    /**
     * 标签添加成员，单次添加user或party过多.
     */
    CODE_81014(81014, "标签添加成员，单次添加user或party过多"),
    /**
     * 邮箱域名需要跟企业邮箱域名一致.
     */
    CODE_81015(81015, "邮箱域名需要跟企业邮箱域名一致"),
    /**
     * logined_userid字段缺失.
     */
    CODE_81016(81016, "logined_userid字段缺失"),
    /**
     * 请求个数超过限制.
     */
    CODE_81017(81017, "请求个数超过限制"),
    /**
     * 该服务商可获取名字数量配额不足.
     */
    CODE_81018(81018, "该服务商可获取名字数量配额不足"),
    /**
     * items数组成员缺少id字段.
     */
    CODE_81019(81019, "items数组成员缺少id字段"),
    /**
     * 该服务商可获取名字数量配额不足.
     */
    CODE_81020(81020, "items数组成员缺少type字段"),
    /**
     * items数组成员的type字段不合法	.
     */
    CODE_81021(81021, "items数组成员的type字段不合法"),
    /**
     * 指定的成员/部门/标签全部无效.
     */
    CODE_82001(82001, "指定的成员/部门/标签全部无效"),
    /**
     * 不合法的PartyID列表长度；发消息，单次不能超过100个部门.
     */
    CODE_82002(82002, "不合法的PartyID列表长度；发消息，单次不能超过100个部门"),
    /**
     * 不合法的TagID列表长度；发消息，单次不能超过100个标签.
     */
    CODE_82003(82003, "不合法的TagID列表长度；发消息，单次不能超过100个标签"),
    /**
     * 不合法的消息内容；消息内容中可能存在使客户端crash的内容.
     */
    CODE_82004(82004, "不合法的消息内容；消息内容中可能存在使客户端crash的内容"),
    /**
     * 成员票据过期.
     */
    CODE_84014(84014, "成员票据过期"),
    /**
     * 成员票据无效；确认user_ticket参数来源是否正确。参考接口：根据code获取成员信息.
     */
    CODE_84015(84015, "成员票据无效；确认user_ticket参数来源是否正确。参考接口：根据code获取成员信息"),
    /**
     * 缺少templateid参数.
     */
    CODE_84019(84019, "缺少templateid参数"),
    /**
     * templateid不存在；确认参数是否有带，并且已创建.
     */
    CODE_84020(84020, "templateid不存在；确认参数是否有带，并且已创建"),
    /**
     * 缺少register_code参数.
     */
    CODE_84021(84021, "缺少register_code参数"),
    /**
     * 无效的register_code参数.
     */
    CODE_84022(84022, "无效的register_code参数"),
    /**
     * 不允许调用设置通讯录同步完成接口.
     */
    CODE_84023(84023, "不允许调用设置通讯录同步完成接口"),
    /**
     * 无注册信息.
     */
    CODE_84024(84024, "无注册信息"),
    /**
     * 不符合的state参数；必须是[a-zA-Z0-9]的参数值，长度不可超过128个字节.
     */
    CODE_84025(84025, "不符合的state参数；必须是[a-zA-Z0-9]的参数值，长度不可超过128个字节"),
    /**
     * 缺少caller参数.
     */
    CODE_84052(84052, "缺少caller参数"),
    /**
     * 缺少callee参数.
     */
    CODE_84053(84053, "缺少callee参数"),
    /**
     * 缺少auth_corpid参数.
     */
    CODE_84054(84054, "缺少auth_corpid参数"),
    /**
     * 超过拨打公费电话频率。排查方法：同一个客服5秒内只能调用api拨打一次公费电话
     */
    CODE_84055(84055, "超过拨打公费电话频率。排查方法：同一个客服5秒内只能调用api拨打一次公费电话"),
    /**
     * 被拨打用户安装应用时未授权拨打公费电话权限.
     */
    CODE_84056(84056, "被拨打用户安装应用时未授权拨打公费电话权限"),
    /**
     * 公费电话余额不足.
     */
    CODE_84057(84057, "公费电话余额不足"),
    /**
     * caller
     */
    CODE_84058(84058, "caller 呼叫号码不支持"),
    /**
     * 号码非法.
     */
    CODE_84059(84059, "号码非法"),
    /**
     * callee
     */
    CODE_84060(84060, "callee 呼叫号码不支持"),
    /**
     * 不存在外部联系人的关系.
     */
    CODE_84061(84061, "不存在外部联系人的关系"),
    /**
     * 未开启公费电话应用.
     */
    CODE_84062(84062, "未开启公费电话应用"),
    /**
     * caller不存在.
     */
    CODE_84063(84063, "caller不存在"),
    /**
     * callee不存在.
     */
    CODE_84064(84064, "callee不存在"),
    /**
     * caller跟callee电话号码一致。排查方法：不允许自己拨打给自己
     */
    CODE_84065(84065, "caller跟callee电话号码一致。排查方法：不允许自己拨打给自己"),
    /**
     * 服务商拨打次数超过限制。排查方法：单个企业管理员，在一天（以上午10
     */
    CODE_84066(84066, "服务商拨打次数超过限制。排查方法：单个企业管理员，在一天（以上午10:00为起始时间）内，对应单个服务商，只能被呼叫【4】次。"),
    /**
     * 管理员收到的服务商公费电话个数超过限制。排查方法：单个企业管理员，在一天（以上午10
     */
    CODE_84067(84067, "管理员收到的服务商公费电话个数超过限制。排查方法：单个企业管理员，在一天（以上午10:00为起始时间）内，一共只能被【3】个服务商成功呼叫。"),
    /**
     * 拨打方被限制拨打公费电话.
     */
    CODE_84069(84069, "拨打方被限制拨打公费电话"),
    /**
     * 不支持的电话号码。排查方法：拨打方或者被拨打方电话号码不支持
     */
    CODE_84070(84070, "不支持的电话号码。排查方法：拨打方或者被拨打方电话号码不支持"),
    /**
     * 不合法的外部联系人授权码。排查方法：非法或者已经消费过
     */
    CODE_84071(84071, "不合法的外部联系人授权码。排查方法：非法或者已经消费过"),
    /**
     * 应用未配置客服.
     */
    CODE_84072(84072, "应用未配置客服"),
    /**
     * 客服userid不在应用配置的客服列表中.
     */
    CODE_84073(84073, "客服userid不在应用配置的客服列表中"),
    /**
     * 没有外部联系人权限.
     */
    CODE_84074(84074, "没有外部联系人权限"),
    /**
     * 不合法或过期的authcode.
     */
    CODE_84075(84075, "不合法或过期的authcode"),
    /**
     * 缺失authcode.
     */
    CODE_84076(84076, "缺失authcode"),
    /**
     * 订单价格过高，无法受理.
     */
    CODE_84077(84077, "订单价格过高，无法受理"),
    /**
     * 购买人数不正确.
     */
    CODE_84078(84078, "购买人数不正确"),
    /**
     * 价格策略不存在.
     */
    CODE_84079(84079, "价格策略不存在"),
    /**
     * 订单不存在.
     */
    CODE_84080(84080, "订单不存在"),
    /**
     * 存在未支付订单.
     */
    CODE_84081(84081, "存在未支付订单"),
    /**
     * 存在申请退款中的订单.
     */
    CODE_84082(84082, "存在申请退款中的订单"),
    /**
     * 非服务人员.
     */
    CODE_84083(84083, "非服务人员"),
    /**
     * 非跟进用户.
     */
    CODE_84084(84084, "非跟进用户"),
    /**
     * 应用已下架.
     */
    CODE_84085(84085, "应用已下架"),
    /**
     * 订单人数超过可购买最大人数.
     */
    CODE_84086(84086, "订单人数超过可购买最大人数"),
    /**
     * 打开订单支付前禁止关闭订单.
     */
    CODE_84087(84087, "打开订单支付前禁止关闭订单"),
    /**
     * 禁止关闭已支付的订单.
     */
    CODE_84088(84088, "禁止关闭已支付的订单"),
    /**
     * 订单已支付.
     */
    CODE_84089(84089, "订单已支付"),
    /**
     * 缺失user_ticket.
     */
    CODE_84090(84090, "缺失user_ticket"),
    /**
     * 订单价格不可低于下限.
     */
    CODE_84091(84091, "订单价格不可低于下限"),
    /**
     * 无法发起代下单操作.
     */
    CODE_84092(84092, "无法发起代下单操作"),
    /**
     * 代理关系已占用，无法代下单.
     */
    CODE_84093(84093, "代理关系已占用，无法代下单"),
    /**
     * 该应用未配置代理分润规则，请先联系应用服务商处理.
     */
    CODE_84094(84094, "该应用未配置代理分润规则，请先联系应用服务商处理"),
    /**
     * 免费试用版，无法扩容.
     */
    CODE_84095(84095, "免费试用版，无法扩容"),
    /**
     * 免费试用版，无法续期.
     */
    CODE_84096(84096, "免费试用版，无法续期"),
    /**
     * 当前企业有未处理订单.
     */
    CODE_84097(84097, "当前企业有未处理订单"),
    /**
     * 固定总量，无法扩容.
     */
    CODE_84098(84098, "固定总量，无法扩容"),
    /**
     * 非购买状态，无法扩容.
     */
    CODE_84099(84099, "非购买状态，无法扩容"),
    /**
     * 未购买过此应用，无法续期.
     */
    CODE_84100(84100, "未购买过此应用，无法续期"),
    /**
     * 企业已试用付费版本，无法全新购买.
     */
    CODE_84101(84101, "企业已试用付费版本，无法全新购买"),
    /**
     * 企业当前应用状态已过期，无法扩容.
     */
    CODE_84102(84102, "企业当前应用状态已过期，无法扩容"),
    /**
     * 仅可修改未支付订单.
     */
    CODE_84103(84103, "仅可修改未支付订单"),
    /**
     * 订单已支付，无法修改.
     */
    CODE_84104(84104, "订单已支付，无法修改"),
    /**
     * 订单已被取消，无法修改.
     */
    CODE_84105(84105, "订单已被取消，无法修改"),
    /**
     * 企业含有该应用的待支付订单，无法代下单.
     */
    CODE_84106(84106, "企业含有该应用的待支付订单，无法代下单"),
    /**
     * 企业含有该应用的退款中订单，无法代下单.
     */
    CODE_84107(84107, "企业含有该应用的退款中订单，无法代下单"),
    /**
     * 企业含有该应用的待生效订单，无法代下单.
     */
    CODE_84108(84108, "企业含有该应用的待生效订单，无法代下单"),
    /**
     * 订单定价不能未0.
     */
    CODE_84109(84109, "订单定价不能未0"),
    /**
     * 新安装应用不在试用状态，无法升级为付费版.
     */
    CODE_84110(84110, "新安装应用不在试用状态，无法升级为付费版"),
    /**
     * 无足够可用优惠券.
     */
    CODE_84111(84111, "无足够可用优惠券"),
    /**
     * 无法关闭未支付订单.
     */
    CODE_84112(84112, "无法关闭未支付订单"),
    /**
     * 无付费信息.
     */
    CODE_84113(84113, "无付费信息"),
    /**
     * 虚拟版本不支持下单.
     */
    CODE_84114(84114, "虚拟版本不支持下单"),
    /**
     * 虚拟版本不支持扩容.
     */
    CODE_84115(84115, "虚拟版本不支持扩容"),
    /**
     * 虚拟版本不支持续期.
     */
    CODE_84116(84116, "虚拟版本不支持续期"),
    /**
     * 在虚拟正式版期内不能扩容.
     */
    CODE_84117(84117, "在虚拟正式版期内不能扩容"),
    /**
     * 虚拟正式版期内不能变更版本.
     */
    CODE_84118(84118, "虚拟正式版期内不能变更版本"),
    /**
     * 当前企业未报备，无法进行代下单.
     */
    CODE_84119(84119, "当前企业未报备，无法进行代下单"),
    /**
     * 当前应用版本已删除.
     */
    CODE_84120(84120, "当前应用版本已删除"),
    /**
     * 应用版本已删除，无法扩容.
     */
    CODE_84121(84121, "应用版本已删除，无法扩容"),
    /**
     * 应用版本已删除，无法续期.
     */
    CODE_84122(84122, "应用版本已删除，无法续期"),
    /**
     * 非虚拟版本，无法升级.
     */
    CODE_84123(84123, "非虚拟版本，无法升级"),
    /**
     * 非行业方案订单，不能添加部分应用版本的订单.
     */
    CODE_84124(84124, "非行业方案订单，不能添加部分应用版本的订单"),
    /**
     * 购买人数不能少于最少购买人数.
     */
    CODE_84125(84125, "购买人数不能少于最少购买人数"),
    /**
     * 购买人数不能多于最大购买人数.
     */
    CODE_84126(84126, "购买人数不能多于最大购买人数"),
    /**
     * 无应用管理权限.
     */
    CODE_84127(84127, "无应用管理权限"),
    /**
     * 无该行业方案下全部应用的管理权限.
     */
    CODE_84128(84128, "无该行业方案下全部应用的管理权限"),
    /**
     * 付费策略已被删除，无法下单.
     */
    CODE_84129(84129, "付费策略已被删除，无法下单"),
    /**
     * 订单生效时间不合法.
     */
    CODE_84130(84130, "订单生效时间不合法"),
    /**
     * 文件转译解析错误。排查方法：只支持utf8文件转译，可能是不支持的文件类型或者格式
     */
    CODE_84200(84200, "文件转译解析错误。排查方法：只支持utf8文件转译，可能是不支持的文件类型或者格式"),
    /**
     * 包含不合法的词语.
     */
    CODE_85002(85002, "包含不合法的词语"),
    /**
     * 每企业每个月设置的可信域名不可超过20个.
     */
    CODE_85004(85004, "每企业每个月设置的可信域名不可超过20个"),
    /**
     * 可信域名未通过所有权校验.
     */
    CODE_85005(85005, "可信域名未通过所有权校验"),
    /**
     * 参数 chatid 不合法.
     */
    CODE_86001(86001, "参数 chatid 不合法"),
    /**
     * 参数 chatid 不存在.
     */
    CODE_86003(86003, "参数 chatid 不存在"),
    /**
     * 参数 群名不合法.
     */
    CODE_86004(86004, "参数 群名不合法"),
    /**
     * 参数 群主不合法.
     */
    CODE_86005(86005, "参数 群主不合法"),
    /**
     * 群成员数过多或过少.
     */
    CODE_86006(86006, "群成员数过多或过少"),
    /**
     * 不合法的群成员.
     */
    CODE_86007(86007, "不合法的群成员"),
    /**
     * 非法操作非自己创建的群.
     */
    CODE_86008(86008, "非法操作非自己创建的群"),
    /**
     * 仅群主才有操作权限.
     */
    CODE_86101(86101, "仅群主才有操作权限"),
    /**
     * 参数 需要chatid.
     */
    CODE_86201(86201, "参数 需要chatid"),
    /**
     * 参数 需要群名.
     */
    CODE_86202(86202, "参数 需要群名"),
    /**
     * 参数 需要群主.
     */
    CODE_86203(86203, "参数 需要群主"),
    /**
     * 参数 需要群成员.
     */
    CODE_86204(86204, "参数 需要群成员"),
    /**
     * 参数 字符串chatid过长.
     */
    CODE_86205(86205, "参数 字符串chatid过长"),
    /**
     * 参数 数字chatid过大.
     */
    CODE_86206(86206, "参数 数字chatid过大"),
    /**
     * 群主不在群成员列表.
     */
    CODE_86207(86207, "群主不在群成员列表"),
    /**
     * 群发类型不合法.
     */
    CODE_86214(86214, "群发类型不合法"),
    /**
     * 会话ID已经存在.
     */
    CODE_86215(86215, "会话ID已经存在"),
    /**
     * 存在非法会话成员ID.
     */
    CODE_86216(86216, "存在非法会话成员ID"),
    /**
     * 会话发送者不在会话成员列表中；会话的发送者，必须是会话的成员列表之一.
     */
    CODE_86217(86217, "会话发送者不在会话成员列表中；会话的发送者，必须是会话的成员列表之一"),
    /**
     * 指定的会话参数不合法.
     */
    CODE_86220(86220, "指定的会话参数不合法"),
    /**
     * 不是受限群，不允许使用该接口.
     */
    CODE_86224(86224, "不是受限群，不允许使用该接口"),
    /**
     * 未认证摇一摇周边.
     */
    CODE_90001(90001, "未认证摇一摇周边"),
    /**
     * 缺少摇一摇周边ticket参数.
     */
    CODE_90002(90002, "缺少摇一摇周边ticket参数"),
    /**
     * 摇一摇周边ticket参数不合法.
     */
    CODE_90003(90003, "摇一摇周边ticket参数不合法"),
    /**
     * 非法的对外属性类型.
     */
    CODE_90100(90100, "非法的对外属性类型"),
    /**
     * 对外属性：文本类型长度不合法；文本长度不可超过12个UTF8字符.
     */
    CODE_90101(90101, "对外属性：文本类型长度不合法；文本长度不可超过12个UTF8字符"),
    /**
     * 对外属性：网页类型标题长度不合法；标题长度不可超过12个UTF8字符.
     */
    CODE_90102(90102, "对外属性：网页类型标题长度不合法；标题长度不可超过12个UTF8字符"),
    /**
     * 对外属性：网页url不合法.
     */
    CODE_90103(90103, "对外属性：网页url不合法"),
    /**
     * 对外属性：小程序类型标题长度不合法；标题长度不可超过12个UTF8字符.
     */
    CODE_90104(90104, "对外属性：小程序类型标题长度不合法；标题长度不可超过12个UTF8字符"),
    /**
     * 对外属性：小程序类型pagepath不合法.
     */
    CODE_90105(90105, "对外属性：小程序类型pagepath不合法"),
    /**
     * 对外属性：请求参数不合法.
     */
    CODE_90106(90106, "对外属性：请求参数不合法"),
    /**
     * 缺少小程序appid参数.
     */
    CODE_90200(90200, "缺少小程序appid参数"),
    /**
     * 小程序通知的content_item个数超过限制；item个数不能超过10个
     */
    CODE_90201(90201, "小程序通知的content_item个数超过限制；item个数不能超过10个"),
    /**
     * 小程序通知中的key长度不合法；不能为空或超过10个汉字.
     */
    CODE_90202(90202, "小程序通知中的key长度不合法；不能为空或超过10个汉字"),
    /**
     * 小程序通知中的value长度不合法；不能为空或超过30个汉字.
     */
    CODE_90203(90203, "小程序通知中的value长度不合法；不能为空或超过30个汉字"),
    /**
     * 小程序通知中的page参数不合法.
     */
    CODE_90204(90204, "小程序通知中的page参数不合法"),
    /**
     * 小程序未关联到企业中.
     */
    CODE_90206(90206, "小程序未关联到企业中"),
    /**
     * 不合法的小程序appid.
     */
    CODE_90207(90207, "不合法的小程序appid"),
    /**
     * 小程序appid不匹配.
     */
    CODE_90208(90208, "小程序appid不匹配"),
    /**
     * 登录时传入的suiteid不合法.
     */
    CODE_90211(90211, "登录时传入的suiteid不合法"),
    /**
     * 登录时传入的suiteid和使用的小程序绑定的第三方应用不匹配.
     */
    CODE_90212(90212, "登录时传入的suiteid和使用的小程序绑定的第三方应用不匹配"),
    /**
     * orderid 不合法.
     */
    CODE_90300(90300, "orderid 不合法"),
    /**
     * 付费应用已过期.
     */
    CODE_90302(90302, "付费应用已过期"),
    /**
     * 付费应用已过期.
     */
    CODE_90303(90303, "付费应用已过期"),
    /**
     * 订单中心服务异常，请稍后重试.
     */
    CODE_90304(90304, "订单中心服务异常，请稍后重试"),
    /**
     * 参数错误，errmsg中有提示具体哪个参数有问题.
     */
    CODE_90305(90305, "参数错误，errmsg中有提示具体哪个参数有问题"),
    /**
     * 商户设置不合法，详情请见errmsg.
     */
    CODE_90306(90306, "商户设置不合法，详情请见errmsg"),
    /**
     * 登录态过期.
     */
    CODE_90307(90307, "登录态过期"),
    /**
     * 在开启IP鉴权的前提下，识别为无效的请求IP.
     */
    CODE_90308(90308, "在开启IP鉴权的前提下，识别为无效的请求IP"),
    /**
     * 订单已经存在，请勿重复下单.
     */
    CODE_90309(90309, "订单已经存在，请勿重复下单"),
    /**
     * 找不到订单
     */
    CODE_90310(90310, "找不到订单"),
    /**
     * 关单失败, 可能原因：该单并没被拉起支付页面; 已经关单；已经支付；渠道失败；单处于保护状态；等等
     */
    CODE_90311(90311, "关单失败, 可能原因：该单并没被拉起支付页面; 已经关单；已经支付；渠道失败；单处于保护状态；等等"),
    /**
     * 退款请求失败, 详情请看errmsg
     */
    CODE_90312(90312, "退款请求失败, 详情请看errmsg"),
    /**
     * 退款调用频率限制，超过规定的阈值
     */
    CODE_90313(90313, "退款调用频率限制，超过规定的阈值"),
    /**
     * 订单状态错误，可能未支付，或者当前状态操作受限
     */
    CODE_90314(90314, "订单状态错误，可能未支付，或者当前状态操作受限"),
    /**
     * 退款请求失败，主键冲突，请核实退款refund_id是否已使用
     */
    CODE_90315(90315, "退款请求失败，主键冲突，请核实退款refund_id是否已使用"),
    /**
     * 退款原因编号不对
     */
    CODE_90316(90316, "退款原因编号不对"),
    /**
     * 尚未注册成为供应商
     */
    CODE_90317(90317, "尚未注册成为供应商"),
    /**
     * 参数nonce_str 为空或者重复，判定为重放攻击
     */
    CODE_90318(90318, "参数nonce_str 为空或者重复，判定为重放攻击"),
    /**
     * 时间戳为空或者与系统时间间隔太大
     */
    CODE_90319(90319, "时间戳为空或者与系统时间间隔太大"),
    /**
     * 订单token无效
     */
    CODE_90320(90320, "订单token无效"),
    /**
     * 订单token已过有效时间
     */
    CODE_90321(90321, "订单token已过有效时间"),
    /**
     * 旧套件（包含多个应用的套件）不支持支付系统
     */
    CODE_90322(90322, "旧套件（包含多个应用的套件）不支持支付系统"),
    /**
     * 单价超过限额
     */
    CODE_90323(90323, "单价超过限额"),
    /**
     * 商品数量超过限额
     */
    CODE_90324(90324, "商品数量超过限额"),
    /**
     * 预支单已经存在
     */
    CODE_90325(90325, "预支单已经存在"),
    /**
     * 预支单单号非法
     */
    CODE_90326(90326, "预支单单号非法"),
    /**
     * 该预支单已经结算下单
     */
    CODE_90327(90327, "该预支单已经结算下单\t"),
    /**
     * 结算下单失败，详情请看errmsg
     */
    CODE_90328(90328, "结算下单失败，详情请看errmsg"),
    /**
     * 该订单号已经被预支单占用
     */
    CODE_90329(90329, "该订单号已经被预支单占用额"),
    /**
     * 创建供应商失败
     */
    CODE_90330(90330, "创建供应商失败"),
    /**
     * 商品数量超过限额
     */
    CODE_90331(90331, "更新供应商失败"),
    /**
     * 还没签署合同
     */
    CODE_90332(90332, "还没签署合同"),
    /**
     * 创建合同失败
     */
    CODE_90333(90333, "创建合同失败"),
    /**
     * 已经过了可退款期限
     */
    CODE_90338(90338, "已经过了可退款期限\t"),
    /**
     * 供应商主体名包含非法字符
     */
    CODE_90339(90339, "供应商主体名包含非法字符"),
    /**
     * 创建客户失败，可能信息真实性校验失败
     */
    CODE_90340(90340, "创建客户失败，可能信息真实性校验失败"),
    /**
     * 退款金额大于付款金额
     */
    CODE_90341(90341, "退款金额大于付款金额"),
    /**
     * 退款金额超过账户余额
     */
    CODE_90342(90342, "退款金额超过账户余额"),
    /**
     * 退款单号已经存在
     */
    CODE_90343(90343, "退款单号已经存在"),
    /**
     * 指定的付款渠道无效
     */
    CODE_90344(90344, "指定的付款渠道无效"),
    /**
     * 超过5w人民币不可指定微信支付渠道
     */
    CODE_90345(90345, "超过5w人民币不可指定微信支付渠道"),
    /**
     * 同一单的退款次数超过限制
     */
    CODE_90346(90346, "同一单的退款次数超过限制"),
    /**
     * 退款金额不可为0
     */
    CODE_90347(90347, "退款金额不可为0"),
    /**
     * 管理端没配置支付密钥
     */
    CODE_90348(90348, "管理端没配置支付密钥"),
    /**
     * 记录数量太大
     */
    CODE_90349(90349, "记录数量太大"),
    /**
     * 银行信息真实性校验失败
     */
    CODE_90350(90350, "银行信息真实性校验失败"),
    /**
     * 应用状态异常
     */
    CODE_90351(90351, "应用状态异常"),
    /**
     * 延迟试用期天数超过限制
     */
    CODE_90352(90352, "延迟试用期天数超过限制"),
    /**
     * 预支单列表不可为空
     */
    CODE_90353(90353, "预支单列表不可为空"),
    /**
     * 预支单列表数量超过限制
     */
    CODE_90354(90354, "预支单列表数量超过限制"),
    /**
     * 关联有退款预支单，不可删除
     */
    CODE_90355(90355, "关联有退款预支单，不可删除"),
    /**
     * 不能0金额下单
     */
    CODE_90356(90356, "不能0金额下单"),
    /**
     * 代下单必须指定支付渠道
     */
    CODE_90357(90357, "代下单必须指定支付渠道"),
    /**
     * 预支单或代下单，不支持部分退款
     */
    CODE_90358(90358, "预支单或代下单，不支持部分退款"),
    /**
     * 预支单与下单者企业不匹配
     */
    CODE_90359(90359, "预支单与下单者企业不匹配"),
    /**
     * 参数 refunded_credit_orderid 不合法
     */
    CODE_90381(90381, "参数 refunded_credit_orderid 不合法"),
    /**
     * 必须指定组织者
     */
    CODE_90456(90456, "必须指定组织者"),
    /**
     * 日历ID异常
     */
    CODE_90457(90457, "日历ID异常"),
    /**
     * 日历ID列表不能为空
     */
    CODE_90458(90458, "日历ID列表不能为空"),
    /**
     * 日历已删除
     */
    CODE_90459(90459, "日历已删除"),
    /**
     * 日程已删除
     */
    CODE_90460(90460, "日程已删除"),
    /**
     * 日程ID异常
     */
    CODE_90461(90461, "日程ID异常"),
    /**
     * 日程ID列表不能为空
     */
    CODE_90462(90462, "日程ID列表不能为空"),
    /**
     * 不能变更组织者
     */
    CODE_990463(90463, "不能变更组织者"),
    /**
     * 参与者数量超过限制
     */
    CODE_90464(90464, "参与者数量超过限制"),
    /**
     * 不支持的重复类型
     */
    CODE_90465(90465, "不支持的重复类型"),
    /**
     * 不能操作别的应用创建的日历/日程
     */
    CODE_90466(90466, "不能操作别的应用创建的日历/日程"),
    /**
     * 星期参数异常
     */
    CODE_90467(90467, "星期参数异常"),
    /**
     * 不能变更组织者
     */
    CODE_90468(90468, "不能变更组织者"),
    /**
     * 每页大小超过限制
     */
    CODE_90469(90469, "每页大小超过限制"),
    /**
     * 页数异常
     */
    CODE_90470(90470, "页数异常"),
    /**
     * 提醒时间异常
     */
    CODE_90471(90471, "提醒时间异常"),
    /**
     * 没有日历/日程操作权限
     */
    CODE_90472(90472, "没有日历/日程操作权限"),
    /**
     * 颜色参数异常
     */
    CODE_90473(90473, "颜色参数异常"),
    /**
     * 组织者不能与参与者重叠
     */
    CODE_90474(90474, "组织者不能与参与者重叠"),
    /**
     * 不是组织者的日历
     */
    CODE_90475(90475, "不是组织者的日历"),
    /**
     * 不允许操作用户创建的日程
     */
    CODE_90479(90479, "不允许操作用户创建的日程"),
    /**
     * 群主并未离职
     */
    CODE_90500(90500, "群主并未离职"),
    /**
     * 该群不是客户群
     */
    CODE_90501(90501, "该群不是客户群"),
    /**
     * 群主已经离职
     */
    CODE_90502(90502, "群主已经离职"),
    /**
     * 满人 & 99个微信成员，没办法踢，要客户端确认
     */
    CODE_90503(90503, "满人 & 99个微信成员，没办法踢，要客户端确认"),
    /**
     * 群主没变
     */
    CODE_90504(90504, "群主没变"),
    /**
     * 离职群正在继承处理中
     */
    CODE_90507(90507, "离职群正在继承处理中"),
    /**
     * 离职群已经继承
     */
    CODE_90508(90508, "离职群已经继承"),
    /**
     * 非企业微信客户群；当前群不是企业微信的客户群
     */
    CODE_90509(90509, "非企业微信客户群；当前群不是企业微信的客户群"),
    /**
     * 企业一年内无活跃用户
     */
    CODE_90510(90510, "企业一年内无活跃用户"),
    /**
     * opengid不存在或者无效
     */
    CODE_90511(90511, "opengid不存在或者无效"),
    /**
     * 事件分类id不合法
     */
    CODE_90603(90603, "事件分类id不合法"),
    /**
     * 网格单元id不合法
     */
    CODE_90604(90604, "网格单元id不合法"),
    /**
     * 该网格单元管理员达到上限，一个网格单元最多有20个管理员
     */
    CODE_90606(90606, "该网格单元管理员达到上限，一个网格单元最多有20个管理员"),
    /**
     * 含有成员的网格单元不能被删除
     */
    CODE_90607(90607, "含有成员的网格单元不能被删除"),
    /**
     * 网格单元的名字重复了
     */
    CODE_90608(90608, "网格单元的名字重复了"),
    /**
     * 网格单元的成员数超过上限
     */
    CODE_90609(90609, "网格单元的成员数超过上限"),
    /**
     * 网格单元的成员数超过上限
     */
    CODE_90610(90610, "网格单元的成员数超过上限"),
    /**
     * 获取ticket的类型无效.
     */
    CODE_91040(91040, "获取ticket的类型无效"),
    /**
     * 成员不在应用可见范围之内.
     */
    CODE_92000(92000, "成员不在应用可见范围之内"),
    /**
     * 应用没有敏感信息权限.
     */
    CODE_92001(92001, "应用没有敏感信息权限"),
    /**
     * 不允许跨企业调用.
     */
    CODE_92002(92002, "不允许跨企业调用"),
    /**
     * 该直播已经开始或取消.
     */
    CODE_92006(92006, "该直播已经开始或取消"),
    /**
     * 该直播回放不能被删除.
     */
    CODE_92007(92007, "该直播回放不能被删除"),
    /**
     * 当前应用没权限操作这个直播	.
     */
    CODE_92008(92008, "当前应用没权限操作这个直播\t"),
    /**
     * 机器人webhookurl不合法或者机器人已经被移除出群.
     */
    CODE_93000(93000, "机器人webhookurl不合法或者机器人已经被移除出群"),
    /**
     * 机器人webhookurl不合法或者机器人已经被移除出群.
     */
    CODE_93004(93004, "机器人被停用"),
    /**
     * 不在群里.
     */
    CODE_93008(93008, "不在群里"),
    /**
     * 应用未开启工作台自定义模式；请在管理端后台应用详情里面开启自定义工作台模式.
     */
    CODE_94000(94000, "应用未开启工作台自定义模式；请在管理端后台应用详情里面开启自定义工作台模式"),
    /**
     * 不合法的type类型.
     */
    CODE_94001(94001, "不合法的type类型"),
    /**
     * 缺少keydata字段.
     */
    CODE_94002(94002, "缺少keydata字段"),
    /**
     * keydata的items列表长度超出限制.
     */
    CODE_94003(94003, "keydata的items列表长度超出限制"),
    /**
     * 缺少list字段.
     */
    CODE_94005(94005, "缺少list字段"),
    /**
     * list的items列表长度超出限制.
     */
    CODE_94006(94006, "list的items列表长度超出限制"),
    /**
     * 缺少webview字段.
     */
    CODE_94007(94007, "缺少webview字段"),
    /**
     * 应用未设置自定义工作台模版类型.
     */
    CODE_94008(94008, "应用未设置自定义工作台模版类型"),
    /**
     * 不合法的open_kfid
     */
    CODE_95000(95000, "不合法的open_kfid"),
    /**
     * 发送客服消息次数限制；当用户主动发送消息给微信客服时，企业最多可发送5条消息给用户；若用户继续发送消息，企业可再次下发消息.
     */
    CODE_95001(95001, "发送客服消息次数限制；当用户主动发送消息给微信客服时，企业最多可发送5条消息给用户；若用户继续发送消息，企业可再次下发消息"),
    /**
     * 发送客服消息时间限制；当用户在主动发送消息给微信客服时，可在48小时内调用该接口发送消息给用户.
     */
    CODE_95002(95002, "发送客服消息时间限制；当用户在主动发送消息给微信客服时，可在48小时内调用该接口发送消息给用户"),
    /**
     * 未验证企业可接待客户咨询数限制；最多可接待100位客户咨询，验证后可不受限制.
     */
    CODE_95003(95003, "未验证企业可接待客户咨询数限制；最多可接待100位客户咨询，验证后可不受限制"),
    /**
     * open_kfid不存在；跨企业使用，或对已删除的帐号操作.
     */
    CODE_95004(95004, "open_kfid不存在；跨企业使用，或对已删除的帐号操作"),
    /**
     * 客服帐号数超过上限；目前最多允许5个客服帐号.
     */
    CODE_95005(95005, "客服帐号数超过上限；目前最多允许5个客服帐号"),
    /**
     * 不合法的客服帐号名.
     */
    CODE_95006(95006, "不合法的客服帐号名"),
    /**
     * 不合法的msgtoken.
     */
    CODE_95007(95007, "不合法的msgtoken"),
    /**
     * 菜单消息的菜单项个数超过上限；目前最多允许10个菜单项.
     */
    CODE_95008(95008, "菜单消息的菜单项个数超过上限；目前最多允许10个菜单项"),
    /**
     * 不不合法的菜单消息的菜单项类型.
     */
    CODE_95009(95009, "不合法的菜单消息的菜单项类型"),
    /**
     * 未在企业微信使用微信客服.
     */
    CODE_95012(95012, "未在企业微信使用微信客服"),
    /**
     * 会话已经结束.
     */
    CODE_95013(95013, "会话已经结束"),
    /**
     * 用户不是接待人员.
     */
    CODE_95014(95014, "用户不是接待人员"),
    /**
     * 管理端已经配置了专属服务.
     */
    CODE_95015(95015, "管理端已经配置了专属服务"),
    /**
     * 不允许这种状态转移；参考概述中的状态流转图.
     */
    CODE_95016(95016, "不允许这种状态转移；参考概述中的状态流转图"),
    /**
     * 系统应用权限下，api开关处于关闭状态.
     */
    CODE_95017(95017, "系统应用权限下，api开关处于关闭状态"),
    /**
     * 无权限操作指定的应用.
     */
    CODE_301002(301002, "无权限操作指定的应用"),
    /**
     * 不允许删除创建者；创建者不允许从通讯录中删除。如果需要删除该成员，需要先在WEB管理端转移创建者身份.
     */
    CODE_301005(301005, "不允许删除创建者；创建者不允许从通讯录中删除。如果需要删除该成员，需要先在WEB管理端转移创建者身份。"),
    /**
     * 参数 position 不合法；长度不允许超过128个字符.
     */
    CODE_301012(301012, "参数 position 不合法；长度不允许超过128个字符"),
    /**
     * 参数 telephone 不合法；telephone必须由1-32位的纯数字或’-‘号组成.
     */
    CODE_301013(301013, "参数 telephone 不合法；telephone必须由1-32位的纯数字或’-‘号组成。"),
    /**
     * 参数 english_name 不合法；参数如果有传递，不允许为空字符串，同时不能超过64字节，只能是由字母、数字、点(.)、减号(-)、空格或下划线(_)组成.
     */
    CODE_301014(301014, "参数 english_name 不合法；参数如果有传递，不允许为空字符串，同时不能超过64字节，只能是由字母、数字、点(.)、减号(-)、空格或下划线(_)组成"),
    /**
     * 参数 mediaid 不合法；请检查 mediaid 来源，应该通过上传临时素材的图片类型获得mediaid.
     */
    CODE_301015(301015, "参数 mediaid 不合法；请检查 mediaid 来源，应该通过上传临时素材的图片类型获得mediaid"),
    /**
     * 上传语音文件不符合系统要求；语音文件的系统限制，参考上传的媒体文件限制.
     */
    CODE_301016(301016, "上传语音文件不符合系统要求；语音文件的系统限制，参考上传的媒体文件限制"),
    /**
     * 上传语音文件仅支持AMR格式；语音文件的系统限制，参考上传的媒体文件限制.
     */
    CODE_301017(301017, "上传语音文件仅支持AMR格式；语音文件的系统限制，参考上传的媒体文件限制"),
    /**
     * 参数 userid 无效；至少有一个userid不存在于通讯录中.
     */
    CODE_301021(301021, "参数 userid 无效；至少有一个userid不存在于通讯录中"),
    /**
     * 获取打卡数据失败；系统失败，可重试处理.
     */
    CODE_301022(301022, "获取打卡数据失败；系统失败，可重试处理"),
    /**
     * useridlist非法或超过限额；列表数量不能为0且不超过100.
     */
    CODE_301023(301023, "useridlist非法或超过限额；列表数量不能为0且不超过100"),
    /**
     * 获取打卡记录时间间隔超限；保证开始时间大于0 且结束时间大于 0 且结束时间大于开始时间，且间隔少于93天.
     */
    CODE_301024(301024, "获取打卡记录时间间隔超限；保证开始时间大于0 且结束时间大于 0 且结束时间大于开始时间，且间隔少于93天"),
    /**
     * 提交审批单请求参数错误
     */
    CODE_301025(301025, "提交审批单请求参数错误"),
    /**
     * 不允许更新该用户的userid.
     */
    CODE_301036(301036, "不允许更新该用户的userid"),
    /**
     * 请求参数错误，请检查输入参数.
     */
    CODE_301039(301039, "请求参数错误，请检查输入参数"),
    /**
     * ip白名单限制，请求ip不在设置白名单范围.
     */
    CODE_301042(301042, "ip白名单限制，请求ip不在设置白名单范围"),
    /**
     * sdkfileid对应的文件不存在或已过期.
     */
    CODE_301048(301048, "sdkfileid对应的文件不存在或已过期"),
    /**
     * 会话存档服务已过期.
     */
    CODE_301052(301052, "会话存档服务已过期"),
    /**
     * 会话存档服务未开启.
     */
    CODE_301053(301053, "会话存档服务未开启"),
    /**
     * 无审批应用权限,或者提单者不在审批应用/自建应用的可见范围
     */
    CODE_301055(301055, "无审批应用权限,或者提单者不在审批应用/自建应用的可见范围"),
    /**
     * 审批应用已停用
     */
    CODE_301056(301056, "审批应用已停用"),
    /**
     * 通用错误码，提交审批单内部接口失败
     */
    CODE_301057(301057, "通用错误码，提交审批单内部接口失败"),
    /**
     * 拉取会话数据请求超过大小限制，可减少limit参数
     */
    CODE_301058(301058, "拉取会话数据请求超过大小限制，可减少limit参数"),
    /**
     * 非内部群，不提供数据
     */
    CODE_301059(301059, "非内部群，不提供数据"),
    /**
     * 拉取同意情况请求量过大，请减少到100个参数以下
     */
    CODE_301060(301060, "拉取同意情况请求量过大，请减少到100个参数以下"),
    /**
     * userid或者exteropenid用户不存在
     */
    CODE_301061(301061, "userid或者exteropenid用户不存在"),
    /**
     * 批量导入任务的文件中userid有重复.
     */
    CODE_302003(302003, "批量导入任务的文件中userid有重复"),
    /**
     * 组织架构不合法（1不是一棵树，2 多个一样的partyid，3 partyid空，4 partyid name 空，5 同一个父节点下有两个子节点 部门名字一样 可能是以上情况，请一一排查）.
     */
    CODE_302004(302004, "组织架构不合法（1不是一棵树，2 多个一样的partyid，3 partyid空，4 partyid name 空，5 同一个父节点下有两个子节点 部门名字一样 可能是以上情况，请一一排查）"),
    /**
     * 批量导入系统失败，请重新尝试导入.
     */
    CODE_302005(302005, "批量导入系统失败，请重新尝试导入"),
    /**
     * 批量导入任务的文件中partyid有重复.
     */
    CODE_302006(302006, "批量导入任务的文件中partyid有重复"),
    /**
     * 批量导入任务的文件中，同一个部门下有两个子部门名字一样.
     */
    CODE_302007(302007, "批量导入任务的文件中，同一个部门下有两个子部门名字一样"),
    /**
     * CorpId参数无效；指定的CorpId不存在.
     */
    CODE_2000002(2000002, "CorpId参数无效；指定的CorpId不存在"),
    /**
     * 不合法的sn；sn可能尚未进行登记.
     */
    CODE_600001(600001, "不合法的sn；sn可能尚未进行登记"),
    /**
     * 设备已注册；可能设备已经建立过长连接.
     */
    CODE_600002(600002, "设备已注册；可能设备已经建立过长连接"),
    /**
     * 不合法的硬件activecode.
     */
    CODE_600003(600003, "不合法的硬件activecode"),
    /**
     * 该硬件尚未授权任何企业.
     */
    CODE_600004(600004, "该硬件尚未授权任何企业"),
    /**
     * 硬件Secret无效.
     */
    CODE_600005(600005, "硬件Secret无效"),
    /**
     * 缺少硬件sn.
     */
    CODE_600007(600007, "缺少硬件sn"),
    /**
     * 缺少nonce参数.
     */
    CODE_600008(600008, "缺少nonce参数"),
    /**
     * 缺少timestamp参数.
     */
    CODE_600009(600009, "缺少timestamp参数在"),
    /**
     * 缺少signature参数.
     */
    CODE_600010(600010, "缺少signature参数"),
    /**
     * 签名校验失败.
     */
    CODE_600011(600011, "签名校验失败"),
    /**
     * 长连接已经注册过设备.
     */
    CODE_600012(600012, "长连接已经注册过设备"),
    /**
     * 缺少activecode参数.
     */
    CODE_600013(600013, "缺少activecode参数"),
    /**
     * 设备未网络注册.
     */
    CODE_600014(600014, "设备未网络注册"),
    /**
     * 缺少secret参数.
     */
    CODE_600015(600015, "缺少secret参数"),
    /**
     * 设备未激活.
     */
    CODE_600016(600016, "设备未激活"),
    /**
     * 无效的起始结束时间.
     */
    CODE_600018(600018, "无效的起始结束时间"),
    /**
     * 设备未登录.
     */
    CODE_600020(600020, "设备未登录"),
    /**
     * 设备sn已存在.
     */
    CODE_600021(600021, "设备sn已存在"),
    /**
     * 时间戳已失效.
     */
    CODE_600023(600023, "时间戳已失效"),
    /**
     * 固件大小超过5M.
     */
    CODE_600024(600024, "固件大小超过5M"),
    /**
     * 固件名为空或者超过20字节.
     */
    CODE_600025(600025, "固件名为空或者超过20字节"),
    /**
     * 固件信息不存在.
     */
    CODE_600026(600026, "固件信息不存在"),
    /**
     * 非法的固件参数.
     */
    CODE_600027(600027, "非法的固件参数"),
    /**
     * 固件版本已存在.
     */
    CODE_600028(600028, "固件版本已存在"),
    /**
     * 非法的固件版本.
     */
    CODE_600029(600029, "非法的固件版本"),
    /**
     * 缺少固件版本参数.
     */
    CODE_600030(600030, "缺少固件版本参数"),
    /**
     * 硬件固件不允许升级.
     */
    CODE_600031(600031, "硬件固件不允许升级"),
    /**
     * 无法解析硬件二维码.
     */
    CODE_600032(600032, "无法解析硬件二维码"),
    /**
     * 设备型号id冲突.
     */
    CODE_600033(600033, "设备型号id冲突"),
    /**
     * 指纹数据大小超过限制.
     */
    CODE_600034(600034, "指纹数据大小超过限制"),
    /**
     * 人脸数据大小超过限制.
     */
    CODE_600035(600035, "人脸数据大小超过限制"),
    /**
     * 设备sn冲突.
     */
    CODE_600036(600036, "设备sn冲突"),
    /**
     * 缺失设备型号id.
     */
    CODE_600037(600037, "缺失设备型号id"),
    /**
     * 设备型号不存在.
     */
    CODE_600038(600038, "设备型号不存在"),
    /**
     * 不支持的设备类型.
     */
    CODE_600039(600039, "不支持的设备类型"),
    /**
     * 打印任务id不存在.
     */
    CODE_600040(600040, "打印任务id不存在"),
    /**
     * 无效的offset或limit参数值.
     */
    CODE_600041(600041, "无效的offset或limit参数值"),
    /**
     * 无效的设备型号id.
     */
    CODE_600042(600042, "无效的设备型号id"),
    /**
     * 门禁规则未设置.
     */
    CODE_600043(600043, "门禁规则未设置"),
    /**
     * 门禁规则不合法.
     */
    CODE_600044(600044, "门禁规则不合法"),
    /**
     * 设备已订阅企业信息.
     */
    CODE_600045(600045, "设备已订阅企业信息"),
    /**
     * 操作id和用户userid不匹配.
     */
    CODE_600046(600046, "操作id和用户userid不匹配"),
    /**
     * secretno的status非法；请确认是否是使用统一初始secretno的设备，如果是无有正确执行换secretno的流程.
     */
    CODE_600047(600047, "secretno的status非法；请确认是否是使用统一初始secretno的设备，如果是无有正确执行换secretno的流程"),
    /**
     * 无效的指纹算法.
     */
    CODE_600048(600048, "无效的指纹算法"),
    /**
     * 无效的人脸识别算法.
     */
    CODE_600049(600049, "无效的人脸识别算法"),
    /**
     * 无效的算法长度.
     */
    CODE_600050(600050, "无效的算法长度"),
    /**
     * 设备过期.
     */
    CODE_600051(600051, "设备过期"),
    /**
     * 无效的文件分块.
     */
    CODE_600052(600052, "无效的文件分块"),
    /**
     * 该链接已经激活.
     */
    CODE_600053(600053, "该链接已经激活"),
    /**
     * 该链接已经订阅.
     */
    CODE_600054(600054, "该链接已经订阅"),
    /**
     * 无效的用户类型.
     */
    CODE_600055(600055, "无效的用户类型"),
    /**
     * 无效的健康状态.
     */
    CODE_600056(600056, "无效的健康状态"),
    /**
     * 缺少体温参数.
     */
    CODE_600057(600057, "缺少体温参数"),
    /**
     * 永久二维码超过每个员工5000的限制.
     */
    CODE_610001(610001, "永久二维码超过每个员工5000的限制"),
    /**
     * scene参数不合法；有效的scene长度为1~64字符，由英文字母、数字、中划线、下划线以及点号构成.
     */
    CODE_610003(610003, "scene参数不合法；有效的scene长度为1~64字符，由英文字母、数字、中划线、下划线以及点号构成"),
    /**
     * userid不在客户联系配置的使用范围内；请在管理端后台 客户联系->配置->配置使用范围配置该用户.
     */
    CODE_610004(610004, "userid不在客户联系配置的使用范围内；请在管理端后台 客户联系->配置->配置使用范围配置该用户"),
    /**
     * 无效的unionid.
     */
    CODE_610014(610014, "无效的unionid"),
    /**
     * 小程序对应的开放平台账号未认证.
     */
    CODE_610015(610015, "小程序对应的开放平台账号未认证"),
    /**
     * 企业未认证.
     */
    CODE_610016(610016, "企业未认证"),
    /**
     * 小程序和企业主体不一致.
     */
    CODE_610017(610017, "小程序和企业主体不一致"),
    /**
     * 微盘不存在当前空间；判断spaceid是否填错.
     */
    CODE_640001(640001, "微盘不存在当前空间；判断spaceid是否填错"),
    /**
     * 文件不存在；判断fileid是否填错.
     */
    CODE_640002(640002, "文件不存在；判断fileid是否填错"),
    /**
     * 文件已删除；判断fileid对应的文件是否已经被删除.
     */
    CODE_640003(640003, "文件已删除；判断fileid对应的文件是否已经被删除"),
    /**
     * 无权限访问；判断当前用户是否有访问.
     */
    CODE_640004(640004, "无权限访问；判断当前用户是否有访问"),
    /**
     * 成员不在空间内；判断当前成员是否在空间内.
     */
    CODE_640005(640005, "成员不在空间内；判断当前成员是否在空间内"),
    /**
     * 超出当前成员拥有的容量；判断当前用户的个人容量是否超出了限制.
     */
    CODE_640006(640006, "超出当前成员拥有的容量；判断当前用户的个人容量是否超出了限制"),
    /**
     * 超出微盘的容量；在管理端查看微盘的容量是否要满了.
     */
    CODE_640007(640007, "超出微盘的容量；在管理端查看微盘的容量是否要满了"),
    /**
     * 没有空间权限；判断当前userid是否有空间权限.
     */
    CODE_640008(640008, "没有空间权限；判断当前userid是否有空间权限"),
    /**
     * 非法文件名；判断file_name字段是否为空.
     */
    CODE_640009(640009, "非法文件名；判断file_name字段是否为空"),
    /**
     * 超出空间的最大成员数；判断当前空间的成员数是否超过了管理端设置的空间最大成员数.
     */
    CODE_640010(640010, "超出空间的最大成员数；判断当前空间的成员数是否超过了管理端设置的空间最大成员数"),
    /**
     * json格式不匹配；判断是否的json格式是否有误.
     */
    CODE_640011(640011, "json格式不匹配；判断是否的json格式是否有误"),
    /**
     * 非法的userid；判断userid字段是否设置错误.
     */
    CODE_640012(640012, "非法的userid；判断userid字段是否设置错误"),
    /**
     * 非法的deptId；判断deptId字段是否设置错误.
     */
    CODE_640013(640013, "非法的deptId；判断deptId字段是否设置错误"),
    /**
     * 空间没有有效的管理员；判断当前空间是否没有有效的管理员.
     */
    CODE_640014(640014, "空间没有有效的管理员；判断当前空间是否没有有效的管理员"),
    /**
     * 不支持设置预览权限；文件预览权限只有VIP用户才能设置.
     */
    CODE_640015(640015, "不支持设置预览权限；文件预览权限只有VIP用户才能设置"),
    /**
     * 不支持设置文件水印；文件水印只有VIP用户才能设置.
     */
    CODE_640016(640016, "不支持设置文件水印；文件水印只有VIP用户才能设置"),
    /**
     * 微盘管理端未开通API 权限；在微盘管理端进行打开.
     */
    CODE_640017(640017, "微盘管理端未开通API 权限；在微盘管理端进行打开"),
    /**
     * 微盘管理端未设置编辑权限；在微盘管理端进行打开编辑权限.
     */
    CODE_640018(640018, "微盘管理端未设置编辑权限；在微盘管理端进行打开编辑权限"),
    /**
     * API 调用次数超出限制；免费版：1000次/企业/月; 付费版：100,000次/企业/月.
     */
    CODE_640019(640019, "API 调用次数超出限制；免费版：1000次/企业/月; 付费版：100,000次/企业/月"),
    /**
     * 非法的权限类型；普通文件：可下载、仅预览; 微文档：可编辑、仅浏览.
     */
    CODE_640020(640020, "非法的权限类型；普通文件：可下载、仅预览; 微文档：可编辑、仅浏览"),
    /**
     * 非法的fatherid；fatherid应该为：文件所在的目录fileid, 在根目录时为fileid（判断当前字段是否为空）.
     */
    CODE_640021(640021, "非法的fatherid；fatherid应该为：文件所在的目录fileid, 在根目录时为fileid（判断当前字段是否为空）"),
    /**
     * 非法的文件内容的base64；文件内容base64，判断本字段是否为空.
     */
    CODE_640022(640022, "非法的文件内容的base64；文件内容base64，判断本字段是否为空"),
    /**
     * 非法的权限范围；auth_scope应该为三个中的其中一个：1:指定人 2:企业内 3:企业外.
     */
    CODE_640023(640023, "非法的权限范围；auth_scope应该为三个中的其中一个：1:指定人 2:企业内 3:企业外"),
    /**
     * 非法的fileid；判断fileid字段是否为空.
     */
    CODE_640024(640024, "非法的fileid；判断fileid字段是否为空"),
    /**
     * 非法的space_name；判断space_name字段是否空.
     */
    CODE_640025(640025, "非法的space_name；判断space_name字段是否空"),
    /**
     * 非法的spaceid；判断spaceid字段是否空.
     */
    CODE_640026(640026, "非法的spaceid；判断spaceid字段是否空"),
    /**
     * 参数错误；判断输入的参数是否有误
     */
    CODE_640027(640027, "参数错误；判断输入的参数是否有误"),
    /**
     * 空间设置了关闭成员邀请链接；查看空间的安全设置的成员邀请链接按钮是否处于关闭状态.
     */
    CODE_640028(640028, "空间设置了关闭成员邀请链接；查看空间的安全设置的成员邀请链接按钮是否处于关闭状态"),
    /**
     * 只支持下载普通文件，不支持下载文件夹等其他非文件实体类型；检查fileid对应的文件是否为普通文件.
     */
    CODE_640029(640029, "只支持下载普通文件，不支持下载文件夹等其他非文件实体类型；检查fileid对应的文件是否为普通文件"),

    CODE_680000(680000, "参数错误；结合返回的errmsg排查"),
    CODE_680001(680001, "邮件群组地址非法；检查邮箱地址格式是否正确，是否为本企业域名"),
    CODE_680002(680002, "邮件群组名称非法；名称是否为空，名称是否过长"),
    CODE_680003(680003, "群组成员非法；检查群组成员邮箱格式是否正确"),
    CODE_680004(680004, "群组中的群组非法;检查群组邮箱格式是否正确，是否为本企业群组"),
    CODE_680006(680006, "群组使用权限;仅支持0: 企业成员, 1任何人， 2:组内成员，3:自定义成员"),
    CODE_680007(680007, "允许使用群组成员邮箱非法;检查群组成员邮箱格式是否正确"),
    CODE_680008(680008, "群组成员为空;检查userlist/grouplist/department是否都为空"),
    CODE_680010(680010, "邮件群组地址重复;检查群组地址在企业内是否已经被用户占用"),
    CODE_680011(680011, "邮件群组地址重复;检查群组地址在企业内是否已经被其他群组占用"),
    CODE_680012(680012, "邮件群组不存在;检查群组地址是否有误，是否存在于本企业"),
    CODE_680015(680015, "模糊搜索fuzzy参数非法;检查fuzzy值是否为0或1"),
    CODE_680032(680032, "业务邮箱地址非法;检查邮箱地址格式是否正确，是否为本企业域名"),
    CODE_680033(680033, "业务邮箱地址重复;检查业务邮箱地址在企业内是否已经被占用"),
    CODE_680034(680034, "业务邮箱名称非法;名称是否为空，名称是否过长"),
    CODE_680035(680035, "业务邮箱ID不存在;检查id是否存在"),
    CODE_680036(680036, "业务邮箱ID非法;检查id数据类型是否正确，是否传值"),
    CODE_680037(680037, "vid账号余量不足;检查vid账号数是否用完"),
    CODE_680040(680040, "业务邮箱名称重复;检查名称是否已经被其他业务邮箱占用"),
    CODE_680041(680041, "获取用户功能属性type字段非法;仅支持1: 强制启用安全登录 2: IMAP/SMTP服务 3: POP/SMTP服务 4: 是否启用安全登录"),
    CODE_680042(680042, "更改用户功能属性type字段非法;仅支持1: 强制启用安全登录 2: IMAP/SMTP服务 3: POP/SMTP服务 4: 是否启用安全登录"),
    CODE_680043(680043, "禁用/启用邮箱时type参数非法;仅支持 1:启用，2:禁用"),
    CODE_680044(680044, "不允许禁用超级管理员、企业创建者;检查userid是否为超级管理员或企业创建者"),
    CODE_680045(680045, "无法禁用已关闭的邮箱;检查邮箱账号是否为关闭"),
    CODE_680046(680046, "启用邮箱数量达到最大限制;检查企业中已启用的邮箱数量"),
    CODE_842002(842002, "代开发应用模版未上线"),
    CODE_842003(842003, "不是代开发应用模版"),
    CODE_842004(842004, "代开发应用模版数量不合法"),
    CODE_842005(842005, "不支持的应用类型;检查应用类型是否与文档说明一致"),
    CODE_844001(844001, "非法的output_file_format;判断输出文件格式是否正确"),
    CODE_844002(844002, "最近安装应用时间已超过7天，不再允许拨打公费电话联系管理员;请检查最近一次安装应用时间是否超过7天"),
    CODE_845001(845001, "openid账号类型不是公众号或小程序"),
    CODE_845002(845002, "openid认证主体和企业认证主体不一致"),
    CODE_845003(845003, "unionid认证主体和企业认证主体不一致"),
    CODE_710000(710000, "非法的open_corpid"),
    CODE_60252(60252, "非法的openkfid;检查openkfid是否拼写正确"),
    CODE_60251(60251, "缺少openkfid"),
    CODE_60253(60253, "客服不在接待人员列表中;检查传入的客服id是否在接待人员列表中"),
    CODE_301007(301007, "企业已解散"),
    CODE_701001(701001, "不是license基础帐号"),
    CODE_701002(701002, "不合法的license帐号"),
    CODE_701003(701003, "激活码已绑定"),
    CODE_701004(701004, "激活码未绑定"),
    CODE_701005(701005, "无效的license订单"),
    CODE_701006(701006, "不合法的license帐号类型"),
    CODE_701007(701007, "不合法的帐号类型"),
    CODE_701008(701008, "没有合法的有互通license的用户"),
    CODE_701009(701009, "灰度期间，month只能为1个月"),
    CODE_701010(701010, "所有的account_code都非法"),
    CODE_701011(701011, "userid已经绑定"),
    CODE_701012(701012, "active_code 超过绑定有效期"),
    CODE_701013(701013, "灰度期间只允许续期一次"),
    CODE_701014(701014, "jobid最多关联100w个userid"),
    CODE_701015(701015, "没有第三方或者代开发授权，不允许下单\t"),
    CODE_701016(701016, "帐号未激活或者已经过期"),
    CODE_701017(701017, "帐号30天内迁移过"),
    CODE_701018(701018, "迁移帐号重叠，接收帐号已有相同类型的帐号"),
    CODE_701019(701019, "用户userid非法或者跨企业"),
    CODE_701020(701020, "有重复的userid"),
    CODE_701021(701021, "非法的激活码"),
    CODE_701022(701022, "激活码还在生成中，稍后再试"),
    CODE_701023(701023, "有重复的激活码"),
    CODE_701024(701024, "批量操作全部失败了"),
    CODE_701025(701025, "批量操作传了空列表"),
    CODE_701026(701026, "列表数量超过最大值"),
    CODE_701027(701027, "测试企业购买帐号个数超限。"),
    CODE_701028(701028, "测试企业购买月份超限，最多只能购买一个月"),
    CODE_701029(701029, "测试企业只允许续期一次");


    private int code;
    private String msg;

    MetaErrorMsgEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 通过错误代码查找其中文含义..
     */
    public static String findMsgByCode(int code) {
        for (MetaErrorMsgEnum value : MetaErrorMsgEnum.values()) {
            if (value.code == code) {
                return value.msg;
            }
        }

        return null;
    }
}
