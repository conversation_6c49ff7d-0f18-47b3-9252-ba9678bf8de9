package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 导入订单明细编号文件返回值
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class OrderProductResponse implements Serializable {
    private static final long serialVersionUID = 5479682464542592712L;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "订单明细编号")
    private String orderProductId;
}
