package com.xmd.achievement.handler.statistics;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 实发业绩提成统计处理器
 * 描述
 * <AUTHOR>
 * @date: 2024/12/25 11:32
 * @version: 1.0.0
 * @return {@link }
 */
@Service
public class ActualCommissionHandler implements StatisticsHandler {
    @Override
    public void statistics(StatisticsFactInfo factInfo) {
        List<AchievementProductDetailModel> achList = factInfo.getAchList();
        factInfo.setActualCommission(achList.stream().map(AchievementProductDetailModel::getAgentActualCommission).reduce(BigDecimal.ZERO, BigDecimal::add));
    }
}