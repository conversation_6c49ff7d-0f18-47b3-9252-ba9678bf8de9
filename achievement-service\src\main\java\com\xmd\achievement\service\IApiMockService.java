package com.xmd.achievement.service;

import com.alibaba.fastjson.TypeReference;

import java.util.List;
import java.util.Map;

public interface IApiMockService {
    /**
     * 添加Mock数据到Redis
     * @param apiName 接口名
     * @param paramJson 参数json字符串
     * @param value 需要存储的对象
     */
    void addMock(String apiName, String paramJson, Object value);

    /**
     * 直接传入key和value添加Mock数据到Redis
     * @param key redis key
     * @param value 需要存储的对象
     */
    void addMock(String key, Object value);

    /**
     * 添加Mock数据到Redis，不对value做序列化处理
     *
     * @param apiName 接口名
     * @param value   值
     */
    void addMockString(String apiName, String value);

    /**
     * 添加Mock数据到Redis，不对hashKey和value做序列化处理
     *
     * @param apiName 接口名
     * @param hashKey 哈希键
     * @param value   值
     */
    void addMockHash(String apiName, String hashKey, String value);

    /**
     * 添加Mock数据到Redis，不对hashKey和value做序列化处理
     *
     * @param apiName 接口名
     * @param map     hashKey-value
     */
    void addMockHash(String apiName, Map<String, String> map);

    /**
     * 添加Mock数据到Redis，value为json字符串
     * @param apiName 接口名
     * @param paramJson 参数json字符串
     * @param valueJson 需要存储的对象json字符串
     */
    void addMock(String apiName, String paramJson, String valueJson);

    /**
     * 生成Redis的key
     * @param apiName 接口名
     * @param paramJson 参数json字符串
     * @return redis key字符串
     */
    String buildMockKey(String apiName, String paramJson);

    /**
     * 查询Mock数据
     * @param apiName 接口名
     * @param paramJson 参数json字符串
     * @param clazz 返回类型
     * @return 查询到的对象
     */
    <T> T getMock(String apiName, String paramJson, Class<T> clazz);

    /**
     * 通过key查询Mock数据
     * @param key redis key
     * @param clazz 返回类型
     * @return 查询到的对象
     */
    <T> T getMock(String key, Class<T> clazz);

    /**
     * 查询集合类型Mock数据
     * @param apiName 接口名
     * @param paramJson 参数json字符串
     * @param typeRef 返回类型引用
     * @return 查询到的集合对象
     */
    <T> T getMock(String apiName, String paramJson, TypeReference<T> typeRef);

    /**
     * 通过key查询集合类型Mock数据
     * @param key redis key
     * @param typeRef 返回类型引用
     * @return 查询到的集合对象
     */
    <T> T getMock(String key, TypeReference<T> typeRef);

    /**
     * 通过key直接获取原始字符串
     * @param key redis key
     * @return redis中的原始字符串
     */
    String getMockString(String key);

    /**
     * 通过apiName和paramJson获取原始字符串
     * @param apiName 接口名
     * @param paramJson 参数json字符串
     * @return redis中的原始字符串
     */
    String getMockString(String apiName, String paramJson);

    /**
     * 通过apiName获取value原始字符串
     *
     * @param apiName 接口名
     * @return {@link List }<{@link Object }>
     */
    List<Object> getMockHash(String apiName);

    Map<Object, Object> getMockHashEntries(String apiName);

    /**
     * 通过apiName和hashKey获取value原始字符串
     *
     * @param apiName 接口名
     * @param hashKey 哈希键
     * @return {@link Object }
     */
    Object getMockHash(String apiName, String hashKey);

    /**
     * 通过apiName和hashKey集合获取value原始字符串
     *
     * @param apiName  接口名
     * @param hashKeys 哈希键集合
     * @return {@link List }<{@link Object }>
     */
    List<Object> getMockHash(String apiName, List<String> hashKeys);

    /**
     * 通过key删除Mock数据
     * @param key redis key
     */
    void deleteMock(String key);

    /**
     * 通过apiName和paramJson删除Mock数据
     * @param apiName 接口名
     * @param paramJson 参数json字符串
     */
    void deleteMock(String apiName, String paramJson);

    void deleteMockHash(String apiName, String hashKey);
}
