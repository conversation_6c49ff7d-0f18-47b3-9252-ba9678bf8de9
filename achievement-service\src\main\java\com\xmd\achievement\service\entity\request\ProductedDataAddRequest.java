package com.xmd.achievement.service.entity.request;

import com.xmd.achievement.service.entity.dto.ProductedDataAddDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/23/16:48
 * @since 1.0
 */
@Data
public class ProductedDataAddRequest implements Serializable {

    List<ProductedDataAddDto> productedDataList;

}
