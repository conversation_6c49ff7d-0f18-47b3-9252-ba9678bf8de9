package com.xmd.achievement.util.encrypt;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import java.math.BigInteger;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.util.Optional;
import java.util.UUID;

/**
 * pbkdf2加密算法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/8/1 下午5:49
 */
public class Pbkdf2Util {
    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(Pbkdf2Util.class);
    /**
     * pbkdf2算法
     */
    private static final String PBKDF2_ALGORITHM = "PBKDF2WithHmacSHA1";
    /**
     * 生成密文的长度
     */
    private static final int HASH_BIT_SIZE = 64 * 4;
    /**
     * 迭代次数
     */
    private static final int PBKDF2_ITERATIONS = 1000;

    /**
     * 对输入的密码进行验证
     *
     * @param attemptedPassword 待验证码密码
     * @param encryptedPassword 密文密码
     * @param salt              盐
     * @return java.util.Optional<java.lang.Boolean>
     * <AUTHOR>
     * @date 2021/8/1 下午5:52
     * @version 1.0.0
     **/
    public static Optional<Boolean> authenticate(String attemptedPassword, String encryptedPassword, String salt) {
        // 用相同的盐值对用户输入的密码进行加密
        Optional<String> encryptedAttemptedPasswordOptional = getEncryptedPassword(attemptedPassword, salt);
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("密码加密结果:{}", encryptedAttemptedPasswordOptional.orElse("密码为空"));
        }
        // 把加密后的密文和原密文进行比较，相同则验证成功，否则失败
        return encryptedAttemptedPasswordOptional.map(s -> s.equals(encryptedPassword));
    }

    /**
     * 生成密文
     *
     * @param password 原密码
     * @param salt     盐
     * @return java.util.Optional<java.lang.String>
     * <AUTHOR>
     * @date 2021/8/1 下午5:58
     * @version 1.0.0
     **/
    public static Optional<String> getEncryptedPassword(String password, String salt) {
        KeySpec spec = new PBEKeySpec(password.toCharArray(), fromHex(salt), PBKDF2_ITERATIONS, HASH_BIT_SIZE);
        try {
            SecretKeyFactory f = SecretKeyFactory.getInstance(PBKDF2_ALGORITHM);
            return Optional.of(toHex(f.generateSecret(spec).getEncoded()));
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("pbkdf2密码加密失败，指定算法不存在，当前指定的算法为:{}", PBKDF2_ALGORITHM, e);
        } catch (InvalidKeySpecException e) {
            LOGGER.error("pbkdf2密码加密失败，指定的密钥无效，当前指定的密钥为:{}", JSON.toJSONString(spec), e);
        }
        return Optional.empty();
    }

    /**
     * 使用uuid作为盐
     *
     * @return java.util.Optional<java.lang.String>
     * <AUTHOR>
     * @date 2021/8/1 下午5:59
     * @version 1.0.0
     **/
    public static Optional<String> generateSalt() {
        return Optional.of(UUID.randomUUID().toString().replaceAll("-", ""));
    }

    /**
     * 十六进制字符串转二进制数组
     *
     * @param hex 16进制字符串
     * @return byte[]
     * <AUTHOR>
     * @date 2021/8/1 下午6:00
     * @version 1.0.0
     **/
    private static byte[] fromHex(String hex) {
        byte[] binary = new byte[hex.length() / 2];
        for (int i = 0; i < binary.length; i++) {
            binary[i] = (byte) Integer.parseInt(hex.substring(2 * i, 2 * i + 2), 16);
        }
        return binary;
    }

    /**
     * 二进制数组转十六进制字符串
     *
     * @param array 二进制
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/8/1 下午6:01
     * @version 1.0.0
     **/
    private static String toHex(byte[] array) {
        BigInteger bi = new BigInteger(1, array);
        String hex = bi.toString(16);
        int paddingLength = (array.length * 2) - hex.length();
        return paddingLength > 0 ? (String.format("%0" + paddingLength + "d", 0) + hex) : hex;
    }
}
