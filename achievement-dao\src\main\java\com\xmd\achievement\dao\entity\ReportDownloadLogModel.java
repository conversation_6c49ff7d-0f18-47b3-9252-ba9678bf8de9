package com.xmd.achievement.dao.entity;

    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableField;
    import com.baomidou.mybatisplus.annotation.TableId;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.extension.activerecord.Model;
    import java.util.Date;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 日报月报导出记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@TableName("report_download_log")
public class ReportDownloadLogModel implements

        Serializable {
            private static final long serialVersionUID = 1L;
                                /**
                     * 主键id
                     */
                                        @TableId(value = "id", type = IdType.AUTO)
                                private Long id;
                    /**
                     * 操作人id
                     */
                @TableField("operator_id")
            private String operatorId;
                    /**
                     * 操作人名称
                     */
                @TableField("operator_name")
            private String operatorName;
                    /**
                     * 操作时间
                     */
                @TableField("operator_time")
            private Date operatorTime;
                    /**
                     * 下载操作参数
                     */
                @TableField("operator_parameter")
            private String operatorParameter;
                    /**
                     * 创建时间
                     */
                @TableField("create_time")
            private Date createTime;
        }