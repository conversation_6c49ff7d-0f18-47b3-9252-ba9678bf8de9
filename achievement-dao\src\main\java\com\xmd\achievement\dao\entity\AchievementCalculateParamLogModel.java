package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 业绩计算参数日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@TableName("achievement_calculate_param_log")
public class AchievementCalculateParamLogModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单id_唯一标识
     */
    @TableField("order_id")
    private Long orderId;
    /**
     * 业绩计算参数
     */
    @TableField("achievement_param")
    private String achievementParam;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}