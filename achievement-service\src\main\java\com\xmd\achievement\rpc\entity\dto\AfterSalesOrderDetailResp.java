package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 售后订单详情响应数据
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class AfterSalesOrderDetailResp implements Serializable {

    private static final long serialVersionUID = 839664729900803492L;

    /**
     * 售后单基本信息
     */
    private AfterSalesOrderResp afterSalesOrderResp;

    /**
     * 售后商品明细列表
     */
    private List<AfterSalesItemResp> afterSalesItemResps;

    /**
     * 售后操作记录列表
     */
    private List<AfterSalesRecordResp> afterSalesRecordResps;

    /**
     * 售后金额详情
     */
    private AfterSalesAmountResp afterSalesAmountResp;

}

