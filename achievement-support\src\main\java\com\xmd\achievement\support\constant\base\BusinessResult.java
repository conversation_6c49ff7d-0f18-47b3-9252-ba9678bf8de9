package com.xmd.achievement.support.constant.base;


import java.io.Serializable;

/**
 * 返回包装体
 *
 * @version 1.0.0
 * @date 2021/05/28 下午3:38
 */
public class BusinessResult<T> implements Serializable {
    private static final long serialVersionUID = -2156057946333564975L;
    private String msg;
    private String code;
    private T data;

    private BusinessResult() {
    }

    private BusinessResult(T data) {
        this.code = BusinessCodeEnum.REQUEST_SUCCESS.getCode();
        this.msg = BusinessCodeEnum.REQUEST_SUCCESS.getMsg();
        this.data = data;
    }

    private BusinessResult(T data, String msg) {
        this.code = BusinessCodeEnum.REQUEST_SUCCESS.getCode();
        this.msg = msg;
        this.data = data;
    }

    private BusinessResult(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private BusinessResult(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    private BusinessResult(BusinessCodeEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMsg();
        }
    }

    public static <T> BusinessResult<T> success(T data) {
        return new BusinessResult<>(data);
    }

    public static <T> BusinessResult<T> success(T data, String msg) {
        return new BusinessResult<>(data, msg);
    }

    public static <T> BusinessResult<T> success() {
        return success(null);
    }

    public static <T> BusinessResult<T> error(BusinessCodeEnum cm) {
        return new BusinessResult<>(cm);
    }

    public static <T> BusinessResult<T> error(BusinessCodeEnum cm, String msg) {
        return new BusinessResult<>(cm.getCode(), msg);
    }

    public static <T> BusinessResult<T> error(String code, String msg) {
        return new BusinessResult<>(code, msg);
    }

    public static <T> BusinessResult<T> error(BusinessResult<?> resultEntity) {
        return new BusinessResult<>(resultEntity.getCode(), resultEntity.getMsg());
    }

    public static <T> BusinessResult<T> error(BusinessResult<?> resultEntity, T data) {
        return new BusinessResult<>(resultEntity.getCode(), resultEntity.getMsg(), data);
    }

    public static <T> T parseResult(BusinessResult<T> resultEntity) {
        if (resultEntity.checkSuccess() && resultEntity.getData() != null) {
            return resultEntity.getData();
        }
        return null;
    }

    public T getData() {
        return this.data;
    }

    public String getMsg() {
        return this.msg;
    }

    public String getCode() {
        return this.code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Boolean checkSuccess() {
        return BusinessCodeEnum.REQUEST_SUCCESS.getCode().equals(this.code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setData(T data) {
        this.data = data;
    }
}
