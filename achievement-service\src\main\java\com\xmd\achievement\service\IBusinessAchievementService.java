package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.BusinessAchievementModel;
import com.xmd.achievement.service.entity.request.RebuildBusinessAchStatInfoRequest;

import javax.validation.Valid;
import java.util.List;

public interface IBusinessAchievementService {
    BusinessAchievementModel getByMonthIdAndEmployeeId(Long monthId, String employeeId);

    /**
     *  批量添加或者更新对象
     * 描述
     * @param models
     * <AUTHOR>
     * @date: 2024/12/26 10:50
     * @version: 1.0.0
     */
    void saveOrUpdateBatch(List<BusinessAchievementModel> models);

    BusinessAchievementModel findByBusinessMonthIdAndBusinessId(Long businessMonthId, String businessId);

    void rebuildBusinessAchStatInfo(@Valid RebuildBusinessAchStatInfoRequest request);

    List<BusinessAchievementModel> selectByBusinessMonthList(String businessMonth);

    void deleteByIdList(List<Long> unList);

    /**
     * 批量查询商务业绩
     * @param businessMonths 商务月份
     * @param employeeIds 员工ID
     * @return 商务业绩列表
     */
    List<BusinessAchievementModel> queryBusinessAchievements(List<String> businessMonths, List<String> employeeIds);
}
