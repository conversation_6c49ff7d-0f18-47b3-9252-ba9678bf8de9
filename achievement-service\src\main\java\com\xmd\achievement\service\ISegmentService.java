package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.AchievementSegmentModel;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PageQuerySegmentResponse;
import com.xmd.achievement.service.entity.response.QuerySegmentDetailResponse;
import com.xmd.achievement.service.entity.response.QuerySegmentListResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/19 16:34
 * @version: 1.0.0
 * @return {@link }
 */
public interface ISegmentService {
    /**
     * 业绩分段保存
     *
     * @param request 请求参数
     * @return Boolean 操作结果
     */
    WebResult<Boolean> saveSegment(SaveSegmentRequest request);

    /**
     * 分页查询业绩分段
     *
     * @param request 请求参数
     * @return PageResponse<PageQuerySegmentResponse> 操作结果
     */
    WebResult<PageResponse<PageQuerySegmentResponse>> pageQuerySegment(PageRequest request);

    /**
     * 查询业绩分段详情
     *
     * @param request 请求参数
     * @return QuerySegmentDetailResponse 操作结果
     */
    WebResult<QuerySegmentDetailResponse> querySegmentDetail(QuerySegmentDetailResquest request);

    /**
     * 修改业绩分段
     *
     * @return List<QuerySegmentListResponse> 操作结果
     */
    WebResult<List<QuerySegmentListResponse>> querySegmentList();

    /**
     * 修改业绩分段
     *
     * @param request 请求参数
     * @return Boolean 操作结果
     */
    WebResult<Boolean> updateSegment(UpdateSegmentRequest request);

    /**
     * 内部使用
     *
     * @param amount 请求参数
     * @return AchievementSegmentModel 操作结果
     */
    AchievementSegmentModel getSegment(BigDecimal amount);
}
