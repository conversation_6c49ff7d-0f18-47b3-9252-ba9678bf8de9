<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.OperateLogMapper">
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.OperateLogModel">
        <id column="id" property="id"/>
        <result column="operate_log_id" property="operateLogId"/>
        <result column="operation_type" property="operationType"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="class_name" property="className"/>
        <result column="before_data" property="beforeData"/>
        <result column="after_data" property="afterData"/>
        <result column="log_time" property="logTime"/>
        <result column="log_user_id" property="logUserId"/>
        <result column="log_user_name" property="logUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,operate_log_id,operation_type,source_system,class_name,before_data,after_data,
        log_time,log_user_id,log_user_name,create_time,create_user_id,create_user_name,
        update_time,update_user_id,update_user_name
    </sql>
</mapper>