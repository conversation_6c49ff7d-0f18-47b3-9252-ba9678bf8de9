package com.xmd.achievement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductRuleConfigModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.entity.CustomerSaasModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.dao.entity.ThirdAchievementModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IAchievementProductRuleConfigRepository;
import com.xmd.achievement.dao.repository.ICustomerSaasRepository;
import com.xmd.achievement.dao.repository.IMqOrderPaymentInfoRepository;
import com.xmd.achievement.handler.wrapper.AchievementProductWrapper;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.OrderContactResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductResponse;
import com.xmd.achievement.rpc.entity.dto.ProtectByCustomer;
import com.xmd.achievement.rpc.entity.dto.QueryCustomerAllRechargeTimeDto;
import com.xmd.achievement.rpc.entity.dto.ServeProductConfigResponse;
import com.xmd.achievement.rpc.entity.dto.UserInfoDetailResp;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.ICustomerSaasService;
import com.xmd.achievement.service.ISaasTabService;
import com.xmd.achievement.service.ThirdAchievementService;
import com.xmd.achievement.service.entity.dto.ChurnResultDto;
import com.xmd.achievement.service.entity.dto.ChurnStatusDto;
import com.xmd.achievement.service.entity.response.CustomerChurnResponse;
import com.xmd.achievement.service.entity.response.RechargeInfoResponse;
import com.xmd.achievement.service.entity.response.ServiceInfoResponse;
import com.xmd.achievement.support.constant.enums.AchStatus;
import com.xmd.achievement.support.constant.enums.AchievementSourceEnum;
import com.xmd.achievement.support.constant.enums.CustomerChurnStatusEnum;
import com.xmd.achievement.support.constant.enums.CustomerSaasEnum;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.LinkManTypeEnum;
import com.xmd.achievement.support.constant.enums.MainSubEnum;
import com.xmd.achievement.support.constant.enums.NeedProductionEnum;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import com.xmd.achievement.support.constant.enums.ProductRuleEnum;
import com.xmd.achievement.support.constant.enums.TaskTypeEnum;
import com.xmd.achievement.support.constant.enums.ThirdAchievementStateEnum;
import com.xmd.achievement.util.enums.CalculateTypeEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class CustomerSaasServiceImpl implements ICustomerSaasService {

    @Resource
    private ICustomerSaasRepository customerSaasRepository;
    @Resource
    private InnerService innerService;
    @Resource
    private ThirdAchievementService thirdAchievementService;
    @Resource
    private IBusinessMonthService businessMonthService;
    @Resource
    private ISaasTabService saasTabService;
    @Resource
    private IAchievementProductRuleConfigRepository ruleConfigRepository;
    @Resource
    private IMqOrderPaymentInfoRepository mqOrderPaymentInfoRepository;
    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;
    @Resource
    private AchievementProductWrapper achievementProductWrapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    private static final ReentrantLock lock = new ReentrantLock();

    private static final String REDIS_KEY_CUSTOMER_SAAS = "customer_saas:last_page";
    private static final String REDIS_KEY_CUSTOMER_HISTORY_LOSS_DATE = "customer_saas:customer_history_loss_date:";
    private static final String REDIS_KEY_SAAS_STATUS_RECALC = "customer_saas:saas_status_recalc:last_page";
    private static final String REDIS_KEY_CUSTOMER_CHURN_INFO = "customer_saas:customer_churn_info:";
    private static final int REDIS_EXPIRE_DAYS = 1;

    private boolean isToday(Date date) {
        if (date == null) {
            return false;
        }
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date);

        Calendar cal2 = Calendar.getInstance();
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR)
                && cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * 从Redis缓存获取客户流失日期列表，如果缓存不存在则调用接口并存入缓存
     *
     * @param customerId 客户ID
     * @return 客户流失日期列表
     */
    @Override
    public List<String> getCustomerLossDateListWithCache(String customerId) {
        if (StringUtils.isEmpty(customerId)) {
            return new ArrayList<>();
        }

        String redisKey = REDIS_KEY_CUSTOMER_HISTORY_LOSS_DATE + customerId;

        try {
            // 先从Redis获取
            String cachedData = stringRedisTemplate.opsForValue().get(redisKey);
            if (!StringUtils.isEmpty(cachedData)) {
                log.debug("从Redis缓存获取客户流失日期列表, customerId: {}", customerId);
                return JSONUtil.toList(cachedData, String.class);
            }

            // 缓存不存在，调用接口获取数据
            log.debug("Redis缓存不存在，调用接口获取客户流失日期列表, customerId: {}", customerId);
            List<String> lossDateList = innerService.getCustomerLossDateListCrm(customerId);

            // 将结果存入Redis，设置1天过期时间
            if (lossDateList != null) {
                stringRedisTemplate.opsForValue().set(redisKey, JSONUtil.toJsonStr(lossDateList),
                        REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
                log.debug("客户流失日期列表已存入Redis缓存, customerId: {}, size: {}", customerId, lossDateList.size());
            } else {
                // 即使是null也要缓存，避免频繁调用接口
                stringRedisTemplate.opsForValue().set(redisKey, "[]",
                        REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
                lossDateList = new ArrayList<>();
            }

            return lossDateList;
        } catch (Exception e) {
            log.error("获取客户流失日期列表缓存失败, customerId: {}, error: {}", customerId, e.getMessage(), e);
            // 缓存失败时直接调用接口
            return innerService.getCustomerLossDateListCrm(customerId);
        }
    }

    public void updateCustomerSaas(Long orderId, Long productId, String orderProductCode, CustomerSaasEnum status,
            Date churnDate) {
        customerSaasRepository.lambdaUpdate()
                .eq(CustomerSaasModel::getOrderId, orderId)
                .eq(CustomerSaasModel::getProductId, productId)
                .eq(CustomerSaasModel::getOrderProductCode, orderProductCode)
                .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                .set(CustomerSaasModel::getChurnStatus, status.getCode())
                .set(CustomerSaasModel::getChurnDate, churnDate)
                .update();
    }

    public void updateCustomerSaas(CustomerSaasModel model) {
        customerSaasRepository.updateById(model);
    }

    public Map<String, ServiceInfoResponse> getServiceInfoMap(String serveNo) {
        Map<String, ServiceInfoResponse> serveNoMap = new HashMap<>();
        if (StringUtils.isEmpty(serveNo)) {
            return serveNoMap;
        }
        List<ServiceInfoResponse> serviceInfoResponseList = innerService
                .getServiceInfoByServeNo(serveNo);
        if (CollectionUtils.isEmpty(serviceInfoResponseList)) {
            return serveNoMap;
        }
        // 将serviceInfoResponseList转为Map，key为serveNo，value为ServiceInfoResponse
        serveNoMap = serviceInfoResponseList.stream()
                .filter(item -> item.getServeNo() != null)
                .collect(Collectors.toMap(ServiceInfoResponse::getServeNo, item -> item, (a, b) -> a));
        return serveNoMap;
    }

    public void customerSaasJob() {
        final int pageSize = 500;
        Long lastId = 0L;
        List<CustomerSaasModel> batchList;
        do {
            batchList = customerSaasRepository.lambdaQuery()
                    .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                    .eq(CustomerSaasModel::getChurnStatus, CustomerChurnStatusEnum.NO_CHURN_CUSTOMER.getCode())
                    .eq(CustomerSaasModel::getOrderSource, AchievementSourceEnum.KUAJINFG.getCode())
                    .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .isNotNull(CustomerSaasModel::getServeNo)
                    .gt(lastId > 0, CustomerSaasModel::getId, lastId)
                    .orderByAsc(CustomerSaasModel::getId)
                    .last("limit " + pageSize)
                    .list();

            // 收集需要更新的记录
            List<CustomerSaasModel> needUpdateList = new ArrayList<>();

            for (CustomerSaasModel customerSaas : batchList) {
                ChurnResultDto customerSaasHandler = customerSaasHandler(customerSaas);

                // 计算新的状态和日期
                Integer newChurnStatus;
                Date newChurnDate;
                if (customerSaasHandler != null && customerSaasHandler.isChurn()) {
                    newChurnStatus = CustomerChurnStatusEnum.CHURN_CUSTOMER.getCode();
                    newChurnDate = customerSaasHandler.getChurnDate();
                } else {
                    newChurnStatus = CustomerChurnStatusEnum.NO_CHURN_CUSTOMER.getCode();
                    newChurnDate = null;
                }
                if (!Objects.equals(customerSaas.getChurnStatus(), newChurnStatus) ||
                        !Objects.equals(customerSaas.getChurnDate(), newChurnDate)) {
                    customerSaas.setChurnStatus(newChurnStatus);
                    customerSaas.setChurnDate(newChurnDate);
                    needUpdateList.add(customerSaas);
                }
            }
            // 批量更新需要修改的记录
            if (!needUpdateList.isEmpty()) {
                customerSaasRepository.updateBatchById(needUpdateList);
            }

            if (!batchList.isEmpty()) {
                lastId = batchList.get(batchList.size() - 1).getId();
            }

        } while (batchList.size() == pageSize);
    }

    private ChurnResultDto customerSaasHandler(CustomerSaasModel customerSaas) {
        // 查询规则
        AchievementProductRuleConfigModel ruleConfig = ruleConfigRepository.getOne(
                new LambdaUpdateWrapper<AchievementProductRuleConfigModel>()
                        .eq(AchievementProductRuleConfigModel::getProductId, customerSaas.getProductId())
                        .eq(AchievementProductRuleConfigModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        if (ruleConfig == null) {
            return new ChurnResultDto(false, null);
        }
        ProductRuleEnum ruleEnum = ProductRuleEnum.getByCode(ruleConfig.getRuleCode());
        ChurnResultDto result = null;
        switch (ruleEnum) {
            case RULE_1:
                result = rule1Handler(customerSaas);
                break;
            case RULE_2:
                result = rule2Handler(customerSaas);
                break;
            case RULE_3:
                result = rule3Handler(customerSaas);
                break;
            case RULE_4:
                result = rule4Handler();
                break;
        }
        return result;
    }

    private ChurnResultDto rule1Handler(CustomerSaasModel customerSaas) {
        Map<String, ServiceInfoResponse> serverNoMap = getServiceInfoMap(customerSaas.getServeNo());
        if (!checkHasServer(customerSaas.getServeNo(), serverNoMap)) {
            return new ChurnResultDto(false, null);
        }
        ServiceInfoResponse service = serverNoMap.get(customerSaas.getServeNo());
        if (Objects.isNull(service.getEndTime())) {
            return new ChurnResultDto(false, null);
        }
        Date endTime = DateUtil.parse(service.getEndTime()).toJdkDate();
        return new ChurnResultDto(true, endTime);
    }

    private ChurnResultDto rule2Handler(CustomerSaasModel customerSaas) {
        Map<String, ServiceInfoResponse> serverNoMap = getServiceInfoMap(customerSaas.getServeNo());
        if (!checkHasServer(customerSaas.getServeNo(), serverNoMap)) {
            return new ChurnResultDto(false, null);
        }
        ServiceInfoResponse service = serverNoMap.get(customerSaas.getServeNo());
        // 不做状态判断，直接取流失时间
        if (Objects.isNull(service.getEndTime())) {
            return new ChurnResultDto(false, null);
        }
        Date endTime = DateUtil.parse(service.getEndTime()).toJdkDate();
        return new ChurnResultDto(true, endTime);
    }

    private ChurnResultDto rule3Handler(CustomerSaasModel customerSaas) {
        Map<String, ServiceInfoResponse> serverNoMap = getServiceInfoMap(customerSaas.getServeNo());
        if (!checkHasServer(customerSaas.getServeNo(), serverNoMap)) {
            return new ChurnResultDto(false, null);
        }
        Date rechargeTime = null;
        List<QueryCustomerAllRechargeTimeDto> queryCustomerAllRechargeTime = innerService
                .queryCustomerAllRechargeTime(customerSaas.getServeNo());
        if (CollectionUtils.isEmpty(queryCustomerAllRechargeTime)) {
            RechargeInfoResponse rechargeInfo = innerService.getRechargeInfo(customerSaas.getCustomerId());
            if (Objects.isNull(rechargeInfo) || Objects.isNull(rechargeInfo.getRechargeTime())) {
                return new ChurnResultDto(false, null);
            } else {
                rechargeTime = rechargeInfo.getRechargeTime();
            }
        } else {
            // 直接取所有rechargeTime最大值
            rechargeTime = queryCustomerAllRechargeTime.stream()
                    .filter(dto -> Objects.nonNull(dto.getRechargeTime()))
                    .map(dto -> DateUtil.parse(dto.getRechargeTime()))
                    .map(DateTime::toJdkDate)
                    .max(Date::compareTo)
                    .orElse(null);
        }
        if (rechargeTime == null) {
            return new ChurnResultDto(false, null);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(rechargeTime);
        calendar.add(Calendar.YEAR, 5);
        return new ChurnResultDto(true, calendar.getTime());
    }

    private ChurnResultDto rule4Handler() {
        return new ChurnResultDto(false, null);
    }

    private boolean checkHasServer(String serverNo, Map<String, ServiceInfoResponse> serverNoMap) {
        return Objects.nonNull(serverNo) && serverNoMap.containsKey(serverNo);
    }

    public boolean isNewCustomer(String customerId, Date date, Long orderId, String thirdId, String orderNo,
            AchievementSourceEnum sourceEnum) {
        ChurnStatusDto thirdChurn = new ChurnStatusDto();

        ChurnStatusDto localChurn = new ChurnStatusDto();

        Boolean resultChurn = null;
        Date resultChurnDate = null;

        localChurn(localChurn, customerId, date, orderId);
        thirdChurn(thirdChurn, customerId, date);

        // 4.只有跨境和中企都流失才算流失
        if (thirdChurn.getIsChurn() && localChurn.getIsChurn()) {
            resultChurn = true;
        } else {
            resultChurn = false;
        }
        resultChurnDate = resultChurnDate(thirdChurn.getChurnDate(), localChurn.getChurnDate());

        log.info("customerId:{} orderId:{} 最大流失时间:{} +180天:{}", customerId, orderId, resultChurnDate, resultChurnDate);
        // 只有客户为流失，且最大流失时间+180天比当前日期小，才为saas新客
        return newSaasCustomerFinalHandler(resultChurn, resultChurnDate, customerId, orderId, thirdId, orderNo,
                sourceEnum,
                date);
    }

    private boolean newSaasCustomerFinalHandler(Boolean resultChurn, Date resultChurnDate, String customerId,
            Long orderId, String thirdId, String orderNo, AchievementSourceEnum sourceEnum, Date date) {

        boolean isSaasOrder = isSaasOrder(orderId, orderNo, thirdId, sourceEnum);
        // 新客或者流失+180
        if (Boolean.TRUE.equals(resultChurn) && (resultChurnDate == null || resultChurnDate.before(date))) {
            // 中企每天同步一次，所以需要判断一下当天是否已经下过中企订单了
            // 只有当date为今天时才进行此判断
            if (isToday(date)) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                Date startOfDay = calendar.getTime();

                long count = customerSaasRepository.lambdaQuery()
                        .eq(CustomerSaasModel::getCustomerId, customerId)
                        .ge(CustomerSaasModel::getOrderPaymentTime, startOfDay)
                        .lt(CustomerSaasModel::getOrderPaymentTime, date)
                        .eq(CustomerSaasModel::getOrderSource, AchievementSourceEnum.ZHONGXIAO.getCode())
                        .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                        .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                        .count();

                if (count == 0) {
                    return true;
                }
            } else {
                // 如果不是今天，直接返回true（历史数据处理）
                return true;
            }
        }
        if (!isSaasOrder) {
            return false; // 如果不是SaaS订单，直接返回false
        }
        CustomerSaasModel result = customerSaasRepository.lambdaQuery()
                .eq(CustomerSaasModel::getCustomerId, customerId)
                .lt(CustomerSaasModel::getOrderPaymentTime, date)
                .ne(CustomerSaasModel::getOrderNo, orderNo)
                .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                .in(CustomerSaasModel::getSaasStatus, Arrays.asList(CustomerSaasEnum.NEW_CUSTOMER.getCode(),
                        CustomerSaasEnum.NEW_SAAS_CUSTOMER.getCode(), CustomerSaasEnum.OLD_SAAS_CUSTOMER.getCode()))
                .orderByDesc(CustomerSaasModel::getOrderPaymentTime) // 按OrderPaymentTime降序
                .last("LIMIT 1") // 只取第一条
                .one();
        // 如果没有记录，说明是新客或者前面全是 老客的记录
        if (result == null) {
            return true;
        }
        // 找到最近一个是saas的记录,说明当前循环中这个订单不是第一笔saas订单
        if (result.getSaasStatus().equals(CustomerSaasEnum.NEW_SAAS_CUSTOMER.getCode())
                || result.getSaasStatus().equals(CustomerSaasEnum.OLD_SAAS_CUSTOMER.getCode())) {
            return false;
        }
        // 找到最近一个不是saas的记录,先判断一下这个订单是不是saas订单
        return !isSaasOrder(result.getOrderId(), result.getOrderNo(), thirdId,
                AchievementSourceEnum.getByCode(result.getOrderSource()));
    }

    private boolean isSaasOrder(Long orderId, String orderNo, String thirdId, AchievementSourceEnum sourceEnum) {
        if (orderId == null || sourceEnum == null) {
            return false;
        }
        if (AchievementSourceEnum.KUAJINFG.getCode().equals(sourceEnum.getCode())) {
            OrderSimpleInfoResponse orderInfo = innerService.getOrderSimpleInfo(orderId);
            if (orderInfo == null) {
                return false;
            }
            List<CustomerSaasModel> refundedOrDeletedSaasList = customerSaasRepository.lambdaQuery()
                    .eq(CustomerSaasModel::getOrderId, orderId)
                    .and(wrapper -> wrapper
                            .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode()) // 已删除
                            .or()
                            .in(CustomerSaasModel::getStatus, AchStatus.REFUND.getType(), AchStatus.NOT_VALID.getType()) // status=3,4
                    )
                    .eq(CustomerSaasModel::getOrderSource, sourceEnum.getCode())
                    .list();

            Set<String> refundedOrDeletedProductIds = refundedOrDeletedSaasList.stream()
                    .map(CustomerSaasModel::getOrderProductCode)
                    .collect(Collectors.toSet());

            for (OrderSimpleProductResponse product : orderInfo.getProductResponseList()) {
                // 跳过已退款或已删除的产品
                if (refundedOrDeletedProductIds.contains(product.getOrderProductCode())) {
                    continue;
                }
                // 对剩余产品进行SaaS判断
                if (saasTabService.checkIsSaasKuaJing(product.getProductId())) {
                    return true; // 只要有一个商品是SaaS产品，就返回true
                }
            }
        } else if (AchievementSourceEnum.ZHONGXIAO.getCode().equals(sourceEnum.getCode())) {
            // 1. 从ThirdAchievementModel表中用orderNo查出对应的所有数据
            List<ThirdAchievementModel> thirdAchievementList = thirdAchievementService.getInfoByOrderNo(orderNo);
            if (thirdAchievementList == null || thirdAchievementList.isEmpty()) {
                return false;
            }

            // 2. 查询已退款或已删除的customer_saas记录（使用orderNo匹配）
            List<CustomerSaasModel> refundedOrDeletedSaasList = customerSaasRepository.lambdaQuery()
                    .eq(CustomerSaasModel::getOrderNo, orderNo)
                    .and(wrapper -> wrapper
                            .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode()) // 已删除
                            .or()
                            .in(CustomerSaasModel::getStatus, AchStatus.REFUND.getType(), AchStatus.NOT_VALID.getType()) // status=3,4
                    )
                    .eq(CustomerSaasModel::getOrderSource, sourceEnum.getCode())
                    .list();

            // 3. 收集已退款或已删除的产品ID
            Set<String> refundedOrDeletedProductIds = refundedOrDeletedSaasList.stream()
                    .map(CustomerSaasModel::getThirdId)
                    .collect(Collectors.toSet());

            // 4. 遍历ThirdAchievement中的所有产品，排除已退款或已删除的产品
            for (ThirdAchievementModel thirdAchievement : thirdAchievementList) {
                // 跳过已退款或已删除的产品
                if (refundedOrDeletedProductIds.contains(thirdAchievement.getThirdId())) {
                    continue;
                }

                // 对剩余产品进行SaaS判断
                if (saasTabService.checkIsSaasZhongQi(thirdAchievement.getProductId())) {
                    return true; // 只要有一个商品是SaaS产品，就返回true
                }
            }
        }
        return false;
    }

    // 返回流失日期+180天
    private Date resultChurnDate(Date thirdChurnDate, Date localChurnDate) {
        Date maxChurnDate = Stream.of(thirdChurnDate, localChurnDate)
                .filter(Objects::nonNull)
                .max(Date::compareTo)
                .orElse(null);
        // 增加180天
        if (maxChurnDate == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(maxChurnDate);
        calendar.add(Calendar.DAY_OF_YEAR, 180);
        return calendar.getTime();
    }

    private void localChurn(ChurnStatusDto localChurn, String customerId, Date date, Long orderId) {
        boolean hasNoChurn = customerSaasRepository.lambdaQuery()
                .eq(CustomerSaasModel::getCustomerId, customerId)
                .lt(CustomerSaasModel::getOrderPaymentTime, date)
                .ne(CustomerSaasModel::getOrderId, orderId)
                .eq(CustomerSaasModel::getChurnStatus, CustomerChurnStatusEnum.NO_CHURN_CUSTOMER.getCode())
                .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(CustomerSaasModel::getOrderSource, AchievementSourceEnum.KUAJINFG.getCode())
                .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                .count() > 0;
        // 存在未流失记录
        if (hasNoChurn) {
            localChurn.setIsChurn(false);
            return; // 如果本地未流失，直接返回false
        }
        localChurn.setIsChurn(true);
        localChurn.setChurnDate(customerSaasRepository.getMaxChurnDateByCustomerId(customerId, date, orderId));
    }

    private void thirdChurn(ChurnStatusDto thirdChurn, String customerId, Date date) {
        boolean isToday = isToday(date);
        if (!isToday) {
            handleHistoryChurn(thirdChurn, customerId, date);
        }
        if (thirdChurn.getIsChurn() == null) {
            handleCurrentChurn(thirdChurn, customerId);
        }

    }

    private void handleHistoryChurn(ChurnStatusDto thirdChurn, String customerId, Date date) {
        List<String> lossDateList = getCustomerLossDateListWithCache(customerId);
        if (CollectionUtils.isEmpty(lossDateList)) {
            return;
        }
        List<Date> lossDateListDate = lossDateList.stream()
                .map(DateUtil::parse)
                .map(DateTime::toJdkDate)
                .sorted()
                .collect(Collectors.toList());
        Integer maxIndex = findMaxIndexBeforeDate(lossDateListDate, date);

        Date start = (maxIndex != null) ? lossDateListDate.get(maxIndex) : null;
        int count = customerSaasRepository.countOrderBetween(customerId, start, date,
                AchievementSourceEnum.ZHONGXIAO.getCode());
        if (count == 0) {
            thirdChurn.setIsChurn(true);
            if (maxIndex != null) {
                thirdChurn.setChurnDate(lossDateListDate.get(maxIndex));
            }
        } else {
            thirdChurn.setIsChurn(false);
        }

    }

    private Integer findMaxIndexBeforeDate(List<Date> dateList, Date date) {
        for (int i = dateList.size() - 1; i >= 0; i--) {
            if (dateList.get(i).before(date)) {
                return i;
            }
        }
        return null;
    }

    private void handleCurrentChurn(ChurnStatusDto thirdChurn, String customerId) {
        CustomerChurnResponse response = getCustomerChurnInfoWithCache(customerId);
        if (Objects.nonNull(response)) {
            log.info("customerId:{},三方客户流失状态:{}", customerId, JSONUtil.toJsonStr(response));
            thirdChurn.setIsChurn(CustomerChurnStatusEnum.CHURN_CUSTOMER.equals(response.getChurnStatus()) ||
                    CustomerChurnStatusEnum.NEW_CUSTOMER.equals(response.getChurnStatus()));
            thirdChurn.setChurnDate(response.getChurnTime());
        } else {
            thirdChurn.setIsChurn(true); // 如果没有返回，默认认为是流失
        }
    }

    /**
     * 从Redis缓存获取客户流失信息，如果缓存不存在则调用接口并存入缓存
     *
     * @param customerId 客户ID
     * @return 客户流失信息
     */
    @Override
    public CustomerChurnResponse getCustomerChurnInfoWithCache(String customerId) {
        if (StringUtils.isEmpty(customerId)) {
            return null;
        }

        String redisKey = REDIS_KEY_CUSTOMER_CHURN_INFO + customerId;

        try {
            // 先从Redis获取
            String cachedData = stringRedisTemplate.opsForValue().get(redisKey);
            if (!StringUtils.isEmpty(cachedData)) {
                log.debug("从Redis缓存获取客户流失信息, customerId: {}", customerId);
                return JSONUtil.toBean(cachedData, CustomerChurnResponse.class);
            }

            // 缓存不存在，调用接口获取数据
            log.debug("Redis缓存不存在，调用接口获取客户流失信息, customerId: {}", customerId);
            CustomerChurnResponse response = innerService.getGetCustomerInfoByCondition(customerId);

            // 只有当不是流失客户缺少流失时间的情况时才存入Redis缓存
            if (!(CustomerChurnStatusEnum.CHURN_CUSTOMER.equals(response.getChurnStatus())
                    && response.getChurnTime() == null)) {
                stringRedisTemplate.opsForValue().set(redisKey, JSONUtil.toJsonStr(response),
                        REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
                log.debug("客户流失信息已存入Redis缓存, customerId: {}", customerId);
            } else {
                log.debug("流失客户但缺少流失时间，不进行缓存, customerId: {}", customerId);
            }

            return response;
        } catch (Exception e) {
            log.error("获取客户流失信息缓存失败, customerId: {}, error: {}", customerId, e.getMessage(), e);
            // 缓存失败时直接调用接口
            return innerService.getGetCustomerInfoByCondition(customerId);
        }
    }

    @Override
    public void handler(MqOrderPaymentInfoModel model) {
        if (model == null) {
            return;
        }
        TaskTypeEnum typeEnum = TaskTypeEnum.getByMsg(model.getTaskType());
        if (typeEnum == null) {
            return;
        }
        switch (typeEnum) {
            case ADD:
                addHandler(model);
                break;
            case UPDATE:
                updateHandler(model);
                break;
            // 移除删除动作
            // case DELETE:
            // deleteHandler(model);
            // break;
        }
    }

    public void addHandler(MqOrderPaymentInfoModel model) {
        lock.lock();
        try {
            if (AchievementSourceEnum.KUAJINFG.getCode().equals(model.getAchievementSource())) {
                addHandlerKJ(model);
            } else if (AchievementSourceEnum.ZHONGXIAO.getCode().equals(model.getAchievementSource())) {
                addHandlerZQ(model);
            }
        } finally {
            lock.unlock();
        }
    }

    private CustomerSaasEnum getAddCustomerSaasEnum(Long productId, boolean isNewCustomer) {
        CustomerSaasEnum customerSaasEnum = null;
        boolean isSaasProduct = saasTabService.checkIsSaas(productId);
        if (isNewCustomer) {
            if (isSaasProduct) {
                customerSaasEnum = CustomerSaasEnum.NEW_SAAS_CUSTOMER;
            } else {
                customerSaasEnum = CustomerSaasEnum.NEW_CUSTOMER;
            }
        } else {
            if (isSaasProduct) {
                customerSaasEnum = CustomerSaasEnum.OLD_SAAS_CUSTOMER;
            } else {
                customerSaasEnum = CustomerSaasEnum.OLD_CUSTOMER;
            }
        }
        return customerSaasEnum;
    }

    public void addHandlerZQ(MqOrderPaymentInfoModel mqModel) {
        try {
            ThirdAchievementModel thirdAchievementModel = thirdAchievementService.getInfoByTaskId(mqModel.getOrderId());
            if (Objects.isNull(thirdAchievementModel)) {
                return;
            }
            // 当state为空或者=1,3的时候直接返回
            if (ThirdAchievementStateEnum.shouldReturn(thirdAchievementModel.getState())) {
                return;
            }
            AchievementProductDetailModel currentModel = achievementProductWrapper
                    .buildFromThirdAchievement(thirdAchievementModel, mqModel);

            // 第三方为退款或者转款
            if (OrderSaleTypeEnum.TRANSFER.getType().equals(currentModel.getSaleType()) ||
                    OrderSaleTypeEnum.REFUND.getType().equals(currentModel.getSaleType())) {
                refundHandler(currentModel.getOrderNo(), currentModel.getProductId());
                return;
            }

            // 不是退款转款视为新增
            // 先查表中是否已存在该订单第三方ID的数据
            if (customerSaasRepository.lambdaQuery()
                    .eq(CustomerSaasModel::getThirdId, thirdAchievementModel.getThirdId())
                    .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .count() > 0) {
                return;
            }
            boolean isNewCustomer = isNewCustomer(currentModel.getCustomerId(),
                    thirdAchievementModel.getToAccountDate(), currentModel.getOrderId(),
                    thirdAchievementModel.getThirdId(), thirdAchievementModel.getOrderRecordCode(),
                    AchievementSourceEnum.ZHONGXIAO);
            CustomerSaasModel model = new CustomerSaasModel();
            BeanUtil.copyProperties(currentModel, model);
            // 取sql的当前时间
            model.setCreateTime(null);
            model.setUpdateTime(null);
            model.setMonthId(currentModel.getBusinessMonthId());
            model.setMonth(currentModel.getBusinessMonth());
            model.setSaasStatus(getAddCustomerSaasEnum(currentModel.getProductId(), isNewCustomer).getCode());
            model.setOrderSource(AchievementSourceEnum.ZHONGXIAO.getCode());
            model.setStatus(AchStatus.VALID.getType());
            model.setOrderPaymentTime(thirdAchievementModel.getToAccountDate());
            model.setThirdId(thirdAchievementModel.getThirdId());
            customerSaasRepository.save(model);
        } catch (Exception e) {
            log.error("addHandlerZQ error, mqModel: {}, error: {}", JSONUtil.toJsonStr(mqModel), e.getMessage(), e);
        }

    }

    private void addHandlerKJ(MqOrderPaymentInfoModel model) {
        // 先根据订单编号查出订单的信息
        OrderSimpleInfoResponse orderInfo = innerService.getOrderSimpleInfo(model.getOrderId());
        if (orderInfo == null) {
            return;
        }

        if (CalculateTypeEnum.PAYMENT.getCode().equals(model.getCalculateType())) {
            if (model.getPaymentTime() == null) {
                return;
            }
            String customerId = orderInfo.getCustomerId();
            boolean isNewCustomer = isNewCustomer(customerId, model.getPaymentTime(), model.getOrderId(),
                    null, orderInfo.getOrderNo(), AchievementSourceEnum.KUAJINFG);
            String businessId = null;
            String businessRepresentative = null;

            ProtectByCustomer protectByCustomerId = innerService.getProtectByCustomerId(orderInfo.getCustomerId());

            if (null == protectByCustomerId) {
                // 客保关系为空 从订单获取
                log.info("buildBaseDtoList 客保关系为空:{}", orderInfo.getCustomerId());
                OrderContactResponse contract = orderInfo.getOrderContactResponseList().stream()
                        .filter(c -> LinkManTypeEnum.SALE.getType().equals(c.getType())).findFirst()
                        .orElse(new OrderContactResponse());
                businessId = contract.getUserId();
                businessRepresentative = contract.getName();
            } else {
                // 商务id
                businessId = protectByCustomerId.getSalerId();
                UserInfoDetailResp userInfoDetail = innerService.getUserInfoDetail(businessId);
                businessRepresentative = userInfoDetail.getName();
            }

            BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(model.getCreateTime());
            for (OrderSimpleProductResponse product : orderInfo.getProductResponseList()) {
                addPaymentHandler(model, orderInfo, product, businessId, businessRepresentative, monthInfo,
                        getAddCustomerSaasEnum(product.getProductId(), isNewCustomer));
            }
        } else if (CalculateTypeEnum.SERVEINPROGRESS.getCode().equals(model.getCalculateType())) {
            updateServiceHandler(model, orderInfo);
        }
    }

    private void updateServiceHandler(MqOrderPaymentInfoModel mqInfo, OrderSimpleInfoResponse orderInfo) {
        String orderProductCode = null;
        if (StringUtils.isEmpty(mqInfo.getOrderProductId())) {
            AchievementProductDetailModel detail = achievementProductDetailRepository.lambdaQuery()
                    .eq(AchievementProductDetailModel::getOrderId, orderInfo.getOrderId())
                    .eq(AchievementProductDetailModel::getProductId, mqInfo.getProductId())
                    .eq(AchievementProductDetailModel::getServeNo, mqInfo.getServeNo())
                    .last("limit 1")
                    .one();
            if (detail == null) {
                return;
            }
            orderProductCode = detail.getOrderProductId();
        } else {
            orderProductCode = mqInfo.getOrderProductId();
        }

        CustomerSaasModel customerSaas = customerSaasRepository.lambdaQuery()
                .eq(CustomerSaasModel::getOrderId, orderInfo.getOrderId())
                .eq(CustomerSaasModel::getProductId, mqInfo.getProductId())
                .eq(CustomerSaasModel::getOrderProductCode, orderProductCode)
                .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                .isNull(CustomerSaasModel::getServeNo)
                .one();
        if (customerSaas == null) {
            return;
        }
        customerSaas.setServeNo(mqInfo.getServeNo());
        ChurnResultDto customerSaasHandler = customerSaasHandler(customerSaas);
        if (customerSaasHandler != null && customerSaasHandler.isChurn()) {
            customerSaas.setChurnStatus(CustomerChurnStatusEnum.CHURN_CUSTOMER.getCode());
            customerSaas.setChurnDate(customerSaasHandler.getChurnDate());
        } else {
            customerSaas.setChurnStatus(CustomerChurnStatusEnum.NO_CHURN_CUSTOMER.getCode());
            customerSaas.setChurnDate(null);
        }
        updateCustomerSaas(customerSaas);
    }

    private void addPaymentHandler(MqOrderPaymentInfoModel paymentInfo, OrderSimpleInfoResponse orderInfo,
            OrderSimpleProductResponse product, String businessId, String businessRepresentative,
            BusinessMonthModel monthInfo, CustomerSaasEnum customerSaasEnum) {
        if (customerSaasRepository.lambdaQuery()
                .eq(CustomerSaasModel::getOrderId, product.getOrderId())
                .eq(CustomerSaasModel::getProductId, product.getProductId())
                .eq(CustomerSaasModel::getOrderProductCode, product.getOrderProductCode())
                .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .count() > 0) {
            return;
        }
        Date paymentTime = paymentInfo.getPaymentTime();
        if (!isToday(paymentTime)) {
            // 历史数据，尝试从业绩记录取 商务
            AchievementProductDetailModel detail = achievementProductDetailRepository.getOne(
                    new LambdaQueryWrapper<AchievementProductDetailModel>()
                            .eq(AchievementProductDetailModel::getOrderId, product.getOrderId())
                            .eq(AchievementProductDetailModel::getProductId, product.getProductId())
                            .eq(AchievementProductDetailModel::getMainSplitPerson, MainSubEnum.MAIN.getType())
                            .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                            .last("limit 1"));
            if (detail != null) {
                businessId = StringUtils.isEmpty(detail.getBusinessId()) ? businessId : detail.getBusinessId();
                businessRepresentative = StringUtils.isEmpty(detail.getBusinessRepresentative())
                        ? businessRepresentative
                        : detail.getBusinessRepresentative();
            }
        }
        CustomerSaasModel model = new CustomerSaasModel();
        List<ServeProductConfigResponse> serveProductConfigList = innerService.getConfigList(product.getProductId());
        if (!CollectionUtils.isEmpty(serveProductConfigList) &&
                NeedProductionEnum.NEED.getCode().equals(serveProductConfigList.get(0).getNeedProduction())) {
            // 有服务商品，直接未流失
            model.setChurnStatus(CustomerChurnStatusEnum.NO_CHURN_CUSTOMER.getCode());
        } else {
            // 无服务商品，直接为当前流失
            model.setChurnStatus(CustomerChurnStatusEnum.CHURN_CUSTOMER.getCode());
            model.setChurnDate(paymentTime);
        }
        model.setCustomerId(orderInfo.getCustomerId());
        model.setCustomerName(orderInfo.getCustomerName());
        model.setOrderId(orderInfo.getOrderId());
        model.setOrderNo(orderInfo.getOrderNo());
        model.setOrderProductId(product.getOrderProductId());
        model.setOrderProductCode(product.getOrderProductCode());
        model.setProductId(product.getProductId());
        model.setProductName(product.getProductName());
        model.setBusinessId(businessId);
        model.setBusinessRepresentative(businessRepresentative);
        model.setMonth(monthInfo != null ? monthInfo.getMonth() : null);
        model.setMonthId(monthInfo != null ? monthInfo.getMonthId() : null);
        model.setSaasStatus(customerSaasEnum.getCode());
        model.setOrderSource(paymentInfo.getAchievementSource());
        model.setStatus(AchStatus.VALID.getType());
        model.setOrderPaymentTime(paymentTime);
        customerSaasRepository.save(model);
    }

    @Override
    public void updateHandler(Long orderId, Long productId, String orderProductCode, String businessId,
            String businessRepresentative) {
        customerSaasRepository.lambdaUpdate()
                .eq(CustomerSaasModel::getOrderId, orderId)
                .eq(CustomerSaasModel::getProductId, productId)
                .eq(CustomerSaasModel::getOrderProductCode, orderProductCode)
                .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .set(CustomerSaasModel::getBusinessRepresentative, businessRepresentative)
                .set(CustomerSaasModel::getBusinessId, businessId)
                .update();
    }

    public void updateHandler(String orderNo, Long productId, String businessId, String businessRepresentative) {
        customerSaasRepository.lambdaUpdate()
                .eq(CustomerSaasModel::getOrderNo, orderNo)
                .eq(CustomerSaasModel::getProductId, productId)
                .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .set(CustomerSaasModel::getBusinessRepresentative, businessRepresentative)
                .set(CustomerSaasModel::getBusinessId, businessId)
                .update();
    }

    public void updateHandler(MqOrderPaymentInfoModel model) {
        try {
            if (!AchievementSourceEnum.ZHONGXIAO.getCode().equals(model.getAchievementSource())) {
                // 非中小企业，不处理
                return;
            }
            ThirdAchievementModel thirdAchievementModel = thirdAchievementService.getInfoByTaskId(model.getOrderId());
            AchievementProductDetailModel currentModel = achievementProductWrapper
                    .buildFromThirdAchievement(thirdAchievementModel, model);
            if (currentModel == null) {
                return;
            }
            updateHandler(currentModel.getOrderNo(), currentModel.getProductId(), currentModel.getBusinessId(),
                    currentModel.getBusinessRepresentative());
        } catch (Exception e) {
            log.error("updateHandler error for orderId:{},productId:{},businessId:{}", model.getOrderId(),
                    model.getProductId(), e);
        }
    }

    private void changeStatus(String orderNo, Long productId, AchStatus status) {
        lock.lock();
        try {
            // 第三方退款，如果一个商品购买多次，只能随机取一个退掉
            CustomerSaasModel saasModel = customerSaasRepository.lambdaQuery()
                    .eq(CustomerSaasModel::getOrderNo, orderNo)
                    .eq(CustomerSaasModel::getProductId, productId)
                    .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                    .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .last("LIMIT 1")
                    .one();
            if (saasModel == null) {
                return;
            }
            saasModel.setStatus(status.getType());
            customerSaasRepository.updateById(saasModel);
            recalculateAfterSaasHandler(saasModel);
        } finally {
            lock.unlock();
        }
    }

    private void changeStatus(Long orderId, Long productId, String orderProductCode, AchStatus status) {
        lock.lock();
        try {
            CustomerSaasModel saasModel = customerSaasRepository.lambdaQuery()
                    .eq(CustomerSaasModel::getOrderId, orderId)
                    .eq(CustomerSaasModel::getProductId, productId)
                    .eq(CustomerSaasModel::getOrderProductCode, orderProductCode)
                    .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                    .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .one();
            if (saasModel == null) {
                return;
            }
            saasModel.setStatus(status.getType());
            customerSaasRepository.updateById(saasModel);
            recalculateAfterSaasHandler(saasModel);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void refundHandler(Long orderId, Long productId, String orderProductCode) {
        changeStatus(orderId, productId, orderProductCode, AchStatus.REFUND);
    }

    public void refundHandler(String orderNo, Long productId) {
        changeStatus(orderNo, productId, AchStatus.REFUND);
    }

    @Override
    public void refundHandler(MqOrderRefundInfoModel refundTask) {
        List<AchievementProductDetailModel> productDetailList = queryAchievementProductDetail(refundTask.getOrderId(),
                refundTask.getOrderProductCode());
        productDetailList.forEach(data->changeStatus(data.getOrderId(), data.getProductId(), data.getOrderProductId(), AchStatus.REFUND));
        
    }

    @Override
    public void deleteHandler(Long orderId, Long productId, String orderProductCode) {
        changeStatus(orderId, productId, orderProductCode, AchStatus.NOT_VALID);
    }

    public void deleteHandler(MqOrderPaymentInfoModel model) {
        if (!AchievementSourceEnum.ZHONGXIAO.getCode().equals(model.getAchievementSource())) {
            // 非中小企业，不处理
            return;
        }
        ThirdAchievementModel thirdAchievementModel = thirdAchievementService.getInfoByTaskId(model.getOrderId());
        changeStatus(thirdAchievementModel.getOrderRecordCode(), thirdAchievementModel.getProductId(),
                AchStatus.NOT_VALID);
    }

    private void recalculateAfterSaasHandler(CustomerSaasModel saasModel) {
        // 重新计算当前订单之后的所有订单的saas状态
        // 应该可以根据时间简化需要检测的数据，目前先直接检测所有
        List<CustomerSaasModel> saasList = customerSaasRepository.lambdaQuery()
                .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                .eq(CustomerSaasModel::getCustomerId, saasModel.getCustomerId())
                .ge(CustomerSaasModel::getOrderPaymentTime, saasModel.getOrderPaymentTime())
                .orderByAsc(CustomerSaasModel::getOrderPaymentTime)
                .list();
        if (CollectionUtils.isEmpty(saasList)) {
            return;
        }
        for (CustomerSaasModel model : saasList) {
            boolean isNewCustomer = isNewCustomer(model.getCustomerId(),
                    model.getOrderPaymentTime(), model.getOrderId(), model.getThirdId(), model.getOrderNo(),
                    AchievementSourceEnum.getByCode(model.getOrderSource()));
            CustomerSaasEnum newSaasStatus = getCustomerSaasEnum(isNewCustomer, model);
            // 只有状态发生变化时才更新
            if (!newSaasStatus.getCode().equals(model.getSaasStatus())) {
                model.setSaasStatus(newSaasStatus.getCode());
                customerSaasRepository.updateById(model);
            }
        }
    }

    private CustomerSaasEnum getCustomerSaasEnum(boolean isNewCustomer, CustomerSaasModel model) {
        CustomerSaasEnum customerSaasEnum = null;
        if (CustomerSaasEnum.NEW_SAAS_CUSTOMER.getCode().equals(model.getSaasStatus())
                || CustomerSaasEnum.OLD_SAAS_CUSTOMER.getCode().equals(model.getSaasStatus())) {
            customerSaasEnum = isNewCustomer ? CustomerSaasEnum.NEW_SAAS_CUSTOMER
                    : CustomerSaasEnum.OLD_SAAS_CUSTOMER;
        } else {
            customerSaasEnum = isNewCustomer ? CustomerSaasEnum.NEW_CUSTOMER : CustomerSaasEnum.OLD_CUSTOMER;
        }
        return customerSaasEnum;
    }

    private List<AchievementProductDetailModel> queryAchievementProductDetail(Long orderId,
            String orderProductCode) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getOrderId, orderId)
                .eq(StrUtil.isNotBlank(orderProductCode), AchievementProductDetailModel::getOrderProductId,
                        orderProductCode)
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        return achievementProductDetailRepository.list(queryWrapper);
    }

    @Override
    public void recalculateHistory() {
        log.info("开始重算历史数据");

        int coreThreads = 4; // 指定核心线程数

        try {
            // 初始化Redis页数（支持断点重续）
            String lastPage = stringRedisTemplate.opsForValue().get(REDIS_KEY_CUSTOMER_SAAS);
            if (StringUtils.isEmpty(lastPage)) {
                // 首次运行，初始化为0
                stringRedisTemplate.opsForValue().set(REDIS_KEY_CUSTOMER_SAAS, "0", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
                log.info("首次运行，初始化Redis页数为0");
            } else {
                // 重启运行，需要回退coreThreads页，因为可能有coreThreads个线程同时在处理
                Long currentValue = Long.parseLong(lastPage);
                if (currentValue > 0) {
                    // 回退线程数量的页数，确保不会遗漏任何页面
                    Long rollbackValue = Math.max(0, currentValue - coreThreads);
                    stringRedisTemplate.opsForValue().set(REDIS_KEY_CUSTOMER_SAAS, rollbackValue.toString(),
                            REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
                    log.info("断点重续，Redis页数从 {} 回退到 {}（回退{}页，避免遗漏）", currentValue, rollbackValue, coreThreads);
                } else {
                    log.info("断点重续，Redis页数为 {}，无需回退", currentValue);
                }
            }

            // 使用CompletableFuture避免队列溢出问题
            log.info("开始并发处理历史支付数据，线程数: {}", coreThreads);

            // 创建固定线程数的CompletableFuture任务
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (int i = 0; i < coreThreads; i++) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        processHistoryPaymentData();
                    } catch (Exception e) {
                        log.error("历史支付数据处理线程异常", e);
                        throw new RuntimeException(e);
                    }
                });
                futures.add(future);
            }

            // 等待所有任务完成 - 无超时限制
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                log.info("所有历史支付数据处理线程已完成");
            } catch (Exception e) {
                log.error("等待任务完成时发生异常", e);
                // 取消所有未完成的任务
                futures.forEach(future -> future.cancel(true));
                throw new RuntimeException("历史数据处理失败", e);
            }

            log.info("历史支付数据处理完成");

            // 2. 处理退款记录
            processRefundData();

            // 3. 全量重算SaaS状态
            recalculateAllSaasStatus();

            log.info("历史数据重算完成");

        } catch (Exception e) {
            log.error("重算历史数据时发生异常", e);
            throw e;
        }
        // 注意：不清理REDIS_KEY_CUSTOMER_SAAS，保留进度以支持断点重续
    }

    /**
     * 处理历史支付数据 - 支持并发执行
     */
    private void processHistoryPaymentData() {
        String threadName = Thread.currentThread().getName();
        log.info("线程 {} 开始处理历史支付数据", threadName);

        int pageSize = 100; // 保持原有分页大小，确保数据处理的连续性
        int processedCount = 0;

        while (!Thread.currentThread().isInterrupted()) {
            // 原子性地获取并递增页数
            Long currentPage = stringRedisTemplate.opsForValue().increment(REDIS_KEY_CUSTOMER_SAAS);
            if (currentPage == null) {
                log.warn("线程 {} 无法获取Redis页数，退出处理", threadName);
                break;
            }

            // 重新设置过期时间，防止长时间处理导致键过期
            stringRedisTemplate.expire(REDIS_KEY_CUSTOMER_SAAS, REDIS_EXPIRE_DAYS, TimeUnit.DAYS);

            // 查询当前页数据
            IPage<MqOrderPaymentInfoModel> page = new Page<>(currentPage, pageSize);
            List<MqOrderPaymentInfoModel> paymentInfoList = mqOrderPaymentInfoRepository.page(
                    page,
                    new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                            .eq(MqOrderPaymentInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                            // .ge(MqOrderPaymentInfoModel::getCreateTime, "2024-10-23")
                            .last("ORDER BY ifnull(payment_time, create_time) ASC, id ASC"))
                    .getRecords();

            // 如果没有数据了，说明处理完成
            if (paymentInfoList.isEmpty()) {
                log.info("线程 {} 处理完成，第 {} 页无数据", threadName, currentPage);
                break;
            }

            // 处理当前页数据
            int currentPageProcessedCount = 0;
            for (MqOrderPaymentInfoModel paymentInfo : paymentInfoList) {
                try {
                    handler(paymentInfo);
                    currentPageProcessedCount++;
                    processedCount++;
                } catch (Exception e) {
                    log.error("线程 {} 处理支付信息失败, orderId: {}, error: {}",
                            threadName, paymentInfo.getOrderId(), e.getMessage(), e);
                }
            }

            log.info("线程 {} 处理第 {} 页完成，本页处理: {} 条，累计处理: {} 条",
                    threadName, currentPage, currentPageProcessedCount, processedCount);

            // 每处理1000条记录输出一次进度
            if (processedCount % 1000 == 0) {
                log.info("线程 {} 已处理 {} 条记录", threadName, processedCount);
            }

            // 如果当前页数据量小于页大小，说明是最后一页
            if (paymentInfoList.size() < pageSize) {
                log.info("线程 {} 处理完成，第 {} 页是最后一页", threadName, currentPage);
                break;
            }
        }

        log.info("线程 {} 历史支付数据处理完成，共处理: {} 条", threadName, processedCount);
    }

    /**
     * 处理退款数据
     */
    private void processRefundData() {
        log.info("开始处理退款数据");

        List<AchievementProductDetailModel> refundList = achievementProductDetailRepository
                .selectDistinctRefundOrderProduct();

        if (CollectionUtils.isEmpty(refundList)) {
            log.info("没有需要处理的退款数据");
            return;
        }

        int processedCount = 0;
        for (AchievementProductDetailModel refund : refundList) {
            try {
                refundHandler(refund.getOrderId(), refund.getProductId(), refund.getOrderProductId());
                processedCount++;
            } catch (Exception e) {
                log.error("处理退款数据失败, orderId: {}, productId: {}, error: {}",
                        refund.getOrderId(), refund.getProductId(), e.getMessage(), e);
            }
        }

        log.info("退款数据处理完成，共处理: {} 条", processedCount);
    }

    /**
     * 全量重算所有客户的SaaS状态（分页处理）
     */
    public void recalculateAllSaasStatus() {
        log.info("开始全量重算SaaS状态");

        int pageSize = 500;
        int pageNum = 1;

        // 从Redis获取上次处理的页码，支持断点续传
        String lastPage = stringRedisTemplate.opsForValue().get(REDIS_KEY_SAAS_STATUS_RECALC);
        if (!StringUtils.isEmpty(lastPage)) {
            pageNum = Integer.parseInt(lastPage);
            log.info("从Redis获取上次处理页码: {}, 继续处理", pageNum);
        }

        List<CustomerSaasModel> saasList;
        int totalProcessedCount = 0;

        do {
            log.info("开始处理第 {} 页SaaS状态重算", pageNum);

            IPage<CustomerSaasModel> page = new Page<>(pageNum, pageSize);
            saasList = customerSaasRepository.page(
                    page,
                    new LambdaQueryWrapper<CustomerSaasModel>()
                            .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                            .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                            .orderByAsc(CustomerSaasModel::getOrderPaymentTime)
                            .orderByAsc(CustomerSaasModel::getId))
                    .getRecords();

            if (CollectionUtils.isEmpty(saasList)) {
                log.info("第 {} 页没有需要重算的SaaS数据", pageNum);
                break;
            }

            int currentPageProcessedCount = 0;
            for (CustomerSaasModel model : saasList) {
                try {
                    boolean isNewCustomer = isNewCustomer(model.getCustomerId(),
                            model.getOrderPaymentTime(), model.getOrderId(), model.getThirdId(), model.getOrderNo(),
                            AchievementSourceEnum.getByCode(model.getOrderSource()));

                    CustomerSaasEnum newSaasStatus = getCustomerSaasEnum(isNewCustomer, model);

                    // 只有状态发生变化时才更新
                    if (!newSaasStatus.getCode().equals(model.getSaasStatus())) {
                        model.setSaasStatus(newSaasStatus.getCode());
                        customerSaasRepository.updateById(model);
                    }

                    currentPageProcessedCount++;
                    totalProcessedCount++;
                } catch (Exception e) {
                    log.error("重算SaaS状态失败, customerId: {}, orderId: {}, error: {}",
                            model.getCustomerId(), model.getOrderId(), e.getMessage(), e);
                }
            }

            // 保存当前处理的页码到Redis
            pageNum++;
            stringRedisTemplate.opsForValue().set(REDIS_KEY_SAAS_STATUS_RECALC, String.valueOf(pageNum),
                    REDIS_EXPIRE_DAYS, TimeUnit.DAYS);

            log.info("第 {} 页处理完成，本页处理: {} 条，累计处理: {} 条",
                    pageNum - 1, currentPageProcessedCount, totalProcessedCount);

        } while (saasList.size() == pageSize);

        log.info("SaaS状态重算完成，总共处理: {} 条", totalProcessedCount);
        stringRedisTemplate.delete(REDIS_KEY_SAAS_STATUS_RECALC);// 处理完成后清除Redis中的页码记录
    }

    @Override
    public List<CustomerSaasModel> searchCustomerSaas(List<String> businessIds, String startDate,
            String endDate) {
        if (CollectionUtils.isEmpty(businessIds) || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            return new ArrayList<>();
        }
        return customerSaasRepository.lambdaQuery()
                .in(CustomerSaasModel::getBusinessId, businessIds)
                .between(CustomerSaasModel::getOrderPaymentTime, startDate, endDate)
                .eq(CustomerSaasModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(CustomerSaasModel::getStatus, AchStatus.VALID.getType())
                .eq(CustomerSaasModel::getSaasStatus, CustomerSaasEnum.NEW_SAAS_CUSTOMER.getCode())
                .list();
    }

    @Override
    public int searchCount(String businessId, Long monthId) {
        QueryWrapper<CustomerSaasModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("COUNT(DISTINCT customer_id) as count")
                .eq("business_id", businessId)
                .eq("month_id", monthId)
                .eq("delete_flag", DeleteFlagEnum.NOT_DELETE.getCode())
                .eq("status", AchStatus.VALID.getType())
                .eq("saas_status", CustomerSaasEnum.NEW_SAAS_CUSTOMER.getCode());
        Map<String, Object> result = customerSaasRepository.getMap(queryWrapper);
        return ((Number) result.get("count")).intValue();
    }

    @Override
    public Map<String, String> getSaasRedisValues() {
        Map<String, String> result = new HashMap<>();
        try {
            String customerSaasValue = stringRedisTemplate.opsForValue().get(REDIS_KEY_CUSTOMER_SAAS);
            String saasStatusRecalcValue = stringRedisTemplate.opsForValue().get(REDIS_KEY_SAAS_STATUS_RECALC);

            result.put("CUSTOMER_SAAS", customerSaasValue);
            result.put("SAAS_STATUS_RECALC", saasStatusRecalcValue);

            log.info("获取SaaS Redis值，CUSTOMER_SAAS: {}, SAAS_STATUS_RECALC: {}", customerSaasValue,
                    saasStatusRecalcValue);
        } catch (Exception e) {
            log.error("获取SaaS Redis值失败，错误: {}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public boolean clearCustomerSaasRedisValue() {
        try {
            Boolean result = stringRedisTemplate.delete(REDIS_KEY_CUSTOMER_SAAS);
            log.info("清除客户SaaS Redis键，结果: {}", result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("清除客户SaaS Redis值失败，错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean clearSaasStatusRecalcRedisValue() {
        try {
            Boolean result = stringRedisTemplate.delete(REDIS_KEY_SAAS_STATUS_RECALC);
            log.info("清除SaaS状态重算Redis键，结果: {}", result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("清除SaaS状态重算Redis值失败，错误: {}", e.getMessage(), e);
            return false;
        }
    }
}
