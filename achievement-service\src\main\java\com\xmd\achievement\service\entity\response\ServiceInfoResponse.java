package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/11/10:57
 * @since 1.0
 */
@Data
public class ServiceInfoResponse {

    @Schema(description = "服务信息id")
    private Integer serveId;

    @Schema(description = "服务编号")
    private String serveNo;

    @Schema(description = "服务类型: 1-软件, 2-服务")
    private Integer serveType;

    @Schema(description = "服务类型名称")
    private String serveTypeName;

    @Schema(description = "商品id")
    private String productId;

    @Schema(description = "服务名称，来源商品名称")
    private String serveName;

    @Schema(description = "客户id")
    private String customerId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "服务状态：1待开通，2开通中，3已开通，4服务中，5服务完成")
    private Integer serveStatus;

    @Schema(description = "服务状态名称")
    private String serveStatusName;

    @Schema(description = "服务交付类型-sop节点名称")
    private String serveNodeName;

    @Schema(description = "服务开始时间")
    private String beginTime;

    @Schema(description = "服务结束时间")
    private String endTime;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "是否网站：0否 1是")
    private Integer siteFlag;

    @Schema(description = "是否广告：0否 1是")
    private Integer adFlag;

}
