package com.xmd.achievement.web.util;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * 请求工具类
 * 获取请求和响应
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 22:13
 **/
public class RequestUtil {

    /**
     * 获取请求信息
     *
     * @return javax.servlet.http.HttpServletRequest
     * <AUTHOR>
     * @date 2023/4/6 22:13
     **/
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = getAttributes();
        //正常请求使用多线程会有为空的风险，正常不用多线程就不会为空
        return Objects.requireNonNull(requestAttributes).getRequest();
    }

    /**
     * 获取响应信息
     *
     * @return javax.servlet.http.HttpServletResponse
     * <AUTHOR>
     * @date 2023/4/6 22:13
     **/
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes requestAttributes = getAttributes();
        //正常请求使用多线程会有为空的风险，正常不用多线程就不会为空
        return Objects.requireNonNull(requestAttributes).getResponse();
    }

    /**
     * 获取当前请求的ip
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/4/6 22:13
     **/
    public static String getIp() {
        return NetUtil.getIpAddress(getRequest());
    }

    /**
     * 获取servlet属性对象
     *
     * @return org.springframework.web.context.request.ServletRequestAttributes
     * <AUTHOR>
     * @date 2023/4/6 22:13
     **/
    private static ServletRequestAttributes getAttributes() {
        return (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    }
}
