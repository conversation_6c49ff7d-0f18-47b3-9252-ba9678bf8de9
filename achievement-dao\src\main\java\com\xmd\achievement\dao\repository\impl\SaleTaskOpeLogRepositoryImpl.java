package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.SaleTaskOpeLogModel;
import com.xmd.achievement.dao.mapper.SaleTaskOpeLogMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.ISaleTaskOpeLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 销售任务修改记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Service
@Slf4j
public class SaleTaskOpeLogRepositoryImpl extends ServiceImpl<SaleTaskOpeLogMapper,SaleTaskOpeLogModel> implements ISaleTaskOpeLogRepository {

@Resource
private SaleTaskOpeLogMapper saleTaskOpeLogMapper;

}