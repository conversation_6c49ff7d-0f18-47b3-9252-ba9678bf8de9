package com.xmd.achievement.web.controller;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.ISpecCombinationService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PageQueryspecCombinationListResponse;
import com.xmd.achievement.service.entity.response.QuerySpecCombinationDetailResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 规格组合
 *
 * <AUTHOR>
 * @date: 2024/12/19 11:16
 * @version: 1.0.0
 * @return {@link }
 */
@Tag(name = "SCC-规格组合接口")
@Slf4j
@RestController
@RequestMapping("/specCombination")
public class SpecCombinationController {

    @Resource
    ISpecCombinationService specCombinationService;

    @Operation(summary = "SCC-01-规格组合保存修改接口")
    @PostMapping("saveSpecCombination")
    public WebResult<Boolean> saveSpecCombination(@RequestBody @Valid SpecCombinationRequest request) {
        log.info("SCC-01-规格组合保存修改接口,请求参数:{}", JSONUtil.toJsonStr(request));
        return specCombinationService.saveSpecCombination(request);
    }

    @Operation(summary = "SCC-02-分页查询规格组合列表接口")
    @PostMapping("pageQuerySpecCombinationList")
    public WebResult<PageResponse<PageQueryspecCombinationListResponse>> pageQuerySpecCombinationList(@RequestBody @Valid PageQueryspecCombinationListRequest request) {
        log.info("SCC-02-分页查询规格组合列表接口,请求参数:{}", JSONUtil.toJsonStr(request));
        return specCombinationService.pageQuerySpecCombinationList(request);
    }

    @Operation(summary = "SCC-03-查询规格组合详情")
    @PostMapping("querySpecCombinationDetail")
    public WebResult<QuerySpecCombinationDetailResponse> querySpecCombinationDetail(@RequestBody @Valid QuerySpecCombinationDetailRequest request) {
        log.info("SCC-03-查询规格组合详情,请求参数:{}", JSONUtil.toJsonStr(request));
        return specCombinationService.querySpecCombinationDetail(request);
    }

    @Operation(summary = "SCC-04-编辑规格组合详情")
    @PostMapping("updateSpecCombination")
    public WebResult<Boolean> updateSpecCombination(@RequestBody @Valid UpdateSpecCombinationRequest request) {
        log.info("SCC-04-编辑规格组合详情,请求参数:{}", JSONUtil.toJsonStr(request));
        return specCombinationService.updateSpecCombination(request);
    }

    @Operation(summary = "SCC-05-删除规格组合")
    @PostMapping("delSpecCombination")
    public WebResult<Boolean> delSpecCombination(@RequestBody @Valid DelSpecCombinationRequest request) {
        log.info("SCC-05-删除规格组合,请求参数:{}", JSONUtil.toJsonStr(request));
        return specCombinationService.delSpecCombination(request);
    }

    @Operation(summary = "SCC-06-禁用/启用规格组合")
    @PostMapping("updateSpecCombinationStatus")
    public WebResult<Boolean> updateSpecCombinationStatus(@RequestBody @Valid UpdateSpecCombinationStatusRequest request) {
        log.info("SCC-06-禁用/启用规格组合,请求参数:{}", JSONUtil.toJsonStr(request));
        return specCombinationService.updateSpecCombinationStatus(request);
    }
}
