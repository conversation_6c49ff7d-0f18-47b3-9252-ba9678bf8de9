package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 订单简单信息
 * <AUTHOR>
 * @date 2024年11月11日 下午1:27
 *
 */
@Data
public class ProductForAchievementResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;

    /**
     * 商品ID
     */

    private String productId;

    /**
     * 商品名称
     */

    private String productName;

    /**
     * 售卖类型集合,逗号分割
     */

    private String saleType;

    /**
     * 商品一级分类ID
     */

    private Long levelOneCategoryId;

    /**
     * 商品一级分类名称
     */

    private String levelOneCategoryName;


    /**
     * 商品二级分类ID
     */
    private Long levelTwoCategoryId;

    /**
     * 商品二级分类名称
     */

    private String levelTwoCategoryName;


    /**
     * 商品三级分类ID
     */
    private Long levelThreeCategoryId;

    /**
     * 商品三级分类名称
     */

    private String levelThreeCategoryName;

    /**
     * 商品状态: 1-上架, 2-下架 3-待上架
     */

    private Integer productStatus;

}
