package com.xmd.achievement.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xmd.achievement.dao.dto.SearchAchievementDetailsDto;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业绩商品明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface IAchievementProductDetailRepository extends IService<AchievementProductDetailModel> {

    List<AchievementProductDetailModel> selectAchievementProductByAchievementIds(List<Long> achievementIdList,Integer source);

    void updateAchievementStatisticsTime(AchievementProductDetailModel model);

    AchievementProductDetailModel getAchievementProductDetailModelById(Long id);

    List<AchievementProductDetailModel> selectAchievementProductByBetweenDate(Date startDate , Date endDate, List<Integer> saleTypeList,String customerId,String productId,String productName,Integer installmentNum,Long orderId);

    void updateByOrderIdAndProductId(List<Long> orderIdList, List<Long> productIdList);

    List<AchievementProductDetailModel> selectListByDateAndName(String startDateStr, String endDateStr, String websiteName,List<Integer> saleTypeList, List<String> productIdList);

    List<AchievementProductDetailModel> listByUpdateTime(Date startTime);

    List<AchievementProductDetailModel> listByStatisticsTime(Date startTime, Date endTime);

    List<AchievementProductDetailModel> listByBusinessMonthId(Long businessMonthId);

    AchievementProductDetailModel selectByOrderProductIdAndInstallmentNum(String orderProductCode, int installmentNum,int mainSplitPerson);

    List<AchievementProductDetailModel> selectAchievementProductDetailListByDate(Date startDate , Date endDate,String employeeId);

    List<AchievementProductDetailModel> selectByOrderIdAndOrderProductId(Long orderId, List<String> orderProductId);

    void logicBatchDelete(List<Long> ids);


    List<SearchAchievementDetailsDto> searchAchievementDetails(List<String> businessMonths,
                                                                         List<String> businessIds,
                                                                         Long companyId);

    List<AchievementProductDetailModel> selectDistinctRefundOrderProduct();
    void deleteByAchievementId(List<Long> achivevementIdList);

    void updateCalculateByIdList(List<Long> refundIdList);

    List<AchievementProductDetailModel> selectAchRefundListByOrderProductId(List<String> orderProductIdList);

    List<AchievementProductDetailModel> selectByOrderIdAndOrderProductIdAndInstallmentNum(Long orderId,String orderProductCode, int installmentNum,int mainSplitPerson);

    List<AchievementProductDetailModel> selectByOrderNo(String orderNo);

    List<AchievementProductDetailModel> selectAchievement(List<Long> companyIds, List<Long> deptIds,
            List<Long> businessMonthIds, List<String> businessIds);

    List<AchievementProductDetailModel> selectAchievementProductDetailListByMonth(String currentMonth, String employeeId);
}
