package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.OperateLogModel;
import com.xmd.achievement.dao.mapper.OperateLogMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IOperateLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Service
@Slf4j
public class OperateLogRepositoryImpl extends ServiceImpl<OperateLogMapper, OperateLogModel> implements IOperateLogRepository {

    @Resource
    private OperateLogMapper operateLogMapper;
} 