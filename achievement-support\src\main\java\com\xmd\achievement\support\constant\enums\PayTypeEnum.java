package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/11:29
 * @since 1.0
 */
@Getter
public enum PayTypeEnum {
    /**
     * 全款
     */
    ALL(1, "全款"),
    /**
     * 分期
     */
    STAGES(2, "分期");

    private final Integer code;
    private final String msg;

    PayTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
