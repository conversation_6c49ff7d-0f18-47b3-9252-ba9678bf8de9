package com.xmd.achievement.util.http;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * okHttp工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/1/24 9:20 上午
 */
@Slf4j
public class OKHttpUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(OKHttpUtil.class);

    //读取超时，单位秒
    private static final int READ_TIME_OUT = 30;
    //写入超时，单位秒
    private static final int WRITE_TIME_OUT = 30;
    //连接超时，单位秒
    private static final int CONNECT_TIME_OUT = 30;

    private static final String GET_REQUEST = "GET";

    private static final String POST_REQUEST = "POST";

    private static final MediaType defaultMediaType = MediaType.parse("application/json");
    private static final String contentTypeForm = "application/x-www-form-urlencoded; charset=utf-8";
    private static final String CONTENT_TYPE = "Content-type";

    public final static String TOKEN_NAME = "authorization";

    private static volatile OkHttpClient client;


    private OKHttpUtil() {
    }

    //获取HTTP客户端
    private static OkHttpClient getClient() {
        if (client == null) {
            synchronized (OKHttpUtil.class) {
                if (client == null) {
                    client = new OkHttpClient.Builder()
                            .readTimeout(READ_TIME_OUT, TimeUnit.SECONDS)
                            .connectTimeout(CONNECT_TIME_OUT, TimeUnit.SECONDS)
                            .writeTimeout(WRITE_TIME_OUT, TimeUnit.SECONDS)
                            .build();
                }
            }
        }
        return client;
    }

    public static HttpResult<String> postSyncForm(OkHttpClient client, String url, Map<String, String> param, Map<String, String> header) {
        try {
            if (null == client) {
                client = getClient();
            }
            Long start = System.currentTimeMillis();
            Request.Builder requestBuilder = new Request.Builder();
            //处理请求头
            if (CollectionUtil.isEmpty(header)) {
                header = new HashMap<>();
            }
            header.put(CONTENT_TYPE, contentTypeForm);
            requestBuilder.headers(buildHeaders(header));
            //处理请求参数
            FormBody.Builder builder = new FormBody.Builder();
            if (!CollectionUtil.isEmpty(param)) {
                for (Map.Entry<String, String> entry : param.entrySet()) {
                    builder.add(entry.getKey(), entry.getValue());
                }
            }
            FormBody formBody = builder.build();
            requestBuilder.post(formBody);
            Response response = client.newCall(requestBuilder.url(url).build()).execute();
            Long duration = System.currentTimeMillis() - start;
            LOGGER.warn("OK-HTTP请求时长调用地址：{}，入参：{}，请求头：{}，调用时长----：{}",
                    url, JSON.toJSONString(param), JSON.toJSONString(header), duration);
            return handleResult(response);
        } catch (IOException e) {
            LOGGER.error("OK-HTTP请求报错url：{}，入参：{}，请求头：{}错误信息：",
                    url, JSON.toJSONString(param), JSON.toJSONString(header), e);
            return HttpResult.error(HttpResultEnum.REQUEST_ERROR.getCode(), e.getMessage());
        }
    }

    public static HttpResult<String> getSyncForm(String url, Map<String, String> param, Map<String, String> headers) {
        return getSync(url, param, headers);
    }

    public static HttpResult<String> getSync(String url) {
        return getSync(url, null, null);
    }

    public static HttpResult<String> getSync(String url, Map<String, String> param) {
        Map<String, String> header = new HashMap<>();
        return getSync(url, param, header);
    }

    public static HttpResult<String> getSyncWithToken(String url, Map<String, String> param, String token) {
        Map<String, String> header = new HashMap<>();
        header.put(TOKEN_NAME, token);
        return getSync(url, param, header);
    }

    public static HttpResult<String> getSync(String url, String param) {
        return getSync(url, JSON.parseObject(param, HashMap.class), null);
    }

    public static HttpResult<String> getSyncWithHeader(String url, Map<String, String> header) {
        return getSync(url, null, header);
    }

    public static HttpResult<String> getSync(String url, Map<String, String> param, Map<String, String> header) {
        return syncRequest(url, param, header, GET_REQUEST);
    }

    public static HttpResult<String> postSync(String url) {
        return syncRequest(url, null, null, POST_REQUEST);
    }

    public static HttpResult<String> postSync(String url, Map<String, String> param) {
        return syncRequest(url, param, null, POST_REQUEST);
    }

    public static HttpResult<String> postSync(String url, String param) {
        return syncRequest(url, JSON.parseObject(param, HashMap.class), null, POST_REQUEST);
    }

    public static HttpResult<String> postSyncWithToken(String url, String param, String token) {
        Map<String, String> header = new HashMap<>();
        header.put(TOKEN_NAME, token);
        return syncRequest(url, JSON.parseObject(param, HashMap.class), header, POST_REQUEST);
    }

    public static HttpResult<String> postSyncWithHeader(String url, Map<String, String> header) {
        return syncRequest(url, null, header, POST_REQUEST);
    }

    public static HttpResult<String> postSync(String url, Map<String, String> param, Map<String, String> header) {
        return syncRequest(url, param, header, POST_REQUEST);
    }

    public static HttpResult<String> postSync(String url, Object body, Map<String, String> header) {
        return syncRequest(url, body, header, POST_REQUEST);
    }

    private static HttpResult<String> syncRequest(String url, Object param, Map<String, String> header, String method) {
        try {
            Long start = System.currentTimeMillis();
            Request request = buildRequest(url, header, param, method);
            Response response = getClient().newCall(request).execute();
            Long duration = System.currentTimeMillis() - start;
            //LOGGER.warn("OK-HTTP请求时长调用地址：{}，入参：{}，请求头：{}，方法：{}，调用时长----：{}, request:{}",
            //        url, JSON.toJSONString(param), JSON.toJSONString(header), method, duration, JSON.toJSONString(request));
            return handleResult(response);
        } catch (IOException e) {
            LOGGER.error("OK-HTTP请求报错url：{}，入参：{}，请求头：{}，方法：{}，错误信息：",
                    url, JSON.toJSONString(param), JSON.toJSONString(header), method, e);
            return HttpResult.error(HttpResultEnum.REQUEST_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 构建请求对象
     *
     * @param url
     * @param headerMap
     * @param param
     * @param requestMethod
     * @return
     */
    private static Request buildRequest(String url, Map<String, String> headerMap, Object param, String requestMethod) {
        Request.Builder requestBuilder = new Request.Builder();
        //处理请求头
        if (MapUtil.isNotEmpty(headerMap)) {
            requestBuilder.headers(buildHeaders(headerMap));
        }
        //处理请求参数
        if (Objects.equals(requestMethod, GET_REQUEST)) {
            if (param instanceof Map) {
                url += buildUrlPram((Map) param);
            }
            requestBuilder.get();
        } else if (Objects.equals(requestMethod, POST_REQUEST)) {
            requestBuilder.post(RequestBody.create(JSON.toJSONString(param), defaultMediaType));
        }
        return requestBuilder.url(url).build();
    }

    private static HttpResult<String> syncRequest(String url, Map<String, String> param, Map<String, String> header, String method) {
        try {
            Long start = System.currentTimeMillis();
            Response response = getClient().newCall(buildRequest(url, header, param, method)).execute();
            Long duration = System.currentTimeMillis() - start;
            LOGGER.warn("OK-HTTP请求时长调用地址：{}，入参：{}，请求头：{}，方法：{}，调用时长----：{}",
                    url, JSON.toJSONString(param), JSON.toJSONString(header), method, duration);
            return handleResult(response);
        } catch (IOException e) {
            LOGGER.error("OK-HTTP请求报错url：{}，入参：{}，请求头：{}，方法：{}，错误信息：",
                    url, JSON.toJSONString(param), JSON.toJSONString(header), method, e);
            return HttpResult.error(HttpResultEnum.REQUEST_ERROR.getCode(), e.getMessage());
        }
    }

    public static void getAsync(String url, Map<String, String> header, Callback callback) {
        asyncRequest(url, null, header, GET_REQUEST, callback);
    }

    public static void getAsync(String url, Callback callback) {
        asyncRequest(url, null, null, GET_REQUEST, callback);
    }

    public static void getAsync(String url) {
        asyncRequest(url, null, null, GET_REQUEST, null);
    }

    public static void getAsyncWithHeader(String url, Map<String, String> header) {
        asyncRequest(url, null, header, GET_REQUEST, null);
    }

    public static void getAsync(String url, Map<String, String> param) {
        asyncRequest(url, param, null, GET_REQUEST, null);
    }

    public static void getAsync(String url, String param) {
        asyncRequest(url, JSON.parseObject(param, HashMap.class), null, GET_REQUEST, null);
    }

    public static void getAsync(String url, Map<String, String> param, Map<String, String> header) {
        asyncRequest(url, param, header, GET_REQUEST, null);
    }

    public static void getAsync(String url, Map<String, String> param, Map<String, String> header, Callback callback) {
        asyncRequest(url, param, header, GET_REQUEST, callback);
    }

    public static void postAsync(String url, Map<String, String> param, Callback callback) {
        asyncRequest(url, param, null, POST_REQUEST, callback);
    }

    public static void postAsyncWithHeader(String url, Map<String, String> header, Callback callback) {
        asyncRequest(url, null, header, POST_REQUEST, callback);
    }

    public static void postAsync(String url) {
        asyncRequest(url, null, null, POST_REQUEST, new OKHttpCallback());
    }

    public static void postAsyncWithHeader(String url, Map<String, String> header) {
        asyncRequest(url, null, header, POST_REQUEST, new OKHttpCallback());
    }

    public static void postAsync(String url, Map<String, String> param) {
        asyncRequest(url, param, null, POST_REQUEST, new OKHttpCallback());
    }

    public static void postAsync(String url, String param) {
        asyncRequest(url, JSON.parseObject(param, HashMap.class), null, POST_REQUEST, new OKHttpCallback());
    }

    public static void postAsync(String url, Map<String, String> param, Map<String, String> header) {
        asyncRequest(url, param, header, POST_REQUEST, new OKHttpCallback());
    }

    public static void postAsync(String url, Map<String, String> param, Map<String, String> header, Callback callback) {
        asyncRequest(url, param, header, POST_REQUEST, callback);
    }

    public static void postAsync(String url, Object param) {
        asyncPostRequest(url, param, null, new OKHttpCallback());
    }

    private static void asyncRequest(String url, Map<String, String> param, Map<String, String> header, String method, Callback callback) {
        try {
            Long start = System.currentTimeMillis();
            getClient().newCall(buildRequest(url, header, param, method)).enqueue(callback == null ? new OKHttpCallback() : callback);
            Long duration = System.currentTimeMillis() - start;
            LOGGER.warn("HTTP请求时长调用地址={},入参={},请求头={},方法={},调用时长={}",
                    url, JSON.toJSONString(param), JSON.toJSONString(header), method, duration);
        } catch (Exception e) {
            LOGGER.error("HTTP请求报错-调用地址={},入参={},请求头={},方法={},错误信息: ",
                    url, JSON.toJSONString(param), JSON.toJSONString(header), method, e);
        }
    }

    private static void asyncPostRequest(String url, Object param, Map<String, String> header, Callback callback) {
        try {
            Long start = System.currentTimeMillis();
            getClient().newCall(buildPostRequest(url, header, param)).enqueue(callback == null ? new OKHttpCallback() : callback);
            Long duration = System.currentTimeMillis() - start;
            LOGGER.warn("HTTP post请求时长调用地址={},入参={},请求头={},调用时长={}",
                    url, JSON.toJSONString(param), JSON.toJSONString(header), duration);
        } catch (Exception e) {
            LOGGER.error("HTTP post请求报错-调用地址={},入参={},请求头={},错误信息: ",
                    url, JSON.toJSONString(param), JSON.toJSONString(header), e);
        }
    }

    private static HttpResult<String> handleResult(Response response) throws IOException {
        try {
            if (response.isSuccessful()) {
                HttpResult<String> result = HttpResult.success();
                result.setData(response.body() == null ? null : response.body().string());
                return result;
            } else {
                return HttpResult.error(response.code(), response.message());
            }
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (Exception e) {
                log.warn("close http err,{}", e.toString());
            }
        }

    }

    /**
     * 构建请求对象
     *
     * @param url
     * @param headerMap
     * @param param
     * @param requestMethod
     * @return
     */
    private static Request buildRequest(String url, Map<String, String> headerMap, Map<String, String> param, String requestMethod) {
        Request.Builder requestBuilder = new Request.Builder();
        //处理请求头
        if (Objects.nonNull(headerMap)) {
            requestBuilder.headers(buildHeaders(headerMap));
        }
        //处理请求参数
        if (Objects.equals(requestMethod, GET_REQUEST)) {
            if (Objects.nonNull(param)) {
                url += buildUrlPram(param);
            }
            requestBuilder.get();
        }
        if (Objects.equals(requestMethod, POST_REQUEST)) {
            if (Objects.nonNull(param)) {
                requestBuilder.post(RequestBody.create(JSON.toJSONString(param), defaultMediaType));
            } else {
                requestBuilder.post(new FormBody.Builder().build());
            }
        }
        return requestBuilder.url(url).build();
    }


    /**
     * 构建Post请求对象
     *
     * @param url
     * @param headerMap
     * @param param
     * @return
     */
    private static Request buildPostRequest(String url, Map<String, String> headerMap, Object param) {
        Request.Builder requestBuilder = new Request.Builder();
        //处理请求头
        if (Objects.nonNull(headerMap)) {
            requestBuilder.headers(buildHeaders(headerMap));
        }
        //处理请求参数
        requestBuilder.post(RequestBody.create(JSON.toJSONString(param), defaultMediaType));
        return requestBuilder.url(url).build();
    }


    /**
     * 构建Get请求参数
     *
     * @param param
     * @return
     */
    private static String buildUrlPram(Map<String, String> param) {
        StringBuilder sb = new StringBuilder();
        sb.append("?");
        int first = 1;
        for (Map.Entry<String, String> entry : param.entrySet()) {
            if (first != 1) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
            first++;
        }
        return sb.toString();
    }

    /**
     * 构建请求头
     *
     * @param headerMap
     * @return
     */
    private static Headers buildHeaders(Map<String, String> headerMap) {
        Headers.Builder headersbuilder = new Headers.Builder();
        if (Objects.nonNull(headerMap)) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                headersbuilder.add(entry.getKey(), entry.getValue());
            }
        }
        return headersbuilder.build();
    }

    private static class OKHttpCallback implements Callback {

        @Override
        public void onFailure(Call call, IOException e) {
            if (!call.isExecuted()) {
                call.cancel();
            }
            Request request = call.request();
            RequestBody body = request.body();
            Buffer buffer = new Buffer();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            String content = null;
            try {
                body.writeTo(buffer);
                if (buffer.size() > 0) {
                    buffer.copyTo(bos);
                    content = bos.toString();
                }
            } catch (Exception e1) {
            }
            LOGGER.error("HTTP请求报错，路径：{}，请求体：{}，错误信息：", request.url(), content, e);
        }

        @Override
        public void onResponse(Call call, Response response) throws IOException {
            LOGGER.info("请求成功：路径:{}", call.request().url());
        }
    }

}
