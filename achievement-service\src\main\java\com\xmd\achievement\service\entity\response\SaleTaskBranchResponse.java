package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 未提交任务的分司
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class SaleTaskBranchResponse implements Serializable {

    private static final long serialVersionUID = 375067679244086447L;

    @Schema(description = "分司名称")
    private String companyName;

    @Schema(description = "分司下的部门集合")
    private List<QuerySaleTaskListResponse> records;
}
