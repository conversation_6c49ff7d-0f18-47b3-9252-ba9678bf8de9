<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.AchievementProductDetailSelectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.AchievementProductDetailSelectModel">
        <id column="id" property="id" />
        <result column="achievement_id" property="achievementId" />
        <result column="business_month_id" property="businessMonthId" />
        <result column="business_month" property="businessMonth" />
        <result column="order_product_id" property="orderProductId" />
        <result column="serve_no" property="serveNo" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="product_type" property="productType" />
        <result column="site_flag" property="siteFlag" />
        <result column="sale_type" property="saleType" />
        <result column="status" property="status" />
        <result column="order_id" property="orderId" />
        <result column="order_no" property="orderNo" />
        <result column="order_source" property="orderSource" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="customer_type" property="customerType" />
        <result column="province_code" property="provinceCode" />
        <result column="province_name" property="provinceName" />
        <result column="city_code" property="cityCode" />
        <result column="city_name" property="cityName" />
        <result column="district_code" property="districtCode" />
        <result column="district_name" property="districtName" />
        <result column="customer_region" property="customerRegion" />
        <result column="contract_no" property="contractNo" />
        <result column="business_id" property="businessId" />
        <result column="business_representative" property="businessRepresentative" />
        <result column="main_split_person" property="mainSplitPerson" />
        <result column="company_id" property="companyId" />
        <result column="region_id" property="regionId" />
        <result column="company" property="company" />
        <result column="division_id" property="divisionId" />
        <result column="division" property="division" />
        <result column="dept_id" property="deptId" />
        <result column="department" property="department" />
        <result column="standard_price" property="standardPrice" />
        <result column="payable_amount" property="payableAmount" />
        <result column="paid_amount" property="paidAmount" />
        <result column="discount_rate" property="discountRate" />
        <result column="delivery_method" property="deliveryMethod" />
        <result column="order_type" property="orderType" />
        <result column="signed_time" property="signedTime" />
        <result column="payment_time" property="paymentTime" />
        <result column="first_year_quote" property="firstYearQuote" />
        <result column="first_year_revenue" property="firstYearRevenue" />
        <result column="renewal_quote" property="renewalQuote" />
        <result column="renewal_revenue" property="renewalRevenue" />
        <result column="net_cash" property="netCash" />
        <result column="agent_commission_achievement" property="agentCommissionAchievement" />
        <result column="agent_actual_commission" property="agentActualCommission" />
        <result column="agent_deferred_commission" property="agentDeferredCommission" />
        <result column="dept_commission" property="deptCommission" />
        <result column="div_commission" property="divCommission" />
        <result column="branch_commission" property="branchCommission" />
        <result column="serve_finish_time" property="serveFinishTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="statistics_time" property="statisticsTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="achievement_source" property="achievementSource" />
        <result column="third_achievement_id" property="thirdAchievementId" />
        <result column="pay_type" property="payType" />
        <result column="pay_cycle_count" property="payCycleCount" />
        <result column="current_cycle_count" property="currentCycleCount" />
        <result column="data_change_type" property="dataChangeType" />
        <result column="remark_history" property="remarkHistory" />
        <result column="latest_remark" property="latestRemark" />
        <result column="calculate_all" property="calculateAll" />
        <result column="installment_num" property="installmentNum" />
        <result column="displayed" property="displayed" />
        <result column="is_saas" property="isSaas"/>
        <result column="aftersale_order_id" property="aftersaleOrderId" />
        <result column="aftersale_order_no" property="aftersaleOrderNo" />
        <result column="aftersale_contract_no" property="aftersaleContractNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, achievement_id, business_month_id, business_month, order_product_id, serve_no, product_id, product_name,
        product_type, site_flag, sale_type, `status`, order_id, order_no, order_source, customer_id, customer_name,
        customer_type, province_code, province_name, city_code, city_name, district_code, district_name, customer_region,
        contract_no, business_id, business_representative, main_split_person, company_id, region_id, company, division_id,
        division, dept_id, department, standard_price, payable_amount, paid_amount, discount_rate, delivery_method, order_type,
        signed_time, payment_time, first_year_quote, first_year_revenue, renewal_quote, renewal_revenue, net_cash,
        agent_commission_achievement, agent_actual_commission, agent_deferred_commission, dept_commission, div_commission,
        branch_commission, serve_finish_time, delete_flag, create_time, statistics_time, create_user_id, create_user_name,
        update_time, update_user_id, update_user_name, achievement_source, third_achievement_id, pay_type, pay_cycle_count,
        current_cycle_count, data_change_type, remark_history, latest_remark, calculate_all, installment_num, displayed,is_saas,
        aftersale_order_id, aftersale_order_no, aftersale_contract_no
    </sql>

    <insert id="batchInsertOrUpdate">
        INSERT INTO achievement_product_detail_select (
        id,
        achievement_id,
        business_month_id,
        business_month,
        order_product_id,
        serve_no,
        product_id,
        product_name,
        product_type,
        site_flag,
        sale_type,
        status,
        order_id,
        order_no,
        order_source,
        customer_id,
        customer_name,
        customer_type,
        province_code,
        province_name,
        city_code,
        city_name,
        district_code,
        district_name,
        customer_region,
        contract_no,
        business_id,
        business_representative,
        main_split_person,
        company_id,
        region_id,
        company,
        division_id,
        division,
        dept_id,
        department,
        standard_price,
        payable_amount,
        paid_amount,
        discount_rate,
        delivery_method,
        order_type,
        signed_time,
        payment_time,
        first_year_quote,
        first_year_revenue,
        renewal_quote,
        renewal_revenue,
        net_cash,
        agent_commission_achievement,
        agent_actual_commission,
        agent_deferred_commission,
        dept_commission,
        div_commission,
        branch_commission,
        serve_finish_time,
        delete_flag,
        create_time,
        statistics_time,
        create_user_id,
        create_user_name,
        update_time,
        update_user_id,
        update_user_name,
        achievement_source,
        third_achievement_id,
        pay_type,
        pay_cycle_count,
        current_cycle_count,
        data_change_type,
        remark_history,
        latest_remark,
        calculate_all,
        installment_num,
        displayed,
        is_saas,
        aftersale_order_id,
        aftersale_order_no,
        aftersale_contract_no
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.achievementId},
            #{item.businessMonthId},
            #{item.businessMonth},
            #{item.orderProductId},
            #{item.serveNo},
            #{item.productId},
            #{item.productName},
            #{item.productType},
            #{item.siteFlag},
            #{item.saleType},
            #{item.status},
            #{item.orderId},
            #{item.orderNo},
            #{item.orderSource},
            #{item.customerId},
            #{item.customerName},
            #{item.customerType},
            #{item.provinceCode},
            #{item.provinceName},
            #{item.cityCode},
            #{item.cityName},
            #{item.districtCode},
            #{item.districtName},
            #{item.customerRegion},
            #{item.contractNo},
            #{item.businessId},
            #{item.businessRepresentative},
            #{item.mainSplitPerson},
            #{item.companyId},
            #{item.regionId},
            #{item.company},
            #{item.divisionId},
            #{item.division},
            #{item.deptId},
            #{item.department},
            #{item.standardPrice},
            #{item.payableAmount},
            #{item.paidAmount},
            #{item.discountRate},
            #{item.deliveryMethod},
            #{item.orderType},
            #{item.signedTime},
            #{item.paymentTime},
            #{item.firstYearQuote},
            #{item.firstYearRevenue},
            #{item.renewalQuote},
            #{item.renewalRevenue},
            #{item.netCash},
            #{item.agentCommissionAchievement},
            #{item.agentActualCommission},
            #{item.agentDeferredCommission},
            #{item.deptCommission},
            #{item.divCommission},
            #{item.branchCommission},
            #{item.serveFinishTime},
            #{item.deleteFlag},
            #{item.createTime},
            #{item.statisticsTime},
            #{item.createUserId},
            #{item.createUserName},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.updateUserName},
            #{item.achievementSource},
            #{item.thirdAchievementId},
            #{item.payType},
            #{item.payCycleCount},
            #{item.currentCycleCount},
            #{item.dataChangeType},
            #{item.remarkHistory},
            #{item.latestRemark},
            #{item.calculateAll},
            #{item.installmentNum},
            #{item.displayed},
            #{item.isSaas},
            #{item.aftersaleOrderId},
            #{item.aftersaleOrderNo},
            #{item.aftersaleContractNo}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        achievement_id = VALUES(achievement_id),
        business_month_id = VALUES(business_month_id),
        business_month = VALUES(business_month),
        order_product_id = VALUES(order_product_id),
        serve_no = VALUES(serve_no),
        product_id = VALUES(product_id),
        product_name = VALUES(product_name),
        product_type = VALUES(product_type),
        site_flag = VALUES(site_flag),
        sale_type = VALUES(sale_type),
        status = VALUES(status),
        order_id = VALUES(order_id),
        order_no = VALUES(order_no),
        order_source = VALUES(order_source),
        customer_id = VALUES(customer_id),
        customer_name = VALUES(customer_name),
        customer_type = VALUES(customer_type),
        province_code = VALUES(province_code),
        province_name = VALUES(province_name),
        city_code = VALUES(city_code),
        city_name = VALUES(city_name),
        district_code = VALUES(district_code),
        district_name = VALUES(district_name),
        customer_region = VALUES(customer_region),
        contract_no = VALUES(contract_no),
        business_id = VALUES(business_id),
        business_representative = VALUES(business_representative),
        main_split_person = VALUES(main_split_person),
        company_id = VALUES(company_id),
        region_id = VALUES(region_id),
        company = VALUES(company),
        division_id = VALUES(division_id),
        division = VALUES(division),
        dept_id = VALUES(dept_id),
        department = VALUES(department),
        standard_price = VALUES(standard_price),
        payable_amount = VALUES(payable_amount),
        paid_amount = VALUES(paid_amount),
        discount_rate = VALUES(discount_rate),
        delivery_method = VALUES(delivery_method),
        order_type = VALUES(order_type),
        signed_time = VALUES(signed_time),
        payment_time = VALUES(payment_time),
        first_year_quote = VALUES(first_year_quote),
        first_year_revenue = VALUES(first_year_revenue),
        renewal_quote = VALUES(renewal_quote),
        renewal_revenue = VALUES(renewal_revenue),
        net_cash = VALUES(net_cash),
        agent_commission_achievement = VALUES(agent_commission_achievement),
        agent_actual_commission = VALUES(agent_actual_commission),
        agent_deferred_commission = VALUES(agent_deferred_commission),
        dept_commission = VALUES(dept_commission),
        div_commission = VALUES(div_commission),
        branch_commission = VALUES(branch_commission),
        serve_finish_time = VALUES(serve_finish_time),
        delete_flag = VALUES(delete_flag),
        create_time = VALUES(create_time),
        statistics_time = VALUES(statistics_time),
        create_user_id = VALUES(create_user_id),
        create_user_name = VALUES(create_user_name),
        update_time = VALUES(update_time),
        update_user_id = VALUES(update_user_id),
        update_user_name = VALUES(update_user_name),
        achievement_source = VALUES(achievement_source),
        third_achievement_id = VALUES(third_achievement_id),
        pay_type = VALUES(pay_type),
        pay_cycle_count = VALUES(pay_cycle_count),
        current_cycle_count = VALUES(current_cycle_count),
        data_change_type = VALUES(data_change_type),
        remark_history = VALUES(remark_history),
        latest_remark = VALUES(latest_remark),
        calculate_all = VALUES(calculate_all),
        installment_num = VALUES(installment_num),
        displayed = VALUES(displayed),
        is_saas = VALUES(is_saas),
        aftersale_order_id = VALUES(aftersale_order_id),
        aftersale_order_no = VALUES(aftersale_order_no),
        aftersale_contract_no = VALUES(aftersale_contract_no)
    </insert>

</mapper>
