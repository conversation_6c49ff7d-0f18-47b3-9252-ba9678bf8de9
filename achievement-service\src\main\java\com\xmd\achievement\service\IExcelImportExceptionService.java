package com.xmd.achievement.service;

import com.xmd.achievement.service.entity.dto.ImportExcelExceptionDTO;
import com.xmd.achievement.support.constant.enums.ExcelImportExceptionEnum;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 导入excel错误日志记录
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface IExcelImportExceptionService {

    /**
     * 导入excel错误日志信息
     * @param excelExceptionDTOList
     * @param typeEnum
     * @since 1.0
     */
    void saveExcelException(List<ImportExcelExceptionDTO> excelExceptionDTOList, ExcelImportExceptionEnum typeEnum);

    void exportExcel(String typeCode, HttpServletResponse response) throws IOException;
}
