package com.xmd.achievement.service.entity.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/15/09:14
 * @since 1.0
 */
@Data
public class ThirdPerformanceRequest implements Serializable {

    @Schema(description = "业绩流水ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotBlank(message = "thirdId不能为空")
    private String thirdId;

    @Schema(description = "商务月ID", example = "202310")
    private String businessMonthId;

    @Schema(description = "商务月", example = "2023年10月")
    private String businessMonth;

    @Schema(description = "订单明细编号", example = "ORD123456")
    @NotBlank(message = "订单明细编号不能为空")
    private String orderDetailNo;

    @Schema(description = "服务编号", example = "SER123456")
    private String serveNo;

    @Schema(description = "商品ID", example = "123456")
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @Schema(description = "商品名称", example = "商品A")
    @NotBlank(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "商品类型", example = "1")
    @NotNull(message = "商品类型不能为空")
    private Integer productType;

    @Schema(description = "业务类型：1新开,2续费,20转款,4另购充值,3310域名转入,3扩容,7升级(ZT升级ZT),5高价赎回,8zadd升级,3115Z+升级ZTSZT,3116Z+升级ZTSZM,6转入,3117NZ+升级ZTSZT,3118\tNZ+升级ZTSZM,169大把推续费升级,12DSP升级,18退款,88升级,188门户扩展升级", example = "1")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @Schema(description = "是否是网站 0=否 1=是", example = "1")
    @NotNull(message = "是否是网站不能为空")
    private Integer webInfo;

    @Schema(description = "业绩状态 0=有效 1=失效 2=已退 3=已完成", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业绩状态不能为空")
    private Integer state;

    @Schema(description = "订单ID", example = "ORD123456")
    private String orderId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORD123456")
    @NotBlank(message = "订单编号不能为空")
    private String orderRecordCode;

    @Schema(description = "订单来源：1BOSS,18新平台线上,19新平台线下,20新平台电子签单,22新云市场", example = "1")
    private Integer dataSource;

    @Schema(description = "客户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "CUST123456")
    @NotBlank(message = "客户ID不能为空")
    private String custId;

    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "客户A")
    @NotBlank(message = "客户名称不能为空")
    private String custName;

    @Schema(description = "客户类型 1老 2新 3非新老", example = "1")
    @NotNull(message = "客户类型不能为空")
    private Integer custType;

    @Schema(description = "客户所在省", example = "广东省")
    private String provinceCode;

    @Schema(description = "客户所在省名字", example = "广东省")
    private String provinceName;

    @Schema(description = "客户所在城市ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "CITY123456")
    private String custCity;

    @Schema(description = "客户所在城市名字", example = "广州市")
    private String cityName;

    @Schema(description = "客户所在县区ID", example = "DIST123456")
    private String districtCode;

    @Schema(description = "客户所在县区名字", example = "天河区")
    private String districtName;

    @Schema(description = "客户所在区", example = "天河区")
    private String customerRegion;

    @Schema(description = "合同编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "CON123456")
    @NotBlank(message = "合同编号不能为空")
    private String contractCode;

    @Schema(description = "商务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "SALER123456")
    @NotBlank(message = "商务ID不能为空")
    private String salerId;

    @Schema(description = "商务代表", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotBlank(message = "商务代表不能为空")
    private String salerName;

    @Schema(description = "主分单人 0副 1主", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "主分单人不能为空")
    private Integer shareType;

    @Schema(description = "公司ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "ORG123456")
    private String orgId;

    @Schema(description = "所属区域", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "AREA123456")
    private String areaId;

    @Schema(description = "公司", example = "公司A")
    private String company;

    @Schema(description = "事业部ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "BU123456")
    private String buId;

    @Schema(description = "事业部", example = "事业部A")
    private String division;

    @Schema(description = "部门ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "DEPT123456")
    private String deptId;

    @Schema(description = "部门", example = "部门A")
    private String department;

    @Schema(description = "标准价", requiredMode = Schema.RequiredMode.REQUIRED, example = "30.00")
    @NotNull(message = "标准价不能为空")
    private Double currentPrice;

    @Schema(description = "应付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000.00")
    @NotNull(message = "应付金额不能为空")
    private Double singingAmount;

    @Schema(description = "实付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "900.00")
    @NotNull(message = "实付金额不能为空")
    private Double actualAccount;

    @Schema(description = "折扣比例", example = "0.1")
    private Double discountAccount;

    @Schema(description = "交付方式: 1-软件交付, 2-服务交付", example = "1")
    private Integer deliveryMethod;

    @Schema(description = "订单类型：1=普通订单，2=折扣订单", example = "1")
    private Integer orderType;

    @Schema(description = "签单时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-10-01 12:00:00")
    @NotNull(message = "签单时间不能为空")
    private String singingDate;

    @Schema(description = "付款时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-10-01 12:00:00")
    @NotNull(message = "付款时间不能为空")
    private String toAccountDate;

    @Schema(description = "首年报价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000.00")
    @NotNull(message = "首年报价不能为空")
    private Double firstStandardAccount;

    @Schema(description = "首年到账金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "900.00")
    @NotNull(message = "首年到账金额不能为空")
    private Double firstActualAccount;

    @Schema(description = "续费报价", requiredMode = Schema.RequiredMode.REQUIRED, example = "800.00")
    @NotNull(message = "续费报价不能为空")
    private Double renewStandardAccount;

    @Schema(description = "续费到账金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "700.00")
    @NotNull(message = "续费到账金额不能为空")
    private Double renewActualAccount;

    @Schema(description = "净现金", requiredMode = Schema.RequiredMode.REQUIRED, example = "600.00")
    @NotNull(message = "净现金不能为空")
    private Double netCashAccount;

    @Schema(description = "商代提成业绩", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    @NotNull(message = "商代提成业绩不能为空")
    private Double saleHiredMoney;

    @Schema(description = "商代实发提成业绩", requiredMode = Schema.RequiredMode.REQUIRED, example = "90.00")
    @NotNull(message = "商代实发提成业绩不能为空")
    private Double relaySaleHiredMoney;

    @Schema(description = "商代缓发提成业绩", requiredMode = Schema.RequiredMode.REQUIRED, example = "10.00")
    @NotNull(message = "商代缓发提成业绩不能为空")
    private Double delaySaleHiredMoney;

    @Schema(description = "部门提成业绩", requiredMode = Schema.RequiredMode.REQUIRED, example = "50.00")
    @NotNull(message = "部门提成业绩不能为空")
    private Double managerHiredMoney;

    @Schema(description = "分司提成业绩", requiredMode = Schema.RequiredMode.REQUIRED, example = "20.00")
    @NotNull(message = "分司提成业绩不能为空")
    private Double subManagerHiredMoney;

    @Schema(description = "服务完成时间", example = "2023-10-01 12:00:00")
    private String serveFinishTime;

    @Schema(description = "删除标记: 0正常数据 1异常数据  3删除数据", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "删除标记不能为空")
    private Integer dataState;

    @Schema(description = "业绩生成时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-10-01 12:00:00")
    @NotNull(message = "业绩生成时间不能为空")
    private String dbInsertTime;

    @Schema(description = "创建人ID", example = "USER123456")
    private String creater;

    @Schema(description = "创建人名称", example = "张三")
    private String createUserName;

    @Schema(description = "更新时间", example = "2023-10-01 12:00:00")
    private String dbUpdateTime;

    @Schema(description = "更新人ID", example = "USER123456")
    private String updater;

    @Schema(description = "更新人名称", example = "李四")
    private String updateUserName;

    @Schema(description = "是否展示", example = " 0 是，1 否")
    private Integer displayed;
}
