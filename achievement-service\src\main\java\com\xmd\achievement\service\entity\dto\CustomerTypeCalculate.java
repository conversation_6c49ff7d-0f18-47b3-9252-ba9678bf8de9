package com.xmd.achievement.service.entity.dto;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.CustomerType;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import lombok.Data;
import lombok.NonNull;

import java.time.LocalDate;
import java.util.List;

/**
 * 客户类型计算
 *
 * <AUTHOR>
 * @date: 2024/12/18 10:21
 */
@Data
public class CustomerTypeCalculate {
    /**
     * 客户订单集合
     */
    @NonNull
    private List<AchievementProductDetailModel> orders;


    private double calculateTotalPaidAmountLast90Days() {
        if (CollectionUtils.isEmpty(orders)) {
            return NumberConstants.DOUBLE_VALUE_0;
        }
        return orders.stream()
                .filter(achievement -> achievement.getSaleType().equals(SaleTypeEnum.NEW_OPEN.getType()) && achievement.getPaymentTime().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate().isAfter(LocalDate.now().minusDays(90)))
                .mapToDouble(a -> a.getPaidAmount().doubleValue())
                .sum();
    }

    public CustomerType calculateCustomerType() {
        double calculateTotalPaidAmountLast90Days = calculateTotalPaidAmountLast90Days();
        if (calculateTotalPaidAmountLast90Days < 3000) {
            return CustomerType.NON_EXISTENT;
        } else if (calculateTotalPaidAmountLast90Days >= 3000) {
            return CustomerType.NEW;
        } else {
            return CustomerType.OLD;
        }
    }
}
