package com.xmd.achievement.web.config.ppk;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ppk配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/9 11:53 上午
 */
@Data
@Component
@ConfigurationProperties(prefix = "ppk")
public class PpkConfiguration {

    /**
     * 公私钥对管理
     */
    private Test test = new Test();

    /**
     * /**
     * 公私钥对管理
     */
    @Data
    public class Test {
        /**
         * 持有公钥
         */
        private String publicKey;
        /**
         * 持有私钥
         */
        private String privateKey;

    }


}
