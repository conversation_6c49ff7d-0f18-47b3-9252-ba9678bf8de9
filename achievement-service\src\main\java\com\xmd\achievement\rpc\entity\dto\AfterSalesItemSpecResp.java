package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 售后商品规格明细
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class AfterSalesItemSpecResp {

    /**
     * 商品规格ID
     */
    private Long orderProductSpecId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品规格id
     */
    private Long productSpecId;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格明细编号
     */
    private String orderProductSpecCode;

    /**
     * 标准价
     */
    private BigDecimal basePrice;

    /**
     * 实付金额
     */
    private BigDecimal paidAmount;

    /**
     * 违约金成本
     */
    private BigDecimal penaltyAmount;

    /**
     * 赠品成本
     */
    private BigDecimal giftAmount;

    /**
     * 商品成本
     */
    private BigDecimal costAmount;

    /**
     * 税金成本
     */
    private BigDecimal taxAmount;

    /**
     * 扣除总成本
     */
    private BigDecimal deductCostAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

}

