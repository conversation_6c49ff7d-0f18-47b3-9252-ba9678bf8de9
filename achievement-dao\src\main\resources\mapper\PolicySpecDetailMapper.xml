<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.PolicySpecDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.PolicySpecDetailModel">
        <id column="id" property="id" />
        <result column="policy_spec_detail_id" property="policySpecDetailId" />
        <result column="policy_id" property="policyId" />
        <result column="spec_category_id" property="specCategoryId" />
        <result column="spec_category" property="specCategory" />
        <result column="spec_id" property="specId" />
        <result column="spec_name" property="specName" />
        <result column="policy_cost_open" property="policyCostOpen" />
        <result column="policy_cost_renew" property="policyCostRenew" />
        <result column="policy_cost_add" property="policyCostAdd" />
        <result column="policy_cost_upgrade" property="policyCostUpgrade" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, policy_spec_detail_id, policy_id, spec_category_id, spec_category, spec_id, spec_name, policy_cost_open, policy_cost_renew, policy_cost_add, policy_cost_upgrade
    </sql>

</mapper>
