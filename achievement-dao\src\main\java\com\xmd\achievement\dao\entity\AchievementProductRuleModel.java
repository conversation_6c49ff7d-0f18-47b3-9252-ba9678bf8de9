package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("achievement_product_rule")
public class AchievementProductRuleModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 规则code
     */
    @TableField("rule_code")
    private String ruleCode;
    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;
    /**
     * 规则类型 1-商品配置 2-其他
     */
    @TableField("rule_type")
    private String ruleType;
    /**
     * 删除标记: 0: 未删除, 1: 删除
     */
    @TableField("delete_flag")
    private Integer deleteFlag;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;
    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 更新人id
     */
    @TableField("update_user_id")
    private String updateUserId;
    /**
     * 更新人名称
     */
    @TableField("update_user_name")
    private String updateUserName;
}