package com.xmd.achievement.handler.achievement;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.OrderSpecTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 首年到账金额=实付金额*首年报价/(首年报价+续费报价)；
 * <AUTHOR>
 * @date: 2024/12/20 14:46
 */
@Slf4j
@Service
public class SpecCalculateFirstYearIncomeAmountHandler implements CalculateAmountHandler {
    @Override
    public void calculate(CalculateFactInfo factInfo) {
        try {
            AchievementSpecDetailModel spec = factInfo.getSpec();
            BigDecimal amount = BigDecimal.ZERO;
            if (spec.getPaidAmount().compareTo(BigDecimal.ZERO) > 0) {
                amount = NumberUtil.mul(spec.getPaidAmount(), spec.getFirstYearQuote()).divide(NumberUtil.add(spec.getFirstYearQuote(), null != spec.getRenewalQuote() ? spec.getRenewalQuote() : BigDecimal.ZERO), NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
            }
            spec.setFirstYearIncome(amount);
        } finally {
            log.warn("首年到账金额业绩 规格id:{},参数:{}", factInfo.getSpec().getSpecId(), JSON.toJSONString(factInfo));
        }
    }
}
