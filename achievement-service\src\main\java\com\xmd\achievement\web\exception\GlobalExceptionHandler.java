package com.xmd.achievement.web.exception;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.support.log.LogObject;
import com.xmd.achievement.util.constant.UtilConstant;
import com.xmd.achievement.web.aop.WebAspect;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.RequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;

/**
 * 全局异常处理器
 *
 * @version 1.0.0
 * @date 2023/4/8 15:48
 **/
@RestControllerAdvice
@RequestMapping("error")
public class GlobalExceptionHandler {

    private final static Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @RequestMapping("400")
    public WebResult<?> error400() {
        return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
    }

    @RequestMapping("404")
    public WebResult<?> error404() {
        return WebResult.error(WebCodeMessageEnum.REQUEST_ADDRESS_EXCEPTION);
    }

    @RequestMapping("500")
    public WebResult<?> error500() {
        return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
    }

    @ExceptionHandler(Exception.class)
    public WebResult<?> error(HttpServletRequest request, Exception exception) {
        if (exception instanceof HttpRequestMethodNotSupportedException) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_METHOD_EXCEPTION);
        }
        if (exception instanceof MethodArgumentTypeMismatchException || exception instanceof MissingServletRequestParameterException) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
        String traceId = MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME);
        WebResult<Object> errorWebResult = WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
        errorWebResult.setData(traceId);
        LogObject logObject = new LogObject();
        logObject.setInvokerName(WebAspect.WEB_INVOKE)
                .setInvokerIp(RequestUtil.getIp())
                .setEventName(request.getRequestURI() + UtilConstant.DATA_VALUE_SEPARATOR + request.getMethod())
                .setTraceId(traceId)
                .setRequest(JSON.toJSONString(request.getParameterMap()))
                .setResponse(JSON.toJSONString(errorWebResult))
                .setMsg(exception.getMessage())
                .setCostTime(System.currentTimeMillis() - Long.parseLong(MDC.get(UtilConstant.Mdc.START_TIME)));
        LOGGER.error("请求异常, 请求数据为:{},异常信息为:", JSON.toJSONString(logObject), exception);
        return errorWebResult;
    }

    /**
     * 业务异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler({BusinessException.class})
    @ResponseBody
    public WebResult exceptionHandler(BusinessException e) {
        return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getErrorMsg());

    }

    /**
     * 请求头Content-type异常拦截
     *
     * @param e
     * @return * @return com.xmd.insight.web.entity.response.WebResult
     * <AUTHOR>
     * @date 2024/3/22
     * @version 1.0.0
     **/
    @ExceptionHandler({HttpMediaTypeNotSupportedException.class})
    @ResponseBody
    public WebResult exceptionHandler(HttpMediaTypeNotSupportedException e) {
        return WebResult.error(WebCodeMessageEnum.REQUEST_METHOD_EXCEPTION, e.getMessage());

    }

    /**
     * 参数绑定异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({BindException.class})
    @ResponseBody
    public WebResult exceptionHandler(BindException exception) {
        StringBuilder message = new StringBuilder();
        List<FieldError> fieldErrorList = exception.getFieldErrors();
        fieldErrorList.forEach(item -> message.append(item.getDefaultMessage()).append(UtilConstant.DATA_SEPARATOR));
        return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, message.substring(0, message.length() - 1));
    }

    /**
     * 方法参数校验异常:如实体类中的@Size注解配置和数据库中该字段的长度不统一等问题
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({ConstraintViolationException.class})
    @ResponseBody
    public WebResult exceptionHandler(ConstraintViolationException exception) {
        StringBuilder message = new StringBuilder();
        Set<ConstraintViolation<?>> violations = exception.getConstraintViolations();
        if (violations != null) {
            violations.forEach(item -> message.append(item.getMessage()).append(UtilConstant.DATA_SEPARATOR));
        }
        return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, message.substring(0, message.length() - 1));
    }

    /**
     * Bean 校验异常
     *
     * @param exception 异常信息
     * @return com.ce.distribution.web.config.response.WebResult<?>
     * @date 2023/4/8 15:48
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    @ResponseBody
    public WebResult handlerParamValid(MethodArgumentNotValidException exception) {
        StringBuilder message = new StringBuilder();
        List<FieldError> fieldErrorList = exception.getBindingResult().getFieldErrors();
        fieldErrorList.forEach(item -> message.append(item.getDefaultMessage()).append(UtilConstant.DATA_SEPARATOR));
        return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, message.substring(0, message.length() - 1));
    }

    /**
     * Bean 校验异常
     *
     * @param exception 异常信息
     * @return com.ce.distribution.web.config.response.WebResult<?>
     * @date 2023/4/8 15:48
     */
    @ExceptionHandler({HttpClientErrorException.class})
    @ResponseBody
    public WebResult exceptionHandler(HttpClientErrorException exception) {

        JSONObject responseObj = JSONObject.parseObject(exception.getResponseBodyAsString());
        JSONObject errorObj = responseObj.getJSONObject("error");
        if (null != errorObj) {
            String errorCode = errorObj.getString("code");
            String msg = errorObj.getString("message");
            LOGGER.error("visitor服务请求异常, 异常信息为:{}", msg);
            return WebResult.error(errorCode, msg);
        }
        return WebResult.error(exception.getStatusCode().toString(), exception.getMessage());
    }

}
