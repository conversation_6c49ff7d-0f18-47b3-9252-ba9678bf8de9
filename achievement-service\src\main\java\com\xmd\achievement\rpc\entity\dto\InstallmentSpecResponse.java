package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分期规格集合
 * <AUTHOR>
 * @date 2024年11月11日 下午1:29
 */
@Data

public class InstallmentSpecResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;
    /**
     * 	分期商品规格id
     */

    private Long installmentSpecId;

    /**
     * 分期明细id
     */

    private Long installmentDetailId;

    /**
     * 分期商品id
     */

    private Long installmentProductId;

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 订单商品规格id
     */

    private Long orderProductSpecId;

    /**
     * 分期期数
     */

    private Integer installmentNum;

    /**
     * 本期应付金额
     */

    private BigDecimal installmentPayableAmount;

}
