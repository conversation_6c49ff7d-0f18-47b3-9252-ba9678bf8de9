<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.SaleTaskOpeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.SaleTaskOpeLogModel">
        <id column="id" property="id" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="task_id" property="taskId" />
        <result column="previous_basic_task" property="previousBasicTask" />
        <result column="modified_basic_task" property="modifiedBasicTask" />
        <result column="ope_type" property="opeType" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, delete_flag, task_id, previous_basic_task, modified_basic_task, ope_type, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

</mapper>
