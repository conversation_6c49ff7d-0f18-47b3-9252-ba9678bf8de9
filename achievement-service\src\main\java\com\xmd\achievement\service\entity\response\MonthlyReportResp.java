package com.xmd.achievement.service.entity.response;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class MonthlyReportResp {

    /**
     * 主键id
     */
    @ExcelIgnore
    private Long id;
    /**
     * 商务月id
     */
    @ExcelIgnore
    private Long businessMonthId;
    /**
     * 商务月
     */
    @ExcelProperty("商务月")
    private String businessMonth;
    /**
     * 机构ID
     */
    @ExcelProperty("机构ID")
    private Long organizationId;
    /**
     * 机构名称
     */
    @ExcelProperty("机构名称")
    private String organizationName;
    /**
     * 所属公司id
     */
    @ExcelProperty("所属分司机构id")
    private Long companyId;
    /**
     * 所属公司名称
     */
    @ExcelProperty("所属分司")
    private String companyName;
    /**
     * 机构领导ID
     */
    @ExcelProperty("机构领导ID")
    private Long organizationLeaderId;
    /**
     * 机构领导名称
     */
    @ExcelProperty("机构领导名称")
    private String organizationLeaderName;
    /**
     * 任职本机构日期
     */
    @ExcelProperty("任职本机构日期")
    private String appointmentDate;
    /**
     * 体系
     */
    @ExcelProperty("体系")
    private String systemCategory;
    /**
     * 市场类别
     */
    @ExcelProperty("市场类别")
    private String marketCategory;
    /**
     * 考核部门数
     */
    @ExcelProperty("考核部门数")
    private BigDecimal examinationDeptCount;
    /**
     * 部门数
     */
    @ExcelProperty("部门数")
    private BigDecimal deptCount;
    /**
     * 商代数量
     */
    @ExcelProperty("商代数量")
    private Long businessRepresentativeCount;
    /**
     * 月签单金额
     */
    @ExcelProperty("月签单金额")
    private BigDecimal monthlySigningAmount;
    /**
     * 月净现金
     */
    @ExcelProperty("月净现金")
    private BigDecimal monthlyNetCash;
    /**
     * 月半净现金
     */
    @ExcelProperty("月半净现金")
    private BigDecimal monthlyHalfNetCash;
    /**
     * 基本任务（元）
     */
    @ExcelProperty("基本任务（元）")
    private BigDecimal basicTask;
    /**
     * 任务完成率
     */
    @ExcelProperty("任务完成率")
    private BigDecimal taskCompletionRate;
    /**
     * 总出单人数
     */
    @ExcelProperty("总出单人数")
    private Long totalSignPersonCount;
    /**
     * 出单率
     */
    @ExcelProperty("出单率")
    private BigDecimal signRate;
    /**
     * 转正商代人数
     */
    @ExcelProperty("正式骨干数量")
    private Long formalBusinessRepresentativeCount;
    /**
     * 在岗人均净现金
     */
    @ExcelProperty("在岗人均净现金")
    private BigDecimal netCashPerEmployee;
    /**
     * 非续费客户数
     */
    @ExcelProperty("非续费客户数")
    private Long nonRenewalCustomerCount;
    /**
     * 月新客户数
     */
    @ExcelProperty("月新客户数")
    private Long monthlyNewCustomerCount;
    /**
     * 月网站新客户数
     */
    @ExcelProperty("月网站新客户数")
    private Long monthlyWebsiteNewCustomerCount;
    /**
     * 月新网站数
     */
    @ExcelProperty("月新网站数")
    private Long monthlyNewWebsiteCount;
    /**
     * 部门提成业绩
     */
    @ExcelProperty("部门提成业绩")
    private BigDecimal deptCommission;
    /**
     * 分公司提成业绩
     */
    @ExcelProperty("分公司提成业绩")
    private BigDecimal branchCommission;
    /**
     * 月网站净现金
     */
    @ExcelProperty("月网站净现金")
    private BigDecimal monthlyWebsiteNetCash;
    /**
     * 业绩段
     */
    @ExcelProperty("业绩段")
    private String achievementSegment;
    /**
     * 业绩段id
     */
    @ExcelProperty("业绩段id")
    private Long achievementSegmentId;
    /**
     * 月老客户数
     */
    @ExcelProperty("月老客户数")
    private Long monthlyOldCustomerCount;
    /**
     * 体系id
     */
    @ExcelIgnore
    private Long systemId;
    /**
     * 市场类别id
     */
    @ExcelIgnore
    private Long marketCategoryId;
    /**
     * 创建人id
     */
    @ExcelIgnore
    private String createUserId;
    /**
     * 创建人名称
     */
    @ExcelIgnore
    private String createUserName;
    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date createTime;
    /**
     * 更新人id
     */
    @ExcelIgnore
    private String updateUserId;
    /**
     * 更新人名称
     */
    @ExcelIgnore
    private String updateUserName;
    /**
     * 更新时间
     */
    @ExcelIgnore
    private Date updateTime;

    @ExcelProperty("saas净现金（元）")
    private BigDecimal saasNetCash;
}
