package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业绩查询开放接口 返回参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Schema(description = "业绩查询开放接口 返回参数")
public class OpenAchievementResponse implements Serializable {

    private static final long serialVersionUID = -5355436132033747372L;
    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 商务月ID
     */
    @Schema(description = "商务月ID")
    private Long businessMonthId;

    /**
     * 商务月
     */
    @Schema(description = "商务月")
    private String businessMonth;

    /**
     * 订单明细编号
     */
    @Schema(description = "订单明细编号")
    private String orderProductId;

    /**
     * 商品id
     */
    @Schema(description = "商品id")
    private Long productId;

    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购
     */
    @Schema(description = "业务类型 1=新开，2=续费，3=升级，4=另购")
    private String saleType;

    /**
     * 业务类型 1=有效，2=已完成
     */
    @Schema(description = "业务类型 1=有效，2=已完成")
    private String status;

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private String customerId;

    /**
     * 商务ID
     */
    @Schema(description = "商务ID")
    private String businessId;

    /**
     * 主分单人 1=主，2=辅
     */
    @Schema(description = "主分单人 1=主，2=辅")
    private String mainSplitPerson;

    /**
     * 公司ID
     */
    @Schema(description = "公司ID")
    private Long companyId;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 业绩生成时间
     */
    @Schema(description = "业绩生成时间")
    private Date statisticsTime;

    /**
     * 净现金
     */
    @Schema(description = "净现金")
    private BigDecimal netCash;

    /**
     * 商代提成业绩
     */
    @Schema(description = "商代提成业绩")
    private BigDecimal agentCommissionAchievement;

    /**
     * 部门提成业绩
     */
    @Schema(description = "部门提成业绩")
    private BigDecimal deptCommission;
}
