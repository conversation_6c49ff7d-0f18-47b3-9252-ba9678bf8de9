package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 规格组合明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class SpecCombinationDetailRequest implements Serializable {
    private static final long serialVersionUID = -9164238856182793076L;

    @Schema(description = "商品id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品id不能为空")
    private Long productId;

    @Schema(description = "规格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "规格id不能为空")
    private Long specId;



    @Schema(description = "新开政策性成本", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.8")
    @NotNull(message = "新开政策性成本不能为空")
    private BigDecimal newPolicyCost;

    @Schema(description = "续费政策性成本", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.8")
    @NotNull(message = "续费政策性成本不能为空")
    private BigDecimal renewalPolicyCost;

    @Schema(description = "升级政策性成本", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.8")
    @NotNull(message = "升级政策性成本不能为空")
    private BigDecimal upgradePolicyCost;

    @Schema(description = "另购政策性成本", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.8")
    @NotNull(message = "另购政策性成本不能为空")
    private BigDecimal additionalPolicyCost;
}