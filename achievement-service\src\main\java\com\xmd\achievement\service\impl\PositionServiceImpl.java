package com.xmd.achievement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.PositionModel;
import com.xmd.achievement.dao.repository.IPositionRepository;
import com.xmd.achievement.service.PositionService;
import com.xmd.achievement.service.entity.response.QueryPositionListResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 职级接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/10:35
 * @since 1.0
 */
@Service
public class PositionServiceImpl implements PositionService {
    @Resource
    IPositionRepository positionRepository;

    @Override
    public List<QueryPositionListResponse> queryPositionList() {
        List<PositionModel> list = positionRepository.list();
        if (ObjectUtil.isEmpty(list)) {
            return null;
        }
        List<QueryPositionListResponse>  responseList = new ArrayList<>();

        Map<String, String> groupedPositions = list.stream()
                .collect(Collectors.groupingBy(
                        PositionModel::getPositionName,
                        Collectors.mapping(PositionModel::getPositionCode, Collectors.joining(","))));

        groupedPositions.forEach((positionName, positionCodes) -> {
            QueryPositionListResponse response = new QueryPositionListResponse();
            response.setPositionName(positionName);
            response.setPositionCodes(positionCodes);
            responseList.add(response);
        });
        return responseList;
    }

    @Override
    public PositionModel getByJobGradeIdAndPassFlag(Integer jobGradeId, Integer passFlag) {
        return null != jobGradeId && null != passFlag ? positionRepository.getOne(new LambdaQueryWrapper<PositionModel>().eq(PositionModel::getPositionId, jobGradeId).eq(PositionModel::getConfirmed, passFlag)) : null;
    }

    @Override
    public List<PositionModel> getAllPositions() {
        return positionRepository.list();
    }
}
