package com.xmd.achievement.async.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.IMqOrderRefundInfoService;
import com.xmd.achievement.service.entity.dto.ReceiveOrderRefundDto;
import com.xmd.achievement.support.constant.enums.AfterSalesOrderStatusEnum;
import com.xmd.achievement.support.constant.enums.AfterSalesTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 转款
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(consumerGroup = "${mq.group.orderTransferFund}", topic = "${mq.topic.orderTransferFund}", consumeMode = ConsumeMode.CONCURRENTLY, messageModel = MessageModel.CLUSTERING)
public class ReceiveOrderTransferFundConsumer implements RocketMQListener<String> {

    private final IMqOrderRefundInfoService mqOrderRefundInfoService;

    @Override
    public void onMessage(String dataJson) {
        log.info("订单退转款数据信息处理,接收数据:{}", dataJson);
        try {
            // 解析
            ReceiveOrderRefundDto dto = JSONUtil.toBean(dataJson, ReceiveOrderRefundDto.class);
            log.info("订单退转款数据信息处理,数据转换:{}", JSONUtil.toJsonStr(dto));

            if (Objects.equals(dto.getAfterSalesStatus(), AfterSalesOrderStatusEnum.REFUND_PENDING.getStatus())) {
                dto.setAfterSalesOrderType(AfterSalesTypeEnum.REFUND.getCode());
                mqOrderRefundInfoService.saveInfo(dto);
                return;
            }
            if (Objects.equals(dto.getAfterSalesStatus(), AfterSalesOrderStatusEnum.TRANSFER_PENDING.getStatus())) {
                dto.setAfterSalesOrderType(AfterSalesTypeEnum.TRANSFER.getCode());
                mqOrderRefundInfoService.saveInfo(dto);
            }
        } catch (Exception e) {
            log.error("订单转款数据信息处理异常，message:{},异常信息", dataJson, e);
            throw e;
        }
    }

}
