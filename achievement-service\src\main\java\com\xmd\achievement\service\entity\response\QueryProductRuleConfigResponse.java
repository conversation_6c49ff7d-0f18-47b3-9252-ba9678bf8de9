package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/09:55
 * @since 1.0
 */
@Data
public class QueryProductRuleConfigResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "规则编号")
    private String ruleCode;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "规则类型")
    private String ruleType;

    @Schema(description = "规则备注")
    private String remark;
}
