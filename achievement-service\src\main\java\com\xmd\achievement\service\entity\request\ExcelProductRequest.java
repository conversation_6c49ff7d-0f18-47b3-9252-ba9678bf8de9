package com.xmd.achievement.service.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/24 15:18
 * @version: 1.0.0
 * @return {@link }
 */
@Data
public class ExcelProductRequest implements Serializable {
    private static final long serialVersionUID = -5203999703263109432L;

    /**
     * 订单明细编号
     */
    @Schema(description = "订单明细编号")
    private String orderProductId;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 商务ID
     */
    @Schema(description = "商务ID")
    private String businessId;

    /**
     * 商务名称
     */
    @Schema(description = "商务名称")
    private String businessName;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractNo;

    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购
     */
    @Schema(description = "业务类型 1=新开，2=续费，3=升级，4=另购")
    private String saleType;

    /**
     * 所属机构ID
     */
    @Schema(description = "所属机构ID")
    private String orgId;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;


    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 数据来源
     */
    @Schema(description = "数据来源 （枚举 0 - 全部、 1- 跨境系统、2 - 中企系统）默认0")
    private Integer achievementSource;

    @Schema(description = "绩效Id")
    private Long achievementId;

    /**
     * 是否为异常业绩
     */
    @Schema(description = "是否异常，0-正常，1-异常")
    private Integer isAbnormal;
}
