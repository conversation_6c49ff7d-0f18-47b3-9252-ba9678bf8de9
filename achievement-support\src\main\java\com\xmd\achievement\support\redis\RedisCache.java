package com.xmd.achievement.support.redis;

import com.xmd.achievement.cache.entity.SortCollectionData;
import com.xmd.achievement.cache.store.Cache;
import com.xmd.achievement.cache.store.CacheTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * redis缓存处理
 *
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @date 2021/05/11 下午4:08
 */
@Component
public class RedisCache implements Cache {

    private final static Logger LOGGER = LoggerFactory.getLogger(RedisCache.class);

    @Resource
    private RedisOperator redisOperator;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取当前操作缓存方式的名称
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public String getName() {
        return CacheTypeEnum.REDIS.getName();
    }

    /**
     * 模糊获取所有的key
     *
     * @param prefix 查询条件
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2023/6/25 10:32
     **/
    @Override
    public Set<String> scan(String prefix) {
        return redisOperator.scan(prefix);
    }

    /**
     * 判断缓存数据是否存在
     *
     * @param key 缓存键
     * @return boolean
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public boolean exist(String key) {
        return redisOperator.hasKey(key);
    }

    /**
     * 重设缓存过期时间
     *
     * @param key 缓存键
     * <AUTHOR>
     * @date: 2024/5/27 2:41 下午
     * @version: 1.0.0
     * @return: void
     */
    @Override
    public void ttl(String key, long time) {
        redisOperator.expire(key, time, TimeUnit.SECONDS);
    }

    /**
     * 删除redis缓存
     *
     * @param key 键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void del(String key) {
        redisOperator.del(key);
    }

    /**
     * 批量删除缓存
     *
     * @param cacheKeyList 缓存键列表
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void batchDel(List<String> cacheKeyList) {
        redisOperator.del(cacheKeyList.toArray(new String[0]));
    }

    /**
     * 批量删除缓存
     *
     * @param cacheKeyList 缓存键列表
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public List<String> batchGet(List<String> cacheKeyList) {
        return redisOperator.mget(cacheKeyList);
    }

    /**
     * 设置缓存数据，带有效期
     *
     * @param key    键
     * @param value  值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void set(String key, String value, long expire) {
        if (!redisOperator.set(key, value, expire, TimeUnit.SECONDS)) {
            LOGGER.error("redis设置缓存失败，key:{},value:{},expire:{}", key, value, expire);
        }
    }

    /**
     * 设置缓存数据，不带有效期
     *
     * @param key   缓存键
     * @param value 缓存值
     * <AUTHOR>
     * @date: 2024/6/3 5:23 下午
     * @version: 1.0.0
     * @return: void
     */
    @Override
    public void setWithoutExpire(String key, String value) {
        redisOperator.set(key, value);
    }

    /**
     * 获取redis缓存
     *
     * @param key 键
     * @return V
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public String get(String key) {
        return redisOperator.get(key);
    }

    /**
     * 获取redis缓存并且删除key
     *
     * @param key 键
     * @return V
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public String getAndDeleteUsingLua(String key) {
        String script = "local val = redis.call('GET', KEYS[1])\n" +
                "redis.call('DEL', KEYS[1])\n" +
                "return val";

        DefaultRedisScript<String> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(script);
        redisScript.setResultType(String.class);
        List<String> keys = Collections.singletonList(key);

        return stringRedisTemplate.execute(redisScript, keys);
    }

    @Override
    public long incr(String key, long delta) {
        return redisOperator.incr(key, delta);
    }

    @Override
    public void hSet(String key, Map<String, String> hValue) {
        redisOperator.hSet(key, hValue);
    }

    /**
     * 设置hash数据
     *
     * @param key    缓存键
     * @param hValue 缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void hSet(String key, Map<String, String> hValue, long expire) {
        redisOperator.hSet(key, hValue, expire, TimeUnit.SECONDS);
    }

    /**
     * 设置hash数据
     *
     * @param key        缓存键
     * @param hashKey    缓存hashKey
     * @param hValueItem 单条缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void hSetItem(String key, String hashKey, String hValueItem) {
        redisOperator.hSet(key, hashKey, hValueItem);
    }

    /**
     * 删除hash
     *
     * @param key
     * @param hashKeys
     * <AUTHOR>
     * @date: 2024/7/1 11:47 上午
     * @version: 1.0.0
     * @return: void
     */
    @Override
    public void hDelItem(String key, List<String> hashKeys) {
        redisOperator.hDel(key, hashKeys.toArray(new String[hashKeys.size()]));
    }

    /**
     * 获取hash数据
     *
     * @param key     缓存键
     * @param hashKey 缓存hashKey
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public String hGetItem(String key, String hashKey) {
        Object value = redisOperator.hGet(key, hashKey);
        return value == null ? null : value.toString();
    }

    /**
     * 获取hash数据
     *
     * @param key 缓存键
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public Map<String, String> hGet(String key) {
        return redisOperator.hGetAll(key);
    }

    /**
     * 设置list数据
     *
     * @param key    缓存键
     * @param lValue 缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void lSet(String key, List<String> lValue, long expire) {
        redisOperator.lSet(key, lValue, expire, TimeUnit.SECONDS);
    }

    /**
     * 设置list数据
     *
     * @param key    缓存键
     * @param lValue 缓存值
     * <AUTHOR>
     * @date: 2024/6/3 4:54 下午
     * @version: 1.0.0
     * @return: void
     */
    @Override
    public void lSetWithoutExpire(String key, List<String> lValue) {
        redisOperator.lSet(key, lValue);
    }

    /**
     * 给list中添加单条数据
     *
     * @param key        缓存键
     * @param lValueItem 缓存值
     * @param insertNode 插入节点
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void lSetItem(String key, String lValueItem, int insertNode) {
        redisOperator.lSet(key, lValueItem, insertNode);
    }

    /**
     * 获取list数据
     *
     * @param key 缓存键
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public List<String> lGet(String key) {
        return redisOperator.lGet(key, RedisOperator.COLLECTION_HEAD_INDEX, RedisOperator.COLLECTION_TAIL_INDEX);
    }

    /**
     * 获取list的长度
     *
     * @param key 缓存键
     * <AUTHOR>
     * @date: 2024/6/3 2:34 下午
     * @version: 1.0.0
     * @return: long
     */
    @Override
    public long lGetListSize(String key) {
        return redisOperator.lGetListSize(key);
    }

    /**
     * 获取list的指定元素
     *
     * @param key   缓存键
     * @param index 下标
     * <AUTHOR>
     * @date: 2024/6/3 6:52 下午
     * @version: 1.0.0
     * @return: java.lang.String
     */
    @Override
    public String lIndex(String key, Integer index) {
        return redisOperator.lGetIndex(key, index);
    }

    /**
     * 设置set数据
     *
     * @param key    缓存键
     * @param sValue 缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void sSet(String key, Set<String> sValue, long expire) {
        redisOperator.sSetAndTime(key, expire, TimeUnit.SECONDS, sValue.toArray(new String[0]));
    }

    /**
     * 设置set数据
     *
     * @param key    缓存键
     * @param sValue 缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void sSetItem(String key, String sValue) {
        redisOperator.sSet(key, sValue);
    }

    /**
     * 获取set数据
     *
     * @param key 缓存键
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public Set<String> sGet(String key) {
        return redisOperator.sGet(key);
    }

    /**
     * 设置有序集合
     *
     * @param key    缓存键
     * @param zValue 元素值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    @SuppressWarnings("unchecked")
    public void zSet(String key, List<SortCollectionData> zValue, long expire) {
        DefaultTypedTuple<String>[] defaultTypedTupleArray = new DefaultTypedTuple[zValue.size()];
        List<DefaultTypedTuple<String>> defaultTypedTupleList = zValue.stream().map(item -> new DefaultTypedTuple<>(item.getValue(), item.getWeight())).collect(Collectors.toList());
        defaultTypedTupleList.toArray(defaultTypedTupleArray);
        redisOperator.zSet(key, expire, TimeUnit.SECONDS, defaultTypedTupleArray);
    }

    /**
     * 设置有序集合
     *
     * @param key        缓存键
     * @param zValueItem 元素值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public void zSetItem(String key, SortCollectionData zValueItem) {
        redisOperator.zSet(key, zValueItem.getValue(), zValueItem.getWeight());
    }

    /**
     * 获取有序集合
     *
     * @param key 缓存键
     * @return java.util.Set<com.xmd.achievement.cache.entity.SortCollectionData>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public List<SortCollectionData> zGet(String key, int orderBy) {
        List<ZSetOperations.TypedTuple<String>> typedTuples = redisOperator.zGetSetWithScoreByIndex(key, RedisOperator.COLLECTION_HEAD_INDEX, RedisOperator.COLLECTION_TAIL_INDEX, orderBy);
        return typedTuples.stream().map(typedTuple -> new SortCollectionData(typedTuple.getValue(), typedTuple.getScore())).collect(Collectors.toList());
    }

    /**
     * 获取有序集合
     *
     * @param key    缓存键
     * @param weight 权重
     * @return java.util.Set<com.xmd.achievement.cache.entity.SortCollectionData>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public Set<SortCollectionData> zGetItem(String key, double weight) {
        return redisOperator.zGetByScore(key, weight, weight).stream().map(SortCollectionData::new).collect(Collectors.toSet());
    }
}
