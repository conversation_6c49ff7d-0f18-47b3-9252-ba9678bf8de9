package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.AchievementBlacklistModel;
import com.xmd.achievement.dao.repository.IAchievementBlacklistRepository;
import com.xmd.achievement.service.AchievementBlacklistService;
import com.xmd.achievement.service.entity.dto.AchievementBlacklistExcelDto;
import com.xmd.achievement.service.entity.request.AchievementBlacklistRequest;
import com.xmd.achievement.service.entity.response.AchievementBlacklistVO;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.EasyExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AchievementBlacklistServiceImpl implements AchievementBlacklistService {

    @Resource
    private IAchievementBlacklistRepository achievementBlacklistRepository;


    @Override
    public WebResult<PageResponse<AchievementBlacklistVO>> selectBlacklist(AchievementBlacklistRequest request) {
        Page<AchievementBlacklistModel> pageOf = new Page<>(request.getPageIndex(), request.getPageSize());
        Page<AchievementBlacklistModel> page = achievementBlacklistRepository.selectBlacklist(pageOf, request.getOrderNo());
        PageResponse<AchievementBlacklistVO> response = new PageResponse<>(page.getTotal(), page.getCurrent(), page.getSize());
        if (CollUtil.isEmpty(page.getRecords())) {
            return WebResult.success(response);
        }
        List<AchievementBlacklistVO> achievementBlacklistVOList = BeanUtil.copyToList(page.getRecords(), AchievementBlacklistVO.class);
        response.setList(achievementBlacklistVOList);
        return WebResult.success(response);
    }

    @Override
    public void importBlacklist(MultipartFile file) throws Exception{
        List<AchievementBlacklistExcelDto> readList = EasyExcelUtil.read(file.getInputStream(), AchievementBlacklistExcelDto.class, IdUtil.randomUUID());
        if (CollUtil.isEmpty(readList)) {
            return;
        }
        List<AchievementBlacklistModel> achievementBlacklistModelList = new ArrayList<>();
        for (AchievementBlacklistExcelDto achievementBlacklistExcelDto : readList) {
            AchievementBlacklistModel achievementBlacklistModel = new AchievementBlacklistModel();
            achievementBlacklistModel.setBlacklistId(IdUtil.getSnowflake().nextId());
            achievementBlacklistModel.setOrderNo(achievementBlacklistExcelDto.getOrderNo());
            achievementBlacklistModel.setOrderInfoNo(achievementBlacklistExcelDto.getOrderInfoNo());
            achievementBlacklistModelList.add(achievementBlacklistModel);
        }
        achievementBlacklistRepository.saveBatch(achievementBlacklistModelList);
    }

    @Override
    public WebResult<Boolean> deleteBlacklist(AchievementBlacklistRequest request) {
        achievementBlacklistRepository.deleteById(request.getId());
        return WebResult.success(true);
    }

    private boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue().trim();
    }
}
