package com.xmd.achievement.handler.statistics;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.NewOldCustomerRecordModel;
import com.xmd.achievement.service.INewOldCustomerRecordService;
import com.xmd.achievement.service.impl.NewOldCustomerRecordServiceImpl;
import com.xmd.achievement.support.constant.enums.CustomerType;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */ /*
 * 新老客户数处理器
 */
@Service
public class NewOldCustomerCountHandler implements StatisticsHandler {
    @Resource
    private INewOldCustomerRecordService newOldCustomerRecordService;

    @Override
    public void statistics(StatisticsFactInfo factInfo) {
        List<AchievementProductDetailModel> achList = factInfo.getAchList();

        Map<String, List<AchievementProductDetailModel>> newCustomerMap = achList.stream().filter(ach -> CustomerType.NEW.getType().equals(ach.getCustomerType())).collect(Collectors.groupingBy(AchievementProductDetailModel::getCustomerId));
        Map<String, List<AchievementProductDetailModel>> oldCustomerMap = achList.stream().filter(ach -> CustomerType.OLD.getType().equals(ach.getCustomerType())).collect(Collectors.groupingBy(AchievementProductDetailModel::getCustomerId));

        //AchievementProductDetailModel achievementProductDetailModel = achList.get(0);
        //List<NewOldCustomerRecordModel> records = newOldCustomerRecordService.getListByMonthIdAndEmployeeId(achievementProductDetailModel.getBusinessMonthId(), achievementProductDetailModel.getBusinessId());
        factInfo.setNewCustomerCount(newCustomerMap.size());
        factInfo.setOldCustomerCount(oldCustomerMap.size());
    }
}