package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 登录人权限校验dto
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/5/23
 */
@Data
public class UserLoginInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    private Long id;

    /**
     * 账号唯一id
     */
    private String userId;

    /**
     * 账号状态：1: 正常, 2: 冻结
     */
    private Integer accountStatus;

    /**
     * 员工状态 1: 邀请中, 2: 在职, 3: 离职
     */
    private Integer status;


    /**
     * 职位
     */
    private String position;


    /**
     * 姓名
     */
    private String name;

    /**
     * 联系人手机号码
     */
    private String mobile;

    /**
     * 联系人邮箱
     */
    private String email;


    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * token
     */
    private String token;

    /**
     * 是否具备功能权限
     */

    private Boolean checkPermissionFlag;

    /**
     * 所属机构信息
     */
    private OrgPathInfoDTO orgPathInfoDTO;


    /**
     * 用户数据权限
     */
    private UserDataPermissionsDTO userDataPermissionsDTO;
}