package com.xmd.achievement.cache.lock;

/**
 * 对锁的操作接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 17:18
 */
public interface Lock {
    /**
     * 获取当前操作锁方式的名称
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    String getName();

    /**
     * 获取锁
     *
     * @param key 锁的key
     * @return boolean
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    boolean lock(String key);

    /**
     * 获取锁
     *
     * @param key         锁的key
     * @param waitTime    加锁等待时间
     * @param timeoutTime 锁超时时间
     * @return boolean
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    boolean tryLock(String key, long waitTime, long timeoutTime);

    /**
     * 释放锁
     *
     * @param key 锁的key
     * @return boolean
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    boolean unLock(String key);
}
