package com.xmd.achievement.cache.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.xmd.achievement.cache.constant.CacheConstant;
import com.xmd.achievement.cache.handler.core.AbstractCacheHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 无需集合缓存处理模板类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 17:18
 */
public abstract class AbstractSetCacheHandler<T> extends AbstractCacheHandler<T, Set<String>, Set<T>> {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(AbstractSetCacheHandler.class);

    /**
     * 获取缓存数据
     *
     * @param cacheKey 缓存key
     * @return Set<String>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected Set<String> getCacheValue(String cacheKey) {
        return getCache().sGet(cacheKey);
    }

    /**
     * 设置缓存数据
     *
     * @param cacheKey   缓存key
     * @param cacheValue 缓存值
     * @param expire     缓存有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected void setCacheData(String cacheKey, Set<String> cacheValue, long expire) {
        getCache().sSet(cacheKey, cacheValue, expire);
    }

    /**
     * 检查是否为空数据
     *
     * @param cacheValue 缓存值
     * @return boolean
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected boolean checkNullData(Set<String> cacheValue) {
        return getNullData().equals(cacheValue);
    }

    /**
     * 缓存数据判空
     *
     * @param dataSet 缓存数据
     * @return boolean
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected boolean isEmpty(Set<String> dataSet) {
        return CollectionUtil.isEmpty(dataSet);
    }

    /**
     * 设置缓存默认数据
     *
     * @return Set<String>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected Set<String> getNullData() {
        return CacheConstant.CacheNullData.SET;
    }

    /**
     * 封装缓存value
     *
     * @param customerValue 自定义传入的value
     * @return String
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected Set<String> packageCacheValue(Set<T> customerValue) {
        return customerValue.stream().map(this::objectToString).collect(Collectors.toSet());
    }

    /**
     * 解析缓存数据
     *
     * @param cacheValue 缓存数据
     * @return T
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected Set<T> parseCacheValue(Set<String> cacheValue) {
        return cacheValue.stream().map(this::stringToObject).collect(Collectors.toSet());
    }

    /**
     * 设置单个缓存数据
     *
     * @param customerKey    自定义key
     * @param cacheValueItem 缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public final void setCacheItem(String customerKey, T cacheValueItem) {
        getCache().sSetItem(getKey(customerKey), objectToString(cacheValueItem));
    }
}
