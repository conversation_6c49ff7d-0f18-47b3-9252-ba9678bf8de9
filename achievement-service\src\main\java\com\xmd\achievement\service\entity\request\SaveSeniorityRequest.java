package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/16:47
 * @since 1.0
 */
@Data
public class SaveSeniorityRequest implements Serializable {
    private static final long serialVersionUID = 7944318837467623499L;

    @Schema(description = "司龄名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "司龄名称")
    @NotBlank(message = "司龄名称不能为空")
    private String seniorityName;

    @Schema(description = "最小值（年限）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "最小值（年限）不能为空")
    private Integer minYears;

    @Schema(description = "最大值（年限）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "最大值（年限）不能为空")
    private Integer maxYears;

}
