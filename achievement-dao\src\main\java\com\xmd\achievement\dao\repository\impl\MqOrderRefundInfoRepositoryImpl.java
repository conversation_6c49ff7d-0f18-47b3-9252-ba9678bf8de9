package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.dao.mapper.MqOrderRefundInfoMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IMqOrderRefundInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 订单退转款消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@Slf4j
public class MqOrderRefundInfoRepositoryImpl extends ServiceImpl<MqOrderRefundInfoMapper,MqOrderRefundInfoModel> implements IMqOrderRefundInfoRepository {

@Resource
private MqOrderRefundInfoMapper mqOrderRefundInfoMapper;

    @Override
    public List<MqOrderRefundInfoModel> selectByOrderNo(String orderNo) {
        LambdaQueryWrapper<MqOrderRefundInfoModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqOrderRefundInfoModel::getTransferInOrderNo, orderNo);
        return mqOrderRefundInfoMapper.selectList(queryWrapper);
    }
}