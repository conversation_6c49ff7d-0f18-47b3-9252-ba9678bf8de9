package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/11:29
 * @since 1.0
 */
@Getter
public enum ServeStatusEnum {
    /**
     * 待开通
     */
    TO_BE_OPENED(1, "待开通"),
    /**
     * 开通中
     */
    OPENING(2, "开通中"),
    /**
     * 已开通
     */
    OPENED(3, "已开通"),
    /**
     * 服务中
     */
    IN_SERVICE(3, "服务中"),
    /**
     * 服务完成
     */
    COMPLETED(3, "服务完成"),;

    private final Integer code;
    private final String msg;

    ServeStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
