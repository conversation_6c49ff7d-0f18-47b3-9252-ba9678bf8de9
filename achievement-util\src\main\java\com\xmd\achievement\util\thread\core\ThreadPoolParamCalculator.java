package com.xmd.achievement.util.thread.core;

/**
 * 线程池参数计算
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/19 下午2:44
 **/
public class ThreadPoolParamCalculator {

    /**
     * CPU核心数
     */
    protected final static int AVAILABLE_PROCESSORS = Runtime.getRuntime().availableProcessors();

    /**
     * 默认任务阻塞系数
     */
    public final static double DEFAULT_BLOCK_RATE = 0.9;

    /**
     * 通过任务类型获取核心线程数，综合型任务取默认阻塞系数
     * Ncpu /（1 - 阻塞系数）= Ncpu x (1 + W/C)，推导出：阻塞系数 = W / (W + C)，即阻塞系数 = 阻塞时间 /（阻塞时间 + 计算时间）
     *
     * @param blockRate 阻塞系数
     * @return int
     * <AUTHOR>
     * @date 2021/05/19 下午2:45
     * @version 1.0.0
     **/
    public static int getMaxThreadNum(Double blockRate) {
        return (int) Math.floor(AVAILABLE_PROCESSORS / (1 - blockRate));
    }
}