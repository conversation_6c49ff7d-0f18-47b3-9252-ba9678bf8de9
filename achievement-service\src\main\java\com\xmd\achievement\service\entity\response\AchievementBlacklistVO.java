package com.xmd.achievement.service.entity.response;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AchievementBlacklistVO implements Serializable {
    /**
     * 自增主键
     */
    private Long id;
    /**
     * 黑名单id
     */
    private Long blacklistId;
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单明细编号
     */
    private String orderInfoNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updateUserName;

}
