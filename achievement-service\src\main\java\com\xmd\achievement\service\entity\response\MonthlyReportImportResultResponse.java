package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 机构月报导入返回信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MonthlyReportImportResultResponse implements Serializable {
    private static final long serialVersionUID = -2840496908037390465L;

    /**
     * 总条数
     */
    @Schema(description = "总条数")
    private Integer totalCount;
    /**
     * 成功条数
     */
    @Schema(description = "成功条数")
    private Integer successCount;
    /**
     * 失败条数
     */
    @Schema(description = "失败条数")
    private Integer failedCount;
    /**
     * 失败日志码
     */
    @Schema(description = "失败日志码")
    private String code;
}
