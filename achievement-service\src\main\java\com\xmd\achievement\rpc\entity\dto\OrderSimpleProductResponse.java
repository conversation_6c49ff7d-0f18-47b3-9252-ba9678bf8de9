package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单商品信息
 * <AUTHOR>
 * @date 2024年11月11日 下午1:29
 */
@Data

public class OrderSimpleProductResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;
    /**
     * 订单商品明细id
     */

    private String orderProductId;

    /**
     * 订单商品编号
     */

    private String orderProductCode;

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 商品id
     */

    private Long productId;

    /**
     * 商品名称
     */

    private String productName;

    /**
     * 交付方式: 1-软件交付, 2-服务交付
     */

    private Integer deliveryMethod;

    /**
     * 基础价
     */

    private BigDecimal basePrice;

    /**
     * 折扣优惠金额
     */

    private BigDecimal discountAmount;

    /**
     * 签单金额
     */

    private BigDecimal signAmount;

    /**
     * 抵扣金额
     */

    private BigDecimal deductibleAmount;

    /**
     * 应付金额
     */

    private BigDecimal payableAmount;

    /**
     * 实付金额
     */

    private BigDecimal paidAmount;

    /**
     * 服务id
     */

    private Long serveId;

    /**
     * 服务编号
     */

    private String serveNo;

    /**
     * 折扣比例
     */

    private BigDecimal discountRate;

    /**
     * 协议id
     */

    private String agreementId;

    /**
     * 删除标记: 0-未删除|1-删除
     */

    private Integer deleteFlag;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 创建人id
     */

    private String createUserId;

    /**
     * 创建人名称
     */

    private String createUserName;

    /**
     * 修改时间
     */

    private Date updateTime;

    /**
     * 修改人id
     */

    private String updateUserId;

    /**
     * 修改人名称
     */

    private String updateUserName;

}
