package com.xmd.achievement.service.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmd.achievement.service.entity.page.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DailyReportQueryReq extends PageRequest {

    @ApiModelProperty("机构类型")
    private Integer type;

    @ApiModelProperty("组织机构id")
    private List<Long> organizationIds;

    @ApiModelProperty("市场类别id")
    private Long marketCategoryId;

    @ApiModelProperty("体系id")
    private Long systemId;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;
}
