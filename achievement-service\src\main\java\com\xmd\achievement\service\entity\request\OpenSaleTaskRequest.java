package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 销售任务开放接口 入参
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Schema(description = "销售任务开放接口 入参")
public class OpenSaleTaskRequest implements Serializable {
    private static final long serialVersionUID = -7915131859930719396L;

    @Schema(description = "商务月")
    private String businessMonth;

    @Schema(description = "机构id")
    private List<Long> orgIds;
}
