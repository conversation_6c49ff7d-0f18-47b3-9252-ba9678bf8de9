package com.xmd.achievement.service.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商务月表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "商务月保存请求参数")
public class BusinessMonthDto extends UpdateBusinessMonthDto implements Serializable {
    private static final long serialVersionUID = -8428618915473495735L;
    /**
     * 商务月
     */
    @Schema(description = "商务月")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "商务月必须是 YYYY-MM 格式")
    private String month;
    /**
     * 开始日期
     */
    @Schema(description = "商务月开始")
    @NotNull
    private Date startDate;

}