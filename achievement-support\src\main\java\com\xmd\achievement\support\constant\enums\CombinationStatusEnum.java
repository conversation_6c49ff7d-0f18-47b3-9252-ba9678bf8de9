package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/11:29
 * @since 1.0
 */
@Getter
public enum CombinationStatusEnum {
    /**
     * 启用
     */
    ENABLE(1, "启用"),

    /**
     * 禁用
     */
    DISABLE(2, "禁用");

    private final Integer code;
    private final String msg;

    CombinationStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
