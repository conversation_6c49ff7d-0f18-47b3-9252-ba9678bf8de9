package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;

import java.util.List;

/**
 * 业绩规格明细接口
 * <AUTHOR>
 * @date: 2024/12/19 09:56
 */
public interface IAchievementSpecDetailService {
    /**
     * 保存业绩规格明细
     * @param spec 规格明细实体
     * <AUTHOR>
     * @date: 2024/12/19 10:00
     * @version: 1.0.0
     */
    void save(AchievementSpecDetailModel spec);

    /**
     *  批量保存业绩规格明细
     * @param models 业绩规格明细实体
     */
    void saveBatch(List<AchievementSpecDetailModel> models);

    /**
     *  批量更新规格
     * @param models 业绩规格明细实体
     * <AUTHOR>
     * @date: 2024/12/24 14:44
     * @version: 1.0.0
     */
    void updateBatch(List<AchievementSpecDetailModel> models);

    List<AchievementSpecDetailModel> listByAchievementCategoryId(Long achievementCategoryId);

    void saveOrUpdateBatch(List<AchievementSpecDetailModel> specList);

    /**
     * 查询有效规格订单
     * @param orderId 订单id
     * @return List<AchievementSpecDetailModel>
     */
    List<AchievementSpecDetailModel> queryValidSpcList(Long orderId, Long productId,Integer installmentNum,String orderProductId);
}
