package com.xmd.achievement.async.job.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.async.job.entity.JobParam;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.handler.achievement.AchievementHandler;
import com.xmd.achievement.service.MqOrderPaymentInfoService;
import com.xmd.achievement.support.constant.enums.ProcessType;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/14:24
 * @since 1.0
 */
@Slf4j
@Component
public class MqOrderPaymentInfoJob {
    @Resource
    MqOrderPaymentInfoService mqOrderPaymentInfoService;

    @Resource
    private AchievementHandler achievementHandler;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.MQ_ORDER_PAYMENT_INFO_JOB)
    public ReturnT<String> jobHandler(String param) {
        JobParam jobParam = JSON.parseObject(param, JobParam.class);
        extracted(jobParam);
        return ReturnT.SUCCESS;
    }

    private void extracted(JobParam jobParam) {
        List<MqOrderPaymentInfoModel> models = mqOrderPaymentInfoService.queryExcutTask();
        if (ObjectUtil.isEmpty(models)) {
            return;
        }
        log.info("执行MqOrderPaymentInfoJob任务Start...");
        boolean flag = false;
        if(null != jobParam && null != jobParam.getSyncAll() && jobParam.getSyncAll()){
            flag = Boolean.TRUE;
        }
        for (MqOrderPaymentInfoModel model : models) {
            try {
                achievementHandler.processAchievement(model);
            } catch (Exception e) {
                log.error("MqOrderPaymentInfoJob任务失败，TaskId:{},失败原因：", model.getTaskId(), e);
                if(flag){
                    flag = Boolean.FALSE;
                }
                break;
            }
        }
        log.info("执行MqOrderPaymentInfoJob任务End...");
        if(flag){
            this.extracted(jobParam);
        }

    }
}
