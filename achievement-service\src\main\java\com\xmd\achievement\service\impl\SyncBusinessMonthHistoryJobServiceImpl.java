package com.xmd.achievement.service.impl;

import com.xmd.achievement.async.job.entity.JobParam;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.entity.SpecCombinationDetailModel;
import com.xmd.achievement.dao.entity.SpecCombinationModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IBusinessMonthRepository;
import com.xmd.achievement.dao.repository.ISpecCombinationDetailRepository;
import com.xmd.achievement.dao.repository.ISpecCombinationRepository;
import com.xmd.achievement.handler.achievement.AchievementHandler;
import com.xmd.achievement.service.SyncBusinessMonthHistoryJobService;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import com.xmd.achievement.web.config.ProductAchievementConfig;
import com.xmd.achievement.web.util.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncBusinessMonthHistoryJobServiceImpl implements SyncBusinessMonthHistoryJobService {

    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    private IBusinessMonthRepository businessMonthRepository;

    @Resource
    private ISpecCombinationRepository specCombinationRepository;

    @Resource
    private ISpecCombinationDetailRepository specCombinationDetailRepository;

    @Resource
    private ProductAchievementConfig productAchievementConfig;

    @Resource
    private AchievementHandler achievementHandler;


    @Override
    public void syncBusinessMonthHistory() {
        //同步商务月
        syncBusinessMonth();

        //同步组合规格详情
        syncSpecCombination();
    }

    @Override
    public void syncAdvertisingPassHistory(JobParam jobParam) {
        //查询中企
        String websiteName = productAchievementConfig.getWebsiteName();

        String website = productAchievementConfig.getWebsite();
        List<String> productIdList = Arrays.asList(website.split(","));

        // 获取当前时间并减去10分钟
        LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(10);
        // 将LocalDateTime转换为Date
        Date startDate = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        String startDateStr = DateUtils.formatDate(startDate, "yyyy-MM-dd HH:mm:ss");

        Date endDate = new Date();
        String endDateStr = DateUtils.formatDate(endDate, "yyyy-MM-dd HH:mm:ss");
        List<Integer> saleTypeList = new ArrayList<>();
        Integer saleOpenType = OrderSaleTypeEnum.OPEN.getType();
        Integer saleUpgradeType = OrderSaleTypeEnum.UPGRADE.getType();
        saleTypeList.add(saleUpgradeType);
        saleTypeList.add(saleOpenType);

        List<AchievementProductDetailModel> achievementProductDetailModelList = new ArrayList<>();


        if(null != jobParam && jobParam.getSyncAll()){
            //同步全量
            startDateStr = "2024-11-01 00:00:00";
            achievementProductDetailModelList = achievementProductDetailRepository.selectListByDateAndName(startDateStr,endDateStr,websiteName,saleTypeList,productIdList);
        }else{
            achievementProductDetailModelList = achievementProductDetailRepository.selectListByDateAndName(startDateStr,endDateStr,websiteName,saleTypeList,productIdList);
        }
        XxlJobLogger.log("syncAdvertisingPassHistory startDateStr :{}，endDateStr：{}",startDateStr,endDateStr);
        if(CollectionUtils.isEmpty(achievementProductDetailModelList)){
            return;
        }
        achievementProductDetailModelList = achievementProductDetailModelList.stream().filter(ach -> ach.getInstallmentNum() <= 1 && ach.getPaidAmount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        Map<Integer, List<AchievementProductDetailModel>> bySaleTypeMap = achievementProductDetailModelList.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getSaleType));

        List<AchievementProductDetailModel> achievementProductDetailModelListOpen= bySaleTypeMap.get(OrderSaleTypeEnum.OPEN.getType());
        if(!CollectionUtils.isEmpty(achievementProductDetailModelListOpen)){
            for (AchievementProductDetailModel achievementProductDetailModel : achievementProductDetailModelListOpen) {
                achievementHandler.syncAdvertisingPassHistory(achievementProductDetailModel,saleOpenType);
            }
        }

        List<AchievementProductDetailModel> achievementProductDetailModelListUpgrade = bySaleTypeMap.get(OrderSaleTypeEnum.UPGRADE.getType());
        if(!CollectionUtils.isEmpty(achievementProductDetailModelListUpgrade)){
            for (AchievementProductDetailModel achievementProductDetailModel : achievementProductDetailModelListUpgrade) {
                achievementHandler.syncAdvertisingPassHistory(achievementProductDetailModel,saleUpgradeType);
            }
        }
    }

    private void syncSpecCombination() {
        //查询所有的组合规格
        List<SpecCombinationModel> specCombinationModelList = specCombinationRepository.selectAllSpecCombination();
        //过滤政策性成本大于等于0
        List<SpecCombinationModel> specCombinationModelFilter = specCombinationModelList.stream().filter(s -> s.getNewPolicyCost().compareTo(BigDecimal.ZERO) >= 0).collect(Collectors.toList());

        List<Long> combinationIdList = specCombinationModelFilter.stream().map(SpecCombinationModel::getCombinationId).collect(Collectors.toList());
        //通过组合id查询组合规格详情
        List<SpecCombinationDetailModel> specCombinationDetailModelList = specCombinationDetailRepository.selectSpecCombinationDetailList(combinationIdList);

        Map<Long, List<SpecCombinationDetailModel>> combinationIdByEntityMap = specCombinationDetailModelList.stream().collect(Collectors.groupingBy(SpecCombinationDetailModel::getCombinationId));

        List<SpecCombinationDetailModel> updateList = new ArrayList<>();

        //同步历史数政策性成本
        for (SpecCombinationModel specCombinationModel : specCombinationModelFilter) {
            List<SpecCombinationDetailModel> specCombinationDetailModels = combinationIdByEntityMap.get(specCombinationModel.getCombinationId());

            for (SpecCombinationDetailModel specCombinationDetailModel : specCombinationDetailModels) {
                SpecCombinationDetailModel specCombinationDetailModelUpdate = new SpecCombinationDetailModel();
                specCombinationDetailModelUpdate.setId(specCombinationDetailModel.getId());
                specCombinationDetailModelUpdate.setUpdateTime(new Date());

                specCombinationDetailModelUpdate.setNewPolicyCost(specCombinationModel.getNewPolicyCost());
                specCombinationDetailModelUpdate.setRenewalPolicyCost(specCombinationModel.getRenewalPolicyCost());
                specCombinationDetailModelUpdate.setUpgradePolicyCost(specCombinationModel.getUpgradePolicyCost());
                specCombinationDetailModelUpdate.setAdditionalPolicyCost(specCombinationModel.getAdditionalPolicyCost());
                updateList.add(specCombinationDetailModelUpdate);
            }
        }
        if(!CollectionUtils.isEmpty(updateList)){
            specCombinationDetailRepository.updateBatchById(updateList);
        }
    }

    private void syncBusinessMonth() {
        //查询所有的商品业绩流水
        List<AchievementProductDetailModel> achievementProductDetailModelList = achievementProductDetailRepository.selectAchievementProductByAchievementIds(null,1);
        //查询所有的商务月
        List<BusinessMonthModel> businessMonthModels = businessMonthRepository.selectAllBusinessMonthList();

        List<AchievementProductDetailModel> updateList = new ArrayList<>();

        //同步历史数据商务月
        for (BusinessMonthModel businessMonthModel : businessMonthModels) {
            Date startDate = businessMonthModel.getStartDate();
            Date endDate = businessMonthModel.getEndDate();

            for (AchievementProductDetailModel achievementProductDetailModel : achievementProductDetailModelList) {
                Date createTime = achievementProductDetailModel.getCreateTime();

                if((createTime.after(startDate) || createTime.equals(startDate)) && (createTime.before(endDate) || createTime.equals(endDate))){
                    AchievementProductDetailModel achievementProductDetailModelUpdate = new AchievementProductDetailModel();
                    achievementProductDetailModelUpdate.setId(achievementProductDetailModel.getId());
                    achievementProductDetailModelUpdate.setBusinessMonth(businessMonthModel.getMonth());
                    updateList.add(achievementProductDetailModelUpdate);
                }
            }

        }
        if(!CollectionUtils.isEmpty(updateList)){
            //achievementProductDetailRepository.updateBatchById(updateList);
        }
    }
}
