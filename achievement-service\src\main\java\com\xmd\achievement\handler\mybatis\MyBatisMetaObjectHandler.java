package com.xmd.achievement.handler.mybatis;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.xmd.achievement.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MyBatisMetaObjectHandler extends HttpFilter implements MetaObjectHandler {

    private static final ThreadLocal<HttpServletRequest> THREAD_LOCAL = new ThreadLocal<>();


    @Override
    public void insertFill(MetaObject metaObject) {
        if (ObjectUtil.isNotEmpty(UserContext.getCurrentUserInfo())) {
            this.setFieldValByName("createUserId", UserContext.getCurrentUserInfo().getUserId(), metaObject);
            this.setFieldValByName("createUserName", UserContext.getCurrentUserInfo().getName(), metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (ObjectUtil.isNotEmpty(UserContext.getCurrentUserInfo())) {
            this.setFieldValByName("updateUserId", UserContext.getCurrentUserInfo().getUserId(), metaObject);
            this.setFieldValByName("updateUserName", UserContext.getCurrentUserInfo().getName(), metaObject);
        }
    }

    @Override
    public void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
        THREAD_LOCAL.set(request);
        chain.doFilter(request, response);
    }
}