package com.xmd.achievement.util.encrypt;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * RSA算法生成公私钥
 * 注意，这里加密内容不能超过117bit，解密内容不能超过128bit
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/1/11 4:58 下午
 */
public class RSAUtil {

    private final static Logger LOGGER = LoggerFactory.getLogger(RSAUtil.class);

    /**
     * 获取KeyFactory实例的算法
     */
    private final static String KEY_ALGORITHM = "RSA";
    /**
     * 生成摘要的算法
     */
    private final static String MESSAGE_DIGEST = "SHA";
    /**
     * 生成数字签名的算法
     */
    private final static String SIGNATURE_ALGORITHM = "SHA1withRSA";
    /**
     * 密钥随机数的算法
     */
    private final static String SECURE_RANDOM_ALGORITHM = "SHA1PRNG";
    /**
     * Cipher的算法
     */
    private final static String CIPHER_ALGORITHM = "RSA/ECB/PKCS1Padding";
    /**
     * 密钥随机数的算法提供方
     */
    private final static String SECURE_RANDOM_PROVIDER = "SUN";
    /**
     * 公私钥生成密钥位数
     * 定义为1024的原因是：
     * 将公私钥进行AES加密存储，AES加密后的长度取决于key、算法长度、加密内容，但是存储长度受限，设置为一个合理值
     *
     * @see AESSupport
     */
    private final static Integer PRIVATE_KEY_LENGTH = 1024;
    /**
     * 指定字符集
     */
    private static final String CHARSET = "UTF-8";
    /**
     * 公钥键
     */
    public static final String PUBLIC_KEY = "RSAPublicKey";
    /**
     * 私钥键
     */
    public static final String PRIVATE_KEY = "RSAPrivateKey";

    /**
     * RSA最大加密明文大小
     */
    private static final int MAX_ENCRYPT_BLOCK = 117;

    /**
     * RSA最大解密密文大小
     */
    private static final int MAX_DECRYPT_BLOCK = 128;

    /**
     * 数字签名(使用私钥加密数据)
     *
     * @param privateKey    私钥
     * @param messageDigest 摘要（要签名的数据，一般应是数字摘要）
     * @return 签名 byte[]
     */
    public static byte[] sign(String privateKey, byte[] messageDigest) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            PrivateKey priKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey)));
            Signature sig = Signature.getInstance(SIGNATURE_ALGORITHM);
            sig.initSign(priKey);
            sig.update(messageDigest);
            return sig.sign();
        } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
            LOGGER.error("数字签名异常NoSuchAlgorithmException:", noSuchAlgorithmException);
        } catch (InvalidKeySpecException invalidKeySpecException) {
            LOGGER.error("数字签名异常InvalidKeySpecException:", invalidKeySpecException);
        } catch (InvalidKeyException invalidKeySpecException) {
            LOGGER.error("数字签名异常InvalidKeyException:", invalidKeySpecException);
        } catch (SignatureException signatureException) {
            LOGGER.error("数字签名异常InvalidKeyException:", signatureException);
        }
        return null;
    }

    /**
     * 验证数字签名
     *
     * @param publicKey           公钥
     * @param sourceMessageDigest 原文的数字摘要
     * @param sign                签名（对原文的数字摘要的签名）
     * @return 是否证实 boolean
     */
    public static boolean verify(String publicKey, byte[] sourceMessageDigest, byte[] sign) {
        try {
            PublicKey pubKey = KeyFactory.getInstance(KEY_ALGORITHM).generatePublic(new X509EncodedKeySpec(Base64.decodeBase64(publicKey)));
            Signature sig = Signature.getInstance(SIGNATURE_ALGORITHM);
            sig.initVerify(pubKey);
            sig.update(sourceMessageDigest);
            return sig.verify(sign);
        } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
            LOGGER.error("数字签名验证异常NoSuchAlgorithmException:", noSuchAlgorithmException);
        } catch (InvalidKeySpecException invalidKeySpecException) {
            LOGGER.error("数字签名验证异常InvalidKeySpecException:", invalidKeySpecException);
        } catch (InvalidKeyException invalidKeySpecException) {
            LOGGER.error("数字签名验证异常InvalidKeyException:", invalidKeySpecException);
        } catch (SignatureException signatureException) {
            LOGGER.error("数字签名验证异常InvalidKeyException:", signatureException);
        }
        return false;
    }

    /**
     * 获取公私钥对
     *
     * @return
     */
    public static Map<String, String> giveKeyPair() {
        try {
            //创建密钥对
            KeyPair newKeyPair = creatPPK();
            //获取公私钥对
            Map<String, String> rsaKeys = new HashMap<>(2);
            rsaKeys.put(PUBLIC_KEY, new String(Base64.encodeBase64(newKeyPair.getPublic().getEncoded())));
            rsaKeys.put(PRIVATE_KEY, new String(Base64.encodeBase64(newKeyPair.getPrivate().getEncoded())));
            return rsaKeys;
        } catch (NoSuchAlgorithmException algorithmException) {
            LOGGER.error("生成公私钥算法异常", algorithmException);
        } catch (NoSuchProviderException providerException) {
            LOGGER.error("生成公私钥算法提供方异常", providerException);
        }
        return null;
    }

    /**
     * 新建密钥对
     *
     * @return KeyPair对象
     */
    private static KeyPair creatPPK() throws NoSuchAlgorithmException, NoSuchProviderException {
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        SecureRandom random = SecureRandom.getInstance(SECURE_RANDOM_ALGORITHM, SECURE_RANDOM_PROVIDER);
        random.setSeed(System.currentTimeMillis());
        keyGen.initialize(PRIVATE_KEY_LENGTH, random);
        KeyPair myPair = keyGen.generateKeyPair();
        return myPair;
    }

    /**
     * 公钥加密数据 (分块处理)
     *
     * @param publicKey 公钥
     * @param content   原数据
     * @return 加密数据
     */
    public static String encryptByPublicKey(String publicKey, String content) {
        try {
            byte[] data = content.getBytes();
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(Base64.decodeBase64(publicKey)));
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            int keySize = ((RSAPublicKey) pubKey).getModulus().bitLength() / 8;
            int inputLength = data.length;
            int offset = 0;
            byte[] outputBuffer = new byte[0];

            while (offset < inputLength) {
                int length = Math.min(keySize - 11, inputLength - offset);
                byte[] chunk = new byte[length];
                System.arraycopy(data, offset, chunk, 0, length);
                byte[] encryptedChunk = cipher.doFinal(chunk);
                byte[] newOutputBuffer = new byte[outputBuffer.length + encryptedChunk.length];
                System.arraycopy(outputBuffer, 0, newOutputBuffer, 0, outputBuffer.length);
                System.arraycopy(encryptedChunk, 0, newOutputBuffer, outputBuffer.length, encryptedChunk.length);
                outputBuffer = newOutputBuffer;
                offset += length;
            }
            return Base64.encodeBase64String(outputBuffer).replaceAll("[+]", "@");
        } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
            LOGGER.error("公钥加密异常NoSuchAlgorithmException:", noSuchAlgorithmException);
        } catch (InvalidKeySpecException invalidKeySpecException) {
            LOGGER.error("公钥加密异常InvalidKeySpecException:", invalidKeySpecException);
        } catch (NoSuchPaddingException invalidKeySpecException) {
            LOGGER.error("公钥加密异常NoSuchPaddingException:", invalidKeySpecException);
        } catch (InvalidKeyException invalidKeySpecException) {
            LOGGER.error("公钥加密异常InvalidKeyException:", invalidKeySpecException);
        } catch (IllegalBlockSizeException illegalBlockSizeException) {
            LOGGER.error("公钥加密异常IllegalBlockSizeException:", illegalBlockSizeException);
        } catch (BadPaddingException badPaddingException) {
            LOGGER.error("公钥加密异常BadPaddingException:", badPaddingException);
        } catch (Exception exception) {
            LOGGER.error("公钥加密异常Exception:", exception);
        }
        return null;
    }

    /**
     * 私钥解密数据  （分块处理）
     *
     * @param privateKey 私钥
     * @param content    已加密数据
     * @return 解密数据
     */
    public static String decryptByPrivateKey(String privateKey, String content) {
        try {
            content = content.replaceAll("@", "+");
            byte[] data = Base64.decodeBase64(content);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            PrivateKey priKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey)));
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, priKey);
            int keySize = ((RSAPrivateKey) priKey).getModulus().bitLength() / 8;
            int inputLength = data.length;
            int offset = 0;
            byte[] outputBuffer = new byte[0];

            while (offset < inputLength) {
                int length = Math.min(keySize, inputLength - offset);
                byte[] chunk = new byte[length];
                System.arraycopy(data, offset, chunk, 0, length);
                byte[] decryptedChunk = cipher.doFinal(chunk);
                byte[] newOutputBuffer = new byte[outputBuffer.length + decryptedChunk.length];
                System.arraycopy(outputBuffer, 0, newOutputBuffer, 0, outputBuffer.length);
                System.arraycopy(decryptedChunk, 0, newOutputBuffer, outputBuffer.length, decryptedChunk.length);
                outputBuffer = newOutputBuffer;
                offset += length;
            }
            return new String(outputBuffer);
        } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
            LOGGER.error("私钥解密异常NoSuchAlgorithmException:", noSuchAlgorithmException);
        } catch (InvalidKeySpecException invalidKeySpecException) {
            LOGGER.error("私钥解密异常InvalidKeySpecException:", invalidKeySpecException);
        } catch (NoSuchPaddingException invalidKeySpecException) {
            LOGGER.error("私钥解密异常NoSuchPaddingException:", invalidKeySpecException);
        } catch (InvalidKeyException invalidKeySpecException) {
            LOGGER.error("私钥解密异常InvalidKeyException:", invalidKeySpecException);
        } catch (IllegalBlockSizeException illegalBlockSizeException) {
            LOGGER.error("私钥解密异常IllegalBlockSizeException:", illegalBlockSizeException);
        } catch (BadPaddingException badPaddingException) {
            LOGGER.error("私钥解密异常BadPaddingException:", badPaddingException);
        } catch (Exception exception) {
            LOGGER.error("私钥解密异常Exception:", exception);
        }
        return null;
    }


    /**
     * 公钥解密
     *
     * @param publicKey 公钥打
     * @param data      要解密的数据
     * @return 解密数据
     */
    public static String decryptByPublicKey(String publicKey, String data) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(Base64.decodeBase64(publicKey)));
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.DECRYPT_MODE, pubKey);
            byte[] bytes = cipher.doFinal(Base64.decodeBase64(data.getBytes(CHARSET)));
            return new String(bytes);
        } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
            LOGGER.error("公钥解密异常NoSuchAlgorithmException:", noSuchAlgorithmException);
        } catch (InvalidKeySpecException invalidKeySpecException) {
            LOGGER.error("公钥解密异常InvalidKeySpecException:", invalidKeySpecException);
        } catch (NoSuchPaddingException invalidKeySpecException) {
            LOGGER.error("公钥解密异常NoSuchPaddingException:", invalidKeySpecException);
        } catch (InvalidKeyException invalidKeySpecException) {
            LOGGER.error("公钥解密异常InvalidKeyException:", invalidKeySpecException);
        } catch (IllegalBlockSizeException illegalBlockSizeException) {
            LOGGER.error("公钥解密异常IllegalBlockSizeException:", illegalBlockSizeException);
        } catch (BadPaddingException badPaddingException) {
            LOGGER.error("公钥解密异常BadPaddingException:", badPaddingException);
        } catch (UnsupportedEncodingException unsupportedEncodingException) {
            LOGGER.error("公钥解密异常UnsupportedEncodingException:", unsupportedEncodingException);
        }
        return null;
    }

    /**
     * 计算字符串的SHA数字摘要，以byte[]形式返回
     */
    public static byte[] msgDigestSHA(String source) {
        try {
            MessageDigest thisMD = MessageDigest.getInstance(MESSAGE_DIGEST);
            byte[] digest = thisMD.digest(source.getBytes(CHARSET));
            return digest;
        } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
            LOGGER.error("计算字符串(" + source + ")的SHA数字摘要算法异常", noSuchAlgorithmException);
        } catch (UnsupportedEncodingException unsupportedEncodingException) {
            LOGGER.error("计算字符串(" + source + ")的SHA数字摘要异常UnsupportedEncodingException:", unsupportedEncodingException);
        }
        return null;
    }

    public static String generateRandomCharacters(int count) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        // 定义数字和字母的字符集
        String characters = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

        for (int i = 0; i < count; i++) {
            // 从字符集中随机选择一个字符
            int randomIndex = random.nextInt(characters.length());
            char randomCharacter = characters.charAt(randomIndex);
            sb.append(randomCharacter);
        }

        // 输出随机生成的字符
        return sb.toString();
    }
}
