package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.OrganizationDailyReportModel;
import com.xmd.achievement.dao.mapper.OrganizationDailyReportMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IOrganizationDailyReportRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 机构日报 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Service
@Slf4j
public class OrganizationDailyReportRepositoryImpl extends ServiceImpl<OrganizationDailyReportMapper,OrganizationDailyReportModel> implements IOrganizationDailyReportRepository {

@Resource
private OrganizationDailyReportMapper organizationDailyReportMapper;

    /**
     * 批量插入或者更新日报
     * @param list
     */
    @Override
    public void batchInsertOrUpdate(List<OrganizationDailyReportModel> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        organizationDailyReportMapper.batchInsertOrUpdate(list);
    }

    @Override
    public List<OrganizationDailyReportModel> listByTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<OrganizationDailyReportModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(OrganizationDailyReportModel::getCreateTime, startTime);
        queryWrapper.le(OrganizationDailyReportModel::getCreateTime, endTime);
        return organizationDailyReportMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteByTime(Date startTime) {

    }
}