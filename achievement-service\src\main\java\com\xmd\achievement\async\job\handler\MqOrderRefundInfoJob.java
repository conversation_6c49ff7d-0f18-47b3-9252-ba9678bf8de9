package com.xmd.achievement.async.job.handler;

import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.handler.achievement.AchievementRefundHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 退款业绩处理任务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
public class MqOrderRefundInfoJob {

    @Resource
    private AchievementRefundHandler achievementRefundHandler;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.MQ_ORDER_REFUND_INFO_JOB)
    public ReturnT<String> jobHandler(String param) {
        execute();
        return ReturnT.SUCCESS;
    }

    private void execute() {
        log.info("执行MqOrderRefundInfoJob任务Start...");
        try {
            achievementRefundHandler.processPendingRefundTasks();
        } catch (Exception e) {
            log.error("MqOrderRefundInfoJob任务失败,失败原因：", e);
        }
        log.info("执行MqOrderRefundInfoJob任务End...");
    }
}
