package com.xmd.achievement.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.dto.SearchAchievementDetailsDto;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.BaseModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IMqOrderPaymentInfoRepository;
import com.xmd.achievement.dao.repository.IMqServeInprogressInfoRepository;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContextV4;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductResponse;
import com.xmd.achievement.rpc.entity.dto.OrgInfoResponse;
import com.xmd.achievement.service.IAchievementProductDetailService;
import com.xmd.achievement.service.entity.dto.ProductedDataAddDto;
import com.xmd.achievement.service.entity.request.ThirdExcelPerformanceRequest;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;
import com.xmd.achievement.util.enums.MainSplitPersonEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import com.xmd.achievement.util.enums.StatusEnum;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * 业绩产品明细接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class IAchievementProductDetailServiceImpl implements IAchievementProductDetailService {
    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    private InnerService innerService;

    @Resource
    private IMqServeInprogressInfoRepository inProgressInfoRepository;

    @Resource
    private CalculateCustomerContextV4 calculateCustomerContextv4;

    @Resource
    private IMqOrderPaymentInfoRepository mqOrderPaymentInfoRepository;

    @Autowired
    @Qualifier("asyncTaskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AchievementProductDetailModel ach) {
        achievementProductDetailRepository.save(ach);
    }

    @Override
    public void saveBatch(List<AchievementProductDetailModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        achievementProductDetailRepository.saveBatch(models, models.size());
    }

    @Override
    public void saveOrUpdateBatch(List<AchievementProductDetailModel> models) {
        if (CollectionUtils.isNotEmpty(models)) {
            // 方便添加索引
            for (AchievementProductDetailModel model : models) {
                model.setThirdAchievementId(String.valueOf(model.getAchievementId()));
            }
            achievementProductDetailRepository.saveOrUpdateBatch(models, models.size());
        }
    }

    @Override
    public List<AchievementProductDetailModel> listByOrderId(Long orderId) {
        return null == orderId ? Collections.emptyList() : achievementProductDetailRepository.list(new LambdaQueryWrapper<AchievementProductDetailModel>().eq(AchievementProductDetailModel::getOrderId, orderId));
    }

    @Override
    public List<AchievementProductDetailModel> listByCustomerId(String customerId) {
        return StringUtils.isEmpty(customerId) ? Collections.emptyList() : achievementProductDetailRepository.list(new LambdaQueryWrapper<AchievementProductDetailModel>()
                .eq(AchievementProductDetailModel::getCustomerId, customerId)
                .eq(AchievementProductDetailModel::getSaleType, SaleTypeEnum.NEW_OPEN.getType())
                .eq(AchievementProductDetailModel::getMainSplitPerson, MainSplitPersonEnum.MAIN.getCode())
                .eq(AchievementProductDetailModel::getStatus, AchStatus.VALID.getType())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
    }

    @Override
    public List<AchievementProductDetailModel> listByCreateTime(Date startTime, Date endTime) {
        return null == startTime || null == endTime
                ? Collections.emptyList()
                : achievementProductDetailRepository.list(
                new LambdaQueryWrapper<AchievementProductDetailModel>()
                        .le(AchievementProductDetailModel::getCreateTime, startTime)
                        .gt(AchievementProductDetailModel::getCreateTime, endTime)
                        .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
    }

    @Override
    public List<AchievementProductDetailModel> getByOrderIdAndProductId(Long orderId, Long productId, Integer mainSplitPerson,Integer installmentNum,String orderProductId) {
        return null == orderId || null == productId ? Collections.emptyList() : achievementProductDetailRepository.list(
                new LambdaQueryWrapper<AchievementProductDetailModel>()
                        .eq(AchievementProductDetailModel::getOrderId, orderId)
                        .eq(AchievementProductDetailModel::getProductId, productId)
                        .eq(AchievementProductDetailModel::getInstallmentNum,installmentNum)
                        .eq(StringUtils.isNotEmpty(orderProductId) && !NumberConstants.STR_ZERO.equals(orderProductId),AchievementProductDetailModel::getOrderProductId,orderProductId)
                        .eq(AchievementProductDetailModel::getMainSplitPerson, mainSplitPerson)
                        .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
    }

    @Override
    public List<AchievementProductDetailModel> selectListByPayTime(String customerId, Date startTime, Date endTime, OrderRuleTypeEnum orderRuleTypeEnum) {
        return achievementProductDetailRepository.list(new LambdaQueryWrapper<AchievementProductDetailModel>()
                .eq(AchievementProductDetailModel::getCustomerId, customerId)
                .eq(AchievementProductDetailModel::getSaleType, SaleTypeEnum.NEW_OPEN.getCode())
                .eq(AchievementProductDetailModel::getMainSplitPerson, MainSplitPersonEnum.MAIN.getCode())
                .eq(AchievementProductDetailModel::getStatus, StatusEnum.VALID.getCode())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .ge(ObjectUtil.isNotEmpty(startTime), AchievementProductDetailModel::getPaymentTime, startTime)
                .le(ObjectUtil.isNotEmpty(endTime), AchievementProductDetailModel::getPaymentTime, endTime)
                .orderByAsc(OrderRuleTypeEnum.ASC.equals(orderRuleTypeEnum), AchievementProductDetailModel::getPaymentTime)
                .orderByDesc(OrderRuleTypeEnum.DESC.equals(orderRuleTypeEnum), AchievementProductDetailModel::getPaymentTime));
    }

    @Override
    public void importUpdateZhongXiaoTime(List<ThirdExcelPerformanceRequest> requests) {
        for (ThirdExcelPerformanceRequest request : requests) {
            achievementProductDetailRepository.update(
                    null,
                    new LambdaUpdateWrapper<AchievementProductDetailModel>()
                            .eq(AchievementProductDetailModel::getThirdAchievementId, request.getThirdId())
                            .set(AchievementProductDetailModel::getSignedTime, request.getSingingDate())
                            .set(AchievementProductDetailModel::getPaymentTime, request.getToAccountDate())
                            .set(AchievementProductDetailModel::getCreateTime, request.getDbInsertTime()));
        }
    }
/*
    @Override
    public Boolean productedDataInsert(ProductedDataAddRequest reques) {

        for (ProductedDataAddDto response : reques.getProductedDataList()) {
            try {
                OrderSimpleInfoResponse orderSimpleInfo;
                String key = "PRODUCTEDDATAINSERT:" + response.getOrderId().toString();
                if (ObjectUtil.isEmpty(redisOps.get(key))) {
                    orderSimpleInfo = innerService.getOrderSimpleInfo(response.getOrderId());
                    redisOps.set(key, JSONUtil.toJsonStr(orderSimpleInfo), 10, TimeUnit.MILLISECONDS);
                } else {
                    orderSimpleInfo = JSONUtil.toBean(redisOps.get(key), OrderSimpleInfoResponse.class);
                }

                Map<String, OrderSimpleProductResponse> productMap = orderSimpleInfo.getProductResponseList()
                        .stream()
                        .collect(Collectors.toMap(
                                OrderSimpleProductResponse::getOrderProductCode,
                                i -> i
                        ));

                OrderSimpleProductResponse productResponse = productMap.get(response.getProductCode());
                if (ObjectUtil.isEmpty(productMap.get(response.getProductCode()))) {
                    continue;
                }

                Long productId = productResponse.getProductId();

                //校验是否存在支付中的数据
                List<MqServeInprogressInfoModel> inprogressInfoModels = inProgressInfoRepository.list(
                        new LambdaQueryWrapper<MqServeInprogressInfoModel>()
                                .eq(MqServeInprogressInfoModel::getOrderId, response.getOrderId())
                                .eq(MqServeInprogressInfoModel::getProductId, productId));
                if (ObjectUtil.isNotEmpty(inprogressInfoModels)) {
                    log.info("orderId:{},ProductCode:{}已清洗生产完成计费节点数据，不处理", response.getOrderId(), response.getProductCode());
                    continue;
                }

                //判断是否已经生成支付完成的数据
                List<AchievementProductDetailModel> detailModels = achievementProductDetailRepository.list(
                        new LambdaQueryWrapper<AchievementProductDetailModel>()
                                .eq(AchievementProductDetailModel::getOrderId, response.getOrderId())
                                .eq(AchievementProductDetailModel::getProductId, productId));
                if (ObjectUtil.isEmpty(detailModels)) {
                    log.info("orderId:{},ProductCode:{},洗生产完成计费节点数据失败，支付完成数据未生成业绩", response.getOrderId(), response.getProductCode());
                    throw new BusinessException(String.format("orderId:%s,ProductCode:%s,洗生产完成计费节点数据失败，支付完成数据未生成业绩", response.getOrderId(), response.getProductCode()));
                }

                //放入MQ中
                MqServeInprogressInfoModel inprogressInfoModel = new MqServeInprogressInfoModel();
                inprogressInfoModel.setTaskId(IdUtil.getSnowflake().nextId());
                inprogressInfoModel.setServeNo("LISHI00001");
                inprogressInfoModel.setOrderId(response.getOrderId());
                inprogressInfoModel.setProductId(productId);
                inprogressInfoModel.setTaskStatus(TaskStatusEnum.NO.getCode().toString());
                inProgressInfoRepository.save(inprogressInfoModel);
                log.info("orderId:{},ProductCode:{},洗生产完成计费节点数据成功", response.getOrderId(), response.getProductCode());
            } catch (Exception ex) {
                log.error("orderId:{},ProductCode:{},清洗生产完成计费节点数据失败", response.getOrderId(), response.getProductCode());
            }
        }
        return true;
    }*/


    @Override
    public Boolean productedDataInsert(List<ProductedDataAddDto> reques) {
        Map<String, List<ProductedDataAddDto>> productMap = reques
                .stream()
                .collect(Collectors.groupingBy(
                        ProductedDataAddDto::getOrderNo
                ));

        productMap.forEach((orderNo, productCodeList) -> {
            OrderSimpleInfoResponse orderSimpleInfo = innerService.getOrderSimpleInfoByOrderNo(orderNo);
            Map<String, OrderSimpleProductResponse> orderProductMap = orderSimpleInfo.getProductResponseList()
                    .stream()
                    .collect(Collectors.toMap(
                            OrderSimpleProductResponse::getOrderProductCode,
                            i -> i
                    ));
            for (ProductedDataAddDto dto : productCodeList) {
                try {
                    OrderSimpleProductResponse productResponse = orderProductMap.get(dto.getProductCode());
                    if (ObjectUtil.isEmpty(productResponse)) {
                        log.error("orderNo:{},ProductCode:{},清洗生产完成计费节点数据，原始订单不存在productCode，不处理", orderNo, dto.getProductCode());
                        continue;
                    }

                    Long productId = productResponse.getProductId();
                    Long orderId = orderSimpleInfo.getOrderId();
                    String orderProductCode = productResponse.getOrderProductCode();

                    //校验是否存在支付中的数据
                    List<MqOrderPaymentInfoModel> inprogressInfoModels = mqOrderPaymentInfoRepository.list(
                            new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                                    .eq(MqOrderPaymentInfoModel::getOrderId, orderId)
                                    .eq(MqOrderPaymentInfoModel::getTaskStatus,NumberConstants.INTEGER_VALUE_2)
                                    .eq(MqOrderPaymentInfoModel::getProductId, productId));
                    if (ObjectUtil.isNotEmpty(inprogressInfoModels)) {
                        log.info("orderNo:{},ProductCode:{}已清洗生产完成计费节点数据，不处理", orderNo, dto.getProductCode());
                        continue;
                    }

                    //判断是否已经生成支付完成的数据
                    List<AchievementProductDetailModel> detailModels = achievementProductDetailRepository.list(
                            new LambdaQueryWrapper<AchievementProductDetailModel>()
                                    .eq(AchievementProductDetailModel::getOrderId, orderId)
                                    .eq(AchievementProductDetailModel::getProductId, productId)
                                    .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
                    if (ObjectUtil.isEmpty(detailModels)) {
                        log.info("orderNo:{},ProductCode:{},洗生产完成计费节点数据失败，支付完成数据未生成业绩", orderNo, dto.getProductCode());
                        throw new BusinessException(String.format("orderNo:%s,ProductCode:%s,洗生产完成计费节点数据失败，支付完成数据未生成业绩", orderNo, dto.getProductCode()));
                    }

                    //放入MQ中
                    MqOrderPaymentInfoModel model = new MqOrderPaymentInfoModel();
                    model.setTaskId(IdUtil.getSnowflake().nextId());
                    model.setServeNo("LISHI00001");
                    model.setOrderId(orderId);
                    model.setProductId(productId);
                    model.setTaskStatus(TaskStatusEnum.NO.getCode().toString());
                    model.setCreateTime(DateUtils.stringToDate("2025-04-30 20:00:00", DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss));
                    model.setUpdateTime(model.getCreateTime());
                    model.setPaymentTime(model.getCreateTime());
                    model.setCalculateType(2);
                    model.setOrderProductId(orderProductCode);
                    mqOrderPaymentInfoRepository.save(model);

//                    MqServeInprogressInfoModel inprogressInfoModel = new MqServeInprogressInfoModel();
//                    inprogressInfoModel.setTaskId(IdUtil.getSnowflake().nextId());
//                    inprogressInfoModel.setServeNo("LISHI00001");
//                    inprogressInfoModel.setOrderId(orderId);
//                    inprogressInfoModel.setProductId(productId);
//                    inprogressInfoModel.setTaskStatus(TaskStatusEnum.NO.getCode().toString());
//                    inProgressInfoRepository.save(inprogressInfoModel);
                    log.info("orderNo:{},ProductCode:{},洗生产完成计费节点数据成功", orderNo, dto.getProductCode());
                } catch (Exception ex) {
                    log.error("orderNo:{},ProductCode:{},清洗生产完成计费节点数据失败", orderNo, dto.getProductCode());
                }
            }
        });
        return true;
    }

    @Override
    public List<AchievementProductDetailModel> getNewCustomerInfo(String customerId, Date achievementCreateTime) {
        return achievementProductDetailRepository.list(new LambdaQueryWrapper<AchievementProductDetailModel>()
                .eq(AchievementProductDetailModel::getCustomerId, customerId)
                .eq(AchievementProductDetailModel::getCustomerType, CustomerType.NEW.getType())
                .eq(AchievementProductDetailModel::getSaleType, SaleTypeEnum.NEW_OPEN.getCode())
                .eq(AchievementProductDetailModel::getMainSplitPerson, MainSplitPersonEnum.MAIN.getCode())
                .eq(AchievementProductDetailModel::getStatus, StatusEnum.VALID.getCode())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .lt(AchievementProductDetailModel::getCreateTime, achievementCreateTime));
    }

    @Override
    public AchievementProductDetailModel firstAchievement(String customerId,Date achievementCreateTime) {
        List<AchievementProductDetailModel> list = achievementProductDetailRepository.list(new LambdaQueryWrapper<AchievementProductDetailModel>()
                .eq(AchievementProductDetailModel::getCustomerId, customerId)
                .eq(AchievementProductDetailModel::getSaleType, SaleTypeEnum.NEW_OPEN.getCode())
                .eq(AchievementProductDetailModel::getMainSplitPerson, MainSplitPersonEnum.MAIN.getCode())
                .eq(AchievementProductDetailModel::getStatus, StatusEnum.VALID.getCode())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .lt(AchievementProductDetailModel::getCreateTime, achievementCreateTime)
                .orderByAsc(AchievementProductDetailModel::getCreateTime)
        );
        return CollectionUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    public List<AchievementProductDetailModel> historyTotalMoney(String customerId, Date startTime, Date endTime, OrderRuleTypeEnum orderRuleTypeEnum) {
        return achievementProductDetailRepository.list(new LambdaQueryWrapper<AchievementProductDetailModel>()
                .eq(AchievementProductDetailModel::getCustomerId, customerId)
                .eq(AchievementProductDetailModel::getSaleType, SaleTypeEnum.NEW_OPEN.getCode())
                .eq(AchievementProductDetailModel::getMainSplitPerson, MainSplitPersonEnum.MAIN.getCode())
                .eq(AchievementProductDetailModel::getStatus, StatusEnum.VALID.getCode())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .ge(ObjectUtil.isNotEmpty(startTime), AchievementProductDetailModel::getCreateTime, startTime)
                .le(ObjectUtil.isNotEmpty(endTime), AchievementProductDetailModel::getCreateTime, endTime)
                .orderByAsc(OrderRuleTypeEnum.ASC.equals(orderRuleTypeEnum), AchievementProductDetailModel::getCreateTime)
                .orderByDesc(OrderRuleTypeEnum.DESC.equals(orderRuleTypeEnum), AchievementProductDetailModel::getCreateTime));
    }

    @Override
    public List<AchievementProductDetailModel> queryCustomerAllProductInfo(String customerId, Date achievementCreateTime) {
        return achievementProductDetailRepository.list(new LambdaQueryWrapper<AchievementProductDetailModel>()
                .eq(AchievementProductDetailModel::getCustomerId, customerId)
                .eq(AchievementProductDetailModel::getSaleType, SaleTypeEnum.NEW_OPEN.getCode())
                .eq(AchievementProductDetailModel::getMainSplitPerson, MainSplitPersonEnum.MAIN.getCode())
                .eq(AchievementProductDetailModel::getDeleteFlag, YesOrNotEnum.NO.getCode())
                .eq(AchievementProductDetailModel::getStatus, StatusEnum.VALID.getCode())
                .lt(AchievementProductDetailModel::getCreateTime, achievementCreateTime)
        );
    }

    @Override
    public void saveProductDetailModels(List<AchievementProductDetailModel> productDetailModels,MqOrderPaymentInfoModel mqPaymentInfoModel) {
        if (ObjectUtil.isEmpty(productDetailModels)) {
            return;
        }
        CustomerType customerType = calculateCustomerContextv4.calculateCustomerV4(mqPaymentInfoModel.getInstallmentStatus(), mqPaymentInfoModel.getInstallmentNum(), productDetailModels.get(0).getCustomerId(), productDetailModels);
        productDetailModels.forEach(e -> {
            e.setCustomerType(customerType.getType());
        });
        achievementProductDetailRepository.saveBatch(productDetailModels);
    }

    @Override
    public void businessOrgInfoRepair() {

        int pageSize = 1000;
        int currentPage = 1;
        LambdaQueryWrapper<AchievementProductDetailModel> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(AchievementProductDetailModel::getAchievementSource, AchievementSourceEnum.ZHONGXIAO.getCode());
        countWrapper.eq(BaseModel::getDeleteFlag, YesOrNotEnum.NO.getCode());
        long total = achievementProductDetailRepository.count(countWrapper);

        while (currentPage <= (total / pageSize) + 1) {
            // 分页查询
            Page<AchievementProductDetailModel> page = new Page<>(currentPage, pageSize);
            LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AchievementProductDetailModel::getAchievementSource, AchievementSourceEnum.ZHONGXIAO.getCode());
            queryWrapper.eq(BaseModel::getDeleteFlag, YesOrNotEnum.NO.getCode());
            Page<AchievementProductDetailModel> resultPage = achievementProductDetailRepository.page(page, queryWrapper);

            List<AchievementProductDetailModel> achievements = resultPage.getRecords();

            if (achievements.isEmpty()) {
                // 如果没有数据了，退出循环
                break;
            }

            // 提取 orgId 列表
            List<Long> companyIds = achievements.stream()
                    .map(AchievementProductDetailModel::getCompanyId)
                    .filter(ObjectUtil::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            // 提取 orgId 列表
            List<Long> deptIds = achievements.stream()
                    .map(AchievementProductDetailModel::getDeptId)
                    .filter(ObjectUtil::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            // 异步逐个调用远程服务获取 orgName
            Map<Long, String> companyNameMap = fetchOrgNamesAsync(companyIds);
            Map<Long, String> deptNameMap = fetchOrgNamesAsync(deptIds);

            // 处理每条数据
            achievements.forEach(achievement -> {
                Long companyId = achievement.getCompanyId();
                if (null != companyId && companyNameMap.containsKey(companyId)) {
                    achievement.setCompany(companyNameMap.get(companyId));
                }
                Long deptId = achievement.getDeptId();
                if (null != deptId && deptNameMap.containsKey(deptId)) {
                    achievement.setDepartment(deptNameMap.get(deptId));
                }
            });

            // 批量更新
            achievementProductDetailRepository.updateBatchById(achievements);
            log.info("当前更新第{}页，数量{}",currentPage, achievements.size());
            // 进入下一页
            currentPage++;
        }
    }
    /**
     * 异步逐个调用远程服务获取 orgName
     *
     * @param orgIds orgId 列表
     * @return orgId 和 orgName 的映射
     */
    private Map<Long, String> fetchOrgNamesAsync(List<Long> orgIds) {
        Map<Long, String> orgNameMap = Collections.synchronizedMap(new HashMap<>());

        List<CompletableFuture<Void>> futures = orgIds.stream()
                .map(orgId -> CompletableFuture.runAsync(() -> {
                    try {
                        OrgInfoResponse orgInfoResponse = innerService.queryOrgInfoById(orgId);
                        if (ObjectUtil.isNotEmpty(orgInfoResponse)) {
                            synchronized (orgNameMap) {
                                orgNameMap.put(orgId, orgInfoResponse.getName());
                            }
                        }
                    } catch (Exception e) {
                        // 处理异常或记录日志
                        System.err.println("Failed to get orgName for orgId: " + orgId + ", error: " + e.getMessage());
                    }
                }, taskExecutor))
                .collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return orgNameMap;
    }

    @Override
    public List<SearchAchievementDetailsDto> searchAchievementDetails(List<String> businessMonths, List<String> businessIds, Long companyId) {
        if (CollectionUtils.isEmpty(businessMonths) || CollectionUtils.isEmpty(businessIds)) {
            return Collections.emptyList();            
        }
        return achievementProductDetailRepository.searchAchievementDetails(businessMonths, businessIds, companyId);
    }
}
