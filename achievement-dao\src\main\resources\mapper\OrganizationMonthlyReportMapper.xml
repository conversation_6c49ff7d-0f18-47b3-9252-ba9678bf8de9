<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.OrganizationMonthlyReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.OrganizationMonthlyReportModel">
        <id column="id" property="id" />
        <result column="business_month_id" property="businessMonthId" />
        <result column="business_month" property="businessMonth" />
        <result column="organization_id" property="organizationId" />
        <result column="organization_name" property="organizationName" />
        <result column="organization_leader_id" property="organizationLeaderId" />
        <result column="organization_leader_name" property="organizationLeaderName" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="system_category" property="systemCategory" />
        <result column="market_category" property="marketCategory" />
        <result column="examination_dept_count" property="examinationDeptCount" />
        <result column="dept_count" property="deptCount" />
        <result column="business_representative_count" property="businessRepresentativeCount" />
        <result column="monthly_signing_amount" property="monthlySigningAmount" />
        <result column="monthly_net_cash" property="monthlyNetCash" />
        <result column="monthly_half_net_cash" property="monthlyHalfNetCash" />
        <result column="basic_task" property="basicTask" />
        <result column="task_completion_rate" property="taskCompletionRate" />
        <result column="total_sign_person_count" property="totalSignPersonCount" />
        <result column="sign_rate" property="signRate" />
        <result column="formal_business_representative_count" property="formalBusinessRepresentativeCount" />
        <result column="net_cash_per_employee" property="netCashPerEmployee" />
        <result column="non_renewal_customer_count" property="nonRenewalCustomerCount" />
        <result column="monthly_new_customer_count" property="monthlyNewCustomerCount" />
        <result column="monthly_website_new_customer_count" property="monthlyWebsiteNewCustomerCount" />
        <result column="monthly_new_website_count" property="monthlyNewWebsiteCount" />
        <result column="dept_commission" property="deptCommission" />
        <result column="branch_commission" property="branchCommission" />
        <result column="monthly_website_net_cash" property="monthlyWebsiteNetCash" />
        <result column="achievement_segment" property="achievementSegment" />
        <result column="achievement_segment_id" property="achievementSegmentId" />
        <result column="monthly_old_customer_count" property="monthlyOldCustomerCount" />
        <result column="saas_net_cash" property="saasNetCash"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business_month_id, business_month, organization_id, organization_name, organization_leader_id, organization_leader_name, appointment_date, system_category, market_category, examination_dept_count, dept_count, business_representative_count, monthly_signing_amount, monthly_net_cash, monthly_half_net_cash, basic_task, task_completion_rate, total_sign_person_count, sign_rate, formal_business_representative_count, net_cash_per_employee, non_renewal_customer_count, monthly_new_customer_count, monthly_website_new_customer_count, monthly_new_website_count, dept_commission, branch_commission, monthly_website_net_cash, achievement_segment, achievement_segment_id, monthly_old_customer_count,saas_net_cash
    </sql>

    <!-- 批量插入或更新 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO organization_monthly_report (
        business_month_id,
        business_month,
        current_day_month,
        organization_id,
        organization_name,
        organization_leader_id,
        organization_leader_name,
        appointment_date,
        system_id,
        system_category,
        market_category_id,
        market_category,
        examination_dept_count,
        dept_count,
        business_representative_count,
        monthly_signing_amount,
        monthly_net_cash,
        monthly_half_net_cash,
        basic_task,
        task_completion_rate,
        total_sign_person_count,
        sign_rate,
        formal_business_representative_count,
        net_cash_per_employee,
        non_renewal_customer_count,
        monthly_new_customer_count,
        monthly_website_new_customer_count,
        monthly_new_website_count,
        dept_commission,
        branch_commission,
        monthly_website_net_cash,
        achievement_segment,
        achievement_segment_id,
        monthly_old_customer_count,
        organization_type,
        create_user_id,
        create_user_name,
        update_user_id,
        update_user_name,
        saas_net_cash
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.businessMonthId},
            #{item.businessMonth},
            #{item.currentDayMonth},
            #{item.organizationId},
            #{item.organizationName},
            #{item.organizationLeaderId},
            #{item.organizationLeaderName},
            #{item.appointmentDate},
            #{item.systemId},
            #{item.systemCategory},
            #{item.marketCategoryId},
            #{item.marketCategory},
            #{item.examinationDeptCount},
            #{item.deptCount},
            #{item.businessRepresentativeCount},
            #{item.monthlySigningAmount},
            #{item.monthlyNetCash},
            #{item.monthlyHalfNetCash},
            #{item.basicTask},
            #{item.taskCompletionRate},
            #{item.totalSignPersonCount},
            #{item.signRate},
            #{item.formalBusinessRepresentativeCount},
            #{item.netCashPerEmployee},
            #{item.nonRenewalCustomerCount},
            #{item.monthlyNewCustomerCount},
            #{item.monthlyWebsiteNewCustomerCount},
            #{item.monthlyNewWebsiteCount},
            #{item.deptCommission},
            #{item.branchCommission},
            #{item.monthlyWebsiteNetCash},
            #{item.achievementSegment},
            #{item.achievementSegmentId},
            #{item.monthlyOldCustomerCount},
            #{item.organizationType},
            #{item.createUserId},
            #{item.createUserName},
            #{item.updateUserId},
            #{item.updateUserName},
            #{item.saasNetCash}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        business_month_id = VALUES(business_month_id),
        current_day_month = VALUES(current_day_month),
        organization_name = VALUES(organization_name),
        organization_leader_id = VALUES(organization_leader_id),
        organization_leader_name = VALUES(organization_leader_name),
        appointment_date = VALUES(appointment_date),
        system_id = VALUES(system_id),
        system_category = VALUES(system_category),
        market_category_id = VALUES(market_category_id),
        market_category = VALUES(market_category),
        examination_dept_count = VALUES(examination_dept_count),
        dept_count = VALUES(dept_count),
        business_representative_count = VALUES(business_representative_count),
        monthly_signing_amount = VALUES(monthly_signing_amount),
        monthly_net_cash = VALUES(monthly_net_cash),
        monthly_half_net_cash = VALUES(monthly_half_net_cash),
        basic_task = VALUES(basic_task),
        task_completion_rate = VALUES(task_completion_rate),
        total_sign_person_count = VALUES(total_sign_person_count),
        sign_rate = VALUES(sign_rate),
        formal_business_representative_count = VALUES(formal_business_representative_count),
        net_cash_per_employee = VALUES(net_cash_per_employee),
        non_renewal_customer_count = VALUES(non_renewal_customer_count),
        monthly_new_customer_count = VALUES(monthly_new_customer_count),
        monthly_website_new_customer_count = VALUES(monthly_website_new_customer_count),
        monthly_new_website_count = VALUES(monthly_new_website_count),
        dept_commission = VALUES(dept_commission),
        branch_commission = VALUES(branch_commission),
        monthly_website_net_cash = VALUES(monthly_website_net_cash),
        achievement_segment = VALUES(achievement_segment),
        achievement_segment_id = VALUES(achievement_segment_id),
        monthly_old_customer_count = VALUES(monthly_old_customer_count),
        organization_type = VALUES(organization_type),
        update_user_id = VALUES(update_user_id),
        update_user_name = VALUES(update_user_name),
        update_time = CURRENT_TIMESTAMP,
        saas_net_cash=VALUES(saas_net_cash);
    </insert>

</mapper>
