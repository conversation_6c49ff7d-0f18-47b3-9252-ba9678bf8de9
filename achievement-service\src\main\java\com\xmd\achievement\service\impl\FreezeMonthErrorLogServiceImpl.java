package com.xmd.achievement.service.impl;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xmd.achievement.dao.entity.FreezeMonthErrorLogModel;
import com.xmd.achievement.dao.repository.IFreezeMonthErrorLogRepository;
import com.xmd.achievement.service.IFreezeMonthErrorLogService;
import com.xmd.achievement.support.constant.enums.SourceSystemEnum;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 商务月冻结异常日志 Service 实现
 */
@Service
@Slf4j
public class FreezeMonthErrorLogServiceImpl implements IFreezeMonthErrorLogService {
    @Resource
    private IFreezeMonthErrorLogRepository freezeMonthErrorLogRepository;
    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private IFreezeMonthErrorLogService self;

    @Override
    public void add(Long monthId, Object data) {
      self.add(monthId, data, SourceSystemEnum.ACHIEVEMENT);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void add(Long monthId, Object data, SourceSystemEnum source) {
        try{
            if (data == null || monthId == null) {
            return;
        }
        FreezeMonthErrorLogModel log = new FreezeMonthErrorLogModel();
        log.setMonthId(monthId);
        log.setClassName(data.getClass().getName());
        log.setData(JSON.toJSONString(data));
        log.setSourceSystem(source.getCode());
        freezeMonthErrorLogRepository.save(log);
        }catch (Exception e){
            e.printStackTrace();
            log.error("添加冻结月异常日志失败");
        }
        
    }

}
