package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 分期支付成功响应
 * 
 * <AUTHOR> Generated
 * @date 2025-01-30
 */
@Data
@Schema(description = "分期支付成功响应")
public class InstallmentPaySuccessResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单支付详情id
     */
    @Schema(description = "订单支付详情id")
    private String orderPayDetailId;

    /**
     * 支付类型
     */
    @Schema(description = "支付类型")
    private Integer payType;
}
