package com.xmd.achievement.rpc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.service.IApiMockService;
import com.xmd.achievement.service.entity.dto.ServiceInfoDto;
import com.xmd.achievement.service.entity.response.CustomerChurnResponse;
import com.xmd.achievement.service.entity.response.CustomerInfoByConditionResponse;
import com.xmd.achievement.service.entity.response.RechargeInfoResponse;
import com.xmd.achievement.service.entity.response.ServiceInfoResponse;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.CustomerChurnStatusEnum;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.http.HttpResult;
import com.xmd.achievement.util.http.OKHttpUtil;
import com.xmd.achievement.util.sign.SignUtils;
import com.xmd.achievement.web.config.ThirdHttpUrlConfiguration;
import com.xmd.achievement.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 业绩调用二方接口
 *
 * <AUTHOR>
 * @date: 2024/12/18 14:15
 * @version: 1.0.0
 * @return {@link }
 */
@Service
@Slf4j
public class InnerServiceImpl implements InnerService {
    @Resource
    private ThirdHttpUrlConfiguration thirdHttpUrlConfiguration;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private IApiMockService apiMockService;

    @Resource
    private RedissonClient redisson;

    @Override
    public Boolean sendWxMessage(Long achievementId) {
        String sendWxMessageUrl = thirdHttpUrlConfiguration.getBspManagement().getSendWxMessage();
        log.info("sendWxMessage发送微信消息入参 achievementId:{}", achievementId);
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("toUser", "105999");
            param.set("content", "有异常业绩，请及时处理，业绩流水ID：" + achievementId);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);

            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    sendWxMessageUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 简单打印返回结果到日志，不做复杂处理
            log.info("sendWxMessage发送微信消息完成，返回结果: {}", responseEntity.getBody());
            return true;
        } catch (Exception e) {
            log.error("sendWxMessage发送微信消息异常 - achievementId: {}, 错误: {}", achievementId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public CustomerChurnResponse getGetCustomerInfoByCondition(String customerId) {
        try {
            Map<String, String> params = new HashMap<>(10);
            params.put("customerId", customerId);
            Map<String, String> hreadMap = new HashMap<>(10);
            String expectedSignature = SignUtils.generateSignature(customerId, thirdHttpUrlConfiguration.getCrmManagement().getSecretKey());
            hreadMap.put("sign", expectedSignature);
            log.info("获取CRM客户流失状态，请求参数:{}", com.alibaba.fastjson.JSONObject.toJSONString(params));
            HttpResult<String> stringHttpResult = OKHttpUtil.postSync(thirdHttpUrlConfiguration.getCrmManagement().getGetCustomerInfoByCondition(), params, hreadMap);
            if (!stringHttpResult.checkSuccess()) {
                log.error("获取CRM客户流失状态，customerId:{},请求参数:{},返回结果:{}", customerId, JSONUtil.toJsonStr(params), JSONUtil.toJsonStr(stringHttpResult));
                throw new BusinessException("获取CRM客户流失状态异常,customerId:" + customerId);
            }

            CustomerChurnResponse response = new CustomerChurnResponse();
            //客户不存在
            if (ObjectUtil.isEmpty(stringHttpResult.getData())) {
                response.setChurnStatus(CustomerChurnStatusEnum.CHURN_CUSTOMER);
                return response;
            }

            WebResult<CustomerInfoByConditionResponse> result = JSON.parseObject(stringHttpResult.getData(), new TypeReference<WebResult<CustomerInfoByConditionResponse>>() {
            });

            // 数据为空或标识为空时，默认为流失客户
            if (ObjectUtil.isEmpty(result) || ObjectUtil.isEmpty(result.getData()) ||
                ObjectUtil.isEmpty(result.getData().getTagLostCust())) {
                response.setChurnStatus(CustomerChurnStatusEnum.CHURN_CUSTOMER);
                return response;
            }

            // 根据流失标识判断客户状态
            if (NumberConstants.INTEGER_VALUE_1.equals(result.getData().getTagLostCust())) {
                response.setChurnStatus(CustomerChurnStatusEnum.CHURN_CUSTOMER);
                response.setChurnTime(result.getData().getTagLostTime());
                return response;
            }
            //客户未流失
            response.setChurnStatus(CustomerChurnStatusEnum.NO_CHURN_CUSTOMER);
            return response;
        } catch (Exception ex) {
            log.error("获取CRM客户流失状态失败，customerId:{},失败原因", customerId, ex);
            throw new BusinessException("获取CRM客户流失状态失败,customerId:" + customerId);

        }

    }

    @Override
    public OrderSimpleInfoResponse getOrderSimpleInfo(Long orderId) {
        String simpleOrderDetail = thirdHttpUrlConfiguration.getOrderManagement().getSimpleOrderDetail();
        log.info("getOrderSimpleInfo订单服务入参 :{}", orderId);
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("orderId", orderId);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    simpleOrderDetail,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("getOrderSimpleInfo订单服务返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return result.get("data", OrderSimpleInfoResponse.class);
            }
        } catch (Exception e) {
            log.error("getOrderSimpleInfo订单服务异常:{}", e.getMessage());
            throw new BusinessException("getOrderSimpleInfo订单服务异常,orderId:" + orderId);
        }
        return null;
    }


    @Override
    public OrderSimpleInfoResponse getOrderSimpleInfoByOrderNo(String orderNo) {
        String simpleOrderDetail = thirdHttpUrlConfiguration.getOrderManagement().getSimpleOrderDetail();
        log.info("getOrderSimpleInfo订单服务入参 :{}", orderNo);
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("orderNo", orderNo);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    simpleOrderDetail,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("getOrderSimpleInfo订单服务返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return result.get("data", OrderSimpleInfoResponse.class);
            }
        } catch (Exception e) {
            log.error("getOrderSimpleInfo订单服务异常:{}", e.getMessage());
            throw new BusinessException("getOrderSimpleInfo订单服务异常,orderNo:" + orderNo);
        }
        return null;
    }

    @Override
    public List<SpecStatisticsResponse> getSpecStatistics(String customerId, List<Long> productIds) {
        String specStatistics = thirdHttpUrlConfiguration.getOrderManagement().getSpecStatistics();
        log.info("getSpecStatistics统计规格入参 ,customerId:{},productIds:{}", customerId, JSONUtil.toJsonStr(productIds));
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("customerId", customerId);
            param.set("productIds", productIds);
            param.set("viewStatus", NumberConstants.INTEGER_VALUE_7);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    specStatistics,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("getSpecStatistics统计规格返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return Arrays.asList(result.get("data", SpecStatisticsResponse[].class));
            }
        } catch (Exception e) {
            log.error("getSpecStatistics统计规格异常:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public QueryContractDetailResponse getContractDetail(Long orderId) {
        String queryContractDetail = thirdHttpUrlConfiguration.getContractManagement().getQueryContractDetail();
        log.info("getContractDetail合同服务入参 :{}", orderId);
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("orderId", orderId);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    queryContractDetail,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("getContractDetail合同服务返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return result.get("data", QueryContractDetailResponse.class);
            }
        } catch (Exception e) {
            log.error("getContractDetail合同服务异常:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public UserInfoDetailResp getUserInfoDetailRedis(String employeeId) {
        if (ObjectUtil.isEmpty(employeeId)) {
            return null;
        }
        String key = "getUserInfoDetail:" + employeeId;
        String mockString = apiMockService.getMockString(key);
        if (StrUtil.isNotBlank(mockString)) {
            return JSON.parseObject(mockString, UserInfoDetailResp.class);
        }

        String lockKey = "bsp-performance:lock:" + key;
        RLock lock = redisson.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!locked) {
                return null;
            }
            mockString = apiMockService.getMockString(key);
            if (StrUtil.isNotBlank(mockString)) {
                return JSON.parseObject(mockString, UserInfoDetailResp.class);
            }

            UserInfoDetailResp userInfoDetail = this.doGetUserInfoDetail(employeeId);
            if (userInfoDetail != null) {
                apiMockService.addMockString(key, JSON.toJSONString(userInfoDetail));
            }
            return userInfoDetail;
        } catch (Exception e) {
            throw new RuntimeException("mock数据出现异常", e);
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public UserInfoDetailResp doGetUserInfoDetail(String userId) {
        String queryUserInfoDetail = thirdHttpUrlConfiguration.getBspManagement().getQueryUserInfoDetail();
        log.info("getUserInfoDetail获取用户信息调用bsp服务入参 :{}", userId);
        try {
            // 使用RestTemplate的exchange方法发送GET请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    queryUserInfoDetail + "?userId=" + userId,
                    HttpMethod.GET,
                    null,
                    String.class
            );
            log.info("getUserInfoDetail获取用户信息调用bsp服务返回值 :{}",
                    JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return result.get("data", UserInfoDetailResp.class);
            }
        } catch (Exception e) {
            log.error("getUserInfoDetail获取用户信息调用bsp服务异常:{}", e.getMessage());
        }
        return null;
    }


    @Override
    public UserInfoDetailResp getUserInfoDetail(String userId) {
        String queryUserInfoDetail = thirdHttpUrlConfiguration.getBspManagement().getQueryUserInfoDetail();
        log.info("getUserInfoDetail获取用户信息调用bsp服务入参 :{}", userId);
        try {
            // 使用RestTemplate的exchange方法发送GET请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    queryUserInfoDetail + "?userId=" + userId,
                    HttpMethod.GET,
                    null,
                    String.class
            );
            log.info("getUserInfoDetail获取用户信息调用bsp服务返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return result.get("data", UserInfoDetailResp.class);
            }
        } catch (Exception e) {
            log.error("getUserInfoDetail获取用户信息调用bsp服务异常:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<ProductListForAchievementResponse> getProductListForAchievement(List<Long> specIds, Integer specStatus) {
        String productsForAchievement = thirdHttpUrlConfiguration.getItemManagement().getProductsForAchievement();
        log.info("getProductListForAchievement获取规格集合调用商品服务入参 :{}", JSONUtil.toJsonStr(specIds));
        if (specIds == null || specIds.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("specIds", specIds);
            if (specStatus != null) {
                param.set("specStatus", specStatus);
            }
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    productsForAchievement,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("根据规格id获取规格集合调用商品服务返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return Arrays.asList(result.get("data", ProductListForAchievementResponse[].class));
            }
        } catch (Exception e) {
            log.error("getSpecListForPromotion调用商品服务异常:{}", e.getMessage());
        }
        return Collections.emptyList();
    }

    @Override
    public List<ProductDiscountRulesListForAchievementResponse> getProductDiscountRulesListForAchievement(List<Long> productIds) {
        String productsForAchievement = thirdHttpUrlConfiguration.getItemManagement().getGetProductDiscountRulesListForAchievement();
        log.info("getProductDiscountRulesListForAchievement获取商品折扣规则入参 :{}", JSONUtil.toJsonStr(productIds));
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("productIds", productIds);
            param.set("validFlag", NumberConstants.INTEGER_VALUE_1);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    productsForAchievement,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("getProductDiscountRulesListForAchievement获取商品折扣规则返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return Arrays.asList(result.get("data", ProductDiscountRulesListForAchievementResponse[].class));
            }
        } catch (Exception e) {
            log.error("getProductDiscountRulesListForAchievement获取商品折扣规则异常:{}", e.getMessage());
        }
        return Collections.emptyList();
    }

    @Override
    public QueryCustomerResponse queryCustomerInfo(String customerId) {
        String queryCustomerInfo = thirdHttpUrlConfiguration.getBspManagement().getQueryCustomerInfo();
        log.info("queryCustomerInfo入参 :{}", customerId);
        if (StringUtils.isBlank(customerId)) {
            return null;
        }
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("customerId", customerId);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    queryCustomerInfo,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("queryCustomerInfo返回 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return result.get("data", QueryCustomerResponse.class);
            }
        } catch (Exception e) {
            log.error("queryCustomerInfo异常:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<ServiceInfoResponse> getServiceInfo(String customerId) {
        String simpleOrderDetail = thirdHttpUrlConfiguration.getServeManagement().getGetServeList();
        log.info("getServiceInfo订单服务入参 :{}", customerId);
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("customerId", customerId);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    simpleOrderDetail,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("getServiceInfo订单服务返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("getServiceInfo订单服务异常，customerId：{},返回结果:{}", customerId, JSONUtil.toJsonStr(result));
                throw new BusinessException("getServiceInfo订单服务异常，customerId:" + customerId + "getServiceInfo订单服务异常");
            }

            ServiceInfoDto serviceInfoDto = JSONUtil.toBean(result.get("data").toString(), ServiceInfoDto.class);
            return serviceInfoDto.getList();
        } catch (Exception e) {
            log.error("getServiceInfo订单服务异常:{}", e.getMessage());
            throw new BusinessException("getServiceInfo订单服务异常,customerId:" + customerId);
        }
    }

    @Override
    public List<ServiceInfoResponse> getServiceInfoByServeNo(String serveNo) {
        String simpleOrderDetail = thirdHttpUrlConfiguration.getServeManagement().getGetServeList();
        log.info("getServiceInfo订单服务入参 :{}", serveNo);
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("serveNo", serveNo);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    simpleOrderDetail,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("getServiceInfo订单服务返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("serveNo{},返回结果:{}", serveNo, JSONUtil.toJsonStr(result));
                throw new BusinessException("serveNo:" + serveNo + "getServiceInfo订单服务异常");
            }

            ServiceInfoDto serviceInfoDto = JSONUtil.toBean(result.get("data").toString(), ServiceInfoDto.class);
            return serviceInfoDto.getList();
        } catch (Exception e) {
            log.error("getServiceInfo订单服务异常:{}", e.getMessage());
            throw new BusinessException("getServiceInfo订单服务异常,serveNo:" + serveNo);
        }
    }

    @Override
    public List<ServeProductConfigResponse> getConfigList(Long productId) {
        String simpleOrderDetail = thirdHttpUrlConfiguration.getServeManagement().getGetConfigList();
        log.info("getConfigList :{}", productId);
        try {
            JSONObject param = new JSONObject();
            param.set("productIds", Collections.singletonList(productId));
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    simpleOrderDetail,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("getConfigList服务返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (!"200".equals(result.getStr("code"))) {
                log.error("getConfigList{},返回结果:{}", productId, JSONUtil.toJsonStr(result));
                throw new BusinessException("getConfigList:" + productId + "getConfigList服务异常");
            }
            // 解析data为List<ServeProductConfigResponse>
            List<ServeProductConfigResponse> dataList = JSONUtil.toList(result.getJSONArray("data"), ServeProductConfigResponse.class);
            return dataList;
        } catch (Exception e) {
            log.error("getConfigList服务异常:{}", e.getMessage());
            throw new BusinessException("getConfigList服务异常: " + e.getMessage());
        }
    }

    @Override
    public RechargeInfoResponse getRechargeInfo(String customerId) {
        String simpleOrderDetail = thirdHttpUrlConfiguration.getAdManagement().getQueryCustomerRechargeTime();
        log.info("getRechargeInfo广告服务入参 :{}", customerId);
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("customerId", customerId);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    simpleOrderDetail,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("customerId:{},getRechargeInfo广告服务返回值 :{}", customerId, JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("getRechargeInfo广告服务异常，customerId：{},返回结果:{}", customerId, JSONUtil.toJsonStr(result));
                throw new BusinessException("getRechargeInfo广告服务异常，customerId:" + customerId + "getServiceInfo订单服务异常");
            }

            String data = result.get("data").toString();
            return JSONUtil.toBean(data, RechargeInfoResponse.class);
        } catch (Exception e) {
            log.error("customerId:{},getRechargeInfo广告服务异常:{}", customerId, e.getMessage());
            throw new BusinessException("getRechargeInfo广告服务异常,customerId:" + customerId);
        }
    }

    @Override
    public List<ProductForAchievementResponse> getAllProductForAchievement() {
        String allProductForAchievementUrl = thirdHttpUrlConfiguration.getItemManagement().getAllProductForAchievement();
        log.info("getAllProductForAchievement allProductForAchievementUrl:{}", allProductForAchievementUrl);
        try {
            // 使用RestTemplate的exchange方法发送GET请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    allProductForAchievementUrl ,
                    HttpMethod.GET,
                    null,
                    String.class
            );
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("getAllProductForAchievement ,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("getAllProductForAchievement 商品服务异常");
            }
            String data = result.get("data").toString();
            Gson gson = new Gson();
            Type productListType = new TypeToken<List<ProductForAchievementResponse>>() {}.getType();
            return gson.fromJson(data, productListType);
        } catch (Exception e) {
            log.error("getAllProductForAchievement 商品服务异常:{}", e.getMessage());
            throw new BusinessException("getAllProductForAchievement 商品服务异常" );
        }
    }

    @Override
    public OrgInfoResponse queryOrgInfoById(Long orgId) {
        String url = thirdHttpUrlConfiguration.getEhrManagement().getEmpUrl() + thirdHttpUrlConfiguration.getEhrManagement().getOrgInfoById();
        log.info("queryOrgInfoById getOrgInfoById:{}", url);
        try {
            //判断orgId 是否为空
            if (ObjectUtil.isEmpty(orgId)) {
                return null;
            }
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("orgId", String.valueOf(orgId));
            // 使用RestTemplate发送GET请求
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(builder.toUriString(), String.class);
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("queryOrgInfoById ,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("queryOrgInfoById ehr服务异常");
            }
            String data = result.get("data").toString();
            return JSON.parseObject(data, new TypeReference<OrgInfoResponse>() {
            });
        } catch (Exception e) {
            log.error("queryOrgInfoById ehr服务异常:{}", e.getMessage());
            throw new BusinessException("queryOrgInfoById ehr服务异常" );
        }
    }

    @Override
    public List<OrgInfoNodeResponse> queryKjOrgTree() {
        String orgInfoNodeUrl = thirdHttpUrlConfiguration.getBspManagement().getQueryKjOrgTree();
        log.info("queryKjOrgTree查询组织树");
        try {
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    orgInfoNodeUrl,
                    HttpMethod.POST,
                    null,
                    String.class
            );
            log.info("queryKjOrgTree查询组织树 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("queryKjOrgTree查询组织树,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("queryKjOrgTree查询组织树 服务异常");
            }
            Gson gson = new Gson();
            Type orgInfoNodeListType = new TypeToken<List<OrgInfoNodeResponse>>() {}.getType();
            return gson.fromJson(result.get("data").toString(), orgInfoNodeListType);
        } catch (Exception e) {
            log.error("queryKjOrgTree查询组织树:{}", e.getMessage());
            throw new BusinessException("queryKjOrgTree查询组织树服务异常");
        }
    }

    @Override
    public List<OrgInfoResp> queryListOrg(List<Long> orgIds) {
        String queryListOrg = thirdHttpUrlConfiguration.getBspManagement().getQueryListOrg();
        log.info("queryListOrg批量查询组织机构信息入参 :{}", JSON.toJSONString(orgIds));
        if (CollUtil.isEmpty(orgIds)) {
            return Collections.emptyList();
        }
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("orgIdList", CollUtil.newHashSet(orgIds));
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    queryListOrg,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("queryListOrg==根据机构id调用bsp服务批量获取机构信息返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return Arrays.asList(result.get("data", OrgInfoResp[].class));
            }
        } catch (Exception e) {
            log.error("queryListOrg==根据机构id调用bsp服务批量获取机构信息服务异常:{}", e.getMessage());
        }
        return Collections.emptyList();
    }

    @Override
    public QueryLeveRelationResponse queryLeveRelation(Long orgId) {
        String url = thirdHttpUrlConfiguration.getBspManagement().getQueryLeveRelation();
        log.info("根据orgId查询上下级信息入参 :{}", JSON.toJSONString(orgId));
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("orgId", orgId);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("根据orgId查询上下级信息 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("根据orgId查询上下级信息,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("根据orgId查询上下级信息 服务异常");
            }
            return result.get("data", QueryLeveRelationResponse.class);
        } catch (Exception e) {
            log.error("根据orgId查询上下级信息:{}", e.getMessage());
            throw new BusinessException("根据orgId查询上下级信息服务异常");
        }
    }

    @Override
    public List<OrgFunctionResp> queryOrgFunctionList(String requestSource) {
        String url = thirdHttpUrlConfiguration.getBspManagement().getQueryOrgFunctionList();
        log.info("查询职能分类列表入参 :{}", JSON.toJSONString(requestSource));
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("requestSource", requestSource);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("查询职能分类列表 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("查询职能分类列表,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("查询职能分类列表服务异常");
            }
            return Arrays.asList(result.get("data", OrgFunctionResp[].class));
        } catch (Exception e) {
            log.error("查询职能分类列表:{}", e.getMessage());
            throw new BusinessException("查询职能分类列表服务异常");
        }
    }

    @Override
    public OrgFunctionResp getOrgFunctionById(String orgFunctionId, String source) {
        String url = thirdHttpUrlConfiguration.getBspManagement().getGetOrgFunctionById();
        log.info("查询职能分类入参 :{}", JSON.toJSONString(orgFunctionId));
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("orgFunctionId", orgFunctionId);
            param.set("requestSource", source);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("查询职能分类,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("查询职能分类服务异常");
            }
            return result.get("data", OrgFunctionResp.class);
        } catch (Exception e) {
            log.error("查询职能分类:{}", e.getMessage());
            throw new BusinessException("查询职能分类服务异常");
        }
    }

    @Override
    public OrgBusinessResponse getOrgBusiness(Long orgId, String dateStr, String source) {
        String url = thirdHttpUrlConfiguration.getBspManagement().getGetOrgBusiness();
        log.info("根据机构id查询机构商务信息入参 :{}, {}, {}", JSON.toJSONString(orgId), JSON.toJSONString(dateStr), JSON.toJSONString(source));
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("orgId", orgId);
            param.set("dateStr", dateStr);
            param.set("requestSource", source);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("根据机构id查询机构商务信息,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("根据机构id查询机构商务信息服务异常");
            }
            return result.get("data", OrgBusinessResponse.class);
        } catch (Exception e) {
            log.error("根据机构id查询机构商务信息:{}", e.getMessage());
            throw new BusinessException("根据机构id查询机构商务信息服务异常");
        }
    }

    @Override
    public  ProtectByCustomer getProtectByCustomerId(String customerId) {
        String url = thirdHttpUrlConfiguration.getCrmManagement().getGetProtectByCustomerId()+"/"+customerId;
        log.info("用户id查询客户客保关系入参 customerId:{}", customerId);
        try {
            Map<String, String> hreadMap = new HashMap<>(10);
            String expectedSignature = SignUtils.generateSignature(customerId, thirdHttpUrlConfiguration.getCrmManagement().getSecretKey());
            hreadMap.put("sign", expectedSignature);

            log.info("用户id查询客户客保关系，请求参数 customerId:{}", customerId);
            HttpResult<String> stringHttpResult = OKHttpUtil.getSync(url, null, hreadMap);
            if (!stringHttpResult.checkSuccess()) {
                log.info("用户id查询客户客保关系，customerId:{},返回结果:{}", customerId,  JSONUtil.toJsonStr(stringHttpResult));
                throw new BusinessException("用户id查询客户客保关系异常,customerId:" + customerId);
            }

            String data =stringHttpResult.getData();
            JSONObject result = JSONUtil.parseObj(data);
            if (result.getInt("code") != 200) {
                log.info("用户id查询客户客保关系,返回结果:{}", JSONUtil.toJsonStr(result));
                return null;
            }
            ProtectByCustomer protectByCustomer = result.get("data", ProtectByCustomer.class);
            if(null == protectByCustomer){
                log.info("用户id查询客户客保关系不存在 url: " + url + "sign: " + expectedSignature);
                return null;
            }
            return protectByCustomer;
        } catch (Exception e) {
            log.error("用户id查询客户客保关系信息:{}", e.getMessage());
            throw new BusinessException("用户id查询客户客保关系服务异常");
        }
    }
    @Override
    public List<ProductInfoForOrderResponse> getProductListForOrder(List<Long> productIds) {
        String productsForAchievement = thirdHttpUrlConfiguration.getItemManagement().getGetProductListForOrder();
        log.info("getProductListForOrder获取商品折扣规则入参 :{}", JSONUtil.toJsonStr(productIds));
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("productIds", productIds);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    productsForAchievement,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("getProductListForOrder获取商品折扣规则返回值 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") == 200) {
                return Arrays.asList(result.get("data", ProductInfoForOrderResponse[].class));
            }
        } catch (Exception e) {
            log.error("getProductListForOrder获取商品折扣规则异常:{}", e.getMessage());
        }
        return Collections.emptyList();
    }

    @Override
    public  List<String> getCustomerLossDateList(String customerId) {
        String url = thirdHttpUrlConfiguration.getBspManagement().getGetCustomerLossDateList();
        log.info("查询客户流失记录 :{}", JSON.toJSONString(customerId));
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("customerId", customerId);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("查询客户流失记录,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("查询客户流失记录服务异常");
            }
            String[] arr = result.get("data", String[].class);
            return arr == null ? Collections.emptyList() : Arrays.asList(arr);
        } catch (Exception e) {
            log.error("查询客户流失记录:{}", e.getMessage());
            throw new BusinessException("查询客户流失记录服务异常");
        }
    }

    @Override
    public  List<String> getCustomerLossDateListCrm(String customerId) {
        String url = thirdHttpUrlConfiguration.getCrmManagement().getGetCustomerLossDateListCrm()+"?customerId="+customerId;
        log.info("用户id查询客户客保关系入参 customerId:{}", customerId);
        try {
            Map<String, String> hreadMap = new HashMap<>(10);
            String expectedSignature = SignUtils.generateSignature(customerId, thirdHttpUrlConfiguration.getCrmManagement().getSecretKey());
            hreadMap.put("sign", expectedSignature);

            log.info("用户id查询客户客户流失历史记录，请求参数 customerId:{}", customerId);
            HttpResult<String> stringHttpResult = OKHttpUtil.getSync(url, null, hreadMap);
            if (!stringHttpResult.checkSuccess()) {
               return Collections.emptyList();
            }
            String data =stringHttpResult.getData();
            JSONObject result = JSONUtil.parseObj(data);
            if (result.getInt("code") != 200) {
                log.info("用户id查询客户客户流失历史记录,返回结果:{}", JSONUtil.toJsonStr(result));
                return Collections.emptyList();
            }
            // data是字符串数组，转为List<String>
            String[] arr = result.get("data", String[].class);
            return arr == null ? Collections.emptyList() : Arrays.asList(arr);
        } catch (Exception e) {
            log.error("用户id查询客户客保关系信息:{}", e.getMessage());
            throw new BusinessException("用户id查询客户客保关系服务异常");
        }
    }

    @Override
    public List<QueryCustomerAllRechargeTimeDto> queryCustomerAllRechargeTime(String serveNo){
        String rechargeTime = thirdHttpUrlConfiguration.getAdManagement().getQueryCustomerAllRechargeTime();
        log.info("queryCustomerAllRechargeTime广告服务入参 :{}", serveNo);
        try {
            // 创建一个HttpEntity对象，包含要发送的请求体
            JSONObject param = new JSONObject();
            param.set("instanceId", serveNo);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    rechargeTime,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("serveNo:{},queryCustomerAllRechargeTime广告服务返回值 :{}", serveNo, JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                // log.error("queryCustomerAllRechargeTime广告服务异常，serveNo：{},返回结果:{}", serveNo, JSONUtil.toJsonStr(result));
                // throw new BusinessException("queryCustomerAllRechargeTime广告服务异常，serveNo:" + serveNo + "queryCustomerAllRechargeTime广告服务异常");
                return Collections.emptyList();
            }

            String data = result.get("data").toString();
            return JSONUtil.toList(data, QueryCustomerAllRechargeTimeDto.class);
        } catch (Exception e) {
            log.error("serveNo:{},getRechargeInfo广告服务异常:{}", serveNo, e.getMessage());
            throw new BusinessException("getRechargeInfo广告服务异常,serveNo:" + serveNo);
        }
    }

    /**
     * 根据id查询组织名称，缓存中没有则调用三方接口，缓存调用方自己提供，不是全局缓存
     *
     * @param orgId       组织ID
     * @param description 描述：区域/公司/部门
     * @param cache       缓存map
     * @return {@link String }
     */
    @Override
    public String queryOrgNameByIdWithCache(Long orgId, String description, Map<String, String> cache) {
        String key = description + ":" + orgId;
        return cache.computeIfAbsent(key, k -> {
            OrgInfoResponse orgInfo = this.queryOrgInfoById(orgId);
            if (ObjectUtil.isNotEmpty(orgInfo)) {
                return orgInfo.getName();
            } else {
                log.warn("查询{}信息为空，{}id：{}", description, description, orgId);
                return "";
            }
        });
    }

    /**
     * 查询售后订单信息
     *
     * @param aftersaleOrderNo 售后单号
     * @return {@link AfterSalesOrderResp }
     * <AUTHOR>
     * @since 1.0
     */
    @Override
    public AfterSalesOrderResp queryAfterSalesOrderInfo(String aftersaleOrderNo) {
        String url = thirdHttpUrlConfiguration.getRefundOrderManagement().getQueryAfterSalesOrderInfo();
        try {
            JSONObject param = new JSONObject();
            param.set("afterSalesOrderNo", aftersaleOrderNo);
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(param);
            // 使用RestTemplate的exchange方法发送POST请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("queryAfterSalesOrderInfo 查询售后订单信息 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("queryAfterSalesOrderInfo 查询售后订单信息,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("queryAfterSalesOrderInfo 查询售后订单信息 服务异常");
            }
            return result.getBean("data", AfterSalesOrderResp.class);
        } catch (Exception e) {
            log.error("queryAfterSalesOrderInfo 查询售后订单信息:{}", e.getMessage());
            throw new BusinessException("queryAfterSalesOrderInfo 查询售后服务异常");
        }
    }

    /**
     * 获取售后订单详情
     *
     * @param aftersaleOrderNo 售后单号
     * @return {@link AfterSalesOrderDetailResp }
     * <AUTHOR>
     * @since 1.0
     */
    @Override
    public AfterSalesOrderDetailResp queryAfterSalesOrderDetail(String aftersaleOrderNo) {
        String url = thirdHttpUrlConfiguration.getRefundOrderManagement().getQueryAfterSalesOrderDetail();
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url + "?afterSalesOrderNo=" + aftersaleOrderNo,
                    HttpMethod.GET,
                    null,
                    String.class
            );
            log.info("queryAfterSalesOrderInfo 查询售后订单详情信息 :{}", JSONUtil.toJsonStr(responseEntity.getBody()));
            JSONObject result = JSONUtil.parseObj(responseEntity.getBody());
            if (result.getInt("code") != 200) {
                log.error("queryAfterSalesOrderInfo 查询售后订单详情信息,返回结果:{}", JSONUtil.toJsonStr(result));
                throw new BusinessException("queryAfterSalesOrderInfo 查询售后订单详情信息 服务异常");
            }
            return result.getBean("data", AfterSalesOrderDetailResp.class);
        } catch (Exception e) {
            log.error("queryAfterSalesOrderInfo 查询售后订单详情信息:{}", e.getMessage());
            throw new BusinessException("queryAfterSalesOrderInfo 查询售后服务异常");
        }
    }
}
