# Technology Stack

## Core Framework
- **Spring Boot 2.3.6** - Main application framework
- **Java 8** - Programming language
- **Maven** - Build system and dependency management

## Database & Persistence
- **MySQL 8.0.29** - Primary database
- **MyBatis Plus 3.5.2** - ORM framework with code generation
- **Druid 1.2.11** - Database connection pool

## Caching & Messaging
- **Redis** - Caching layer with Spring Data Redis 2.3.6
- **Redisson 3.17.7** - Distributed Redis client
- **RocketMQ 2.0.3** - Message queue for async processing

## Utilities & Libraries
- **Lombok 1.18.24** - Code generation for POJOs
- **FastJSON 1.2.83** - JSON processing
- **Hutool 5.8.11** - Java utility library
- **OkHttp 4.9.3** - HTTP client
- **EasyExcel/FastExcel** - Excel processing
- **OpenCSV 5.7.1** - CSV processing

## Monitoring & Jobs
- **CAT 3.0.0** - Application monitoring
- **XXL-Job 1.0.11** - Distributed task scheduling
- **Knife4j 4.3.0** - API documentation (Swagger)

## Testing
- **JUnit** - Unit testing framework
- **Spring Boot Test** - Integration testing

## Build Profiles
- **test** - Testing environment (1GB heap)
- **pre** - Pre-production environment (1GB heap)
- **release** - Production environment (2GB heap)

## Common Commands

### Build & Package
```bash
# Clean and compile
mvn clean compile

# Package application
mvn clean package

# Skip tests during build
mvn clean package -DskipTests

# Build with specific profile
mvn clean package -P release
```

### Development
```bash
# Run application locally
mvn spring-boot:run

# Generate MyBatis Plus code
# Use the generator in achievement-dao module

# Run tests
mvn test
```

### Deployment
```bash
# Create distribution package
mvn clean package -P release

# The packaged application will be in achievement-service/target/
```