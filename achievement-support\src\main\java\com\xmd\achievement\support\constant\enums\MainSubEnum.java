package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date: 2024/12/19 11:44
 */
@Getter
public enum MainSubEnum {
    MAIN(1, "主"), SUB(2, "辅");

    private final Integer type;
    private final String description;

    MainSubEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public static String getDescriptionByType(int type) {
        for (MainSubEnum flag : MainSubEnum.values()) {
            if (flag.getType().equals(type)) {
                return flag.getDescription();
            }
        }
        throw new IllegalArgumentException("No enum constant with type " + type);
    }
}
