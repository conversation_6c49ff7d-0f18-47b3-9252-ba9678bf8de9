package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.OrganizationMonthlyReportModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 机构月报 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface IOrganizationMonthlyReportRepository extends IService<OrganizationMonthlyReportModel> {

    void batchInsertOrUpdate(List<OrganizationMonthlyReportModel> list);

    void deleteByMonth(String preMonth);

    OrganizationMonthlyReportModel selectByMonthAndOrgId(String month, Long orgId);
}
