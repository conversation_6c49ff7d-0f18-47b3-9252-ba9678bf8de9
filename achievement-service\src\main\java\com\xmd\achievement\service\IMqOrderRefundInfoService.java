package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.service.entity.dto.ReceiveOrderRefundDto;

/**
 * 退转款信息处理
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface IMqOrderRefundInfoService {

    /**
     * 保存信息
     *
     * @param refundDto 退款消息
     * @return boolean
     * <AUTHOR>
     * @since 1.0
     */
    boolean saveInfo(ReceiveOrderRefundDto refundDto);

    /**
     * 售后回调
     *
     * @param refundInfoModel 退款信息模型
     * <AUTHOR>
     * @since 1.0
     */
    void aftersaleCallback(MqOrderRefundInfoModel refundInfoModel);

    /**
     * 通过消息id获取
     *
     * @param messageId 消息id
     * @return {@link MqOrderRefundInfoModel }
     * <AUTHOR>
     * @since 1.0
     */
    MqOrderRefundInfoModel getByMessageId(String messageId);
}
