package com.xmd.achievement;

import org.apache.rocketmq.client.log.ClientLogger;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PreDestroy;

@SpringBootApplication
@EnableTransactionManagement
@MapperScan("com.xmd.achievement.dao.mapper")
public class BspAchievementServiceApplication {
    private final static Logger LOGGER = LoggerFactory.getLogger(BspAchievementServiceApplication.class);


    public static void main(String[] args) {
        System.setProperty(ClientLogger.CLIENT_LOG_USESLF4J, "true");
        SpringApplication.run(BspAchievementServiceApplication.class, args);
        LOGGER.error("CbpAchievementServiceApplication is started（项目启动成功）");
    }

    @PreDestroy
    public void destroy() {
        LOGGER.error("CbpAchievementServiceApplication is started（项目即将重启）");
    }

}
