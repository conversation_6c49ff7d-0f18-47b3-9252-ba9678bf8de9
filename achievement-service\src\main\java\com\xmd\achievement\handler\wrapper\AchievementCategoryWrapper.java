package com.xmd.achievement.handler.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.repository.IAchievementCategoryDetailRepository;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.service.IAchievementCategoryDetailService;
import com.xmd.achievement.support.constant.enums.CurrentStatusEnum;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/02/20/17:08
 * @since 1.0
 */
@Component
@Log4j2
public class AchievementCategoryWrapper {
    @Resource
    private IAchievementCategoryDetailService achievementCategoryService;
    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;
    @Resource
    private IAchievementCategoryDetailRepository achievementCategoryDetailRepository;

    public AchievementCategoryDetailModel buildServiceCategoryDetailModel(Integer mainSplitPerson,Long achievementCategoryId, List<AchievementSpecDetailModel> value, Long orderId, Long key) {
        AchievementCategoryDetailModel model = new AchievementCategoryDetailModel();
        List<AchievementCategoryDetailModel> models = achievementCategoryService.getCategoryByProductCategoryId(orderId, value.get(0).getProductId(), value.get(0).getProductCategoryId(),mainSplitPerson,0);
        BeanUtil.copyProperties(models.get(0), model);
        model.setAchievementCategoryId(achievementCategoryId);
        model.setCreateTime(new Date());
        model.setStatus(CurrentStatusEnum.SERVICE.getStatus());
        model.setMainSplitPerson(mainSplitPerson);
        model.setCreateTime(new Date());
        model.setUpdateTime(new Date());

        // 计算所有需要的总和 -stream流比较消耗性能
        BigDecimal standardPriceSum = BigDecimal.ZERO;
        BigDecimal payableAmountSum = BigDecimal.ZERO;
        BigDecimal paidAmountSum = BigDecimal.ZERO;
        BigDecimal firstYearQuoteSum = BigDecimal.ZERO;
        BigDecimal firstYearIncomeSum = BigDecimal.ZERO;
        BigDecimal renewalQuoteSum = BigDecimal.ZERO;
        BigDecimal renewalIncomeSum = BigDecimal.ZERO;
        BigDecimal netCashSum = BigDecimal.ZERO;
        BigDecimal agentCommAchvSum = BigDecimal.ZERO;
        BigDecimal agentActCommAchvSum = BigDecimal.ZERO;
        //BigDecimal agentDefCommAchvSum = BigDecimal.ZERO;
        BigDecimal deptCommAchvSum = BigDecimal.ZERO;
        BigDecimal buCommAchvSum = BigDecimal.ZERO;
        BigDecimal branchCommAchvSum = BigDecimal.ZERO;

        for (AchievementSpecDetailModel spec : value) {
//            standardPriceSum = standardPriceSum.add(spec.getStandardPrice());
//            payableAmountSum = payableAmountSum.add(spec.getPayableAmount());
//            paidAmountSum = paidAmountSum.add(spec.getPaidAmount());
//            firstYearQuoteSum = firstYearQuoteSum.add(spec.getFirstYearQuote());
//            firstYearIncomeSum = firstYearIncomeSum.add(spec.getFirstYearIncome());
//            renewalQuoteSum = renewalQuoteSum.add(spec.getRenewalQuote());
//            renewalIncomeSum = renewalIncomeSum.add(spec.getRenewalIncome());
//            netCashSum = netCashSum.add(spec.getNetCash());
//            agentCommAchvSum = agentCommAchvSum.add(spec.getAgentCommAchv());
            agentActCommAchvSum = agentActCommAchvSum.add(spec.getAgentActCommAchv());
            //agentDefCommAchvSum = agentDefCommAchvSum.add(spec.getAgentDefCommAchv());
//            deptCommAchvSum = deptCommAchvSum.add(spec.getDeptCommAchv());
//            buCommAchvSum = buCommAchvSum.add(spec.getBuCommAchv());
//            branchCommAchvSum = branchCommAchvSum.add(spec.getBranchCommAchv());
        }
        model.setStandardPrice(standardPriceSum);
        model.setPayableAmount(payableAmountSum);
        model.setPaidAmount(paidAmountSum);
        model.setFirstYearQuote(firstYearQuoteSum);
        model.setFirstYearIncome(firstYearIncomeSum);
        model.setRenewalQuote(renewalQuoteSum);
        model.setRenewalIncome(renewalIncomeSum);
        model.setNetCash(netCashSum);
        model.setAgentCommAchv(agentCommAchvSum);
        model.setAgentActCommAchv(agentActCommAchvSum);
        model.setAgentDefCommAchv(BigDecimal.ZERO);
        model.setDeptCommAchv(deptCommAchvSum);
        model.setBuCommAchv(buCommAchvSum);
        model.setBranchCommAchv(branchCommAchvSum);
        return model;
    }

    public List<AchievementCategoryDetailModel> newBuildCategoryDetailModels(Integer mainSplitPerson, List<AchievementSpecDetailModel> newModels, MqOrderPaymentInfoModel mqOrderPaymentInfoModel) {
        Map<Long, List<AchievementSpecDetailModel>> spcCategoryMap = newModels.stream().collect(Collectors.groupingBy(AchievementSpecDetailModel::getProductCategoryId));
        List<AchievementCategoryDetailModel> newCategoryDetailModelList = new ArrayList<>();
        for (Map.Entry<Long, List<AchievementSpecDetailModel>> categoryEntry : spcCategoryMap.entrySet()) {
            AchievementCategoryDetailModel model = new AchievementCategoryDetailModel();
            List<AchievementCategoryDetailModel> models = achievementCategoryService.getCategoryByProductCategoryId(mqOrderPaymentInfoModel.getOrderId(), mqOrderPaymentInfoModel.getProductId(), categoryEntry.getKey(), mainSplitPerson,mqOrderPaymentInfoModel.getInstallmentNum());
            if(CollectionUtils.isEmpty(models)){
                String orderProductId = mqOrderPaymentInfoModel.getOrderProductId();
                List<AchievementProductDetailModel> achievementProductDetailModelList = achievementProductDetailRepository.selectByOrderIdAndOrderProductIdAndInstallmentNum(mqOrderPaymentInfoModel.getOrderId(), mqOrderPaymentInfoModel.getOrderProductId(), mqOrderPaymentInfoModel.getInstallmentNum(), mainSplitPerson );
                List<Long> achievementIdList = achievementProductDetailModelList.stream().map(AchievementProductDetailModel::getAchievementId).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(achievementIdList)){
                    log.error("未查询到商品业绩数据 orderId:{},orderProductId:{}",mqOrderPaymentInfoModel.getOrderId(),orderProductId);
                    return null;
                }
                models = achievementCategoryDetailRepository.selectCategoryByAchievementIdsAndProductCategoryId(achievementIdList, categoryEntry.getKey());
                if(CollectionUtils.isEmpty(models)){
                    log.error("未查询到商品业绩分类数据 orderId:{},productId:{}",mqOrderPaymentInfoModel.getOrderId(), mqOrderPaymentInfoModel.getProductId());
                    return null;
                }

            }
            log.info("newBuildCategoryDetailModels sourceModel:{}",JSONUtil.toJsonStr(models.get(0)));
            BeanUtil.copyProperties(models.get(0), model);
            model.setAchievementCategoryId(IdUtil.getSnowflakeNextId());
            model.setCreateTime(new Date());
            model.setId(null);
            model.setAchievementId(null);
            model.setStatus(CurrentStatusEnum.SERVICE.getStatus());
            model.setMainSplitPerson(mainSplitPerson);
            model.setCreateTime(new Date());
            model.setUpdateTime(new Date());

            // 计算所有需要的总和 -stream流比较消耗性能
            BigDecimal standardPriceSum = BigDecimal.ZERO;
            BigDecimal payableAmountSum = BigDecimal.ZERO;
            BigDecimal paidAmountSum = BigDecimal.ZERO;
            BigDecimal firstYearQuoteSum = BigDecimal.ZERO;
            BigDecimal firstYearIncomeSum = BigDecimal.ZERO;
            BigDecimal renewalQuoteSum = BigDecimal.ZERO;
            BigDecimal renewalIncomeSum = BigDecimal.ZERO;
            BigDecimal netCashSum = BigDecimal.ZERO;
            BigDecimal agentCommAchvSum = BigDecimal.ZERO;
            BigDecimal agentActCommAchvSum = BigDecimal.ZERO;
            //BigDecimal agentDefCommAchvSum = BigDecimal.ZERO;
            BigDecimal deptCommAchvSum = BigDecimal.ZERO;
            BigDecimal buCommAchvSum = BigDecimal.ZERO;
            BigDecimal branchCommAchvSum = BigDecimal.ZERO;

            for (AchievementSpecDetailModel spec : categoryEntry.getValue()) {
//                standardPriceSum = standardPriceSum.add(spec.getStandardPrice());
//                payableAmountSum = payableAmountSum.add(spec.getPayableAmount());
//                paidAmountSum = paidAmountSum.add(spec.getPaidAmount());
//                firstYearQuoteSum = firstYearQuoteSum.add(spec.getFirstYearQuote());
//                firstYearIncomeSum = firstYearIncomeSum.add(spec.getFirstYearIncome());
//                renewalQuoteSum = renewalQuoteSum.add(spec.getRenewalQuote());
//                renewalIncomeSum = renewalIncomeSum.add(spec.getRenewalIncome());
//                netCashSum = netCashSum.add(spec.getNetCash());
//                agentCommAchvSum = agentCommAchvSum.add(spec.getAgentCommAchv());
                agentActCommAchvSum = agentActCommAchvSum.add(spec.getAgentActCommAchv());
//                //agentDefCommAchvSum = agentDefCommAchvSum.add(spec.getAgentDefCommAchv());
//                deptCommAchvSum = deptCommAchvSum.add(spec.getDeptCommAchv());
//                buCommAchvSum = buCommAchvSum.add(spec.getBuCommAchv());
//                branchCommAchvSum = branchCommAchvSum.add(spec.getBranchCommAchv());
            }
            model.setStandardPrice(standardPriceSum);
            model.setPayableAmount(payableAmountSum);
            model.setPaidAmount(paidAmountSum);
            model.setFirstYearQuote(firstYearQuoteSum);
            model.setFirstYearIncome(firstYearIncomeSum);
            model.setRenewalQuote(renewalQuoteSum);
            model.setRenewalIncome(renewalIncomeSum);
            model.setNetCash(netCashSum);
            model.setAgentCommAchv(agentCommAchvSum);
            model.setAgentActCommAchv(agentActCommAchvSum);
            model.setAgentDefCommAchv(BigDecimal.ZERO);
            model.setDeptCommAchv(deptCommAchvSum);
            model.setBuCommAchv(buCommAchvSum);
            model.setBranchCommAchv(branchCommAchvSum);
            model.setInstallmentNum(mqOrderPaymentInfoModel.getInstallmentNum());
            log.info("newBuildCategoryDetailModels newModel:{}",JSONUtil.toJsonStr(model));
            newCategoryDetailModelList.add(model);
        }
        log.info("taskId:{},计算服务中业绩，最终分类业绩信息newCategoryDetailModelList:{}", mqOrderPaymentInfoModel.getTaskId(), JSONUtil.toJsonStr(newCategoryDetailModelList));
        return newCategoryDetailModelList;
    }
}
