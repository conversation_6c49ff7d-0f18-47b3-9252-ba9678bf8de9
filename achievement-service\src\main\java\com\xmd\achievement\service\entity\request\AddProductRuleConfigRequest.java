package com.xmd.achievement.service.entity.request;

import com.itextpdf.kernel.ProductInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/09:51
 * @since 1.0
 */
@Data
@Schema(description = "新增商品规格")
public class AddProductRuleConfigRequest implements Serializable {
    @Schema(description = "商品规则编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ruleCode;

    @Schema(description = "商品信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<ProductConfigInfoRequest> productConfigInfoRequests;

}
