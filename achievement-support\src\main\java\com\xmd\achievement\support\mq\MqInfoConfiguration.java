package com.xmd.achievement.support.mq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/9 11:53 上午
 */
@Data
@Component
@ConfigurationProperties(prefix = "mq")
public class MqInfoConfiguration {

    private Topic topic = new Topic();

    private Group group = new Group();

    /**
     * mq：topic
     */
    @Data
    public class Topic {
        private String orderChangeStatus;
        private String serveInProgress;
        private String serveFinishTime;
        private String orderRefund;
        private String orderTransferFund;
        private String orderRefundCallback;
        private String orderTransferFundCallback;
    }

    /**
     * mq：group
     */
    @Data
    public class Group {
        private String orderChangeStatus;
        private String serveInProgress;
        private String serveFinishTime;
        private String orderRefund;
        private String orderTransferFund;
        private String orderRefundCallback;
        private String orderTransferFundCallback;
    }


}
