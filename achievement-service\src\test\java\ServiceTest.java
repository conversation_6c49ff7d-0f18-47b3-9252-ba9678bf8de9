import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.xmd.achievement.BspAchievementServiceApplication;
import com.xmd.achievement.async.job.entity.JobParam;
import com.xmd.achievement.async.job.handler.*;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.SpecCombinationDetailModel;
import com.xmd.achievement.dao.repository.IMqOrderRefundInfoRepository;
import com.xmd.achievement.handler.achievement.*;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContext;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContextV4;
import com.xmd.achievement.handler.statistics.BusinessAchHandler;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.OrgBusinessResponse;
import com.xmd.achievement.rpc.entity.dto.OrgFunctionResp;
import com.xmd.achievement.rpc.entity.dto.ProtectByCustomer;
import com.xmd.achievement.service.IAchievementProductDetailService;
import com.xmd.achievement.service.IAchievementService;
import com.xmd.achievement.service.ICustomerSaasService;
import com.xmd.achievement.service.SyncBusinessAchievementStatisticsService;
import com.xmd.achievement.service.SyncBusinessMonthHistoryJobService;
import com.xmd.achievement.service.entity.request.RecalculateOrderProductRequest;
import com.xmd.achievement.service.entity.response.OrderProductResponse;
import com.xmd.achievement.support.constant.enums.CurrentStatusEnum;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import com.xmd.achievement.support.constant.enums.PayTypeEnum;
import com.xmd.achievement.util.date.DateTimeFormatUtil;
import com.xmd.achievement.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date: 2024/11/19 09:36
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = BspAchievementServiceApplication.class)
public class ServiceTest {
    @Resource
    private InnerService innerService;
    @Resource
    private AchievementHandler achievementHandler;
    @Resource
    BusinessAchHandler businessAchHandler;
    @Resource
    IAchievementService achievementService;
    @Resource
    MqOrderPaymentInfoJob mqOrderPaymentInfoJob;

    @Resource
    private MqServeFinishTimeInfoJob mqServeFinishTimeInfoJob;
    @Resource
    private CalculateCustomerContext customerContext;
    @Resource
    private CalculateCustomerContextV4 calculateCustomerContextV4;
    @Autowired
    private PolicyCostCalculator costCalculator;
    @Autowired
    private IAchievementProductDetailService achievementProductDetailService;
    @Autowired
    private AchievementDataTransferJob achievementDataTransferJob;
    @Autowired
    private ReportHandler reportHandler;
    @Autowired
    private DailyReportJob dailyReportJob;
    @Autowired
    private MonthlyReportJob monthlyReportJob;
    @Autowired
    private ICustomerSaasService customerSaasService;
    @Autowired
    private MqOrderRefundInfoJob mqOrderRefundInfoJob;
    @Autowired
    private AchievementRefundHandler refundHandler;
    @Autowired
    private IMqOrderRefundInfoRepository mqOrderRefundInfoRepository;

    @Test
    public void calculateCustomerType() {
        String customerId = "AEFEF7BBC60C484094D2BC3C5C642D39";

        System.out.println(calculateCustomerContextV4.calculateCustomerV4(1, 2, customerId, null).getType());
        //System.out.println(customerContext.calculateCustomer(customerId, null).getType());

    }

    @Test
    public void processAchievement_new() {
        Long[] ids = {
                1904457927770677248L
        };
        for (Long id : ids) {
            achievementHandler.processAchievement(null
            );
        }
    }

    @Test
    public void extracted() {
        String json = "{\"syncAll\": false}";
        mqOrderPaymentInfoJob.jobHandler(json);
    }

    @Test
    public void testRecalculate() {
        RecalculateOrderProductRequest request = new RecalculateOrderProductRequest();
        List<OrderProductResponse> orderProducts = Lists.newArrayList();
        OrderProductResponse orderProductResponse = new OrderProductResponse();
        orderProductResponse.setOrderNo("KJ2025060997715776247");
        orderProductResponse.setOrderProductId("SKJ2025060999860106765");
        orderProducts.add(orderProductResponse);
        request.setOrderProducts(orderProducts);
        achievementService.recalculateOrderProductAchievement(request);
    }

    @Test
    public void processAchievement_new_service() {
        MqOrderPaymentInfoModel model = new MqOrderPaymentInfoModel();
        model.setOrderId(1913050943804555264L);
        model.setAchievementSource(1);
        model.setCalculateType(1);
        model.setInstallmentStatus(1);
        model.setInstallmentNum(0);
        achievementHandler.processAchievement(model);
    }

    @Test
    public void calculateBusinessAch() {
        AchievementProductDetailModel ach = achievementService.getAchievementProductDetailModel(1871447938224959488L);
        businessAchHandler.achStatistics(ListUtil.of(ach), 1, 1);
    }

    @Test
    public void mqOrderPaymentInfoJob() {
        System.out.println(mqOrderPaymentInfoJob.jobHandler(null));
    }

    @Test
    public void businessOrgInfoRepairTest() {
        achievementProductDetailService.businessOrgInfoRepair();
    }

    @Test
    public void processDailyReportTest() {
        reportHandler.processDailyReport("2025-06");
    }

    @Test
    public void processDailyReportJobTest() {
        String param = "{\"currentDate\":\"2025-05\"}";
        //String param = null;
        dailyReportJob.jobHandler(param);
    }

    @Test
    public void processMonthlyReport() {
        //String startTime = "2025-03-31 00:00:00";
        //Date startDate = DateUtils.stringToDate(startTime, DateTimeFormatUtil.STANDARD_DATE_TIME.getFormat());
        //reportHandler.processMonthlyReport(startDate);
        //reportHandler.processMonthlyReport(null);
        String param = "{currentTime: \"2025-06-07 00:00:00\"}";
        //String param = null;
        monthlyReportJob.jobHandler(param);
    }

    @Test
    public void getOrgBusiness() {
        OrgBusinessResponse orgBusiness = innerService.getOrgBusiness(852L, "2025-03", "bsp-achievement");
    }

    @Test
    public void getOrgFunctionById() {
        OrgFunctionResp orgFunctionResp = innerService.getOrgFunctionById("16", "bsp-achievement");
    }


    @Test
    public void mqServeFinishTimeInfoJob() {
        mqServeFinishTimeInfoJob.jobHandler("test");
    }

    @Test
    public void dataTransferTest() {
        achievementDataTransferJob.jobHandler("2025-04-29 11:15:00");
    }

    public void prettyJson(Object obj) {
        log.warn(JSONUtil.toJsonPrettyStr(obj));
    }

    @Test
    public void getProtectByCustomerId() {
        ProtectByCustomer orgBusiness = innerService.getProtectByCustomerId("AEFEF7BBC60C484094D2BC3C5C642D39");
        System.out.println(orgBusiness);
    }


    public static void main(String[] args) {
        PolicyCostCalculator policyCostCalculator = new PolicyCostCalculator();
        // 示例输入数据
        List<Long> specIds = Arrays.asList(1L, 2L, 3L, 4L); // 当前订单的规格 ID 集合

        // 组合规格 ID 映射
        Map<Long, List<Long>> combinationSpecIds = new HashMap<>();
        combinationSpecIds.put(101L, Arrays.asList(1L, 2L)); // A 和 B 的组合
        combinationSpecIds.put(102L, Arrays.asList(2L, 3L)); // B 和 C 的组合
        combinationSpecIds.put(103L, Arrays.asList(1L, 2L, 3L)); // A、B,C,D 组合

        // 单规格政策性成本
        Map<Long, BigDecimal> specPolicyCosts = new HashMap<>();
        specPolicyCosts.put(1L, new BigDecimal("0.6")); // A 的政策性成本
        specPolicyCosts.put(2L, new BigDecimal("0.7")); // B 的政策性成本
        specPolicyCosts.put(3L, new BigDecimal("0.8")); // C 的政策性成本
        specPolicyCosts.put(4L, new BigDecimal("0.9")); // D 的政策性成本
        specPolicyCosts.put(5L, new BigDecimal("0.95")); // E 的政策性成本


        List<SpecCombinationDetailModel> specCombinationDetailModelOne = new ArrayList<>();
        SpecCombinationDetailModel specCombinationDetailModel = new SpecCombinationDetailModel();
        specCombinationDetailModel.setCombinationId(101L);
        specCombinationDetailModel.setSpecId(1L);
        specCombinationDetailModel.setNewPolicyCost(BigDecimal.valueOf(0.61));
        specCombinationDetailModel.setRenewalPolicyCost(BigDecimal.valueOf(0.62));
        specCombinationDetailModel.setUpgradePolicyCost(BigDecimal.valueOf(0.63));
        specCombinationDetailModel.setAdditionalPolicyCost(BigDecimal.valueOf(0.64));
        specCombinationDetailModelOne.add(specCombinationDetailModel);

        SpecCombinationDetailModel specCombinationDetailModel1 = new SpecCombinationDetailModel();
        specCombinationDetailModel1.setCombinationId(101L);
        specCombinationDetailModel1.setSpecId(2L);
        specCombinationDetailModel1.setNewPolicyCost(BigDecimal.valueOf(0.7));
        specCombinationDetailModel1.setRenewalPolicyCost(BigDecimal.valueOf(0.81));
        specCombinationDetailModel1.setUpgradePolicyCost(BigDecimal.valueOf(0.71));
        specCombinationDetailModel1.setAdditionalPolicyCost(BigDecimal.valueOf(0.61));
        specCombinationDetailModelOne.add(specCombinationDetailModel1);

        List<SpecCombinationDetailModel> specCombinationDetailModelTwo = new ArrayList<>();
        SpecCombinationDetailModel specCombinationDetailModel2 = new SpecCombinationDetailModel();
        specCombinationDetailModel2.setCombinationId(102L);
        specCombinationDetailModel2.setSpecId(2L);
        specCombinationDetailModel2.setNewPolicyCost(BigDecimal.valueOf(0.51));
        specCombinationDetailModel2.setRenewalPolicyCost(BigDecimal.valueOf(0.52));
        specCombinationDetailModel2.setUpgradePolicyCost(BigDecimal.valueOf(0.53));
        specCombinationDetailModel2.setAdditionalPolicyCost(BigDecimal.valueOf(0.54));
        specCombinationDetailModelTwo.add(specCombinationDetailModel2);

        SpecCombinationDetailModel specCombinationDetailModel3 = new SpecCombinationDetailModel();
        specCombinationDetailModel3.setCombinationId(102L);
        specCombinationDetailModel3.setSpecId(3L);
        specCombinationDetailModel3.setNewPolicyCost(BigDecimal.valueOf(0.9));
        specCombinationDetailModel3.setRenewalPolicyCost(BigDecimal.valueOf(0.91));
        specCombinationDetailModel3.setUpgradePolicyCost(BigDecimal.valueOf(0.91));
        specCombinationDetailModel3.setAdditionalPolicyCost(BigDecimal.valueOf(0.91));
        specCombinationDetailModelTwo.add(specCombinationDetailModel3);

        List<SpecCombinationDetailModel> specCombinationDetailModelThree = new ArrayList<>();
        SpecCombinationDetailModel specCombinationDetailModel4 = new SpecCombinationDetailModel();
        specCombinationDetailModel4.setCombinationId(103L);
        specCombinationDetailModel4.setSpecId(1L);
        specCombinationDetailModel4.setNewPolicyCost(BigDecimal.valueOf(0.51));
        specCombinationDetailModel4.setRenewalPolicyCost(BigDecimal.valueOf(0.52));
        specCombinationDetailModel4.setUpgradePolicyCost(BigDecimal.valueOf(0.53));
        specCombinationDetailModel4.setAdditionalPolicyCost(BigDecimal.valueOf(0.54));
        specCombinationDetailModelThree.add(specCombinationDetailModel4);

        SpecCombinationDetailModel specCombinationDetailModel5 = new SpecCombinationDetailModel();
        specCombinationDetailModel5.setCombinationId(103L);
        specCombinationDetailModel5.setSpecId(2L);
        specCombinationDetailModel5.setNewPolicyCost(BigDecimal.valueOf(0.96));
        specCombinationDetailModel5.setRenewalPolicyCost(BigDecimal.valueOf(0.91));
        specCombinationDetailModel5.setUpgradePolicyCost(BigDecimal.valueOf(0.91));
        specCombinationDetailModel5.setAdditionalPolicyCost(BigDecimal.valueOf(0.91));
        specCombinationDetailModelThree.add(specCombinationDetailModel5);

        SpecCombinationDetailModel specCombinationDetailModel6 = new SpecCombinationDetailModel();
        specCombinationDetailModel6.setCombinationId(103L);
        specCombinationDetailModel6.setSpecId(3L);
        specCombinationDetailModel6.setNewPolicyCost(BigDecimal.valueOf(0.51));
        specCombinationDetailModel6.setRenewalPolicyCost(BigDecimal.valueOf(0.52));
        specCombinationDetailModel6.setUpgradePolicyCost(BigDecimal.valueOf(0.53));
        specCombinationDetailModel6.setAdditionalPolicyCost(BigDecimal.valueOf(0.54));
        specCombinationDetailModelThree.add(specCombinationDetailModel6);

//        SpecCombinationDetailModel specCombinationDetailModel7 = new SpecCombinationDetailModel();
//        specCombinationDetailModel7.setCombinationId(103L);
//        specCombinationDetailModel7.setSpecId(4L);
//        specCombinationDetailModel7.setNewPolicyCost(BigDecimal.valueOf(0.9));
//        specCombinationDetailModel7.setRenewalPolicyCost(BigDecimal.valueOf(0.91));
//        specCombinationDetailModel7.setUpgradePolicyCost(BigDecimal.valueOf(0.91));
//        specCombinationDetailModel7.setAdditionalPolicyCost(BigDecimal.valueOf(0.91));
//        specCombinationDetailModelThree.add(specCombinationDetailModel7);

        // 组合规格政策性成本
        Map<Long, List<SpecCombinationDetailModel>> combinationPolicyCosts = new HashMap<>();
        combinationPolicyCosts.put(101L, specCombinationDetailModelOne); // A 和 B 的组合
        combinationPolicyCosts.put(102L, specCombinationDetailModelTwo); // B 和 C 的组合
        combinationPolicyCosts.put(103L, specCombinationDetailModelThree); // A、B,C 的组合

        OrderSaleTypeEnum orderSaleTypeEnum = OrderSaleTypeEnum.OPEN; // 订单销售类型

        // 调用计算方法
        Map<Long, BigDecimal> result = policyCostCalculator.calculatePolicyCosts(specIds, combinationSpecIds, specPolicyCosts, combinationPolicyCosts, orderSaleTypeEnum);

        // 输出结果
        result.forEach((specId, cost) -> System.out.println("规格 ID: " + specId + ", 政策性成本: " + cost));
    }
    @Resource
    private SyncBusinessMonthHistoryJobService syncBusinessMonthHistoryJobService;

    @Test
    public void syncBusinessMonthHistory() {
        syncBusinessMonthHistoryJobService.syncBusinessMonthHistory();
    }

    @Test
    public void syncAdvertisingPassHistory() {
        String json = null;
        JobParam jobParam = JSON.parseObject(json, JobParam.class);
        syncBusinessMonthHistoryJobService.syncAdvertisingPassHistory(jobParam);
    }

    @Resource
    private SyncBusinessAchievementStatisticsService syncBusinessAchievementStatisticsService;

    @Test
    public void syncBusinessAchievementStatistics() {
        String json = "{\"employeeId\": \"\",\"businessMonth\": \"2025-03\"}";
        JobParam jobParam = JSON.parseObject(json, JobParam.class);
        String employeeId = null;
        String businessMonth = null;
        Boolean syncAll = false;
        if(null != jobParam){
            employeeId = jobParam.getEmployeeId();
            businessMonth = jobParam.getBusinessMonth();
            syncAll = jobParam.getSyncAll();
        }
        syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(employeeId,businessMonth,syncAll);
    }

    @Test
    public void customerSaasServiceTest() {
       customerSaasService.customerSaasJob();
    }

    @Test
    public void customerSaasServiceCountTest() {
       customerSaasService.searchCount("AEFEF7BBC60C484094D2BC3C5C642D39",1925094067891179520L);
    }

    @Test    
    public void mqOrderRefundInfoJob() {
       mqOrderRefundInfoJob.jobHandler("");
    }
    @Test    
    public void refundHandler() {
       refundHandler.processRefundTaskByType(mqOrderRefundInfoRepository.getById(43));
    }
    
}
