package com.xmd.achievement.rpc.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
@Data
public class Item {
    private String serveId;            // 服务ID（与服务主表关联）
    private String serveItemId;        // 服务项ID
    private String serveItemCode;      // 服务项编码
    private String serveItemName;      // 服务项名称
    private String serveCategoryCode;  // 服务项分类编码
    private String serveCategoryName;  // 服务项分类名称
    private String productSpecId;      // 产品规格ID
    private String productSpecCode;    // 产品规格编码
    private String thirdInstanceId;    // 第三方实例ID
    private Integer serveItemStatus;   // 服务项状态（数值）
    private Integer haveTimeExpend;    // 已耗时长（可能为null）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime openTime;    // 开放时间（可能为null）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expirationTime;// 过期时间（可能为null）
    private Integer upgradeStatus;     // 升级状态（数值）
    private Integer afterServeStatus;  // 服务后状态（数值）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;  // 创建时间
    private String specCode;           // 规格编码
    private Integer billingType;       // 计费类型（数值）
    private Double totalQuantity;      // 总数量（数值）
    private Double expendQuantity;     // 已消耗数量（数值）
    private String itemUnit;           // 计量单位
    private Integer itemType;          // 项目类型（数值）
    private String itemTypeName;       // 项目类型名称
    private String serveItemExpendId;  // 服务项消耗ID
    private String productItemId;      // 产品项ID
    private String itemCode;           // 项目编码
    private List<BillingItem> billingItemList; // 计费项列表（嵌套对象）
}
