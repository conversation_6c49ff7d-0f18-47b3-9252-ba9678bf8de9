# Product Overview

BSP Achievement System is a comprehensive business achievement management platform built with Spring Boot. The system handles achievement tracking, performance calculations, and business metrics for various organizational structures.

## Core Functionality
- Achievement tracking and calculation
- Business performance metrics
- Customer management and categorization
- Order processing and refund handling
- Statistical reporting and data export
- Task management and audit trails
- Multi-tenant SaaS support

## Key Features
- Real-time achievement calculation
- Excel import/export capabilities
- Message queue integration for async processing
- Redis caching for performance optimization
- Scheduled job processing
- API documentation with Knife4j/Swagger