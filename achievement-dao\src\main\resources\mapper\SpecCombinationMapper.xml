<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.SpecCombinationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.SpecCombinationModel">
        <id column="id" property="id" />
        <result column="combination_id" property="combinationId" />
        <result column="combination_name" property="combinationName" />
        <result column="new_policy_cost" property="newPolicyCost" />
        <result column="renewal_policy_cost" property="renewalPolicyCost" />
        <result column="upgrade_policy_cost" property="upgradePolicyCost" />
        <result column="additional_policy_cost" property="additionalPolicyCost" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, combination_id, combination_name, new_policy_cost, renewal_policy_cost, upgrade_policy_cost, additional_policy_cost, `status`, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

</mapper>
