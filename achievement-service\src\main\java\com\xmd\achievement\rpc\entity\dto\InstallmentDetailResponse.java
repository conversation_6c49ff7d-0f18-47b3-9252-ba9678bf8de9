package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 分期详情集合
 * <AUTHOR>
 * @date 2024年11月11日 下午1:29
 */
@Data

public class InstallmentDetailResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;
    /**
     * 分期明细id
     */

    private Long installmentDetailId;

    /**
     * 分期单号
     */

    private String installmentNo;

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 分期申请id
     */

    private Long installmentApplyId;

    /**
     * 分期期数
     */

    private Integer installmentNum;

    /**
     * 本期应付金额
     */

    private BigDecimal installmentPayableAmount;

    /**
     * 分期金额
     */

    private BigDecimal installmentAmount;

    /**
     * 分期付款装：1待付款，2付款中，3已付款
     */

    private Integer installmentPayStatus;

    /**
     * 分期支付关联响应集合
     */
    private List<InstallmentPayRelResponse> installmentPayRelResponses;

    /**
     * 分期支付成功响应
     */
    private InstallmentPaySuccessResponse installmentPaySuccessResponse;

}
