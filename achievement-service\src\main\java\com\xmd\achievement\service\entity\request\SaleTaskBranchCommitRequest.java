package com.xmd.achievement.service.entity.request;

import com.xmd.achievement.service.entity.response.SaleTaskBranchResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class SaleTaskBranchCommitRequest implements Serializable {
    private static final long serialVersionUID = 6001545648904226210L;

    @Schema(description = "商务月")
    private String businessMonth;

    @Schema(description = "任务集合")
    private List<SaleTaskBranchResponse> saleTasks;

    @Schema(description = "参考任务值（前端传入）")
    private BigDecimal referenceTask;
}
