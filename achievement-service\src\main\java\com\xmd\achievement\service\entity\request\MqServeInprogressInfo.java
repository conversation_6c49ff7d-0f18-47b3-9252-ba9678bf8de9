package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/14/14:29
 * @since 1.0
 */
@Data
public class MqServeInprogressInfo implements Serializable {
    @Schema(description = "serveNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotEmpty(message = "serveNo不能为空")
    private String serveNo;

    @Schema(description = "orderId", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotEmpty(message = "orderId不能为空")
    private Long orderId;

    @Schema(description = "orderIds", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotEmpty(message = "productId不能为空")
    private Long productId;
}
