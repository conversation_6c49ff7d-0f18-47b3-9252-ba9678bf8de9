package com.xmd.achievement.async.job.handler;

import cn.hutool.core.util.ObjectUtil;
import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.dao.entity.ThirdAchievementModel;
import com.xmd.achievement.service.MqOrderPaymentInfoService;
import com.xmd.achievement.service.ThirdAchievementService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/15/11:26
 * @since 1.0
 */
@Slf4j
@Component
public class BackUpThirdAchievementToPaymentOrderJob {
    @Resource
    private MqOrderPaymentInfoService mqOrderPaymentInfoService;

    @Resource
    private ThirdAchievementService thirdAchievementService;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.THIRD_ACHIEVEMENT_TO_PAYMENT_ORDER_JOB)
    public ReturnT<String> jobHandler(String param) {
        execute();
        return ReturnT.SUCCESS;
    }

    private void execute() {
        log.info("执行ThirdAchievementToPaymentOrderJob任务Start...");
        List<ThirdAchievementModel> models = thirdAchievementService.queryExecuteTask();
        if (ObjectUtil.isEmpty(models)) {
            return;
        }
        boolean flag = Boolean.TRUE;
        for (ThirdAchievementModel model : models) {
            try {
                mqOrderPaymentInfoService.handelThirdAchievementToPaymentOrder(model);
            } catch (Exception e) {
                log.error("ThirdAchievementToPaymentOrderJob任务失败，TaskId:{},失败原因：", model.getTaskId(), e);
                flag = Boolean.FALSE;
                break;
            }
        }
        if (flag) {
            this.execute();
        }
        log.info("执行ThirdAchievementToPaymentOrderJob任务End...");
    }
}
