package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 政策收入节点枚举
 * <AUTHOR>
 * @date: 2024/12/24 10:48
 */
@Getter
public enum PolicyRevenueNodeEnum {
    PAID_FINISHED(1, "支付完成"), SERV_FINISHED(2, "服务完成");
    private final Integer type;
    private final String desc;

    PolicyRevenueNodeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
