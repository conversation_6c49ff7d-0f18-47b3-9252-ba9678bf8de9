package com.xmd.achievement.handler.wrapper;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContext;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContextV4;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.ProtectByCustomer;
import com.xmd.achievement.rpc.entity.dto.QueryCustomerResponse;
import com.xmd.achievement.service.IAchievementProductDetailService;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.ISaasTabService;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.CustomerTypeEnum;
import com.xmd.achievement.util.enums.MainSplitPersonEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import com.xmd.achievement.util.enums.StatusEnum;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import com.xmd.achievement.rpc.entity.dto.UserInfoDetailResp;
import org.springframework.util.CollectionUtils;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/15/15:50
 * @since 1.0
 */
@Log4j2
@Component
public class AchievementProductWrapper {
    @Resource
    IBusinessMonthService businessMonthService;
    @Resource
    InnerService innerService;
    @Resource
    private ISaasTabService saasTabService;
    @Resource
    private CalculateCustomerContext calculateCustomerContext;
    @Resource
    private CalculateCustomerContextV4 calculateCustomerContextV4;
    @Resource
    private IAchievementProductDetailService achievementProductDetailService;

    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    public AchievementProductDetailModel buildFromThirdAchievement(ThirdAchievementModel model, MqOrderPaymentInfoModel mqModel) throws ParseException {
        //===================================================我方数据======================================================
        AchievementProductDetailModel achievementProductDetailModel = new AchievementProductDetailModel();
        //业绩来源：1-跨境 2-中小
        achievementProductDetailModel.setAchievementSource(AchievementSourceEnum.ZHONGXIAO.getCode());
        //三方业绩流水ID
        achievementProductDetailModel.setThirdAchievementId(model.getThirdId());
        //业绩流水ID
        achievementProductDetailModel.setAchievementId(IdUtil.getSnowflake().nextId());
        //订单明细编号 跨境自己生成
        achievementProductDetailModel.setOrderProductId(IdUtil.getSnowflake().nextIdStr());
        //订单id 跨境自己生成
        achievementProductDetailModel.setOrderId(IdUtil.getSnowflake().nextId());
        //是否网站：0=否 1=是
        achievementProductDetailModel.setSiteFlag(model.getWebInfo());

        //映射中企业务类型 新开 续费 升级 另购  转款/退款/高价赎回
        setSaleType(model, achievementProductDetailModel);

        achievementProductDetailModel.setProductId(model.getProductId());
        achievementProductDetailModel.setProductName(model.getProductName());
        // Saas标签
        if (saasTabService.checkIsSaasZhongQi(model.getProductId())) {
            achievementProductDetailModel.setIsSaas(SaasEnum.YES.getCode());
        } else {
            achievementProductDetailModel.setIsSaas(SaasEnum.NO.getCode());
        }
        //服务完成时间
        if (ObjectUtil.isNotEmpty(model.getServeFinishTime())) {
            achievementProductDetailModel.setServeFinishTime(model.getServeFinishTime());
        }
        //标准价
        achievementProductDetailModel.setStandardPrice(BigDecimal.valueOf(model.getCurrentPrice()));
        //交付方式: 1-软件交付, 2-服务交付
        achievementProductDetailModel.setDeliveryMethod(NumberConstants.INTEGER_VALUE_1);
        //订单类型：1=普通订单，2=折扣订单
        achievementProductDetailModel.setOrderType(OrderTypeEnum.NORMAL.getType());

        //计算商务月,通过mq的落库时间
        if (ObjectUtil.isNotEmpty(mqModel.getCreateTime())) {
            BusinessMonthModel businessMonth = businessMonthService.getMonthInfo(mqModel.getCreateTime());
            if (ObjectUtil.isNotEmpty(businessMonth)) {
                achievementProductDetailModel.setBusinessMonthId(businessMonth.getMonthId());
                achievementProductDetailModel.setBusinessMonth(businessMonth.getMonth());
            }
        }
        QueryCustomerResponse queryCustomerResponse = innerService.queryCustomerInfo(model.getCustId());
        if (ObjectUtil.isNotEmpty(queryCustomerResponse)) {
            achievementProductDetailModel.setProvinceCode(queryCustomerResponse.getProvinceCode());
            achievementProductDetailModel.setProvinceName(queryCustomerResponse.getProvinceName());
            achievementProductDetailModel.setCityCode(queryCustomerResponse.getCityCode());
            achievementProductDetailModel.setCityName(queryCustomerResponse.getCityName());
            achievementProductDetailModel.setDistrictCode(queryCustomerResponse.getDistrictCode());
            achievementProductDetailModel.setDistrictName(queryCustomerResponse.getDistrictName());
        }

        //===================================================三方数据======================================================
        achievementProductDetailModel.setCompany(model.getCompany());
        achievementProductDetailModel.setDivision(model.getDivision());
        achievementProductDetailModel.setDepartment(model.getDepartment());
        //商务公司ID
        achievementProductDetailModel.setCompanyId(Long.valueOf(model.getOrgId()));
        //商务区域ID
        achievementProductDetailModel.setRegionId(Long.valueOf(model.getAreaId()));
        //商务事业部ID
        achievementProductDetailModel.setDivisionId(ObjectUtil.isEmpty(model.getBuId()) ? null : Long.valueOf(model.getBuId()));
        //商务部门ID
        achievementProductDetailModel.setDeptId(ObjectUtil.isEmpty(model.getDeptId()) ? null : Long.valueOf(model.getDeptId()));
        //提供枚举
        achievementProductDetailModel.setProductType(model.getProductType().toString());
        //业绩状态 1=有效，2=已完成
        if (NumberConstants.INTEGER_VALUE_0.equals(model.getState())) {
            achievementProductDetailModel.setStatus(StatusEnum.VALID.getCode());
        }
        if (NumberConstants.INTEGER_VALUE_1.equals(model.getState())) {
            achievementProductDetailModel.setStatus(StatusEnum.NOT_VALID.getCode());
        }
        if (NumberConstants.INTEGER_VALUE_2.equals(model.getState())) {
            achievementProductDetailModel.setStatus(StatusEnum.REFUND.getCode());
        }
        if (NumberConstants.INTEGER_VALUE_3.equals(model.getState())) {
            achievementProductDetailModel.setStatus(StatusEnum.COMPLETED.getCode());
        }
        //订单编号
        achievementProductDetailModel.setOrderNo(model.getOrderRecordCode());

        //订单来源：1BOSS,18新平台线上,19新平台线下,20新平台电子签单,22新云市场
        if (NumberConstants.INTEGER_VALUE_1.equals(model.getDataSource())) {
            model.setDataSource(17);
        }
        //订单来源统一置为0
        achievementProductDetailModel.setOrderSource(OrderSourceEnum.UNKNOWN_ZERO.getType());
        //客户ID
        achievementProductDetailModel.setCustomerId(model.getCustId());
        //客户名称
        achievementProductDetailModel.setCustomerName(model.getCustName());
        //合同编号
        achievementProductDetailModel.setContractNo(model.getContractCode());
        //商务ID
        achievementProductDetailModel.setBusinessId(model.getSalerId());
        //商务名称
        achievementProductDetailModel.setBusinessRepresentative(model.getSalerName());
        //商务类型
        if (NumberConstants.INTEGER_VALUE_0.equals(model.getShareType())) {
            achievementProductDetailModel.setMainSplitPerson(MainSplitPersonEnum.ASSISTANT.getCode());
        }
        if (NumberConstants.INTEGER_VALUE_1.equals(model.getShareType())) {
            achievementProductDetailModel.setMainSplitPerson(MainSplitPersonEnum.MAIN.getCode());
        }
        //应付金额
        achievementProductDetailModel.setPayableAmount(BigDecimal.valueOf(model.getSingingAmount()));
        //实付金额
        achievementProductDetailModel.setPaidAmount(BigDecimal.valueOf(model.getActualAccount()));
        //折扣比例
        achievementProductDetailModel.setDiscountRate(BigDecimal.valueOf(model.getDiscountAccount()));
        //业绩生成时间
        achievementProductDetailModel.setCreateTime(mqModel.getCreateTime());
        //业绩统计时间
        achievementProductDetailModel.setStatisticsTime(mqModel.getCreateTime());
        //签单时间
        achievementProductDetailModel.setSignedTime(model.getSingingDate());
        //付款时间
        achievementProductDetailModel.setPaymentTime(model.getToAccountDate());
        //首年报价
        achievementProductDetailModel.setFirstYearQuote(BigDecimal.valueOf(model.getFirstStandardAccount()));
        //首年到账金额
        achievementProductDetailModel.setFirstYearRevenue(BigDecimal.valueOf(model.getFirstActualAccount()));
        //续费报价
        achievementProductDetailModel.setRenewalQuote(BigDecimal.valueOf(model.getRenewStandardAccount()));
        //续费到账金额
        achievementProductDetailModel.setRenewalRevenue(BigDecimal.valueOf(model.getRenewActualAccount()));
        //净现金
        achievementProductDetailModel.setNetCash(BigDecimal.valueOf(model.getNetCashAccount()));
        //商代提成业绩
        achievementProductDetailModel.setAgentCommissionAchievement(BigDecimal.valueOf(model.getSaleHiredMoney()));
        //商代实发提成业绩
        achievementProductDetailModel.setAgentActualCommission(BigDecimal.valueOf(model.getRelaySaleHiredMoney()));
        //商代缓发提成业绩
        achievementProductDetailModel.setAgentDeferredCommission(BigDecimal.valueOf(model.getDelaySaleHiredMoney()));
        //部门提成业绩
        achievementProductDetailModel.setDeptCommission(BigDecimal.valueOf(model.getManagerHiredMoney()));
        //事业部提成业绩
        achievementProductDetailModel.setDivCommission(BigDecimal.valueOf(model.getCurrentPrice()));
        //分司提成业绩
        achievementProductDetailModel.setBranchCommission(BigDecimal.valueOf(model.getSubManagerHiredMoney()));
        //创建人id
        achievementProductDetailModel.setCreateUserId(model.getCreateUserId());
        //创建人名称
        achievementProductDetailModel.setCreateUserName(model.getCreateUserName());
        // 是否展示
        achievementProductDetailModel.setDisplayed(ObjectUtil.isEmpty(model.getDisplayed()) ? NumberConstants.INTEGER_VALUE_0 : model.getDisplayed());

        //从2025年01月21日 11点45分开始从新计算新老客户信息
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date1 = sdf.parse("2025-01-13 20:04:29");
        if (DateUtils.isDateAfter(date1, model.getCreateTime())) {
            log.info("thirdId:{},客户类型计算,处理方式：以三方数据提供", model.getThirdId());
            //客户类型 1:新客户 2:老客户 3:非新老客户
            if (model.getCustType().equals(NumberConstants.INTEGER_VALUE_1)) {
                achievementProductDetailModel.setCustomerType(CustomerTypeEnum.OLD.getCode());
            }
            if (model.getCustType().equals(NumberConstants.INTEGER_VALUE_2)) {
                achievementProductDetailModel.setCustomerType(CustomerTypeEnum.NEW.getCode());
            }
            if (model.getCustType().equals(NumberConstants.INTEGER_VALUE_3)) {
                achievementProductDetailModel.setCustomerType(CustomerTypeEnum.NON_NEW_OLD.getCode());
            }
        } else {
            log.info("thirdId:{},客户类型计算,处理方式：系统逻辑", model.getThirdId());
            CustomerType customerTypeEnum = calculateCustomerContextV4.calculateCustomerV4(mqModel.getInstallmentStatus(), mqModel.getInstallmentNum(), model.getCustId(), Collections.singletonList(achievementProductDetailModel));
            //CustomerType customerTypeEnum = calculateCustomerContext.calculateCustomer(model.getCustId(), Collections.singletonList(achievementProductDetailModel));
            Integer customerType = customerTypeEnum.getType();
            achievementProductDetailModel.setCustomerType(customerType);
        }
        return achievementProductDetailModel;
    }

    /**
     * 映射中企业务类型 新开 续费 升级 另购  转款/退款/高价赎回
     * @param model
     * @param achievementProductDetailModel
     */
    private void setSaleType(ThirdAchievementModel model, AchievementProductDetailModel achievementProductDetailModel) {
        if(SaleTypeEnum.NEW_OPEN.getCode().equals(model.getBusinessType())){
            //1新开 -> 1新开
            achievementProductDetailModel.setSaleType(OrderSaleTypeEnum.OPEN.getType());
        }else if(SaleTypeEnum.RENEW.getCode().equals(model.getBusinessType())){
            //2续费 -> 2续费
            achievementProductDetailModel.setSaleType(OrderSaleTypeEnum.RENEW.getType());
        }else if(SaleTypeEnum.TRANSFER.getCode().equals(model.getBusinessType())){
            //20转款 -> 5转款
            achievementProductDetailModel.setSaleType(OrderSaleTypeEnum.TRANSFER.getType());
        }else if(SaleTypeEnum.ADDITIONAL_PURCHASE.getCode().equals(model.getBusinessType())){
            //4另购充值 -> 4另购
            achievementProductDetailModel.setSaleType(OrderSaleTypeEnum.ANOTHER_BUY.getType());
        }else if(SaleTypeEnum.DOMAIN_TRANSFER.getCode().equals(model.getBusinessType())){
            //3310域名转入 -> 1新开
            achievementProductDetailModel.setSaleType(OrderSaleTypeEnum.OPEN.getType());
        }else if(SaleTypeEnum.UPGRADE.getCode().equals(model.getBusinessType())){
            //3扩容 -> 4另购
            achievementProductDetailModel.setSaleType(OrderSaleTypeEnum.ANOTHER_BUY.getType());
        }else if(SaleTypeEnum.HIGH_PRICE_REDEMPTION.getCode().equals(model.getBusinessType())){
            //5高价赎回 -> 7高价赎回
            achievementProductDetailModel.setSaleType(OrderSaleTypeEnum.HIGH_PRICE_REDEMPTION.getType());
        }else if(SaleTypeEnum.ZADD_UPGRADE.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.CAPACITY_EXPANSION_ZT.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.ZTSZT_UPGRADE.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.ZTSZM_UPGRADE.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.TRANSFER_IN.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.NZTSZT_UPGRADE.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.NZTSZM_UPGRADE.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.NZTSZM_UPGRADE_DA.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.DSP_UPGRADE.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.EIGHTY_EIGHT_UPGRADE.getCode().equals(model.getBusinessType()) ||
                SaleTypeEnum.PORTAL_EXTENSION_UPGRADE.getCode().equals(model.getBusinessType())){
            //7升级(ZT升级ZT) 8zadd升级 3115Z+升级ZTSZT 3116Z+升级ZTSZM 6转入 3117NZ+升级ZTSZT  3118NZ+升级ZTSZM 169大把推续费升级 12DSP升级 88升级 188门户扩展升级 -> 3升级
            achievementProductDetailModel.setSaleType(OrderSaleTypeEnum.UPGRADE.getType());
        }else if(SaleTypeEnum.REFUND.getCode().equals(model.getBusinessType())){
            //18退款 -> 6退款
            achievementProductDetailModel.setSaleType(OrderSaleTypeEnum.REFUND.getType());
        }
    }

    public AchievementProductDetailModel buildServiceProductDetailModel(Integer mainSplitPerson, Long achievementId, List<AchievementCategoryDetailModel> value, Long orderId, Long productId, String serveNo) {
        AchievementProductDetailModel model = new AchievementProductDetailModel();
        List<AchievementProductDetailModel> models = achievementProductDetailService.getByOrderIdAndProductId(orderId, productId, mainSplitPerson,0,null);
        BeanUtils.copyProperties(models.get(0), model);
        model.setAchievementId(achievementId);
        //更新商务月时间 更新商务月
        BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(models.get(0).getPaymentTime());
        model.setBusinessMonthId(monthInfo.getMonthId());
        model.setBusinessMonth(monthInfo.getMonth());
        //更改状态
        model.setStatus(AchStatus.FINISHED.getType());
        //更新服务编号
        model.setServeNo(serveNo);
        //业绩生成时间
        model.setCreateTime(new Date());

        model.setUpdateTime(new Date());
        //状态
        model.setStatus(CurrentStatusEnum.SERVICE.getStatus());
        model.setMainSplitPerson(mainSplitPerson);
        // 计算所有需要的总和
        BigDecimal standardPriceSum = BigDecimal.ZERO;
        BigDecimal payableAmountSum = BigDecimal.ZERO;
        BigDecimal paidAmountSum = BigDecimal.ZERO;
        BigDecimal firstYearQuoteSum = BigDecimal.ZERO;
        BigDecimal firstYearRevenueSum = BigDecimal.ZERO;
        BigDecimal renewalQuoteSum = BigDecimal.ZERO;
        BigDecimal renewalRevenueSum = BigDecimal.ZERO;
        BigDecimal netCashSum = BigDecimal.ZERO;
        BigDecimal agentCommissionAchievementSum = BigDecimal.ZERO;
        BigDecimal agentActualCommissionSum = BigDecimal.ZERO;
        //BigDecimal agentDeferredCommissionSum = BigDecimal.ZERO;
        BigDecimal deptCommissionSum = BigDecimal.ZERO;
        BigDecimal divCommissionSum = BigDecimal.ZERO;
        BigDecimal branchCommissionSum = BigDecimal.ZERO;

        for (AchievementCategoryDetailModel categoryDetail : value) {
//            standardPriceSum = standardPriceSum.add(categoryDetail.getStandardPrice());
//            payableAmountSum = payableAmountSum.add(categoryDetail.getPayableAmount());
//            paidAmountSum = paidAmountSum.add(categoryDetail.getPaidAmount());
//            firstYearQuoteSum = firstYearQuoteSum.add(categoryDetail.getFirstYearQuote());
//            firstYearRevenueSum = firstYearRevenueSum.add(categoryDetail.getFirstYearIncome());
//            renewalQuoteSum = renewalQuoteSum.add(categoryDetail.getRenewalQuote());
//            renewalRevenueSum = renewalRevenueSum.add(categoryDetail.getRenewalIncome());
//            netCashSum = netCashSum.add(categoryDetail.getNetCash());
//            agentCommissionAchievementSum = agentCommissionAchievementSum.add(categoryDetail.getAgentCommAchv());
            agentActualCommissionSum = agentActualCommissionSum.add(categoryDetail.getAgentActCommAchv());
            //agentDeferredCommissionSum = agentDeferredCommissionSum.add(categoryDetail.getAgentDefCommAchv());
//            deptCommissionSum = deptCommissionSum.add(categoryDetail.getDeptCommAchv());
//            divCommissionSum = divCommissionSum.add(categoryDetail.getBuCommAchv());
//            branchCommissionSum = branchCommissionSum.add(categoryDetail.getBranchCommAchv());
        }

        model.setStandardPrice(standardPriceSum);
        model.setPayableAmount(payableAmountSum);
        model.setPaidAmount(paidAmountSum);
        model.setFirstYearQuote(firstYearQuoteSum);
        model.setFirstYearRevenue(firstYearRevenueSum);
        model.setRenewalQuote(renewalQuoteSum);
        model.setRenewalRevenue(renewalRevenueSum);
        model.setNetCash(netCashSum);
        model.setAgentCommissionAchievement(agentCommissionAchievementSum);
        model.setAgentActualCommission(agentActualCommissionSum);
        model.setAgentDeferredCommission(BigDecimal.ZERO);
        model.setDeptCommission(deptCommissionSum);
        model.setDivCommission(divCommissionSum);
        model.setBranchCommission(branchCommissionSum);

        return model;
    }

    public List<AchievementProductDetailModel> newBuildProductDetailModels(Integer mainSplitPerson, List<AchievementCategoryDetailModel> newModels, MqOrderPaymentInfoModel mqOrderPaymentInfoModel) {
        Map<Long, List<AchievementCategoryDetailModel>> categoryProductIdMap = newModels.stream().collect(Collectors.groupingBy(AchievementCategoryDetailModel::getProductId));
        List<AchievementProductDetailModel> newProductDetailModels = new ArrayList<>();
        for (Map.Entry<Long, List<AchievementCategoryDetailModel>> entry : categoryProductIdMap.entrySet()) {
            AchievementProductDetailModel model = new AchievementProductDetailModel();
            List<AchievementProductDetailModel> models = achievementProductDetailService.getByOrderIdAndProductId(mqOrderPaymentInfoModel.getOrderId(), mqOrderPaymentInfoModel.getProductId(), mainSplitPerson,mqOrderPaymentInfoModel.getInstallmentNum(),mqOrderPaymentInfoModel.getOrderProductId());
            if(CollectionUtils.isEmpty(models)){
                models = achievementProductDetailRepository.selectByOrderIdAndOrderProductIdAndInstallmentNum(mqOrderPaymentInfoModel.getOrderId(), mqOrderPaymentInfoModel.getOrderProductId(), mqOrderPaymentInfoModel.getInstallmentNum(), mainSplitPerson);
                if(CollectionUtils.isEmpty(models)){
                    log.error("未查询到商品业绩数据 orderId:{},orderProductId:{}",mqOrderPaymentInfoModel.getOrderId(),mqOrderPaymentInfoModel.getOrderProductId());
                    return null;
                }
            }
            BeanUtils.copyProperties(models.get(0), model);
            model.setId(null);
            model.setAchievementId(IdUtil.getSnowflakeNextId());

            if(MainSplitPersonEnum.MAIN.getMainSplitPerson().equals(mainSplitPerson)){
                //通过客户id获取商务id
                ProtectByCustomer protectByCustomerId = innerService.getProtectByCustomerId(models.get(0).getCustomerId());
                if(null != protectByCustomerId){
                    //商务id
                    String salerId = protectByCustomerId.getSalerId();
                    //商务信息
                    UserInfoDetailResp userInfoDetail = innerService.getUserInfoDetail(salerId);
                    model.setBusinessId(salerId);
                    model.setBusinessRepresentative(userInfoDetail.getName());
                }else{
                    log.info("newBuildProductDetailModels 客保关系为空:{}",models.get(0).getCustomerId());
                }
            }else{

                //查询分单人是否是客户代表或商务主管 并且在职 给分单人
                UserInfoDetailResp userInfoDetail = innerService.getUserInfoDetailRedis(model.getBusinessId());
                if(!(null != userInfoDetail &&  (PositionNameEnum.CUSTOMER_REPRESENTATIVE.getName().equals(userInfoDetail.getPosition()) ||
                        PositionNameEnum.BUSINESS_SUPERVISOR.getName().equals(userInfoDetail.getPosition())) &&
                        NumberConstants.INTEGER_VALUE_0.equals(userInfoDetail.getStatus()))){
                    //异动或不在职给主单
                    List<AchievementProductDetailModel> mainModel = achievementProductDetailService.getByOrderIdAndProductId(mqOrderPaymentInfoModel.getOrderId(), mqOrderPaymentInfoModel.getProductId(), NumberConstants.INTEGER_VALUE_1,mqOrderPaymentInfoModel.getInstallmentNum(),mqOrderPaymentInfoModel.getOrderProductId());
                    model.setBusinessId(mainModel.get(0).getBusinessId());
                    model.setBusinessRepresentative(mainModel.get(0).getBusinessRepresentative());
                    model.setCompany(mainModel.get(0).getCompany());
                    model.setCompanyId(mainModel.get(0).getCompanyId());
                    model.setRegionId(mainModel.get(0).getRegionId());
                    model.setDivisionId(mainModel.get(0).getDivisionId());
                    model.setDivision(mainModel.get(0).getDivision());
                    model.setDeptId(mainModel.get(0).getDeptId());
                    model.setDepartment(mainModel.get(0).getDepartment());
                }
            }

            //更新商务月时间 更新商务月
            BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(mqOrderPaymentInfoModel.getCreateTime());
            model.setBusinessMonthId(monthInfo.getMonthId());
            model.setBusinessMonth(monthInfo.getMonth());
            //更改状态
            model.setStatus(AchStatus.FINISHED.getType());
            //更新服务编号
            model.setServeNo(mqOrderPaymentInfoModel.getServeNo());
            //业绩生成时间
            model.setCreateTime(mqOrderPaymentInfoModel.getCreateTime());
            //业绩统计时间
            model.setStatisticsTime(mqOrderPaymentInfoModel.getCreateTime());
            //修改时间
            model.setUpdateTime(new Date());
            //状态
            model.setStatus(CurrentStatusEnum.SERVICE.getStatus());
            model.setMainSplitPerson(mainSplitPerson);
            // 计算所有需要的总和
            BigDecimal standardPriceSum = BigDecimal.ZERO;
            BigDecimal payableAmountSum = BigDecimal.ZERO;
            BigDecimal paidAmountSum = BigDecimal.ZERO;
            BigDecimal firstYearQuoteSum = BigDecimal.ZERO;
            BigDecimal firstYearRevenueSum = BigDecimal.ZERO;
            BigDecimal renewalQuoteSum = BigDecimal.ZERO;
            BigDecimal renewalRevenueSum = BigDecimal.ZERO;
            BigDecimal netCashSum = BigDecimal.ZERO;
            BigDecimal agentCommissionAchievementSum = BigDecimal.ZERO;
            BigDecimal agentActualCommissionSum = BigDecimal.ZERO;
            //BigDecimal agentDeferredCommissionSum = BigDecimal.ZERO;
            BigDecimal deptCommissionSum = BigDecimal.ZERO;
            BigDecimal divCommissionSum = BigDecimal.ZERO;
            BigDecimal branchCommissionSum = BigDecimal.ZERO;

            for (AchievementCategoryDetailModel categoryDetailModel : entry.getValue()) {
//                standardPriceSum = standardPriceSum.add(categoryDetailModel.getStandardPrice());
//                payableAmountSum = payableAmountSum.add(categoryDetailModel.getPayableAmount());
//                paidAmountSum = paidAmountSum.add(categoryDetailModel.getPaidAmount());
//                firstYearQuoteSum = firstYearQuoteSum.add(categoryDetailModel.getFirstYearQuote());
//                firstYearRevenueSum = firstYearRevenueSum.add(categoryDetailModel.getFirstYearIncome());
//                renewalQuoteSum = renewalQuoteSum.add(categoryDetailModel.getRenewalQuote());
//                renewalRevenueSum = renewalRevenueSum.add(categoryDetailModel.getRenewalIncome());
//                netCashSum = netCashSum.add(categoryDetailModel.getNetCash());
//                agentCommissionAchievementSum = agentCommissionAchievementSum.add(categoryDetailModel.getAgentCommAchv());
                agentActualCommissionSum = agentActualCommissionSum.add(categoryDetailModel.getAgentActCommAchv());
//                //agentDeferredCommissionSum = agentDeferredCommissionSum.add(categoryDetail.getAgentDefCommAchv());
//                deptCommissionSum = deptCommissionSum.add(categoryDetailModel.getDeptCommAchv());
//                divCommissionSum = divCommissionSum.add(categoryDetailModel.getBuCommAchv());
//                branchCommissionSum = branchCommissionSum.add(categoryDetailModel.getBranchCommAchv());
            }
            if(StringUtils.isNoneEmpty(mqOrderPaymentInfoModel.getOrderProductId()) && !NumberConstants.STR_ZERO.equals(mqOrderPaymentInfoModel.getOrderProductId())){
                model.setOrderProductId(mqOrderPaymentInfoModel.getOrderProductId());
            }

            model.setStandardPrice(standardPriceSum);
            model.setPayableAmount(payableAmountSum);
            model.setPaidAmount(paidAmountSum);
            model.setFirstYearQuote(firstYearQuoteSum);
            model.setFirstYearRevenue(firstYearRevenueSum);
            model.setRenewalQuote(renewalQuoteSum);
            model.setRenewalRevenue(renewalRevenueSum);
            model.setNetCash(netCashSum);
            model.setAgentCommissionAchievement(agentCommissionAchievementSum);
            model.setAgentActualCommission(agentActualCommissionSum);
            model.setAgentDeferredCommission(BigDecimal.ZERO);
            model.setDeptCommission(deptCommissionSum);
            model.setDivCommission(divCommissionSum);
            model.setBranchCommission(branchCommissionSum);
            model.setInstallmentNum(mqOrderPaymentInfoModel.getInstallmentNum());
            if (saasTabService.checkIsSaasKuaJing(model.getProductId())){
                model.setIsSaas(SaasEnum.YES.getCode());
            }else {
                model.setIsSaas(SaasEnum.NO.getCode());
            }
            newProductDetailModels.add(model);
        }
        log.info("taskId:{},计算服务中业绩，最终商品业绩信息newProductDetailModelList:{}", mqOrderPaymentInfoModel.getTaskId(), JSONUtil.toJsonStr(newProductDetailModels));
        return newProductDetailModels;
    }

    public void relationId(List<AchievementSpecDetailModel> newSpecDetailModelList, List<AchievementCategoryDetailModel> newCategoryDetailModelList, List<AchievementProductDetailModel> newProductDetailModelList, MqOrderPaymentInfoModel mqPaymentInfoModel) {
        //原始 商品id 和 商品业绩表ID
        Map<Long, Long> productIdMapCategoryIds = newProductDetailModelList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        AchievementProductDetailModel::getProductId,
                        AchievementProductDetailModel::getAchievementId,
                        (existing, replacement) -> replacement
                ));

        //原始 分类id 和 分类业绩ID
        Map<Long, Long> categoryIdMapSpecIds = newCategoryDetailModelList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        AchievementCategoryDetailModel::getCategoryId,
                        AchievementCategoryDetailModel::getAchievementCategoryId,
                        (existing, replacement) -> replacement
                ));

        //分类业绩对应的商品ID
        for (AchievementCategoryDetailModel categoryDetailModel : newCategoryDetailModelList) {
            Long achievementId = productIdMapCategoryIds.get(categoryDetailModel.getProductId());
            if (ObjectUtil.isNotEmpty(achievementId)) {
                categoryDetailModel.setAchievementId(achievementId);
            } else {
                throw new BusinessException("taskId:" + mqPaymentInfoModel.getTaskId() + ",计算服务中业绩,分类业绩对应的商品ID未找到");
            }
        }

        //规格业绩对应的分类ID
        for (AchievementSpecDetailModel specDetailModel : newSpecDetailModelList) {
            log.info("商品分类id productCategoryId:{}",specDetailModel.getProductCategoryId());
            Long achievementCategoryId = categoryIdMapSpecIds.get(specDetailModel.getProductCategoryId());
            if (ObjectUtil.isNotEmpty(achievementCategoryId)) {
                specDetailModel.setAchievementCategoryId(achievementCategoryId);
            } else {
                throw new BusinessException("taskId:" + mqPaymentInfoModel.getTaskId() + ",计算服务中业绩,规格业绩对应的分类ID未找到");
            }
        }
        log.info("taskId:{},计算服务中业绩，关联ID最终结果，newProductDetailModelList:{},newCategoryDetailModelList:{},newSpecDetailModelList:{}", mqPaymentInfoModel.getTaskId(), JSONUtil.toJsonStr(newProductDetailModelList), JSONUtil.toJsonStr(newCategoryDetailModelList), JSONUtil.toJsonStr(newSpecDetailModelList));
    }

}
