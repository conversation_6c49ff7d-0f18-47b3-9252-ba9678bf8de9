# BSP Achievement System - 类索引

## 核心业务处理器 (Handlers)

### 统计处理器 (Statistics Handlers)
- `ActualCommissionHandler` - 实际佣金处理
- `BusinessHandler` - 业务处理
- `BusinessAchHandler` - 商务业绩处理
- `NetCashReceiptHandler` - 净现金收入处理
- `AgentCommissionAchievementHandler` - 代理佣金业绩处理
- `NonRenewalOrdersHandler` - 非续费订单处理
- `SiteNetCashReceiptHandler` - 站点净现金收入处理
- `NewWebsiteCountHandler` - 新网站数量处理

### 其他处理器
- `AchievementRefundHandler` - 退款业绩处理 (从代码片段可知)
- `AchievementHandler` - 主要业绩处理 (从测试代码可知)

## REST 控制器 (Controllers)

### 业务控制器
- `AchievementBlacklistController` - 业绩黑名单管理
- `BusinessMonthController` - 商务月度管理
- `CustomerSaasController` - 客户SaaS管理
- `OrderController` - 订单管理
- `PolicyController` - 政策管理

### 数据和报表控制器
- `DataRepairController` - 数据修复
- `ExportPerformanceController` - 性能导出
- `OrganizationReportController` - 组织报表
- `SegmentController` - 分段管理
- `MqDataController` - 消息队列数据管理

## 服务接口 (Services)

### 核心业务服务
- `IAchievementService` - 业绩服务接口
- `IAchievementCategoryDetailService` - 业绩分类明细服务
- `IAchievementSpecDetailService` - 业绩规格明细服务
- `IBusinessAchievementService` - 商务业绩服务
- `IApiMockService` - API Mock服务

### 外部服务集成
- `InnerService` - 内部服务接口
- `InnerServiceImpl` - 内部服务实现 (从代码片段可知)

## 异步处理 (Async/Jobs)

### 消息队列处理
- `MqOrderPaymentInfoJob` - 订单支付信息任务
- `MqServeFinishTimeInfoJob` - 服务完成时间信息任务
- `MqOrderRefundInfoJob` - 订单退款信息任务

### 定时任务
- `DailyReportJob` - 日报任务
- `MonthlyReportJob` - 月报任务
- `AchievementDataTransferJob` - 业绩数据传输任务

## 数据模型 (Models)

### 核心业绩模型
- `AchievementProductDetailModel` - 业绩商品明细模型
- `AchievementCategoryDetailModel` - 业绩分类明细模型
- `AchievementSpecDetailModel` - 业绩规格明细模型
- `MqOrderRefundInfoModel` - 订单退款信息模型
- `MqOrderPaymentInfoModel` - 订单支付信息模型

## 工具和配置类

### 异常处理
- `GlobalExceptionHandler` - 全局异常处理器

### 缓存处理
- `RedisHeartbeatCacheHandler` - Redis心跳缓存处理器

### 计算器和上下文
- `CalculateCustomerContext` - 客户计算上下文
- `CalculateCustomerContextV4` - 客户计算上下文V4
- `PolicyCostCalculator` - 政策成本计算器

## 测试类
- `ServiceTest` - 主要服务测试类
- `InnerServiceTest` - 内部服务测试类

## 关键业务流程映射

### 业绩计算流程
1. `AchievementHandler.processAchievement()` - 主要业绩处理入口
2. `BusinessAchHandler.achStatistics()` - 商务业绩统计
3. 各种统计处理器进行具体计算

### 退款处理流程
1. `AchievementRefundHandler.processPendingRefundTasks()` - 处理待处理退款任务
2. `AchievementRefundHandler.processRefundTaskByType()` - 按类型处理退款任务
3. 生成退款业绩记录

### 外部服务集成
1. `InnerServiceImpl` - 统一的外部服务调用入口
2. 集成订单、客户、商品、合同等系统
3. 提供缓存和重试机制