package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 任务状态
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 13:57
 **/
@Getter
public enum TaskTypeEnum {
    /**
     * 新增
     */
    ADD(1, "ADD"),
    /**
     * 修改
     */
    UPDATE(2, "UPDATE"),
    /**
     * 删除
     */
    DELETE(3, "DELETE"),
    ;

    private final Integer code;
    private final String msg;

    TaskTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public static TaskTypeEnum getByMsg(String msg) {
        for (TaskTypeEnum type : TaskTypeEnum.values()) {
            if (type.getMsg().equals(msg)) {
                return type;
            }
        }
        return null;
    }

}