package com.xmd.achievement.service.entity.dto;

import com.opencsv.bean.CsvBindByName;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/23/16:49
 * @since 1.0
 */
@Data
public class ProductedDataAddDto implements Serializable {

    @CsvBindByName(column = "orderNo")
    private String orderNo;

    @CsvBindByName(column = "productCode")
    private String productCode;
}
