<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.AchievementSpecDetailSelectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.AchievementSpecDetailSelectModel">
        <id column="id" property="id" />
        <result column="achievement_spec_id" property="achievementSpecId" />
        <result column="achievement_category_id" property="achievementCategoryId" />
        <result column="spec_id" property="specId" />
        <result column="spec_name" property="specName" />
        <result column="item_type" property="itemType" />
        <result column="order_spec_type" property="orderSpecType" />
        <result column="item_unit" property="itemUnit" />
        <result column="item_num" property="itemNum" />
        <result column="serve_item_no" property="serveItemNo" />
        <result column="billing_price" property="billingPrice" />
        <result column="renewal_price" property="renewalPrice" />
        <result column="standard_price" property="standardPrice" />
        <result column="payable_amount" property="payableAmount" />
        <result column="paid_amount" property="paidAmount" />
        <result column="first_year_quote" property="firstYearQuote" />
        <result column="first_year_income" property="firstYearIncome" />
        <result column="renewal_quote" property="renewalQuote" />
        <result column="renewal_income" property="renewalIncome" />
        <result column="net_cash" property="netCash" />
        <result column="agent_comm_achv" property="agentCommAchv" />
        <result column="agent_act_comm_achv" property="agentActCommAchv" />
        <result column="agent_def_comm_achv" property="agentDefCommAchv" />
        <result column="dept_comm_achv" property="deptCommAchv" />
        <result column="bu_comm_achv" property="buCommAchv" />
        <result column="branch_comm_achv" property="branchCommAchv" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="revenue_node" property="revenueNode" />
        <result column="order_id" property="orderId" />
        <result column="product_id" property="productId" />
        <result column="product_category_id" property="productCategoryId" />
        <result column="status" property="status" />
        <result column="main_split_person" property="mainSplitPerson" />
        <result column="data_change_type" property="dataChangeType" />
        <result column="installment_num" property="installmentNum" />
        <result column="order_product_id" property="orderProductId" />
        <result column="is_saas" property="isSaas" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, achievement_spec_id, achievement_category_id, spec_id, spec_name, item_type, order_spec_type, item_unit, item_num, serve_item_no, billing_price, renewal_price, standard_price, payable_amount, paid_amount, first_year_quote, first_year_income, renewal_quote, renewal_income, net_cash, agent_comm_achv, agent_act_comm_achv, agent_def_comm_achv, dept_comm_achv, bu_comm_achv, branch_comm_achv, delete_flag, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name, revenue_node, order_id, product_id, product_category_id, `status`, main_split_person, data_change_type, installment_num, order_product_id,is_saas
    </sql>

    <insert id="batchInsertOrUpdate">
        INSERT INTO achievement_spec_detail_select (
        id,
        achievement_spec_id,
        achievement_category_id,
        spec_id,
        spec_name,
        item_type,
        order_spec_type,
        item_unit,
        item_num,
        serve_item_no,
        billing_price,
        renewal_price,
        standard_price,
        payable_amount,
        paid_amount,
        first_year_quote,
        first_year_income,
        renewal_quote,
        renewal_income,
        net_cash,
        agent_comm_achv,
        agent_act_comm_achv,
        agent_def_comm_achv,
        dept_comm_achv,
        bu_comm_achv,
        branch_comm_achv,
        delete_flag,
        create_time,
        create_user_id,
        create_user_name,
        update_time,
        update_user_id,
        update_user_name,
        revenue_node,
        order_id,
        product_id,
        product_category_id,
        status,
        main_split_person,
        data_change_type,
        installment_num,
        order_product_id,
        is_saas
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.achievementSpecId},
            #{item.achievementCategoryId},
            #{item.specId},
            #{item.specName},
            #{item.itemType},
            #{item.orderSpecType},
            #{item.itemUnit},
            #{item.itemNum},
            #{item.serveItemNo},
            #{item.billingPrice},
            #{item.renewalPrice},
            #{item.standardPrice},
            #{item.payableAmount},
            #{item.paidAmount},
            #{item.firstYearQuote},
            #{item.firstYearIncome},
            #{item.renewalQuote},
            #{item.renewalIncome},
            #{item.netCash},
            #{item.agentCommAchv},
            #{item.agentActCommAchv},
            #{item.agentDefCommAchv},
            #{item.deptCommAchv},
            #{item.buCommAchv},
            #{item.branchCommAchv},
            #{item.deleteFlag},
            #{item.createTime},
            #{item.createUserId},
            #{item.createUserName},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.updateUserName},
            #{item.revenueNode},
            #{item.orderId},
            #{item.productId},
            #{item.productCategoryId},
            #{item.status},
            #{item.mainSplitPerson},
            #{item.dataChangeType},
            #{item.installmentNum},
            #{item.orderProductId},
            #{item.isSaas}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        achievement_spec_id = VALUES(achievement_spec_id),
        achievement_category_id = VALUES(achievement_category_id),
        spec_id = VALUES(spec_id),
        spec_name = VALUES(spec_name),
        item_type = VALUES(item_type),
        order_spec_type = VALUES(order_spec_type),
        item_unit = VALUES(item_unit),
        item_num = VALUES(item_num),
        serve_item_no = VALUES(serve_item_no),
        billing_price = VALUES(billing_price),
        renewal_price = VALUES(renewal_price),
        standard_price = VALUES(standard_price),
        payable_amount = VALUES(payable_amount),
        paid_amount = VALUES(paid_amount),
        first_year_quote = VALUES(first_year_quote),
        first_year_income = VALUES(first_year_income),
        renewal_quote = VALUES(renewal_quote),
        renewal_income = VALUES(renewal_income),
        net_cash = VALUES(net_cash),
        agent_comm_achv = VALUES(agent_comm_achv),
        agent_act_comm_achv = VALUES(agent_act_comm_achv),
        agent_def_comm_achv = VALUES(agent_def_comm_achv),
        dept_comm_achv = VALUES(dept_comm_achv),
        bu_comm_achv = VALUES(bu_comm_achv),
        branch_comm_achv = VALUES(branch_comm_achv),
        delete_flag = VALUES(delete_flag),
        create_time = VALUES(create_time),
        create_user_id = VALUES(create_user_id),
        create_user_name = VALUES(create_user_name),
        update_time = VALUES(update_time),
        update_user_id = VALUES(update_user_id),
        update_user_name = VALUES(update_user_name),
        revenue_node = VALUES(revenue_node),
        order_id = VALUES(order_id),
        product_id = VALUES(product_id),
        product_category_id = VALUES(product_category_id),
        status = VALUES(status),
        main_split_person = VALUES(main_split_person),
        data_change_type = VALUES(data_change_type),
        installment_num = VALUES(installment_num),
        order_product_id = VALUES(order_product_id),
        is_saas = VALUES(is_saas)
    </insert>

</mapper>
