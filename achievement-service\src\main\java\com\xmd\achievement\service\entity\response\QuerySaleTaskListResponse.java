package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 销售任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
public class QuerySaleTaskListResponse implements
        Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "机构ID")
    private Long orgId;

    @Schema(description = "机构类型")
    private Integer orgType;

    @Schema(description = "机构名称")
    private String orgName;

    @Schema(description = "商务月")
    private String businessMonth;

    @Schema(description = "市场分类")
    private String marketClassification;

    @Schema(description = "基本任务（元）")
    private BigDecimal basicTask;

    @Schema(description = "分公司任务汇总（元）")
    private BigDecimal branchOfficeTotal;

    @Schema(description = "部门任务汇总（元）")
    private BigDecimal departmentTotal;

    @Schema(description = "事业部任务汇总（元）")
    private BigDecimal businessUnitTotal;

    @Schema(description = "任务状态")
    private Integer taskStatus;

    @Schema(description = "上级id")
    private Long parentId;

    @Schema(description = "是否没有leader 0没有  1有")
    private Integer noLeader;

    @Schema(description = "下级机构任务列表")
    private List<QuerySaleTaskListResponse> children;
}