package com.xmd.achievement.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.AchievementBlacklistModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 业绩黑名单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface IAchievementBlacklistRepository extends IService<AchievementBlacklistModel> {

    Page<AchievementBlacklistModel> selectBlacklist(Page<AchievementBlacklistModel> pageOf,String orderNo);

    List<AchievementBlacklistModel> selectAll();

    void deleteById(Long id);
}
