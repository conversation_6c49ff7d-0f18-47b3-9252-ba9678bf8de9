package com.xmd.achievement.support.constant.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * '订单销售类型：1=新开，2=续费，3=升级，4=另购',
 */
@Getter
public enum OrderSaleTypeEnum {

    OPEN(1, "新开"),
    RENEW(2, "续费"),
    UPGRADE(3, "升级"),
    ANOTHER_BUY(4, "另购"),
    TRANSFER(5, "转款"),
    REFUND(6, "退款"),
    HIGH_PRICE_REDEMPTION(7, "高价赎回");
    private final Integer type;

    private final String visitorCode;

    OrderSaleTypeEnum(Integer type, String visitorCode) {
        this.type = type;
        this.visitorCode = visitorCode;
    }

    public static OrderSaleTypeEnum getOrderSaleTypeByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        OrderSaleTypeEnum[] values = OrderSaleTypeEnum.values();
        for (int i = 0; i < values.length; i++) {
            OrderSaleTypeEnum value = values[i];
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static OrderSaleTypeEnum getOrderSaleTypeByVisitorCode(String visitorCode) {
        if (StringUtils.isEmpty(visitorCode)) {
            return null;
        }
        OrderSaleTypeEnum[] values = OrderSaleTypeEnum.values();
        for (int i = 0; i < values.length; i++) {
            OrderSaleTypeEnum value = values[i];
            if (StringUtils.equals(value.getVisitorCode(), visitorCode)) {
                return value;
            }
        }
        return null;
    }

    public static OrderSaleTypeEnum fromValue(Integer saleType) {
        return Arrays.stream(OrderSaleTypeEnum.values()).filter(orderSaleTypeEnum -> orderSaleTypeEnum.getType().equals(saleType)).findFirst().orElse(null);
    }
}
