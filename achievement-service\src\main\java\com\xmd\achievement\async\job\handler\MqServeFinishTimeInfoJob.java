package com.xmd.achievement.async.job.handler;

import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.dao.entity.MqServeFinishTimeInfoModel;
import com.xmd.achievement.service.IAchievementService;
import com.xmd.achievement.service.MqServeFinishTimeInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/14:24
 * @since 1.0
 */
@Slf4j
@Component
public class MqServeFinishTimeInfoJob {
    @Resource
    MqServeFinishTimeInfoService finishTimeInfoService;
    @Resource
    private IAchievementService achievementService;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.MQ_SERVE_FINISH_INFO_JOB)
    public ReturnT<String> jobHandler(String param) {
        log.info("执行MqServeFinishTimeInfoJob任务Start...");

        List<MqServeFinishTimeInfoModel> models = finishTimeInfoService.queryExecuteTask();
        for (MqServeFinishTimeInfoModel model : models) {
            try {
                achievementService.updateServeFinishTime(model.getOrderId(), model.getProductId(), model.getServeNo(), model.getServeFinishTime());
            } catch (Exception e) {
                log.error("MqServeFinishTimeInfoJob任务失败，TaskId:{},失败原因：", model.getTaskId(), e);
            }
        }
        log.info("执行MqServeFinishTimeInfoJob任务End...");

        return ReturnT.SUCCESS;
    }
}
