package com.xmd.achievement.service.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/19 17:23
 * @version: 1.0.0
 * @return {@link }
 */
@Data
public class BusinessSearch implements Serializable {
    private static final long serialVersionUID = -7548541177150438396L;


    /**
     * 员工姓名
     */
    @Schema(description = "员工姓名")
    private String employeeName;

    /**
     * 员工ID
     */
    @Schema(description = "员工ID")
    private String employeeId;

    /**
     * 职级code
     */
    @Schema(description = "职级code")
    private String positionCode;

    /**
     * 司龄分段ID
     */
    @Schema(description = "司龄分段ID")
    private String seniorityId;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private String orgId;

    /**
     * 业绩段ID
     */
    @Schema(description = "业绩段ID")
    private String achievementSegmentId;

    @Schema(description = "商务月开始")
    @JsonFormat(pattern = "yyyy-MM")
    private String startDate;
    /**
     * 结束日期
     */
    @Schema(description = "商务月结束")
    @JsonFormat(pattern = "yyyy-MM")
    private String endDate;
}
