package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商务月表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("business_month")
public class BusinessMonthModel  extends BaseModel  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 商务月id
     */
    @TableField("month_id")
    private Long monthId;
    /**
     * 商务月
     */
    @TableField("`month`")
    private String month;
    /**
     * 开始日期
     */
    @TableField("start_date")
    private Date startDate;
    /**
     * 月半日期
     */
    @TableField("mid_date")
    private Date midDate;
    /**
     * 结束日期
     */
    @TableField("end_date")
    private Date endDate;
    /**
     * 是否冻结 0-未冻结 1-已冻结
     */
    @TableField("is_freeze")
    private Integer isFreeze;

    /**
     * 是否为春节月 0-否 1-是
     */
    @TableField("is_spring_festival_month")
    private Integer isSpringFestivalMonth;

}