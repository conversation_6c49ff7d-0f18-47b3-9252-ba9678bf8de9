package com.xmd.achievement.web.controller;

import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.entity.dto.UpdateBusinessMonthDto;
import com.xmd.achievement.service.entity.request.BusinessMonthFreezeRequest;
import com.xmd.achievement.service.entity.request.BusinessMonthSaveRequest;
import com.xmd.achievement.service.entity.request.MonthPageRequest;
import com.xmd.achievement.service.entity.response.BusinessMonthInfoResponse;
import com.xmd.achievement.service.entity.response.BusinessMonthListResponse;
import com.xmd.achievement.service.entity.response.BusinessStartTimeResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.DateUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import static com.xmd.achievement.web.entity.response.WebCodeMessageEnum.*;
import static com.xmd.achievement.web.util.DateUtils.convertToLocalDateTime;

/**
 * 商务月接口
 *
 * <AUTHOR>
 */
@Tag(name = "商务月接口")
@Slf4j
@RestController
@RequestMapping("businessMonth")
public class BusinessMonthController {
    @Resource
    private IBusinessMonthService businessMonthService;

    /**
     * 保存
     **/
    @Operation(summary = "商务月保存接口")
    @PostMapping("saveMonth")
    public WebResult<Boolean> saveMonth(@RequestBody @Valid BusinessMonthSaveRequest request) {
        WebResult<Boolean> booleanWebResult = null;
        Date startDate = DateUtils.getStartOfDay(request.getStartDate());
        Date midDate = DateUtils.getEndOfDay(request.getMidDate());
        Date endDate = DateUtils.getEndOfDay(request.getEndDate());

        LocalDateTime localStartDateTime = convertToLocalDateTime(startDate);
        LocalDateTime localMidDateTime = convertToLocalDateTime(midDate);
        LocalDateTime localEndDateTime = convertToLocalDateTime(endDate);
        LocalDate currentDate = LocalDate.now();

  /*      if (localStartDateTime.toLocalDate().isBefore(currentDate)) {
            return WebResult.error(THE_START_DATE_CANNOT_BE_A_PAST_DATE);
        }*/
        if (localMidDateTime.isBefore(localStartDateTime)) {
            return WebResult.error(MONTH_HALF_DATE_START_DATE);
        }
        if (localEndDateTime.isBefore(localMidDateTime)) {
            return WebResult.error(END_DATE_HALF_MONTH_DATE);
        }

        request.setStartDate(startDate);
        request.setMidDate(midDate);
        request.setEndDate(endDate);

        booleanWebResult = businessMonthService.saveMonth(request);
        return booleanWebResult;
    }



    /**
     * 修改
     **/
    @Operation(summary = "商务月修改接口")
    @PostMapping("updateMonth")
    public WebResult<Boolean> updateMonth(@RequestBody @Valid UpdateBusinessMonthDto updateBusinessMonthDto) {

        Date midDate = DateUtils.getEndOfDay(updateBusinessMonthDto.getMidDate());
        Date endDate = DateUtils.getEndOfDay(updateBusinessMonthDto.getEndDate());


        LocalDateTime localMidDateTime = convertToLocalDateTime(midDate);
        LocalDateTime localEndDateTime = convertToLocalDateTime(endDate);
        LocalDate currentDate = LocalDate.now();

//        if (localMidDateTime.toLocalDate().isBefore(currentDate)) {
//            return WebResult.error(MONTH_HALF_DATE_CUR_DATE);
//        }
        if (localEndDateTime.isBefore(localMidDateTime)) {
            return WebResult.error(END_DATE_HALF_MONTH_DATE);
        }

        updateBusinessMonthDto.setMidDate(midDate);
        updateBusinessMonthDto.setEndDate(endDate);

        return businessMonthService.updateMonth(updateBusinessMonthDto);
    }

    /**
     * 列表
     **/
    @Operation(summary = "商务月列表接口")
    @PostMapping("monthList")
    public WebResult<PageResponse<BusinessMonthListResponse>> monthList(@RequestBody @Valid MonthPageRequest monthPageRequest) {
        return WebResult.success(businessMonthService.monthList(monthPageRequest));
    }

    @Operation(summary = "商务月查看")
    @GetMapping("/getMonthInfo")
    public WebResult<BusinessMonthInfoResponse> getMonthInfo(Long monthId) {
        if (ObjectUtils.isEmpty(monthId)) {
            return WebResult.error(ID_CANNOT_BE_EMPTY);
        }
        return WebResult.success(businessMonthService.getMonth(monthId));
    }

    @Operation(summary = "商务月查看")
    @GetMapping("/getStarTime")
    public WebResult<BusinessStartTimeResponse> getStarTime() {
        return WebResult.success(businessMonthService.getMonthStartDate());
    }

    @Operation(summary = "获取当前默认商务月")
    @GetMapping("/getCurrentBusinessesMonth")
    public WebResult<String> getCurrentBusinessesMonth() {
        return WebResult.success(businessMonthService.getCurrentBusinessesMonth());
    }

    @Operation(summary = "商务月冻结接口")
    @PostMapping("freezeMonth")
    public WebResult<Boolean> freezeMonth(@RequestBody @Valid BusinessMonthFreezeRequest request) {
        return WebResult.success(businessMonthService.freezeMonth(request.getId()));
    }

}
