package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.AchievementBlacklistModel;
import com.xmd.achievement.dao.mapper.AchievementBlacklistMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IAchievementBlacklistRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 业绩黑名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@Slf4j
public class AchievementBlacklistRepositoryImpl extends ServiceImpl<AchievementBlacklistMapper,AchievementBlacklistModel> implements IAchievementBlacklistRepository {

@Resource
private AchievementBlacklistMapper achievementBlacklistMapper;

    @Override
    public Page<AchievementBlacklistModel> selectBlacklist(Page<AchievementBlacklistModel> pageOf,String orderNo) {
        LambdaQueryWrapper<AchievementBlacklistModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(!StringUtils.isEmpty(orderNo),AchievementBlacklistModel::getOrderNo,orderNo);
        queryWrapper.eq(AchievementBlacklistModel::getDeleteFlag,0);
        return  achievementBlacklistMapper.selectPage(pageOf, queryWrapper);
    }

    @Override
    public List<AchievementBlacklistModel> selectAll() {
        LambdaQueryWrapper<AchievementBlacklistModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementBlacklistModel::getDeleteFlag,0);
        return  achievementBlacklistMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteById(Long id) {
        LambdaUpdateWrapper<AchievementBlacklistModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AchievementBlacklistModel::getDeleteFlag,1);
        updateWrapper.eq(AchievementBlacklistModel::getId,id);
        achievementBlacklistMapper.update(null,updateWrapper);
    }
}