package com.xmd.achievement.cache.access;

import com.xmd.achievement.cache.lock.Lock;
import com.xmd.achievement.cache.lock.LockManager;
import com.xmd.achievement.cache.lock.LockTypeEnum;
import com.xmd.achievement.cache.store.Cache;
import com.xmd.achievement.cache.store.CacheManager;
import com.xmd.achievement.cache.store.CacheTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 缓存基类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/6/19 下午11:49
 */
@Component
public class BaseManager {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(BaseManager.class);

    /**
     * 缓存管理
     */
    @Resource
    private CacheManager cacheManager;

    /**
     * 分布式锁管理
     */
    @Resource
    private LockManager lockManager;

    /**
     * 获取缓存
     *
     * @param cacheTypeEnum 缓存实现类型
     * @return com.xmd.achievement.cache.store.Cache
     * <AUTHOR>
     * @date 2023/4/6 17:16
     **/
    public Cache getCache(CacheTypeEnum cacheTypeEnum) {
        if (this.cacheManager == null) {
            LOGGER.error("缓存管理注入为空");
            throw new RuntimeException("cacheManager autowired fail...");
        }
        return cacheManager.getCache(cacheTypeEnum.getName());
    }

    /**
     * 获取缓存
     *
     * @return com.xmd.achievement.cache.store.Cache
     * <AUTHOR>
     * @date 2023/4/6 17:16
     **/
    public Cache getCache() {
        return getCache(CacheTypeEnum.REDIS);
    }

    /**
     * 获取分布式锁
     *
     * @param lockTypeEnum 锁实现类型
     * @return com.xmd.achievement.cache.lock.Lock
     * <AUTHOR>
     * @date 2023/4/6 17:16
     **/
    public Lock getLock(LockTypeEnum lockTypeEnum) {
        if (this.lockManager == null) {
            LOGGER.error("分布式锁注入为空");
            throw new RuntimeException("lockManager autowired fail...");
        }
        return lockManager.getLock(lockTypeEnum.getName());
    }

    /**
     * 获取分布式锁
     *
     * @return com.xmd.achievement.cache.lock.Lock
     * <AUTHOR>
     * @date 2023/4/6 17:16
     **/
    public Lock getLock() {
        return getLock(LockTypeEnum.REDIS);
    }
}
