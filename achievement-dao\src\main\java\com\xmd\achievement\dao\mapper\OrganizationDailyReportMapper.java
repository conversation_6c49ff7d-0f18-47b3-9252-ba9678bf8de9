package com.xmd.achievement.dao.mapper;

import com.xmd.achievement.dao.entity.OrganizationDailyReportModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 机构日报 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface OrganizationDailyReportMapper extends BaseMapper<OrganizationDailyReportModel> {

    void batchInsertOrUpdate(@Param("list") List<OrganizationDailyReportModel> list);
}
