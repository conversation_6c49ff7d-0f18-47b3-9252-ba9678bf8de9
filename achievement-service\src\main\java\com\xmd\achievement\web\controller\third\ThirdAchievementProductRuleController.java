package com.xmd.achievement.web.controller.third;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/09:48
 * @since 1.0
 */

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.AchievementProductRuleService;
import com.xmd.achievement.service.entity.request.CheckProductRuleStatusRequest;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 商品业绩类
 *
 * <AUTHOR>
 */
@Tag(name = "APRC-商品规则配置")
@Slf4j
@RestController
@RequestMapping("api/product/rule")
public class ThirdAchievementProductRuleController {
    @Resource
    AchievementProductRuleService achievementProductRuleService;

    @Operation(summary = "APRC-01-商品上架规则配置判断")
    @PostMapping("/checkProductRuleStatus")
    public WebResult<Boolean> checkProductRuleStatus(@RequestBody @Valid CheckProductRuleStatusRequest request) {
        log.info("APRC-01-商品上架规则配置判断,请求参数:{}", JSONUtil.toJsonStr(request));
        return WebResult.success(achievementProductRuleService.checkProductRuleStatus(request));
    }
}
