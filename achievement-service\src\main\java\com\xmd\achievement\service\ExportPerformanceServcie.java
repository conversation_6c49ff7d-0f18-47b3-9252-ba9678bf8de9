package com.xmd.achievement.service;

import com.xmd.achievement.service.entity.request.ExportKuajingAchievementRequest;
import com.xmd.achievement.service.entity.request.ExportPerformancRequest;
import com.xmd.achievement.service.entity.response.ExportKuajingAchievementResponse;
import com.xmd.achievement.service.entity.response.PrformanceInfoResponse;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/10/17:22
 * @since 1.0
 */
public interface ExportPerformanceServcie {
    /**
     * 提供销管系统数据信息
     *
     * @param request 请求参数
     * @return List<PrformanceInfoResponse>
     */
    List<PrformanceInfoResponse> export(ExportPerformancRequest request);

    /**
     * 提供中小数据信息
     *
     * @param request 请求参数
     * @return List<ExportKuajingAchievementResponse>
     */
    List<ExportKuajingAchievementResponse> exportKuajingAchievement(ExportKuajingAchievementRequest request);
}
