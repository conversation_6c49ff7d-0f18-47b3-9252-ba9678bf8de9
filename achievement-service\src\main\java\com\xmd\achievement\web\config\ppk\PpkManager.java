package com.xmd.achievement.web.config.ppk;

import java.util.Objects;

/**
 * 公私钥管理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/5/31 5:36 下午
 */
public class PpkManager {

    /**
     * 公私钥
     */
    public enum Ppk {
        /**
         * 公私钥对管理
         */
        test,
        ;
    }


    /**
     * 获取解密key
     * [需要自定义]
     *
     * @param ppk
     * @param configuration
     * @return
     */
    public static String getRequestDecryptKey(Ppk ppk, PpkConfiguration configuration) {
        if (Objects.isNull(configuration)) {
            return null;
        }
        String decryptKey;
        switch (ppk) {
            case test:
                decryptKey = configuration.getTest().getPrivateKey();
                break;
            default:
                decryptKey = "";
                break;
        }
        return decryptKey;
    }


}
