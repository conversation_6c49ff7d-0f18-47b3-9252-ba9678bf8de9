package com.xmd.achievement.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 业绩规格明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface IAchievementSpecDetailRepository extends IService<AchievementSpecDetailModel> {

    List<AchievementSpecDetailModel> selectAllAchievementSpecList();

    List<AchievementSpecDetailModel> selectAchievementSpecListByCategoryIds(List<Long> achievementCategoryIdList);

    void updateChangeTypeByAchievementCategoryId(List<Long> achievementCategoryIds, Integer changeType);

    List<AchievementSpecDetailModel> selectSpecDetailListByBetweenDate(Date startDate, Date endDate);

    List<AchievementSpecDetailModel> listByUpdateTime(Date startTime);

    void logicDeleteByOrderIdAndOrderProductId(Long orderId, List<String> orderProductId);

    /**
     * 根据商品业绩Id查询对应所有规格业绩流水
     *
     * @param achievementId 商品业绩Id
     * @return {@link List }<{@link AchievementSpecDetailModel }>
     * <AUTHOR>
     * @since 1.0
     */
    List<AchievementSpecDetailModel> listByAchievementId(Long achievementId);

    List<AchievementSpecDetailModel> selectByOrderIdAndSpecId(Long orderId, Long specId);
}
