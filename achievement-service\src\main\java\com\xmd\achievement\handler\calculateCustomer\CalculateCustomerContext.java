package com.xmd.achievement.handler.calculateCustomer;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.service.IAchievementProductDetailService;
import com.xmd.achievement.service.entity.request.AchievementProductDetailNodelRequst;
import com.xmd.achievement.service.entity.request.CalculateCustomerContextRequest;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.CustomerTypeEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 计算客户类型
 *
 * <AUTHOR>
 * @date: 2024/12/18 17:06
 */
@Service
@Slf4j
public class CalculateCustomerContext {

    @Resource
    NewCustomer newCustomer;
    @Resource
    NonExistingCustomer nonExistingCustomer;
    @Resource
    OldCustomer oldCustomer;
    @Resource
    IAchievementProductDetailService activityProductDetailService;
    @Resource
    CalculateCustomerContextV4 calculateCustomerContextV4;
    @Resource
    CalculateCustomerContext calculateCustomerContext;
/*
    public CustomerType calculateCustomer(String customerId, List<AchievementProductDetailModel> currentAchList) {
       *//* boolean allNewOpenType = currentAchList.stream().allMatch(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType()));
        if (allNewOpenType) {
            List<AchievementProductDetailModel> aches = achProductService.listByCustomerId(customerId);
            if (CollectionUtils.isNotEmpty(currentAchList)) {
                aches.addAll(currentAchList);
            }
            List<AchievementProductDetailModel> mainAches = aches.stream()
                    .filter(a -> MainSubEnum.MAIN.getType().equals(a.getMainSplitPerson()) && AchStatus.VALID.getType().equals(a.getStatus()))
                    .collect(Collectors.toList());

            return oldCustomer.calculateCustomerType(mainAches) ? CustomerType.OLD : newCustomer.calculateCustomerType(mainAches) ? CustomerType.NEW : nonExistingCustomer.calculateCustomerType(mainAches) ? CustomerType.NON_EXISTENT : null;
        } else {
            //■订单类型为续费、升级、另购时，生成的业绩流水中客户类型为老客户；
            return CustomerType.OLD;
        }*//*
        return calculateCustomerNew(customerId, currentAchList);
    }*/

    public CustomerType calculateCustomerBack(String customerId, List<AchievementProductDetailModel> currentAchList) {
        boolean allNewOpenType = currentAchList.stream().allMatch(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType()));
        if (allNewOpenType) {
            List<AchievementProductDetailModel> aches = activityProductDetailService.listByCustomerId(customerId);
            //计算新开类型总支付金额
            List<AchievementProductDetailModel> newAchList = currentAchList.stream()
                    .filter(a -> MainSubEnum.MAIN.getType().equals(a.getMainSplitPerson())
                            && AchStatus.VALID.getType().equals(a.getStatus())
                            && SaleTypeEnum.NEW_OPEN.getType().equals(a.getSaleType()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(newAchList)) {
                throw new BusinessException("未找到新老客户计算的流水.");
            }

            BigDecimal totalAmount = newAchList.stream().map(AchievementProductDetailModel::getPaidAmount).reduce(BigDecimal::add).get();

            //新开单计算逻辑 关于金额是否大于3000(是否有老单子)
            if (CollectionUtils.isEmpty(aches)) {
                //判断新开类型订单累计到账金额是否大于3000
                if (totalAmount.compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) < 0) {
                    return CustomerType.NON_EXISTENT;
                } else {
                    return CustomerType.NEW;
                }
            }
            // 存在历史流水计算逻辑（第一笔新开类型订单付款时间至今是否超过90天）
            aches.addAll(newAchList);

            Date minTime = aches.stream()
                    .map(AchievementProductDetailModel::getPaymentTime)
                    .min(Comparator.naturalOrder()).get();
            Date maxTime = aches.stream()
                    .map(AchievementProductDetailModel::getPaymentTime)
                    .max(Comparator.naturalOrder()).get();

            //判断当前订单的支付时间是否在90内
            if (DateUtil.between(minTime, maxTime, DateUnit.DAY) <= NumberConstants.INTEGER_VALUE_90) {
                //获取所有新开类型订单支付总金额
                totalAmount = aches.stream().map(AchievementProductDetailModel::getPaidAmount).reduce(BigDecimal::add).get();
                if (totalAmount.compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) < 0) {
                    return CustomerType.NON_EXISTENT;
                } else {
                    boolean haveNewCustomer = aches.stream().anyMatch(a -> CustomerType.NEW.getType().equals(a.getCustomerType()));
                    if (haveNewCustomer) {
                        return CustomerType.OLD;
                    } else {
                        return CustomerType.NEW;
                    }
                }
            } else {
                //是否有服务结束时间
                boolean haveServeFinishTime = aches.stream().anyMatch(a -> ObjectUtil.isNotNull(a.getServeFinishTime()));
                if (haveServeFinishTime) {
                    Date maxServeFinishTime = aches.stream()
                            .map(AchievementProductDetailModel::getServeFinishTime)
                            .filter(Objects::nonNull)
                            .max(Comparator.naturalOrder()).get();
                    maxTime = aches.stream()
                            .map(AchievementProductDetailModel::getPaymentTime)
                            .max(Comparator.naturalOrder()).get();
                    if (DateUtil.between(maxServeFinishTime, maxTime, DateUnit.DAY) <= NumberConstants.INTEGER_VALUE_180) {
                        return CustomerType.OLD;
                    } else {
                        //判断当前新开类型订单是否大于等于3000
                        if (totalAmount.compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) < 0) {
                            return CustomerType.NON_EXISTENT;
                        } else {
                            return CustomerType.NEW;
                        }
                    }
                } else {
                    return CustomerType.OLD;
                }
            }
        } else {
            //■订单类型为续费、升级、另购时，生成的业绩流水中客户类型为老客户；
            return CustomerType.OLD;
        }
    }


    Date calculateDate(Date time, Integer days) {
        Calendar instance = Calendar.getInstance();
        if (null != time) {
            instance.setTime(time);
        }
        instance.add(Calendar.DAY_OF_MONTH, days);
        return instance.getTime();
    }


    public CustomerType calculateCustomer(String customerId, List<AchievementProductDetailModel> currentAchList) {
        log.info("customerId:{} 新老客户计算,,currentAchList:{}", customerId, JSONUtil.toJsonStr(currentAchList));
        boolean allNewOpenType = currentAchList.stream().allMatch(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType()));
        //订单类型为续费、升级、另购时，生成的业绩流水中客户类型为老客户；
        if (allNewOpenType) {
            return calculateNewTypeCustomer(customerId, currentAchList);
        }
        return CustomerType.OLD;
    }

    private CustomerType calculateNewTypeCustomer(String customerId, List<AchievementProductDetailModel> currentAchList) {
        //获取180天前的时间 todo 所有的订单包含升级 另购续费
        Date currentPaymentTime = currentAchList.get(0).getPaymentTime();
        Date before180Date = DateUtils.calculateDate(currentAchList.get(0).getPaymentTime(), TimeOperationEnum.SUBTRACT, NumberConstants.INTEGER_VALUE_180);
        //查询出前180天的订单
        List<AchievementProductDetailModel> achievementProductDetail180Models = activityProductDetailService.selectListByPayTime(customerId, before180Date, currentPaymentTime, OrderRuleTypeEnum.ASC);

        //如果"前180天的订单" 为空或者"服务结束时间"为空：执行180外逻辑
        List<AchievementProductDetailModel> serviceTimeModels = achievementProductDetail180Models.stream().filter(e -> e.getServeFinishTime() != null).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(achievementProductDetail180Models) || ObjectUtil.isEmpty(serviceTimeModels)) {
            log.info("customerId:{},新老客户计算,执行180外逻辑", customerId);
            return calculate180DayForeign(customerId, currentAchList, currentPaymentTime, before180Date, achievementProductDetail180Models);
        } else {
            log.info("customerId:{},新老客户计算,执行180内逻辑", customerId);
            // 如果"前180天的订单" 不为空 并且有"服务结束时间"为空：执行180内逻辑
            return calculate180DayDomestic(customerId, currentAchList, currentPaymentTime, before180Date, achievementProductDetail180Models);
        }
    }

    private CustomerType calculate180DayForeign(String customerId, List<AchievementProductDetailModel> currentAchList, Date currentPaymentTime, Date before180Date, List<AchievementProductDetailModel> achievementProductDetail180Models) {
        //为空直接判断金额
        if (ObjectUtil.isEmpty(achievementProductDetail180Models)) {
            //为空直接计算金额
            List<AchievementProductDetailModel> collect = currentAchList.stream()
                    .filter(a -> MainSubEnum.MAIN.getType().equals(a.getMainSplitPerson())
                            && AchStatus.VALID.getType().equals(a.getStatus())
                            && SaleTypeEnum.NEW_OPEN.getType().equals(a.getSaleType()))
                    .collect(Collectors.toList());
            BigDecimal totalMoney = collect.stream().map(AchievementProductDetailModel::getPaidAmount).reduce(BigDecimal::add).get();
            return calculateNewCustomer(totalMoney);
        }

        //不为空判断90天
        //取第一条的支付时间
        AchievementProductDetailModel model = achievementProductDetail180Models.get(0);
        Date satrtPayTime = model.getPaymentTime();

        //计算出和当前支付时间的差
        long distanceDay = DateUtils.calculateDifference(currentPaymentTime, satrtPayTime).getDays();
        log.info("customerId:{},新老客户计算,执行180外逻辑，distanceDay:{}", customerId, distanceDay);

        //计算出总金额
        List<AchievementProductDetailModel> totalModels = new ArrayList<>();
        totalModels.addAll(currentAchList);
        totalModels.addAll(achievementProductDetail180Models);
        List<AchievementProductDetailModel> collect = totalModels.stream()
                .filter(a -> MainSubEnum.MAIN.getType().equals(a.getMainSplitPerson())
                        && AchStatus.VALID.getType().equals(a.getStatus())
                        && SaleTypeEnum.NEW_OPEN.getType().equals(a.getSaleType()))
                .collect(Collectors.toList());
        BigDecimal totalMoney = collect.stream().map(AchievementProductDetailModel::getPaidAmount).reduce(BigDecimal::add).get();
        return foreignCalculate90Days(totalMoney, distanceDay, achievementProductDetail180Models);
    }

    private CustomerType calculate180DayDomestic(String customerId, List<AchievementProductDetailModel> currentAchList, Date currentPaymentTime, Date before180Date, List<AchievementProductDetailModel> achievementProductDetail180Models) {
        //查询出前90天的升序订单 取出第一条
        Date before90Day = DateUtils.calculateDate(currentAchList.get(0).getPaymentTime(), TimeOperationEnum.SUBTRACT, NumberConstants.INTEGER_VALUE_90);
        List<AchievementProductDetailModel> achievementProductDetail190Models = activityProductDetailService.selectListByPayTime(customerId, before90Day, currentPaymentTime, OrderRuleTypeEnum.ASC);

        //没有数据直接定义为老客户
        if (ObjectUtil.isEmpty(achievementProductDetail190Models)) {
            return CustomerType.OLD;
        }

        //判断90天内是否存在新客户类型
        boolean isHaveNewCustomerType = achievementProductDetail190Models.stream().anyMatch(model -> model.getCustomerType().equals(CustomerTypeEnum.NEW.getCode()));
        if (isHaveNewCustomerType) {
            return CustomerType.OLD;
        }

        //判断90天内是否存在非新老类型
        boolean isHaveNotNewOldCustomerType = achievementProductDetail190Models.stream().anyMatch(model -> model.getCustomerType().equals(CustomerTypeEnum.NON_NEW_OLD.getCode()));
        if (!isHaveNotNewOldCustomerType) {
            return CustomerType.OLD;
        }

        // 1. 查询出最近的一笔非新老客户订单的支付时间
        Optional<Date> endPaymentTimeOpt = achievementProductDetail190Models.stream()
                .filter(model -> CustomerTypeEnum.NON_NEW_OLD.getCode().equals(model.getCustomerType()))
                .map(AchievementProductDetailModel::getPaymentTime)
                .max(Comparator.naturalOrder());
        if (!endPaymentTimeOpt.isPresent()) {
            throw new IllegalArgumentException("未找到符合条件的非新老客户订单");
        }

        Date endPaymentTime = endPaymentTimeOpt.get();

        //计算90天前的起始时间
        Date startPaymentTime = DateUtils.calculateDate(endPaymentTime, TimeOperationEnum.SUBTRACT, NumberConstants.INTEGER_VALUE_90);

        //查询90天内的非新老客户订单
        List<AchievementProductDetailModel> achievementNotNewOld90Models = activityProductDetailService.selectListByPayTime(
                customerId, startPaymentTime, endPaymentTime, OrderRuleTypeEnum.ASC);

        //获取90天内最早的一笔非新老客户订单的支付时间
        Optional<Date> firstTimeOpt = achievementNotNewOld90Models.stream()
                .filter(model -> CustomerTypeEnum.NON_NEW_OLD.getCode().equals(model.getCustomerType()))
                .map(AchievementProductDetailModel::getPaymentTime)
                .min(Comparator.naturalOrder());
        if (!firstTimeOpt.isPresent()) {
            throw new IllegalArgumentException("未找到90天内的非新老客户订单");
        }
        Date firstTime = firstTimeOpt.get();

        // 计算时间差
        long distanceDay = DateUtils.calculateDifference(currentPaymentTime, firstTime).getDays();
        log.info("customerId:{},新老客户计算,执行180内逻辑，distanceDay:{}", customerId, distanceDay);


        //计算总金额
        achievementNotNewOld90Models.addAll(currentAchList);
        BigDecimal totalMoney = achievementNotNewOld90Models.stream()
                .filter(a -> MainSubEnum.MAIN.getType().equals(a.getMainSplitPerson())
                        && AchStatus.VALID.getType().equals(a.getStatus())
                        && SaleTypeEnum.NEW_OPEN.getType().equals(a.getSaleType()))
                .map(AchievementProductDetailModel::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 返回计算结果
        return domesticCalculate90Days(totalMoney, distanceDay);
    }

    private CustomerType foreignCalculate90Days(BigDecimal totalMoney, long distanceDay, List<AchievementProductDetailModel> achievementProductDetail180Models) {
        if (distanceDay < NumberConstants.INTEGER_VALUE_90) {
            //判断180天内是否存在新客户类型
            boolean isHaveNewCustomerType = achievementProductDetail180Models.stream().anyMatch(model -> model.getCustomerType().equals(CustomerTypeEnum.NEW.getCode()));
            if (isHaveNewCustomerType) {
                return CustomerType.OLD;
            } else {
                return calculateNewCustomer(totalMoney);
            }
        }
        return CustomerType.OLD;
    }


    private CustomerType domesticCalculate90Days(BigDecimal totalMoney, long distanceDay) {
        if (distanceDay < NumberConstants.INTEGER_VALUE_90) {
            return calculateNewCustomer(totalMoney);
        }
        return CustomerType.OLD;
    }

    private CustomerType calculateNewCustomer(BigDecimal totalMoney) {
        return totalMoney.compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) < 0 ? CustomerType.NON_EXISTENT : CustomerType.NEW;
    }


    public CustomerType calculateTest(CalculateCustomerContextRequest request) {
        List<AchievementProductDetailModel> currentAchList = request.getCurrentAchList().stream().map(e -> {
            AchievementProductDetailModel model = new AchievementProductDetailModel();
            BeanUtils.copyProperties(e, model);
            return model;
        }).collect(Collectors.toList());
        return calculateCustomerContextV4.calculateCustomerV4(1, 2, request.getCustomerId(), currentAchList);
        // return calculateCustomerContext.calculateCustomer(request.getCustomerId(), currentAchList);
    }

}
