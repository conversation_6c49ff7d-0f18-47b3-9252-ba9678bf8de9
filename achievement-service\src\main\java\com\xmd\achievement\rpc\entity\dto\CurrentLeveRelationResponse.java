package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/09/17:17
 * @since 1.0
 */
@Data
public class CurrentLeveRelationResponse implements Serializable {
    @Schema(description = "机构id")
    private Long id;

    @Schema(description = "机构ID")
    private String orgId;

    @Schema(description = "机构名称")
    private String name;

    @Schema(description = "机构上级id")
    private Long parentId;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "可用状态（0否；1是）")
    private Integer state;

    @Schema(description = "机构层级类型（HQ总部；AREA区域；SUB分公司；DEPT部门）")
    private String type;

    @Schema(description = "机构业务类型（0综合；1商务；2技术；3服务；4生产；5财务；6管家；9其他）")
    private Integer businessType;

    @Schema(description = "商务标签（0-通用 ，1-电商）")
    private Integer orgClass2;

    @Schema(description = "创建时间")
    private Date createDate;

    @Schema(description = "更新时间")
    private Date updateDate;

    @Schema(description = "是否商务机构（0-否 1-是）")
    private Integer isCommerce;

    @Schema(description = "n级市场分类")
    private Integer marketCategoryId;

    @Schema(description = "机构ID路径")
    private String orgPath;
}
