package com.xmd.achievement.service.entity.page;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class PageRequest {

    /**
     * 当前页
     */
    protected Long pageIndex;

    /**
     * 页大小
     */
    protected Long pageSize;

    public Long getPageIndex() {
        if (this.pageIndex == null || this.pageIndex == 0) {
            pageIndex = 1L;
        }
        return pageIndex;
    }

    public Long getPageSize() {
        if (this.pageSize == null || this.pageSize == 0) {
            pageSize = 10L;
        }
        return pageSize;
    }

    public <T> IPage<T> toPage() {
        if (this.pageIndex == null || this.pageIndex == 0) {
            pageIndex = 1L;
        }
        if (this.pageSize == null || this.pageSize == 0) {
            pageSize = 10L;
        }
        return new Page<>(pageIndex, pageSize);
    }

    public Long getOffset() {
        long offset = (this.getPageIndex() - 1L) * this.getPageSize();
        return Math.max(offset, 0L);
    }

    public void clearPage() {
        this.pageIndex = null;
        this.pageSize = null;
    }

    public void clearPageParam() {
        this.pageIndex = 0L;
        this.pageSize = -1L;
    }

}
