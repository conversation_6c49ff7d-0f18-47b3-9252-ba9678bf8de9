package com.xmd.achievement.dao.mapper;

import com.xmd.achievement.dao.dto.SearchAchievementDetailsDto;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 业绩商品明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface AchievementProductDetailMapper extends BaseMapper<AchievementProductDetailModel> {
    List<SearchAchievementDetailsDto> searchAchievementDetails(@Param("businessMonths") List<String> businessMonths,
                                                                         @Param("businessIds") List<String> businessIds,
                                                                         @Param("companyId") Long companyId);
    List<AchievementProductDetailModel> selectDistinctRefundOrderProduct();
}
