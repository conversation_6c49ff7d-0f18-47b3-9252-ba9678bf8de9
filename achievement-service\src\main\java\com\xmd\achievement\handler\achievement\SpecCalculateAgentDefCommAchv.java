package com.xmd.achievement.handler.achievement;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.AchievementPolicyModel;
import com.xmd.achievement.dao.entity.PolicySpecDetailModel;
import com.xmd.achievement.service.IPolicyService;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.CurrentStatusEnum;
import com.xmd.achievement.support.constant.enums.PolicyRevenueNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 商代缓发业绩提成
 * 商代缓发业绩提成:</br>
 * ○若商品业绩计收节点为支付完成，即订单付款完成，则单付款完成，商务缓发业绩提成=商代提成业绩x（1-实发业绩提成比例) （实发业绩提成比例固定为100%）；</br>
 * ○若商品业绩计收节点为生产完成，则：</br>
 * ■订单付款完成，商务缓发业绩提成=商代提成业绩x（1-实发业绩提成比例)；</br>
 * ■服务交付完成，商务缓发业绩提成=0;</br>
 *
 * <AUTHOR>
 * @date: 2024/12/24 14:00
 */
@Slf4j
@Service
public class SpecCalculateAgentDefCommAchv implements CalculateAmountHandler {
    @Resource
    private IPolicyService policyService;

    @Override
    public void calculate(CalculateFactInfo factInfo) {
        try {
            //获取商品业绩政策
            PolicySpecDetailModel policy = policyService.getPolicyDetailBySpecId(factInfo.getSpec().getSpecId());
            if (policy != null) {
                //根据商品业绩计收节点计算商代实发提成业绩
                BigDecimal amount;
                if (CurrentStatusEnum.PAID.getStatus().equals(factInfo.getCurrentStatus()) && factInfo.getSpec().getPaidAmount().compareTo(BigDecimal.ZERO) > 0) {
                    if (PolicyRevenueNodeEnum.PAID_FINISHED.getType().equals(policy.getRevenueNode())) {
                        amount = NumberUtil.mul(factInfo.getSpec().getAgentCommAchv(), NumberUtil.sub(NumberConstants.INTEGER_VALUE_1, NumberConstants.INTEGER_VALUE_1)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                    } else {
                        amount = NumberUtil.mul(factInfo.getSpec().getAgentCommAchv(), NumberUtil.sub(NumberConstants.INTEGER_VALUE_1, NumberUtil.div(policy.getCommissionRatio(), NumberConstants.INTEGER_VALUE_100))).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                    }
                } else {
                    amount = BigDecimal.ZERO;
                }
                factInfo.getSpec().setAgentDefCommAchv(amount);
            }
        } finally {
            log.warn("商代缓发业绩提成业绩 规格id:{},参数:{}", factInfo.getSpec().getSpecId(), JSON.toJSONString(factInfo));
        }
    }
}
