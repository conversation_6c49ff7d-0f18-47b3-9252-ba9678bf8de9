package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

@Getter
public enum CustomerSaasEnum {
    
    NEW_SAAS_CUSTOMER(0, "SAAS新客户"),
    
    OLD_SAAS_CUSTOMER(1, "SAAS老客户"),

    NEW_CUSTOMER(2, "新客户"),

    OLD_CUSTOMER(3, "老客户");

    private final Integer code;
    private final String msg;

    CustomerSaasEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
