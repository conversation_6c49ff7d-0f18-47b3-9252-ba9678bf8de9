package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/15:47
 * @since 1.0
 */
@Data
public class UpdateSpecCombinationRequest implements Serializable {
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "ID不能为空")
    private Long combinationId;

    @Schema(description = "组合名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "组合名称")
    @NotBlank(message = "组合名称不能为空")
    private String combinationName;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "备注")
    private String remark;

    @Schema(description = "状态 1=启用 2=禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "规格明细", requiredMode = Schema.RequiredMode.REQUIRED, example = "json")
    @NotEmpty(message = "规格明细不能为空")
    private List<SpecCombinationDetailRequest> specDetailList;
}
