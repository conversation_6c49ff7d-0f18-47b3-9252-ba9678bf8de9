package com.xmd.achievement.service.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 业绩分段表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class AchievementSegmentDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 业绩分段id
     */
    @Schema(description = "业绩分段id")
    private Long segmentId;

    /**
     * 业绩分段名称
     */
    @Schema(description = "业绩分段名称")
    private String segmentName;

    /**
     * 最小值
     */
    @Schema(description = "最小值")
    private Integer minValue;

    /**
     * 最大值
     */
    @Schema(description = "最大值")
    private Integer maxValue;


}