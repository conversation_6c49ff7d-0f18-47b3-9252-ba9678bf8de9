package com.xmd.achievement.handler.statistics;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.enums.SaasEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 净现金到账处理器 （计算有效的）
 * <AUTHOR>
 * @date: 2024/12/25 11:54
 */
@Service
public class NetCashReceiptHandler implements StatisticsHandler {

    @Override
    public void statistics(StatisticsFactInfo factInfo) {
        List<AchievementProductDetailModel> achList = factInfo.getAchList();
        BigDecimal netCash = achList.stream()
                .map(AchievementProductDetailModel::getNetCash)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        factInfo.setNetCashReceipt(netCash);
        factInfo.setSaasNetCash(achList.stream()
                .filter(ach -> SaasEnum.YES.getCode().equals(ach.getIsSaas()))
                .map(AchievementProductDetailModel::getNetCash)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
    }
}
