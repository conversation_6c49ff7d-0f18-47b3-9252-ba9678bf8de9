package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 业绩分段表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@TableName("achievement_segment")
public class AchievementSegmentModel  extends BaseModel  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业绩分段id
     */
    @TableField("segment_id")
    private Long segmentId;
    /**
     * 业绩分段名称
     */
    @TableField("segment_name")
    private String segmentName;
    /**
     * 最小值
     */
    @TableField("min_value")
    private Integer minValue;
    /**
     * 最大值
     */
    @TableField("max_value")
    private Integer maxValue;

}