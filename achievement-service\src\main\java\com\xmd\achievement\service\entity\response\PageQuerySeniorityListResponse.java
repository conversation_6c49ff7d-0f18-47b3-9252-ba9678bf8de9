package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/17:06
 * @since 1.0
 */
@Data
public class PageQuerySeniorityListResponse implements Serializable {
    @Schema(description = "司龄ID")
    private Long seniorityId;

    @Schema(description = "司龄名称")
    private String seniorityName;

    @Schema(description = "最小值（年限）")
    private Integer minYears;

    @Schema(description = "最大值（年限）")
    private Integer maxYears;
}
