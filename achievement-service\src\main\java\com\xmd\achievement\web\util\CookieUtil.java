package com.xmd.achievement.web.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 操作cookie工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/6/17 下午5:40
 */
public class CookieUtil {
    /**
     * 默认cookie路径
     */
    private final static String COOKIE_PATH = "/";

    /**
     * 永久有效期
     */
    private final static int FOREVER = -1;

    /**
     * cookie失效
     */
    private final static int INVALID = 0;


    private static final Logger logger = LoggerFactory.getLogger(CookieUtil.class);

    /**
     * 获取请求头中的cookie信息
     *
     * @param request 请求信息
     * @param name    cookie名称
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/6/17 下午5:41
     * @version 1.0.0
     **/
    public static String getCookie(HttpServletRequest request, String name) {
        Cookie[] cookies = request.getCookies();
        logger.info("cookie:" + Arrays.toString(cookies));
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(name)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 设置响应值中的cookie信息
     *
     * @param response 响应信息
     * @param name     cookie名称
     * @param value    cookie值
     * <AUTHOR>
     * @date 2021/6/18 上午9:42
     * @version 1.0.0
     **/
    public static void setCookie(HttpServletResponse response, String name, String value, List<String> domainList) {
        domainList.forEach(domain -> setCookie(response, name, value, FOREVER, domain));
    }

    /**
     * 设置cookie
     *
     * @param response 相应数据
     * @param name     cookie名称
     * @param value    cookie值
     * @param domain   域
     * @return void
     * <AUTHOR>
     * @date 2021/10/12 上午11:50
     * @version 1.0.0
     **/
    private static void setCookie(HttpServletResponse response, String name, String value, int maxAge, String domain) {
        //实例化cookie
        Cookie cookie = new Cookie(name, value);
        //设置只能在https传输
        //cookie.setSecure(true);
        //设置只能服务端程序修改
        cookie.setHttpOnly(true);
        //设置cookie路径
        cookie.setPath(COOKIE_PATH);
        //设置有效期为永久
        cookie.setMaxAge(maxAge);
        //设置域名
        cookie.setDomain(domain);
        response.addCookie(cookie);
    }


    /**
     * 删除响应值中的cookie信息
     *
     * @param response 响应信息
     * @param name     cookie名称
     * <AUTHOR>
     * @date 2021/6/18 上午9:42
     * @version 1.0.0
     **/
    public static void delCookie(HttpServletResponse response, String name, List<String> domainList) {
        domainList.forEach(domain -> setCookie(response, name, null, INVALID, domain));
    }
}
