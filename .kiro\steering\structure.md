# Project Structure

## Multi-Module Maven Architecture

This is a multi-module Maven project with clear separation of concerns following layered architecture principles.

## Module Organization

### Root Module (`bsp-achievement`)
- Parent POM defining shared dependencies and versions
- Aggregates all sub-modules
- Contains global configuration and build profiles

### Core Modules

#### `achievement-util`
- **Purpose**: Common utilities and helper classes
- **Dependencies**: FastJSON, Hutool, Apache Commons, OkHttp, Selenium
- **Contains**: Utility classes, common constants, helper methods
- **No Spring dependencies** - Pure utility module

#### `achievement-dao` 
- **Purpose**: Data access layer
- **Dependencies**: MyBatis Plus, MySQL connector, Velocity (for code generation)
- **Contains**: Entity classes, mappers, database configurations
- **Responsibility**: Database operations and ORM mapping

#### `achievement-cache`
- **Purpose**: Caching layer abstraction
- **Dependencies**: Spring Boot starter, achievement-util
- **Contains**: Cache configurations, cache service interfaces
- **Responsibility**: Redis caching logic

#### `achievement-support`
- **Purpose**: Infrastructure and support services
- **Dependencies**: All other modules, Redis, RocketMQ, CAT monitoring
- **Contains**: Configuration classes, infrastructure services, external integrations
- **Responsibility**: Cross-cutting concerns, external service integrations

#### `achievement-service`
- **Purpose**: Main application module and business logic
- **Dependencies**: achievement-dao, achievement-support
- **Contains**: REST controllers, business services, application main class
- **Responsibility**: API endpoints, business logic, application entry point

## Package Structure (Java)

All Java code follows the package convention: `com.xmd.achievement`

### Service Module Structure
```
com.xmd.achievement/
├── async/           # Asynchronous processing (events, jobs, MQ)
├── handler/         # Business logic handlers
├── rpc/            # RPC interfaces and implementations  
├── service/        # Business service interfaces and implementations
├── support/        # Support utilities and constants
├── web/            # Web layer (controllers, config, exceptions)
└── BspAchievementServiceApplication.java  # Main application class
```

### Web Layer Organization
```
web/
├── annotate/       # Custom annotations
├── aop/           # Aspect-oriented programming
├── config/        # Web configuration
├── controller/    # REST controllers
├── entity/        # Web DTOs and request/response objects
├── exception/     # Exception handlers
├── interceptor/   # Request interceptors
└── util/          # Web utilities
```

## Configuration Files

### Application Configuration
- `application.yml` - Main configuration
- `application-{profile}.yml` - Environment-specific configs (dev, test, pre, release)
- `logback-{profile}.xml` - Logging configuration per environment

### Build Configuration
- `pom.xml` - Maven configuration at each level
- `assembly.xml` - Custom packaging configuration
- Build profiles: test, pre, release with different JVM settings

## Dependency Flow

```
achievement-service (main app)
├── achievement-dao (data access)
├── achievement-support (infrastructure)
    ├── achievement-cache (caching)
    ├── achievement-util (utilities)  
    └── achievement-dao (data access)
```

## Development Guidelines

### Module Boundaries
- **Util**: No Spring dependencies, pure Java utilities
- **DAO**: Only database and ORM concerns
- **Cache**: Only caching abstractions
- **Support**: Infrastructure, configuration, external integrations
- **Service**: Business logic, REST APIs, main application

### Code Organization
- Use standard Spring Boot package structure
- Separate interfaces from implementations
- Group related functionality in handler packages
- Keep web concerns in the web package
- Use meaningful package names that reflect functionality