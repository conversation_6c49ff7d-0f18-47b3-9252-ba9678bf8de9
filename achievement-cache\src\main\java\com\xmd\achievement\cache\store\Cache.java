package com.xmd.achievement.cache.store;

import com.xmd.achievement.cache.entity.SortCollectionData;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 缓存处理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 17:18
 */
public interface Cache {
    /**
     * 获取当前操作缓存方式的名称
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    String getName();

    //--------------------------------------通用命令------------------------------

    /**
     * 模糊获取所有的key
     *
     * @param prefix 查询条件
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2023/6/25 10:32
     **/
    Set<String> scan(String prefix);

    /**
     * 判断缓存数据是否存在
     *
     * @param key 缓存键
     * @return boolean
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    boolean exist(String key);

    /**
     * 重设缓存过期时间
     *
     * @param key 缓存键
     * <AUTHOR>
     * @date: 2024/5/27 2:41 下午
     * @version: 1.0.0
     * @return: void
     */
    void ttl(String key, long time);

    /**
     * 删除缓存
     *
     * @param key 缓存键
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void del(String key);

    /**
     * 批量删除缓存
     *
     * @param cacheKeyList 缓存键列表
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void batchDel(List<String> cacheKeyList);

    /**
     * 批量删除缓存
     *
     * @param cacheKeyList 缓存键列表
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    List<String> batchGet(List<String> cacheKeyList);

    //--------------------------------------String类型------------------------------

    /**
     * 设置缓存数据，带有效期
     *
     * @param key    缓存键
     * @param value  缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void set(String key, String value, long expire);

    /**
     * 设置缓存数据，不带有效期
     *
     * @param key   缓存键
     * @param value 缓存值
     * <AUTHOR>
     * @date: 2024/6/3 5:23 下午
     * @version: 1.0.0
     * @return: void
     */
    void setWithoutExpire(String key, String value);

    /**
     * 获取缓存数据
     *
     * @param key 缓存键
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    String get(String key);

    /**
     * 获取缓存数据并且删除key
     * @return
     */
    String getAndDeleteUsingLua(String key);

    /**
     * 递增
     *
     * @param key   键
     * @param delta 递增因子
     * @return long 返回当前的递增值，如果为空，则代表key不存在
     * <AUTHOR>
     * @date 2021/05/20 5:07 下午
     * @version 1.0.0
     **/
    public long incr(String key, long delta);

    //--------------------------------------hash类型------------------------------

    /**
     * 设置hash数据 不设置有效期
     *
     * @param key    缓存键
     * @param hValue 缓存值
     * @date 2023/4/6 17:18
     **/
    void hSet(String key, Map<String, String> hValue);

    /**
     * 设置hash数据
     *
     * @param key    缓存键
     * @param hValue 缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void hSet(String key, Map<String, String> hValue, long expire);

    /**
     * 设置hash数据
     *
     * @param key        缓存键
     * @param hashKey    缓存hashKey
     * @param hValueItem 单条缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void hSetItem(String key, String hashKey, String hValueItem);

    /**
     * 删除hash
     *
     * @param key
     * @param hashKeys
     * <AUTHOR>
     * @date: 2024/7/1 11:47 上午
     * @version: 1.0.0
     * @return: void
     */
    public void hDelItem(String key, List<String> hashKeys);

    /**
     * 获取hash数据
     *
     * @param key     缓存键
     * @param hashKey 缓存hashKey
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    String hGetItem(String key, String hashKey);

    /**
     * 获取hash数据
     *
     * @param key 缓存键
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    Map<String, String> hGet(String key);

    //--------------------------------------list类型------------------------------

    /**
     * 设置list数据
     *
     * @param key    缓存键
     * @param lValue 缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void lSet(String key, List<String> lValue, long expire);

    /**
     * 设置list数据
     *
     * @param key    缓存键
     * @param lValue 缓存值
     * <AUTHOR>
     * @date: 2024/6/3 4:54 下午
     * @version: 1.0.0
     * @return: void
     */
    public void lSetWithoutExpire(String key, List<String> lValue);

    /**
     * 给list中添加单条数据
     *
     * @param key        缓存键
     * @param lValueItem 缓存值
     * @param insertNode 插入节点
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void lSetItem(String key, String lValueItem, int insertNode);

    /**
     * 获取list数据
     *
     * @param key 缓存键
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    List<String> lGet(String key);

    /**
     * 获取list的长度
     *
     * @param key 缓存键
     * <AUTHOR>
     * @date: 2024/6/3 2:34 下午
     * @version: 1.0.0
     * @return: long
     */
    long lGetListSize(String key);

    /**
     * 获取list的指定元素
     *
     * @param key   缓存键
     * @param index 下标
     * <AUTHOR>
     * @date: 2024/6/3 6:52 下午
     * @version: 1.0.0
     * @return: java.lang.String
     */
    String lIndex(String key, Integer index);


    //--------------------------------------set类型-------------------------------

    /**
     * 设置set数据
     *
     * @param key    缓存键
     * @param sValue 缓存值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void sSet(String key, Set<String> sValue, long expire);

    /**
     * 设置set数据
     *
     * @param key    缓存键
     * @param sValue 缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void sSetItem(String key, String sValue);

    /**
     * 获取set数据
     *
     * @param key 缓存键
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    Set<String> sGet(String key);
    //--------------------------------------zSet类型------------------------------

    /**
     * 设置有序集合
     *
     * @param key    缓存键
     * @param zValue 元素值
     * @param expire 有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void zSet(String key, List<SortCollectionData> zValue, long expire);

    /**
     * 设置有序集合
     *
     * @param key        缓存键
     * @param zValueItem 元素值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    void zSetItem(String key, SortCollectionData zValueItem);

    /**
     * 获取有序集合
     *
     * @param key     缓存键
     * @param orderBy 排序方式
     * @return java.util.Set<com.xmd.achievement.cache.entity.SortCollectionData>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    List<SortCollectionData> zGet(String key, int orderBy);

    /**
     * 获取有序集合
     *
     * @param key    缓存键
     * @param weight 权重
     * @return java.util.Set<com.xmd.achievement.cache.entity.SortCollectionData>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    Set<SortCollectionData> zGetItem(String key, double weight);
}
