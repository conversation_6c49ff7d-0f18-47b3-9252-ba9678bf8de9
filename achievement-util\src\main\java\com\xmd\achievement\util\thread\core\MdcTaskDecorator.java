package com.xmd.achievement.util.thread.core;

import com.xmd.achievement.util.context.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;
import org.springframework.lang.NonNull;

import java.util.Map;

/**
 * mdc任务装饰器，为线程池任务提供MDC上下文传递功能
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public class MdcTaskDecorator implements TaskDecorator {

    @NonNull
    @Override
    public Runnable decorate(@NonNull Runnable runnable) {
        String traceId = TraceContext.get();
        Map<String, String> contextMap = MDC.getCopyOfContextMap();

        return () -> {
            try {
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                if (traceId != null) {
                    TraceContext.set(traceId);
                }
                runnable.run();
            } catch (Throwable t) {
                log.error("线程池任务执行异常", t);
                throw t;
            } finally {
                TraceContext.clear();
            }
        };
    }
}
