package com.xmd.achievement.handler.achievement;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.dao.repository.IAchievementCategoryDetailRepository;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IAchievementSpecDetailRepository;
import com.xmd.achievement.dao.repository.IBusinessMonthRepository;
import com.xmd.achievement.dao.repository.IMqOrderRefundInfoRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.support.constant.enums.AchStatus;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import com.xmd.achievement.web.config.ProductAchievementConfig;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class AchievementRefundWebsiteHandler {

    @Resource
    private IMqOrderRefundInfoRepository mqOrderRefundInfoRepository;

    @Resource
    private IAchievementSpecDetailRepository achievementSpecDetailRepository;

    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    private IAchievementCategoryDetailRepository achievementCategoryDetailRepository;

    @Resource
    private IBusinessMonthRepository businessMonthRepository;

    @Resource
    private IBusinessMonthService businessMonthService;

    @Resource
    private InnerService innerService;

    @Resource
    private ProductAchievementConfig productAchievementConfig;

    @Resource
    private AchievementRefundCommonHandler refundCreateHandler;


        // ==================== 网站产品处理方法 ====================

    /**
     * 处理网站产品的特殊退款逻辑
     *
     * @param originalProduct    原始商品明细
     * @param refundTask         退款任务
     * @param refundProductList  退款商品明细列表
     * @param refundCategoryList 退款分类明细列表
     * @param refundSpecList     退款规格明细列表
     */
    public void processWebsiteProductRefund(AchievementProductDetailModel originalProduct,
            MqOrderRefundInfoModel refundTask,
            List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList) {

        // 1. 首先通过originalProduct的sale_type判断是新开还是升级
        Integer saleType = originalProduct.getSaleType();
        int n;
        if (OrderSaleTypeEnum.OPEN.getType().equals(saleType)) {
            // 新开：n=3
            n = 3;
        } else if (OrderSaleTypeEnum.UPGRADE.getType().equals(saleType)) {
            // 升级：n=1
            n = 1;
        } else {
            // 其他类型不处理
            return;
        }

        // 2. 通过getBusinessMonthTimeRange方法查询商务月ID列表
        List<Long> businessMonthIds = getBusinessMonthTimeRange(originalProduct.getBusinessMonth(), n);
        if (businessMonthIds.isEmpty()) {
            return;
        }

        // 3. 处理advertisement配置（逗号分隔的字符串）
        String advertisementConfig = productAchievementConfig.getAdvertisement();
        if (StrUtil.isBlank(advertisementConfig)) {
            return;
        }

        List<Long> advertisementProductIds = new ArrayList<>();
        String[] advertisementArray = advertisementConfig.split(",");
        for (String productIdStr : advertisementArray) {
            if (StrUtil.isNotBlank(productIdStr.trim())) {
                try {
                    advertisementProductIds.add(Long.valueOf(productIdStr.trim()));
                } catch (NumberFormatException e) {
                    // 忽略无效的ID
                }
            }
        }

        if (advertisementProductIds.isEmpty()) {
            return;
        }

        // 4. 查询匹配的广告通数据
        List<AchievementProductDetailModel> advertisementProducts = queryAdvertisementProducts(businessMonthIds,
                advertisementProductIds, originalProduct.getCustomerId());

        // 5.
        // 根据orderProductId形成Map，key为orderProductId，value为AchievementProductDetailModel集合
        Map<String, List<AchievementProductDetailModel>> advertisementProductMap = advertisementProducts.stream()
                .filter(product -> StrUtil.isNotBlank(product.getOrderProductId()))
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getOrderProductId));

        // 6. 遍历Map，查找相关的网站产品数据
        for (Map.Entry<String, List<AchievementProductDetailModel>> entry : advertisementProductMap.entrySet()) {
            String orderProductId = entry.getKey();
            List<AchievementProductDetailModel> advertisementProductList = entry.getValue();

            // 检查advertisementProductList中的标准价是否都为0
            boolean allStandardPriceZero = advertisementProductList.stream()
                    .allMatch(product -> {
                        BigDecimal standardPrice = product.getStandardPrice();
                        return standardPrice == null || standardPrice.compareTo(BigDecimal.ZERO) == 0;
                    });

            // 如果标准价都为0说明只找到了补的，没找到原，直接进入下一个循环
            if (allStandardPriceZero) {
                continue;
            }

            // 如果不是都为0，用orderProductId查询全部数据
            List<AchievementProductDetailModel> allProductData = queryAllProductDataByOrderProductId(orderProductId);
            processAdvertisementProductForWebsite(orderProductId, allProductData, originalProduct.getOrderProductId(),
                    refundTask, refundProductList, refundCategoryList, refundSpecList);
        }
    }

    /**
     * 处理广告通商品的特殊退款逻辑
     *
     * @param originalProduct    原始商品明细
     * @param refundTask         退款任务
     * @param refundProductList  退款商品明细列表
     * @param refundCategoryList 退款分类明细列表
     * @param refundSpecList     退款规格明细列表
     */

    /**
     * 查询指定商务月前后n个月的商务月ID列表
     *
     * @param businessMonth 当前商务月字符串（格式：YYYY-MM）
     * @param n             月份范围参数（如果n=1返回当前月，如果n=3返回前2个月+当前月+后2个月，共2*n-1个月）
     * @return 商务月ID列表，按时间顺序排列
     */
    private List<Long> getBusinessMonthTimeRange(String businessMonth, int n) {
        try {
            List<Long> monthIds = new ArrayList<>();

            // 判断n是否大于0
            if (n <= 0) {
                return monthIds;
            }

            // 查询当前商务月信息
            BusinessMonthModel currentMonth = businessMonthRepository.lambdaQuery()
                    .eq(BusinessMonthModel::getMonth, businessMonth)
                    .eq(BusinessMonthModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .one();

            if (currentMonth == null) {
                return monthIds;
            }

            if (n == 1) {
                // 如果n=1，只返回当前月
                monthIds.add(currentMonth.getMonthId());
                return monthIds;
            }

            // 查询前(n-1)个月
            List<BusinessMonthModel> previousMonths = businessMonthRepository.lambdaQuery()
                    .lt(BusinessMonthModel::getMonth, businessMonth)
                    .eq(BusinessMonthModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .orderByDesc(BusinessMonthModel::getMonth)
                    .last("limit " + (n - 1))
                    .list();

            // 查询后(n-1)个月
            List<BusinessMonthModel> nextMonths = businessMonthRepository.lambdaQuery()
                    .gt(BusinessMonthModel::getMonth, businessMonth)
                    .eq(BusinessMonthModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .orderByAsc(BusinessMonthModel::getMonth)
                    .last("limit " + (n - 1))
                    .list();

            // 按时间顺序添加月份ID：前(n-1)个月 + 当前月 + 后(n-1)个月
            // 前(n-1)个月需要倒序添加（因为查询时是按月份倒序的）
            for (int i = previousMonths.size() - 1; i >= 0; i--) {
                monthIds.add(previousMonths.get(i).getMonthId());
            }

            // 添加当前月
            monthIds.add(currentMonth.getMonthId());

            // 添加后(n-1)个月
            for (BusinessMonthModel nextMonth : nextMonths) {
                monthIds.add(nextMonth.getMonthId());
            }

            return monthIds;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }



    /**
     * 查询匹配的广告通产品数据（排除已退款的）
     *
     * @param businessMonthIds        商务月ID列表
     * @param advertisementProductIds 广告通产品ID列表
     * @param customerId              客户ID
     * @return 匹配的广告通产品列表（未退款的）
     */
    private List<AchievementProductDetailModel> queryAdvertisementProducts(List<Long> businessMonthIds,
            List<Long> advertisementProductIds, String customerId) {
        // 1. 先查询指定时间范围内的有效状态记录
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AchievementProductDetailModel::getBusinessMonthId, businessMonthIds)
                .eq(AchievementProductDetailModel::getCalculateAll, 0) // calculate_all=0
                .in(AchievementProductDetailModel::getProductId, advertisementProductIds) // product_id为广告通产品ID
                .in(AchievementProductDetailModel::getStatus, AchStatus.VALID.getType()) // status=1
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(AchievementProductDetailModel::getCustomerId, customerId); // 添加customer_id条件

        List<AchievementProductDetailModel> validRecords = achievementProductDetailRepository.list(queryWrapper);

        if (validRecords.isEmpty()) {
            return validRecords;
        }

        // 2. 收集所有的orderProductId
        Set<String> orderProductIds = validRecords.stream()
                .map(AchievementProductDetailModel::getOrderProductId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        if (orderProductIds.isEmpty()) {
            return validRecords;
        }

        // 3. 查询这些orderProductId是否有退款状态的主商品记录（标准价≠0且状态为退款）
        LambdaQueryWrapper<AchievementProductDetailModel> refundQueryWrapper = new LambdaQueryWrapper<>();
        refundQueryWrapper.in(AchievementProductDetailModel::getOrderProductId, orderProductIds)
                .eq(AchievementProductDetailModel::getStatus, AchStatus.REFUND.getType())
                .ne(AchievementProductDetailModel::getStandardPrice, BigDecimal.ZERO) // 标准价≠0（主商品）
                .in(AchievementProductDetailModel::getSaleType,OrderSaleTypeEnum.REFUND.getType(),OrderSaleTypeEnum.TRANSFER.getType())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        List<AchievementProductDetailModel> refundedMainProducts = achievementProductDetailRepository
                .list(refundQueryWrapper);

        // 4. 收集已退款的主商品的orderProductId
        Set<String> refundedOrderProductIds = refundedMainProducts.stream()
                .map(AchievementProductDetailModel::getOrderProductId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 5. 过滤掉已退款主商品对应的所有记录
        return validRecords.stream()
                .filter(product -> !refundedOrderProductIds.contains(product.getOrderProductId()))
                .collect(Collectors.toList());
    }

    /**
     * 处理单个orderProductId对应的广告通产品集合，查找相关的网站产品数据
     *
     * @param orderProductId           广告通订单产品ID
     * @param advertisementProductList 广告通产品数据集合
     * @param websiteOrderProductId    网站产品的订单产品ID（用于排除查询）
     * @param refundTask               退款任务
     * @param refundProductList        退款商品明细列表
     * @param refundCategoryList       退款分类明细列表
     * @param refundSpecList           退款规格明细列表
     */
    private void processAdvertisementProductForWebsite(String orderProductId,
            List<AchievementProductDetailModel> advertisementProductList,
            String websiteOrderProductId,
            MqOrderRefundInfoModel refundTask,
            List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList) {

        if (advertisementProductList == null || advertisementProductList.isEmpty()) {
            return;
        }

        int count = advertisementProductList.size();

        // 1. 如果条数=1
        if (count == 1) {
            // 走到一个处理100%的逻辑
            processFullPercentageAdvertisement(orderProductId, advertisementProductList.get(0), websiteOrderProductId,
                    refundTask, refundProductList, refundCategoryList, refundSpecList);
            return;
        }

        // 3. 如果条数>1，先判断business_id是否都一样
        if (count > 1) {
            // 检查business_id去重后的数量
            long distinctBusinessIdCount = advertisementProductList.stream()
                    .map(AchievementProductDetailModel::getBusinessId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .count();

            if (distinctBusinessIdCount <= 1) {
                // 说明是2条50%，走到50%的处理方法
                processHalfPercentageAdvertisement(orderProductId, advertisementProductList, websiteOrderProductId,
                        refundTask, refundProductList, refundCategoryList, refundSpecList);
            } else {
                // business_id有不同的，按business_id来分组为map
                Map<String, List<AchievementProductDetailModel>> businessIdMap = advertisementProductList.stream()
                        .filter(product -> StrUtil.isNotBlank(product.getBusinessId()))
                        .collect(Collectors.groupingBy(AchievementProductDetailModel::getBusinessId));

                for (Map.Entry<String, List<AchievementProductDetailModel>> entry : businessIdMap.entrySet()) {
                    List<AchievementProductDetailModel> businessProductList = entry.getValue();
                    processAdvertisementProductForWebsite(orderProductId, businessProductList, websiteOrderProductId,
                            refundTask, refundProductList, refundCategoryList, refundSpecList);
                }
            }
        }
    }

    /**
     * 处理100%的广告通产品逻辑
     *
     * @param orderProductId        广告通订单产品ID
     * @param product               广告通产品数据
     * @param websiteOrderProductId 网站产品的订单产品ID（用于排除查询）
     * @param refundTask            退款任务
     * @param refundProductList     退款商品明细列表
     * @param refundCategoryList    退款分类明细列表
     * @param refundSpecList        退款规格明细列表
     */
    private void processFullPercentageAdvertisement(String orderProductId,
            AchievementProductDetailModel product,
            String websiteOrderProductId,
            MqOrderRefundInfoModel refundTask,
            List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList) {

        // 检查网站产品是否都为0，如果不是都为0直接返回
        String businessMonth = product.getBusinessMonth();
        if (!checkWebsiteProductsAllZero(businessMonth, product.getCustomerId(), websiteOrderProductId)) {
            return;
        }

        // 都为0，走新的逻辑 - 反向生成
        processFullPercentageReverseGeneration(product, refundTask, refundProductList, refundCategoryList,
                refundSpecList);
    }

    // ==================== 反向生成处理方法 ====================

    /**
     * 处理100%广告通产品的反向生成逻辑
     *
     * @param product            广告通产品数据
     * @param refundTask         退款任务
     * @param refundProductList  退款商品明细列表
     * @param refundCategoryList 退款分类明细列表
     * @param refundSpecList     退款规格明细列表
     */
    private void processFullPercentageReverseGeneration(AchievementProductDetailModel product,
            MqOrderRefundInfoModel refundTask,
            List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList) {

        // 1. 先查询对应的AchievementCategoryDetailModel
        List<AchievementCategoryDetailModel> categoryList = queryAchievementCategoryByAchievementId(
                product.getAchievementId());

        boolean hasGeneratedRecords = false; // 标记是否生成了记录

        for (AchievementCategoryDetailModel categoryDetail : categoryList) {
            // 2. 查询对应的AchievementSpecDetailModel
            List<AchievementSpecDetailModel> specList = queryAchievementSpecDetailByCategoryId(
                    categoryDetail.getAchievementCategoryId());

            // 3. 处理specId配置，找到目标对象
            String specIdConfig = productAchievementConfig.getSpecId();
            if (StrUtil.isBlank(specIdConfig)) {
                continue;
            }

            // 解析specId配置（逗号分隔的字符串）
            List<Long> targetSpecIds = parseSpecIds(specIdConfig);
            if (targetSpecIds.isEmpty()) {
                continue;
            }

            // 4. 找到匹配的spec记录
            for (AchievementSpecDetailModel specDetail : specList) {
                if (targetSpecIds.contains(specDetail.getSpecId())) {
                    // 找到目标对象，进行反向生成（不更新原始产品）
                    generateReverseRecordsWithoutUpdate(specDetail, categoryDetail, product, refundTask,
                            refundProductList, refundCategoryList, refundSpecList);
                    hasGeneratedRecords = true;
                }
            }
        }

        // 5. 只在生成了记录的情况下才更新原始产品的calculate_all
        if (hasGeneratedRecords) {
            product.setCalculateAll(1);
            achievementProductDetailRepository.updateById(product);
        }
    }

    /**
     * 处理50%的广告通产品逻辑
     *
     * @param orderProductId           广告通订单产品ID
     * @param advertisementProductList 广告通产品数据集合
     * @param websiteOrderProductId    网站产品的订单产品ID（用于排除查询）
     * @param refundTask               退款任务
     * @param refundProductList        退款商品明细列表
     * @param refundCategoryList       退款分类明细列表
     * @param refundSpecList           退款规格明细列表
     */
    private void processHalfPercentageAdvertisement(String orderProductId,
            List<AchievementProductDetailModel> advertisementProductList,
            String websiteOrderProductId,
            MqOrderRefundInfoModel refundTask,
            List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList) {

        // 首先找到标准价不等于0的记录，取其businessMonth和customerId
        String businessMonth = null;
        String customerId = null;
        for (AchievementProductDetailModel product : advertisementProductList) {
            BigDecimal standardPrice = product.getStandardPrice();
            if (standardPrice != null && standardPrice.compareTo(BigDecimal.ZERO) != 0) {
                businessMonth = product.getBusinessMonth();
                customerId = product.getCustomerId();
                break;
            }
        }

        if (businessMonth == null || customerId == null) {
            // 如果没有找到标准价不等于0的记录，直接返回
            return;
        }

        // 检查网站产品是否都为0，如果不是都为0直接返回
        if (!checkWebsiteProductsAllZero(businessMonth, customerId, websiteOrderProductId)) {
            return;
        }

        // 都为0，走新的逻辑
        // 找到标准价为0的记录进行处理
        for (AchievementProductDetailModel product : advertisementProductList) {
            BigDecimal standardPrice = product.getStandardPrice();
            if (standardPrice != null && standardPrice.compareTo(BigDecimal.ZERO) == 0) {
                // 处理标准价为0的记录
                processZeroStandardPriceProduct(product, refundTask, refundProductList, refundCategoryList,
                        refundSpecList);
            }
        }
    }

    /**
     * 解析specId配置字符串
     *
     * @param specIdConfig 逗号分隔的specId字符串
     * @return specId列表
     */
    private List<Long> parseSpecIds(String specIdConfig) {
        List<Long> specIds = new ArrayList<>();
        if (StrUtil.isBlank(specIdConfig)) {
            return specIds;
        }

        String[] specIdArray = specIdConfig.split(",");
        for (String specIdStr : specIdArray) {
            if (StrUtil.isNotBlank(specIdStr.trim())) {
                try {
                    specIds.add(Long.valueOf(specIdStr.trim()));
                } catch (NumberFormatException e) {
                    // 忽略无效的ID
                }
            }
        }
        return specIds;
    }

    /**
     * 生成反向记录（spec -> category -> product）- 不更新原始产品
     *
     * @param originalSpec       原始规格明细
     * @param originalCategory   原始分类明细
     * @param originalProduct    原始商品明细
     * @param refundTask         退款任务
     * @param refundProductList  退款商品明细列表
     * @param refundCategoryList 退款分类明细列表
     * @param refundSpecList     退款规格明细列表
     */
    private void generateReverseRecordsWithoutUpdate(AchievementSpecDetailModel originalSpec,
            AchievementCategoryDetailModel originalCategory,
            AchievementProductDetailModel originalProduct,
            MqOrderRefundInfoModel refundTask,
            List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList) {

        // 获取商务月信息
        BusinessMonthModel businessMonth = businessMonthService.getMonthInfo(refundTask.getCreateTime());

        // 生成新的ID，避免重复
        Long newAchievementId = IdUtil.getSnowflakeNextId();
        Long newAchievementCategoryId = IdUtil.getSnowflakeNextId();

        // 1. 先生成AchievementSpecDetailModel
        AchievementSpecDetailModel refundSpec = refundCreateHandler.createReverseRefundSpec(originalSpec, newAchievementCategoryId,
                refundTask.getCreateTime());
        refundSpecList.add(refundSpec);

        // 2. 生成对应的AchievementCategoryDetailModel，传入refundSpec
        AchievementCategoryDetailModel refundCategory = refundCreateHandler.createReverseRefundCategory(originalCategory,
                newAchievementCategoryId, newAchievementId, refundTask.getCreateTime(), refundSpec);
        refundCategoryList.add(refundCategory);

        // 3. 生成AchievementProductDetailModel，传入refundSpec
        AchievementProductDetailModel refundProduct = refundCreateHandler.createReverseRefundProduct(originalProduct, businessMonth,
                refundTask.getCreateTime(), refundTask, refundSpec);
        // 设置新的achievement_id
        refundProduct.setAchievementId(newAchievementId);
        refundProductList.add(refundProduct);
    }

    /**
     * 处理标准价为0的产品记录
     *
     * @param originalProduct    原始产品数据（标准价为0）
     * @param refundTask         退款任务
     * @param refundProductList  退款商品明细列表
     * @param refundCategoryList 退款分类明细列表
     * @param refundSpecList     退款规格明细列表
     */
    private void processZeroStandardPriceProduct(AchievementProductDetailModel originalProduct,
            MqOrderRefundInfoModel refundTask,
            List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList) {

        // 获取商务月信息
        BusinessMonthModel businessMonth = businessMonthService.getMonthInfo(refundTask.getCreateTime());

        // 1. 生成新的商品明细退款记录（数值的反值）
        AchievementProductDetailModel refundProduct = refundCreateHandler.createRefundProduct(originalProduct, businessMonth,
                refundTask.getCreateTime(), refundTask);
        // 设置新记录的calculate_all为1
        refundProduct.setCalculateAll(1);
        // 设置sale_type为新开
        refundProduct.setSaleType(OrderSaleTypeEnum.OPEN.getType());
        refundProductList.add(refundProduct);

        // 2. 根据原始产品查出对应的achievement_category_detail
        List<AchievementCategoryDetailModel> categoryList = queryAchievementCategoryByAchievementId(
                originalProduct.getAchievementId());

        for (AchievementCategoryDetailModel originalCategory : categoryList) {
            // 创建分类明细退款记录（数值的反值）
            AchievementCategoryDetailModel refundCategory = refundCreateHandler.createRefundCategory(originalCategory,
                    refundProduct.getAchievementId(), refundTask.getCreateTime());
            refundCategoryList.add(refundCategory);

            // 3. 根据分类明细查出对应的achievement_spec_detail
            List<AchievementSpecDetailModel> specList = queryAchievementSpecDetailByCategoryId(
                    originalCategory.getAchievementCategoryId());

            for (AchievementSpecDetailModel originalSpec : specList) {
                // 创建规格明细退款记录（数值的反值）
                AchievementSpecDetailModel refundSpec = refundCreateHandler.createRefundSpec(originalSpec,
                        refundCategory.getAchievementCategoryId(), refundTask.getCreateTime());
                refundSpecList.add(refundSpec);
            }
        }

        // 4. 更新老数据的calculate_all为1
        originalProduct.setCalculateAll(1);
        achievementProductDetailRepository.updateById(originalProduct);
    }

    /**
     * 检查网站产品是否存在，如果n=1和n=3查出来的count都为0则返回true，否则返回false
     *
     * @param businessMonth  商务月
     * @param customerId     客户ID
     * @param orderProductId 订单产品ID（排除当前订单产品）
     * @return 如果都为0返回true，否则返回false
     */
    private boolean checkWebsiteProductsAllZero(String businessMonth, String customerId, String orderProductId) {
        // 查找前后3个月的新开网站产品数据
        List<Long> threeMonthIds = getBusinessMonthTimeRange(businessMonth, 3);
        long newOpenCount = 0;
        if (!threeMonthIds.isEmpty()) {
            newOpenCount = queryWebsiteProductsCount(threeMonthIds, OrderSaleTypeEnum.OPEN.getType(), customerId,
                    orderProductId);
        }

        // 查找前后1个月的升级网站产品数据
        List<Long> oneMonthIds = getBusinessMonthTimeRange(businessMonth, 1);
        long upgradeCount = 0;
        if (!oneMonthIds.isEmpty()) {
            upgradeCount = queryWebsiteProductsCount(oneMonthIds, OrderSaleTypeEnum.UPGRADE.getType(), customerId,
                    orderProductId);
        }

        // 如果不是都为0，直接返回false
        return newOpenCount == 0 && upgradeCount == 0;
    }

    /**
     * 查询网站产品数据数量
     *
     * @param businessMonthIds 商务月ID列表
     * @param saleType         销售类型（新开或升级）
     * @param customerId       客户ID
     * @param orderProductId   订单产品ID（排除当前订单产品）
     * @return 匹配的网站产品数量
     */
    private long queryWebsiteProductsCount(List<Long> businessMonthIds, Integer saleType, String customerId,
            String orderProductId) {
        // 获取网站产品ID和产品名称配置
        String websiteProductId = productAchievementConfig.getWebsite();
        String websiteProductName = productAchievementConfig.getWebsiteName();

        if (StrUtil.isBlank(websiteProductId) && StrUtil.isBlank(websiteProductName)) {
            return 0L;
        }

        // 1. 先查询指定时间范围内的有效状态记录
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AchievementProductDetailModel::getBusinessMonthId, businessMonthIds)
                .eq(AchievementProductDetailModel::getSaleType, saleType) // 销售类型：新开或升级
                .gt(AchievementProductDetailModel::getPaidAmount, BigDecimal.ZERO) // 实付金额>0
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(AchievementProductDetailModel::getCustomerId, customerId) // 添加customer_id条件
                .ne(StrUtil.isNotBlank(orderProductId), AchievementProductDetailModel::getOrderProductId,
                        orderProductId) // 排除当前订单产品
                .in(AchievementProductDetailModel::getStatus, AchStatus.VALID.getType(), AchStatus.FINISHED.getType()); // 只查询有效状态

        // 添加产品ID或产品名称的条件
        queryWrapper.and(wrapper -> {
            if (StrUtil.isNotBlank(websiteProductId)) {
                try {
                    Long productId = Long.valueOf(websiteProductId);
                    wrapper.eq(AchievementProductDetailModel::getProductId, productId);
                } catch (NumberFormatException e) {
                    // 如果产品ID格式不正确，只使用产品名称条件
                }
            }
            if (StrUtil.isNotBlank(websiteProductName)) {
                if (StrUtil.isNotBlank(websiteProductId)) {
                    wrapper.or().eq(AchievementProductDetailModel::getProductName, websiteProductName);
                } else {
                    wrapper.eq(AchievementProductDetailModel::getProductName, websiteProductName);
                }
            }
        });

        // 查询有效状态的记录
        List<AchievementProductDetailModel> validRecords = achievementProductDetailRepository.list(queryWrapper);

        if (validRecords.isEmpty()) {
            return 0L;
        }

        // 2. 收集所有的orderProductId
        Set<String> orderProductIds = validRecords.stream()
                .map(AchievementProductDetailModel::getOrderProductId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        if (orderProductIds.isEmpty()) {
            return 0L;
        }

        // 3. 查询这些orderProductId是否有退款状态的记录（不限时间范围）
        LambdaQueryWrapper<AchievementProductDetailModel> refundQueryWrapper = new LambdaQueryWrapper<>();
        refundQueryWrapper.in(AchievementProductDetailModel::getOrderProductId, orderProductIds)
                .eq(AchievementProductDetailModel::getStatus, AchStatus.REFUND.getType())
                .in(AchievementProductDetailModel::getSaleType,OrderSaleTypeEnum.REFUND.getType(),OrderSaleTypeEnum.TRANSFER.getType())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        List<AchievementProductDetailModel> refundRecords = achievementProductDetailRepository.list(refundQueryWrapper);

        // 4. 收集已退款的orderProductId
        Set<String> refundedOrderProductIds = refundRecords.stream()
                .map(AchievementProductDetailModel::getOrderProductId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 5. 统计有效的orderProductId数量（排除已退款的）
        long validCount = orderProductIds.stream()
                .filter(id -> !refundedOrderProductIds.contains(id))
                .count();

        return validCount;
    }

 

    // ==================== 数据查询方法 ====================

    /**
     * 根据orderProductId查询全部AchievementProductDetailModel数据
     *
     * @param orderProductId 订单产品ID
     * @return 业绩商品明细列表
     */
    private List<AchievementProductDetailModel> queryAllProductDataByOrderProductId(String orderProductId) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getOrderProductId, orderProductId)
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(AchievementProductDetailModel::getCalculateAll, 0)
                .in(AchievementProductDetailModel::getStatus, AchStatus.VALID.getType());

        return achievementProductDetailRepository.list(queryWrapper);
    }

    /**
     * 根据 achievement_id 查询业绩分类明细表
     *
     * @param achievementId 业绩ID
     * @return 业绩分类明细列表
     */
    private List<AchievementCategoryDetailModel> queryAchievementCategoryByAchievementId(Long achievementId) {
        LambdaQueryWrapper<AchievementCategoryDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementCategoryDetailModel::getAchievementId, achievementId)
                .eq(AchievementCategoryDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        return achievementCategoryDetailRepository.list(queryWrapper);
    }

    /**
     * 根据 achievement_category_id 查询业绩规格明细表
     *
     * @param achievementCategoryId 业绩分类ID
     * @return 业绩规格明细列表
     */
    private List<AchievementSpecDetailModel> queryAchievementSpecDetailByCategoryId(Long achievementCategoryId) {
        LambdaQueryWrapper<AchievementSpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementSpecDetailModel::getAchievementCategoryId, achievementCategoryId)
                .eq(AchievementSpecDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        return achievementSpecDetailRepository.list(queryWrapper);
    }



    

}
