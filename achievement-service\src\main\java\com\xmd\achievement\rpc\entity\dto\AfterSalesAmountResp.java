package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 售后金额详情
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class AfterSalesAmountResp {

    /**
     * 系统计算违约金总额
     */
    private BigDecimal systemPenaltyAmount;

    /**
     * 系统计算商品成本总额
     */
    private BigDecimal systemCostAmount;

    /**
     * 系统计算赠品成本总额
     */
    private BigDecimal systemGiftAmount;

    /**
     * 系统计算税费成本总额
     */
    private BigDecimal systemTaxAmount;

    /**
     * 实际违约金金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 实际商品成本金额
     */
    private BigDecimal costAmount;

    /**
     * 实际赠品成本金额
     */
    private BigDecimal giftAmount;

    /**
     * 实际税费成本金额
     */
    private BigDecimal taxAmount;

    /**
     * 总支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 总扣除成本金额
     */
    private BigDecimal deductCostAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 实际退款金额
     */
    private BigDecimal actualRefundAmount;

}


