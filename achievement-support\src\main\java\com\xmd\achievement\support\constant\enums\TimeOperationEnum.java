package com.xmd.achievement.support.constant.enums;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/17/15:08
 * @since 1.0
 */
public enum TimeOperationEnum {
    /**
     * 增加
     */
    ADD(1, "ADD"),
    /**
     * 减少
     */
    SUBTRACT(2, "SUBTRACT");

    private final Integer code;
    private final String msg;

    TimeOperationEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }
}
