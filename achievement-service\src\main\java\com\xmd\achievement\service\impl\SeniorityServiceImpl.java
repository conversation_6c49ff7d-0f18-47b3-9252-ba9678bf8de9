package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.SeniorityModel;
import com.xmd.achievement.dao.repository.ISeniorityRepository;
import com.xmd.achievement.service.ISeniorityService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PageQuerySeniorityListResponse;
import com.xmd.achievement.service.entity.response.QuerySeniorityDetailResponse;
import com.xmd.achievement.service.entity.response.QuerySeniorityListResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/19 16:51
 * @version: 1.0.0
 * @return {@link }
 */
@Service
public class SeniorityServiceImpl implements ISeniorityService {
    @Resource
    private ISeniorityRepository seniorityRepository;

    @Override
    public WebResult<Boolean> saveSeniority(SaveSeniorityRequest request) {
        //检查名称
        List<SeniorityModel> checkName = seniorityRepository.list(
                new LambdaQueryWrapper<SeniorityModel>()
                        .eq(SeniorityModel::getSeniorityName, request.getSeniorityName()));
        if (ObjectUtil.isNotEmpty(checkName)) {
            return WebResult.error(WebCodeMessageEnum.SENIORITY_NAME_EXIST);
        }

        if (request.getMinYears() >= request.getMaxYears()) {
            return WebResult.error(WebCodeMessageEnum.MIN_THEN_MAX_ERROR);
        }

        //检查最大，最小值区间
        SeniorityModel checkPart = seniorityRepository.getOne(
                new LambdaQueryWrapper<SeniorityModel>()
                        .orderByDesc(SeniorityModel::getMaxYears)
                        .last("LIMIT 1"));
        if (ObjectUtil.isNotEmpty(checkPart) && request.getMaxYears() <= checkPart.getMaxYears()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_DUPLICATE, "最大值必须大于:" + checkPart.getMaxYears());
        }
        if (ObjectUtil.isNotEmpty(checkPart) && !checkPart.getMaxYears().equals(request.getMinYears())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_DUPLICATE, "最小值必须从:" + checkPart.getMaxYears() + "设置");
        }
        //保存
        SeniorityModel saveModel = new SeniorityModel();
        BeanUtil.copyProperties(request, saveModel);
        saveModel.setSeniorityId(IdUtil.getSnowflake().nextId());
        seniorityRepository.save(saveModel);
        return WebResult.success(true);
    }

    @Override
    public WebResult<PageResponse<PageQuerySeniorityListResponse>> pageQuerySeniorityList(PageRequest request) {
        Page<SeniorityModel> page = new Page<>(request.getPageIndex(), request.getPageSize());
        Page<SeniorityModel> result = seniorityRepository.page(page, new LambdaQueryWrapper<>());
        PageResponse<PageQuerySeniorityListResponse> pageResponse = new PageResponse<>(result.getTotal(), result.getCurrent(), result.getSize());
        if (ObjectUtil.isEmpty(result.getRecords())) {
            return WebResult.success(pageResponse);
        }

        List<PageQuerySeniorityListResponse> list = result.getRecords().stream().map(e -> {
            PageQuerySeniorityListResponse response = new PageQuerySeniorityListResponse();
            BeanUtil.copyProperties(e, response);
            return response;
        }).collect(Collectors.toList());
        pageResponse.setList(list);
        return WebResult.success(pageResponse);
    }

    @Override
    public WebResult<QuerySeniorityDetailResponse> querySeniorityDetail(QuerySeniorityDetailRequest request) {
        SeniorityModel model = seniorityRepository.getOne(
                new LambdaQueryWrapper<SeniorityModel>()
                        .eq(SeniorityModel::getSeniorityId, request.getSeniorityId()));
        if (ObjectUtil.isEmpty(model)) {
            return WebResult.success(null);
        }

        QuerySeniorityDetailResponse response = new QuerySeniorityDetailResponse();
        BeanUtil.copyProperties(model, response);
        return WebResult.success(response);
    }

    @Override
    public WebResult<List<QuerySeniorityListResponse>> querySeniorityList() {
        List<SeniorityModel> models = seniorityRepository.list();
        if (ObjectUtil.isEmpty(models)) {
            return WebResult.success(null);
        }

        List<QuerySeniorityListResponse> responses = models.stream().map(e -> {
            QuerySeniorityListResponse response = new QuerySeniorityListResponse();
            BeanUtil.copyProperties(e, response);
            return response;
        }).collect(Collectors.toList());
        return WebResult.success(responses);
    }

    @Override
    public WebResult<Boolean> updateSeniority(UpdateSeniorityRequest request) {
        //检查名称
        List<SeniorityModel> checkName = seniorityRepository.list(
                new LambdaQueryWrapper<SeniorityModel>()
                        .eq(SeniorityModel::getSeniorityName, request.getSeniorityName())
                        .ne(SeniorityModel::getSeniorityId, request.getSeniorityId()));
        if (ObjectUtil.isNotEmpty(checkName)) {
            return WebResult.error(WebCodeMessageEnum.SENIORITY_NAME_EXIST);
        }

        //检查最小值区间
        SeniorityModel checMinkPart = seniorityRepository.getOne(
                new LambdaQueryWrapper<SeniorityModel>()
                        .eq(SeniorityModel::getSeniorityId, request.getSeniorityId()));
        if (!checMinkPart.getMinYears().equals(request.getMinYears())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_DUPLICATE, "最小值必须从:" + checMinkPart.getMinYears() + "设置");
        }

        //检查最大值区间
        SeniorityModel checkMaxPart = seniorityRepository.getOne(
                new LambdaQueryWrapper<SeniorityModel>()
                        .ge(SeniorityModel::getMinYears, checMinkPart.getMaxYears())
                        .orderByAsc(SeniorityModel::getMaxYears)
                        .last("LIMIT 1"));
        if (ObjectUtil.isNotEmpty(checkMaxPart) && request.getMaxYears() > checkMaxPart.getMinYears()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_DUPLICATE, "最大值必须小于:" + checkMaxPart.getMinYears());
        }

        //修改
        SeniorityModel model = seniorityRepository.getOne(new LambdaQueryWrapper<SeniorityModel>().eq(SeniorityModel::getSeniorityId, request.getSeniorityId()));
        SeniorityModel saveModel = new SeniorityModel();
        BeanUtil.copyProperties(request, saveModel);
        saveModel.setId(model.getId());
        seniorityRepository.updateById(saveModel);
        return WebResult.success(true);
    }

    @Override
    public SeniorityModel getSeniority(Integer tenure) {
        return null != tenure ? seniorityRepository.getOne(new LambdaQueryWrapper<SeniorityModel>().le(SeniorityModel::getMinYears, tenure).gt(SeniorityModel::getMaxYears, tenure)) : null;
    }

    @Override
    public List<SeniorityModel> getAllSeniorities() {
        return seniorityRepository.list();
    }


}
