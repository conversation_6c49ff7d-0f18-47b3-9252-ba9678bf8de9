package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.entity.AchievementProductDetailSelectModel;
import com.xmd.achievement.dao.mapper.AchievementProductDetailSelectMapper;
import com.xmd.achievement.dao.repository.IAchievementProductDetailSelectRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 业绩商品明细表(查询) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@Slf4j
public class AchievementProductDetailSelectRepositoryImpl extends ServiceImpl<AchievementProductDetailSelectMapper,AchievementProductDetailSelectModel> implements IAchievementProductDetailSelectRepository {

@Resource
private AchievementProductDetailSelectMapper achievementProductDetailSelectMapper;

    @Override
    public void insertOrUpdate(List<AchievementProductDetailSelectModel> models) {
        if (ObjectUtils.isNotEmpty(models)) {
            achievementProductDetailSelectMapper.batchInsertOrUpdate(models);
        }
    }

    @Override
    public List<AchievementProductDetailSelectModel> selectAchievement(List<Long> companyIds, List<Long> deptIds, List<Long> businessMonthIds, List<String> businessIds) {
        LambdaQueryWrapper<AchievementProductDetailSelectModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailSelectModel::getDeleteFlag, 0);
        queryWrapper.eq(AchievementProductDetailSelectModel::getDisplayed, 0);
        if (companyIds != null && !companyIds.isEmpty()) {
            queryWrapper.in(AchievementProductDetailSelectModel::getCompanyId, companyIds);
        }
        if (deptIds != null && !deptIds.isEmpty()) {
            queryWrapper.in(AchievementProductDetailSelectModel::getDeptId, deptIds);
        }
        if (businessMonthIds != null && !businessMonthIds.isEmpty()) {
            queryWrapper.in(AchievementProductDetailSelectModel::getBusinessMonthId, businessMonthIds);
        }
        if (businessIds != null && !businessIds.isEmpty()) {
            queryWrapper.in(AchievementProductDetailSelectModel::getBusinessId, businessIds);
        }
        return achievementProductDetailSelectMapper.selectList(queryWrapper);
    }

    @Override
    public void logicDeleteByAchievementId(List<Long> achivevementIdList) {
        LambdaUpdateWrapper<AchievementProductDetailSelectModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AchievementProductDetailSelectModel::getAchievementId, achivevementIdList);
        updateWrapper.eq(AchievementProductDetailSelectModel::getDeleteFlag, 0);
        updateWrapper.set(AchievementProductDetailSelectModel::getDeleteFlag, 1);
        achievementProductDetailSelectMapper.update(null, updateWrapper);
    }
}