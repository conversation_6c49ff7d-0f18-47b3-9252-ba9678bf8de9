package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.PolicySpecDetailModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 政策规格明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IPolicySpecDetailRepository extends IService<PolicySpecDetailModel> {

    PolicySpecDetailModel getPolicySpecDetail(Long specId, Long specCategoryId);

    List<PolicySpecDetailModel> getPolicySpecDetailByCategoryId(Long specCategoryId);

    PolicySpecDetailModel getPolicySpecDetailBySpecId(Long specId);
}
