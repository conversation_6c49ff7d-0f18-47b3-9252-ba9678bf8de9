package com.xmd.achievement.async.event.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xmd.achievement.async.event.ProductAchievementUpdatedEvent;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.service.SyncBusinessAchievementStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 商务业绩监听器
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BusinessAchievementListener {

    private final SyncBusinessAchievementStatisticsService syncBusinessAchievementStatisticsService;

    @Transactional
    @TransactionalEventListener
    @Async("achievementUpdateListenerExecutor")
    public void onBusinessAchievementUpdate(ProductAchievementUpdatedEvent event) {
        AchievementProductDetailModel oldAchievement = event.getOldAchievement();
        AchievementProductDetailModel newAchievement = event.getNewAchievement();
        log.info("商务业绩监听器，监听到{}，事件内容：{}", event.getEVENT_NAME(), event);
        this.updateBusinessAchievement(oldAchievement, newAchievement);
    }

    private void updateBusinessAchievement(AchievementProductDetailModel oldAchievement, AchievementProductDetailModel newAchievement) {
        if (oldAchievement == null) {
            syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(newAchievement.getBusinessId(), newAchievement.getBusinessMonth(), false);
            return;
        }
        if (newAchievement == null) {
            syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(oldAchievement.getBusinessId(), oldAchievement.getBusinessMonth(), false);
            return;
        }
        // 商务变动，需要重算两个人的业绩，这种情况包含了其他条件也变了的情况
        if (dataChanged(oldAchievement.getBusinessId(), newAchievement.getBusinessId())) {
            syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(oldAchievement.getBusinessId(), oldAchievement.getBusinessMonth(), false);
            String currentBusinessMonth = StrUtil.blankToDefault(newAchievement.getBusinessMonth(), oldAchievement.getBusinessMonth());
            syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(newAchievement.getBusinessId(), currentBusinessMonth, false);
            return;
        }
        // 商务不变，但商务月变动，需要重算当前人的前后商务月业绩
        if (dataChanged(oldAchievement.getBusinessMonth(), newAchievement.getBusinessMonth())) {
            syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(oldAchievement.getBusinessId(), oldAchievement.getBusinessMonth(), false);
            syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(oldAchievement.getBusinessId(), newAchievement.getBusinessMonth(), false);
            return;
        }
        // 客户类型变动，需要重算新客户数
        if (dataChanged(oldAchievement.getCustomerType(), newAchievement.getCustomerType())) {
            syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(oldAchievement.getBusinessId(), oldAchievement.getBusinessMonth(), false);
            return;
        }
        // 规格业绩有一条修改就需要重算
        if (!ObjectUtil.isAllEmpty(newAchievement.getNetCash(), newAchievement.getAgentCommissionAchievement(),
                newAchievement.getAgentActualCommission(), newAchievement.getAgentDeferredCommission(),
                newAchievement.getDeptCommission(), newAchievement.getDivCommission(), newAchievement.getBranchCommission())) {
            syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(oldAchievement.getBusinessId(), oldAchievement.getBusinessMonth(), false);
        }
    }

    private boolean dataChanged(Object existing, Object current) {
        return ObjectUtil.isNotEmpty(current) && ObjectUtil.notEqual(existing, current);
    }
}
