<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.NewOldCustomerRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.NewOldCustomerRecordModel">
        <id column="id" property="id"/>
        <result column="business_month_id" property="businessMonthId"/>
        <result column="employee_id" property="employeeId"/>
        <result column="new_customer_id" property="newCustomerId"/>
        <result column="old_customer_id" property="oldCustomerId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , business_month_id, employee_id, new_customer_id, old_customer_id
    </sql>

</mapper>
