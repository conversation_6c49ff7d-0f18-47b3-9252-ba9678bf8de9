package com.xmd.achievement.dao.mapper;

import com.xmd.achievement.dao.entity.AchievementCalculateParamLogModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 业绩计算参数日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface AchievementCalculateParamLogMapper extends BaseMapper<AchievementCalculateParamLogModel> {

    void insertOrUpdate(
            @Param("achievementParam") String achievementParam,
            @Param("orderId") Long businessKey,
            @Param("remark") String remark);
}
