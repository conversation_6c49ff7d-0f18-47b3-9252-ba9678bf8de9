package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ProductSpecCategoryForOrderResponse {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "规格分类ID")
    private Long specCategoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "必选状态")
    private Integer mandatoryStatus;

    @Schema(description = "必选最小数量")
    private Integer mandatoryMin;

    @Schema(description = "必选最大数量")
    private Integer mandatoryMax;
}
