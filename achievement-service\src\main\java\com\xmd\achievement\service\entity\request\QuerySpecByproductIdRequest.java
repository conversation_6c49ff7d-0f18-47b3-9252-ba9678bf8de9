package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25/17:42
 * @since 1.0
 */
@Data
public class QuerySpecByproductIdRequest implements Serializable {
    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "商品ID不能为空")
    private String productId;
}
