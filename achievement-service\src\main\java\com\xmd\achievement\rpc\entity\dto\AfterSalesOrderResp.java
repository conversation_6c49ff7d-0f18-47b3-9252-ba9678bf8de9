package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 售后订单响应对象
 */
@Data
public class AfterSalesOrderResp implements Serializable {

    private static final long serialVersionUID = 5494261070028104940L;

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 售后单ID
     */
    private Long afterSalesOrderId;

    /**
     * 售后单号
     */
    private String afterSalesOrderNo;

    /**
     * 售后来源：1客户发起 2客诉 3邮件 4其他
     */
    private Integer afterSalesSource;

    /**
     * 售后类型：1退款 2转款
     */
    private Integer afterSalesType;

    /**
     * 销售订单id
     */
    private Long transferOutOrderId;

    /**
     * 销售订单编号
     */
    private String transferOutOrderNo;

    /**
     * 转入销售订单id
     */
    private Long transferInOrderId;

    /**
     * 转入销售订单编号
     */
    private String transferInOrderNo;

    /**
     * 客户id（公司）
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 收款公司id
     */
    private String payeeCompanyId;

    /**
     * 收款公司名称
     */
    private String payeeCompanyName;

    /**
     * 售后状态：1金额核算中 2金额待确认 3待分司/总部审核 4合同待上传 5合同待审核 6待退款 7待转款 8售后完成 9售后取消
     */
    private Integer afterSalesStatus;

    /**
     * 退款原因（1-非我司原因 2-我司原因）
     */
    private Integer companyReason;

    /**
     * 签单商务id，从订单取，加签入参需要
     */
    private String sellerUserId;

    /**
     * 签单商务名称，从订单取，加签入参需要
     */
    private String sellerUserName;

    /**
     * 违约金免除（1-否 2-是）
     */
    private Integer waivePenalty;

    /**
     * 银行卡户主（企业名称/个人姓名）
     */
    private String bankCardHolder;

    /**
     * 开户银行名称
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 退转款原因
     */
    private String afterSalesReason;

    /**
     * 免除违约金凭证
     */
    private String penaltyImgUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 申请人id
     */
    private String applyUserId;

    /**
     * 申请人名称
     */
    private String applyUserName;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 系统总违约金
     */
    private BigDecimal systemPenaltyAmount;

    /**
     * 系统总商品成本金额
     */
    private BigDecimal systemCostAmount;

    /**
     * 系统总赠品金额
     */
    private BigDecimal systemGiftAmount;

    /**
     * 系统总商品税金金额
     */
    private BigDecimal systemTaxAmount;

    /**
     * 总违约金
     */
    private BigDecimal penaltyAmount;

    /**
     * 总商品成本金额
     */
    private BigDecimal costAmount;

    /**
     * 总赠品金额
     */
    private BigDecimal giftAmount;

    /**
     * 总商品税金金额
     */
    private BigDecimal taxAmount;

    /**
     * 实付总金额
     */
    private BigDecimal paidAmount;

    /**
     * 扣除成本
     */
    private BigDecimal deductCostAmount;

    /**
     * 总退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 订单所属销售部门id
     */
    private Long deptId;

    /**
     * 订单所属事业中心部门ID
     */
    private Long careerId;

    /**
     * 订单所属公司名称
     */
    private String companyName;

    /**
     * 订单所属销售公司id
     */
    private Long companyId;

    /**
     * 订单所属区域id
     */
    private Long regionId;

    /**
     * 删除标记: 0-未删除|1-删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 更新人名称
     */
    private String updateUserName;

    /**
     * 售后类型描述
     */
    private String afterSalesTypeDesc;

    /**
     * 售后状态描述
     */
    private String afterSalesStatusDesc;

    /**
     * 售后来源描述
     */
    private String afterSalesSourceDesc;
}
