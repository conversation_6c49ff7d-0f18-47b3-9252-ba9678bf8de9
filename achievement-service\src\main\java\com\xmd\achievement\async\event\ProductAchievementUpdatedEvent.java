package com.xmd.achievement.async.event;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * 商品业绩更改事件
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@ToString
public class ProductAchievementUpdatedEvent extends ApplicationEvent {

    private final String EVENT_NAME = "商品业绩更新事件";
    private final AchievementProductDetailModel oldAchievement;
    private final AchievementProductDetailModel newAchievement;

    public ProductAchievementUpdatedEvent(Object source, AchievementProductDetailModel oldAchievement, AchievementProductDetailModel newAchievement)  {
        super(source);
        this.oldAchievement = oldAchievement;
        this.newAchievement = newAchievement;
    }
}
