package com.xmd.achievement.util.encrypt.crypto.keygen;

import com.xmd.achievement.util.encrypt.AESSupport;
import com.xmd.achievement.util.encrypt.RSAUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/1/21 10:56 上午
 */
public class KeyTest {
    //平台--aes密钥
    static String secret = "a6b0295328da643c05ff4470f25faddd";
    //三方--aes密钥
    static String secretThird = "7a42011dc6ccafbec77a07eaeb41a67b";
    //平台--aes加密的公钥
    static String pubKeyEncrypt = "StzjLpto3E968gCRjWlVYoFuEKcKfBEyqp3AE5isfO1XW9JzZXGlz0LqFuHQsaXvNH5kdAwl+COOBmxbH3IHXoji/i7yKwG6cSFOv+9Nfm0nywQZXntbaPJzTzw7+lBxKFWgO+iC9StaB+moOtLR77KtFOvygPqq1382EbWIacV2xjgzIq/VzCnfWBYA3HRq66BWFpvB9ecZ4xo4wzzu8ovGr4pu6s9J99pfX9GPkiKbzicEjBaOVGFJKCVqBxOWXPWddtqj3QO4uDGpqG9icXr/E7Tz3Cwogo+UmY9M42wEv3gw1Yll37eH/ulqvyc3wwfcrA==";
    //平台--aes加密的私钥
    static String priKeyEncrypt = "mU527XEwhNHNdUDDvQMCpdp2Gpj/819KeBy5vNBU0VIduGORTnzINy6knf4+tmge1DdKbjbPdKRZcQhwZttxJUkPsBX6MO3nMIrvI9+UNo/rPX3UbybrKgg1Myhwl0cU98ZAk6RqbOB5HQ/Im5/BfBk9/dr3Jkx4AdSagsqmBlJOiXcRLzBXKtrK5mBtJu0p+5zcbokci6+kc6zcGgAiZCCu/CgscKWTCLUbxMKT+b2h5SvnTiPibVHV4vyTtpgoL5fa2npsQ3Dn5EzvFB2N84BVFPgQqIP5A0u2J3UWiQj4/g4jPwj6jQiIO4ZjYcfrubDo9q4adYhFocpqMEZRYHHsd3fpv7mExzjksUxpy5Ax2TPy2z1J8ajJdr/sZlwD+4uidiVPTlXQCk6kwaY/sTrZeNjERgMT87oacgrgpe+TNqlTUplnQwfZbMh7F05em0RlyAwNiGM39DnEWRGlQhWNSor2U5+5CL6dBDrWlf5jYM40i4352H2NUI19kXhUqsfPOh5ZpDD9MYfG/687XahRFGf4IFFXaM8PUTVR3oaFPP3017Bs6Y4wkr5cF+UrU8eXcDPJFn72COqxxh5hi4FYoHLUo/SfQpC886d7X9sOCmWjWK6kg6jJB9pex3HyizMEaUsjcDREXWYO1fj9lRLqs8lqn2kLFQ+NT+2KSygwR/pVPGENMRDQvO6sY8BTzBSpydgEUo6e2W/NzlskU6WoYGswsOo3KRKT+5Io6a2mXZG16UJXLOtZ/UPSKq/Mn+qRU2TQ3AivVpjasKDwEAQsDJE8xA9aDF6EHY2TXzsn8fVNL7+ZalvuiFu+AC6Dm2dYCDSX4vWAcrcuy9HV3vuWZyY0XY4xuMQY6fUtzPN+MtV7J5dchyr0hp/HWLZXzzUfDM69B85jDkygw61dGyFe3xYoeCqYFNyqzMxQjEWcBHXjdXphsgtI7fDl+zUttgzyc0AndCtNxALKJ1efoOdh/hL0fJuTFPaB4M0L1zosnrpqne6bYlSFfUaL49vDEj/tNm+mxjDIVmMkA5Xg4hpHlC6Op0IugKGpXE1cmOqNWlja2KbPdLoqRzVow/q8dozFuK7zf1+woz0tA05vZoN1U/EZcVejILRT7q87GjxaP8zHzYfcvXmhXQafRxfDtFNcjd/xZqGlu1Qf";
    //三方--aes加密的公钥
    static String pubKeyEncryptThird = "JYF/0cuF7BsJvzQN9HJCRmlmUmkFOFep9CidG87Y+uZHjV5yT+Zrnd+AdRphQ0fAZ90RAtlv3i8mPhecuRXHyXRcRM9MXN+Y7M+iiD25R93gAEWnEMsc5huHjPlwm0psz/lzDldf5XSTutmHz1nkC2B1okGxL8kJzSayMHWeSw3r9h6eZIeTmwJ3ia7wnKcvWCvP4LT0fPmdc/ospL6XZ8U5BSR/1xgtrcJD3j30bqnVHbrzoentlcUsSu5FthhorKEq61WMbBhm0sKttAfD31v0NlCJZbtiIf4QcbqXqhqX985paWWe3FSLVoE4qcV/CehwLQ==";
    //三方--aes加密的私钥
    static String priKeyEncryptThird = "KgK1KF2YZD5DIayis5p6Nk20St+pdHRIZTRISR/pFEbvCf5wR0zBEUX05tAPwyoBYU9ZFiVRyYf0xExxmoX1pyXuqu3tt6CxnLFjPsHbgpZfMouj5PkA1MTfuNmzcB6RiPpmdH+PSojL/xlJcoAG1BmVCykz0CuP4ATsRRLsssvdcfAEuS9XcdpTbIwJ1DvCpeLlVUpgLOooy1QWgl9UaA6NRpg8EpmHJaVTXwGxtLPac2bf0FyjtAmv/Y+VV6EOuJbbOH+++oSHQ7aGCahpDlFwKj7lzmBmDaYctELPFVc+avZ3e/ZVWzzK4yKF/FsuPubVi8PCXv3CvnfNNzWIWuPPbtWqvmfOVr3eXKlEskIMhXT8qGfEyCxTGjZelebyo3+8CJiBEbA+DWFVI8CH8WcE32lcz0R82DpTGz9MwRZaOSrmsaJOLJ6xZd1jiv41hiSd9mCUPCr430lTyTz/Bt9RF2EAcgpDOhCW5b0H7qEZ4QUAaDzDZpxPbvX+7N0nxEiabiAY84g2OlBhiPKTw24Hag7bm40BPSopgd9PLS966E/D4iKKm1wSB+6BJlgeVtsmi2YaFgx6Ji/Fkws4zxz+lLdhexAerXPGUkncWTdx8n/7Csta6+o3OhYfhXB04dMwkeuY5jW++LjZDsTtUnxRs8RSOGTVtYk9XCj+jnp3wR0i8ZPb13c+cJY2U9DnrN86J3DYDO7MfyFV4CtTjTVOcO5YFXME0m4hPA2xvYzUrAtZyaNfL9ey+kJfN0souzSwoIycIiNYJr3w4r6WMLldCgab/rnuvAs+ywW1zIyxW4YpGTJ3G5b0XVEWgPLw0YTqYrVTl9kRtLJoJ1zh38LT5PHFuoepDwyWuqFhJEthe48LNQmk5za6/AVZydyEhozyXqryRNbPSbvvbLEBiKW2N8ekeX2yIb85OEDajRiAfIJzatWIZ6FedynqFX5QMP66RifxQ70zviepPlaLBxO/gRCA7uK0vBOnAfrHip+NGIk/GZ4SYfQvT4ctgP0Zkb/1KhTYIUaRTY28Cq/85h3TjW+twfVy9QDDBPWnbpQtqAn4OyWc7SRuOdyhDXgTYu0pVN+lz/VvIzqsE70wbBv4DC0/ZEZ1EVvGAjohI8ElJi4yx6xDK3bBnYrOFIAcoxvpJJOOxFBc5xJe";
    //三方--aes加密的appSecret
    static String appSecretAES = "yjhUOxzZ9GDfj7xbtIOvwuX6uAR+2648yEyJBKx9mC3YScJ1LJONy8b8rX9S+bMdxekmBKWUQ1S7s9HPHQ==";
    //三方--公钥加密的内容
    static String accessTokenPubK = "RG6VPqW2aw5Rhz5I26kR3lgEbl74JbjHJg7P/thKPPV05I3elIZcODwZCJnvLI7DiqeFVskkhRSkonoccRIax7uxoU1q31goySu63CnpzeQ63ce3EaKferT5cH5o4pCWtAu6gNjg2SSj6BqkmanVTCde5cVQLbgbRYz6t3FuH2M=";
    //appID
    static String appId = "zq32c15d4d7b4e38af2";
    //随机数字
    static String nonce = "1jd456SsaI9nOsih";
    //协助单ID
    static String assistanceId = "1";
    //商户实例ID
    static String instanceId = "1";
    //协助人ID
    static String assistanceUserId = "1";

    public static void main(String[] args) {
//
//        System.out.println("--------------获取协助单账号凭证---------------");
//        System.out.println(Pbkdf2Util.getEncryptedPassword(new StringBuffer(assistanceId).append(instanceId).append(assistanceUserId).toString(), "a6a465cf7dee13ab7ec3"));

        System.out.println("--------------三方AES解密appSecret---------------");
        String appSecret = aesDecrypt(secretThird, appSecretAES);
//
//        System.out.println("--------------平台公钥加密appId---------------");
//        encryptPPK(secret,pubKeyEncrypt,priKeyEncrypt,appId);
//
//        System.out.println("--------------平台公钥加密appSecret--------------");
//        encryptPPK(secret,pubKeyEncrypt,priKeyEncrypt,appSecret);

        System.out.println("--------------三方私钥解密accessToken--------------");
        decryptPPK(secretThird, pubKeyEncryptThird, priKeyEncryptThird, accessTokenPubK);

//        System.out.println("--------------平台公钥加密app--------------");
//        String app = new StringBuilder(appId).append(PublicConstant.DATA_SEPARATOR).append(appSecret).append(PublicConstant.DATA_SEPARATOR).append(System.currentTimeMillis()).append(PublicConstant.DATA_SEPARATOR).append(nonce).toString();
//        String cgi = encryptPPK(secret, pubKeyEncrypt, priKeyEncrypt, app);

//        System.out.println("--------------平台私钥解密app--------------");
//        String content="rnceo7hFaQwEGUirVCxhzdGx282cUFJabogTeKS04hFfSYLTzRbMBVVnM7frfya2lHAgqlBetiF+NgxVu5y01Tdy/ueZidr3N322iEwxbpNzIZ6le8J+g8f0npaRQiIvi10DbJbNLgSUml7FbZtnFicrbbrxbU7PXIZOYNjZH4U=";
//        decryptPPK(secret,pubKeyEncrypt,priKeyEncrypt,content);

//        System.out.println("--------------平台私钥解密cgi--------------");
//        System.out.println(RSAUtil.decryptByPrivateKey(new AESSupport(secret).decrypt(priKeyEncrypt), cgi));
    }

//    public static void main(String[] args) {
//        createPPK(secretThird);
//    }

    /**
     * 创建公私钥对
     *
     * @param secret AES加密密钥
     */
    public static void createPPK(String secret) {
        Map<String, String> v = RSAUtil.giveKeyPair();
        String publicKey = v.get(RSAUtil.PUBLIC_KEY);
        String privateKey = v.get(RSAUtil.PRIVATE_KEY);
        System.out.println("生成的公钥：" + publicKey);
        System.out.println("生成的私钥：" + privateKey);
        String encrypt = aesEncrypt(secret, publicKey);
        String encrypt1 = aesEncrypt(secret, privateKey);
        System.out.println("AES加密后的公钥：" + encrypt);
        System.out.println("AES加密后的私钥：" + encrypt1);
    }


    /**
     * 解析公私钥，并利用私钥解密内容
     *
     * @param secret      aes密钥
     * @param publicKey   aes加密的公钥
     * @param privateKey  aes加密的私钥
     * @param encryptData 公钥加密的数据
     */
    public static void decryptPPK(String secret, String publicKey, String privateKey, String encryptData) {
        //解密私钥-公钥对
        AESSupport aesSupport = new AESSupport(secret);
        System.out.println("AES密钥：" + secret);
        String decryptP = aesSupport.decrypt(publicKey);
        String decryptS = aesSupport.decrypt(privateKey);
        System.out.println("AES解密后的公钥：" + decryptP);
        System.out.println("AES解密后的私钥：" + decryptS);
        //需要解密的数据
        String decryptData = RSAUtil.decryptByPrivateKey(decryptS, encryptData);
        System.out.println("私钥解密后的数据：" + decryptData);
    }

    /**
     * 解析公私钥，并利用公钥加密内容
     *
     * @param secret      aes密钥
     * @param publicKey   aes加密的公钥
     * @param privateKey  aes加密的私钥
     * @param encryptData 需要加密的数据
     */
    public static String encryptPPK(String secret, String publicKey, String privateKey, String encryptData) {
        //解密私钥-公钥对
        AESSupport aesSupport = new AESSupport(secret);
        System.out.println("AES密钥：" + secret);
        String decryptP = aesSupport.decrypt(publicKey);
        String decryptS = aesSupport.decrypt(privateKey);
        System.out.println("AES解密后的公钥：" + decryptP);
        System.out.println("AES解密后的私钥：" + decryptS);
        //需要解密的数据
        String decryptData = RSAUtil.encryptByPublicKey(decryptP, encryptData);
        System.out.println("公钥加密后的数据：" + decryptData);
        return decryptData;
    }


    /**
     * 根据AES 加密
     *
     * @param secret  AES加密密钥
     * @param content 需要加密的内容
     */
    public static String aesEncrypt(String secret, String content) {
        System.out.println("获取密钥:" + secret);
        AESSupport support = new AESSupport(secret);
        String encrypt = support.encrypt(content);
        System.out.println("AES加密后内容：" + encrypt);
        return encrypt;
    }

    /**
     * 根据AES 解密
     *
     * @param secret  AES加密密钥
     * @param content 需要解密的内容
     */
    public static String aesDecrypt(String secret, String content) {
        System.out.println("获取密钥:" + secret);
        AESSupport support = new AESSupport(secret);
        String decrypt = support.decrypt(content);
        System.out.println("AES解密后内容：" + decrypt);
        return decrypt;
    }
}
