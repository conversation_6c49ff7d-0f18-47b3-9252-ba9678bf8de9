package com.xmd.achievement.web.interceptor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 拦截器配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/10/26 上午9:48
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Resource
    private LogInterceptor logInterceptor;
    @Resource
    private LoginInterceptor loginInterceptor;


    @Resource
    private ThirdApiInterceptor thirdApiInterceptor;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(logInterceptor);
        registry.addInterceptor(loginInterceptor)
                .addPathPatterns("/**")
                //放开其他服务调取接口
                .excludePathPatterns("/api/**")
                //放开swagger文档信息
                .excludePathPatterns("/swagger-ui.html")
                .excludePathPatterns("/configuration/ui")
                .excludePathPatterns("/swagger-resources")
                .excludePathPatterns("/configuration/security")
                .excludePathPatterns("/v3/api-docs/**")
                .excludePathPatterns("/error")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/doc.html")
                .excludePathPatterns("/**/favicon.ico")
                .excludePathPatterns("/user/login/**").order(1);

        if (!"release".equals(active)) {
            return;
        }
        //三方拦截器
        registry.addInterceptor(thirdApiInterceptor)
                .addPathPatterns("/api/**").order(2);

    }

    /**
     * 添加跨域配置
     *
     * @return org.springframework.web.cors.CorsConfiguration
     * @date 2021/12/14 10:51 上午
     **/
    private CorsConfiguration addCorsConfig() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        List<String> list = new ArrayList<>();
        list.add("*");
        corsConfiguration.setAllowedOrigins(list);
        corsConfiguration.addAllowedOrigin("*");
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.addAllowedMethod("*");
//        corsConfiguration.setMaxAge(3600L);
        corsConfiguration.setAllowCredentials(true);
        return corsConfiguration;
    }

    /**
     * 跨域处理，优先级升高，防止因为顺序问题导致跨域问题
     *
     * @return org.springframework.web.filter.CorsFilter
     * @date 2021/12/15 2:47 下午
     **/
    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", addCorsConfig());
        return new CorsFilter(source);
    }
}
