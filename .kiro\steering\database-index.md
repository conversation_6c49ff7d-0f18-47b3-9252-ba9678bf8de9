# BSP Achievement System - 数据库模型索引

## 数据模型概览

基于 MyBatis Plus 的 ORM 框架，所有实体类继承 `BaseModel`，提供统一的审计字段。

## 基础模型

### BaseModel
**表**: 所有表的基础字段
**字段**:
- `delete_flag` - 删除标记 (0未删除|1删除)
- `create_time` - 创建时间
- `create_user_id` - 创建人ID (自动填充)
- `create_user_name` - 创建人名称 (自动填充)
- `update_time` - 更新时间
- `update_user_id` - 更新人ID (自动填充)
- `update_user_name` - 更新人名称 (自动填充)

## 核心业绩模型

### 1. AchievementProductDetailModel
**表**: `achievement_product_detail` - 业绩商品明细表
**主键**: `id` (自增)
**核心字段**:
- `achievement_id` - 业绩流水ID
- `business_month_id` - 商务月ID
- `business_month` - 商务月
- `order_product_id` - 订单明细编号
- 其他业绩相关字段...

### 2. AchievementCategoryDetailModel
**表**: `achievement_category_detail` - 业绩分类明细表
**功能**: 存储业绩按分类的明细数据

### 3. AchievementSpecDetailModel
**表**: `achievement_spec_detail` - 业绩规格明细表
**功能**: 存储业绩按规格的明细数据

### 4. BusinessAchievementModel
**表**: `business_achievement` - 商务业绩表
**功能**: 商务人员业绩汇总数据

### 5. BusinessMonthModel
**表**: `business_month` - 商务月表
**功能**: 商务月度管理

## 配置和规则模型

### 6. AchievementPolicyModel
**表**: `achievement_policy` - 业绩政策表
**功能**: 业绩计算政策配置

### 7. AchievementProductRuleModel
**表**: `achievement_product_rule` - 业绩商品规则表
**功能**: 商品级别的业绩规则

### 8. AchievementProductRuleConfigModel
**表**: `achievement_product_rule_config` - 业绩商品规则配置表
**功能**: 商品规则的详细配置

### 9. PolicySpecDetailModel
**表**: `policy_spec_detail` - 政策规格明细表
**功能**: 政策与规格的关联配置

## 辅助和管理模型

### 10. AchievementBlacklistModel
**表**: `achievement_blacklist` - 业绩黑名单表
**功能**: 业绩黑名单管理

### 11. AchievementSegmentModel
**表**: `achievement_segment` - 业绩分段表
**功能**: 业绩分段管理

### 12. AchievementCalculateParamLogModel
**表**: `achievement_calculate_param_log` - 业绩计算参数日志表
**功能**: 记录业绩计算的参数日志

## 查询模型 (Select Models)

### 13. AchievementProductDetailSelectModel
**功能**: 业绩商品明细查询专用模型

### 14. AchievementCategoryDetailSelectModel
**功能**: 业绩分类明细查询专用模型

### 15. AchievementSpecDetailSelectModel
**功能**: 业绩规格明细查询专用模型

## 消息队列相关模型

### 16. MqOrderPaymentInfoModel
**表**: `mq_order_payment_info` - 订单支付信息消息表
**功能**: 存储订单支付相关的消息队列数据

### 17. MqOrderRefundInfoModel
**表**: `mq_order_refund_info` - 订单退款信息消息表
**功能**: 存储订单退款相关的消息队列数据

### 18. MqServeFinishTimeInfoModel
**表**: `mq_serve_finish_time_info` - 服务完成时间信息消息表
**功能**: 存储服务完成时间相关的消息队列数据

## 客户和服务模型

### 19. CustomerSaasModel
**表**: `customer_saas` - 客户SaaS表
**功能**: 客户SaaS服务相关数据

### 20. NewOldCustomerRecordModel
**表**: `new_old_customer_record` - 新老客户记录表
**功能**: 客户类型记录管理

## 数据库设计特点

### 1. 统一审计
- 所有表继承 `BaseModel`
- 自动填充创建和更新信息
- 软删除机制 (`delete_flag`)

### 2. MyBatis Plus 注解
- `@TableName` - 指定表名
- `@TableId` - 主键配置
- `@TableField` - 字段映射
- `@FieldFill` - 自动填充策略

### 3. 数据类型
- 主键: `Long` (自增)
- 金额: `BigDecimal`
- 时间: `Date`
- 标识: `String`
- 状态: `Integer`

### 4. 命名规范
- 表名: 下划线分隔 (snake_case)
- 字段名: 下划线分隔
- 模型类: 驼峰命名 + Model 后缀

## 关联关系

### 主要关联
- `AchievementProductDetailModel` ← `achievement_id` → 业绩主表
- `BusinessAchievementModel` ← `business_month_id` → `BusinessMonthModel`
- 各种明细表通过 `achievement_id` 关联到主业绩记录

### 外键约束
- 大部分关联通过业务逻辑维护
- 关键业务数据有外键约束
- 消息队列表相对独立