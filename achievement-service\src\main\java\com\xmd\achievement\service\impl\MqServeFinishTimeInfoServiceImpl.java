package com.xmd.achievement.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.MqServeFinishTimeInfoModel;
import com.xmd.achievement.dao.entity.MqServeInprogressInfoModel;
import com.xmd.achievement.dao.repository.IMqOrderPaymentInfoRepository;
import com.xmd.achievement.dao.repository.IMqServeFinishTimeInfoRepository;
import com.xmd.achievement.dao.repository.IMqServeInprogressInfoRepository;
import com.xmd.achievement.service.MqServeFinishTimeInfoService;
import com.xmd.achievement.service.entity.dto.ReceiveServeFinishInProgressDto;
import com.xmd.achievement.service.entity.request.InfoSaveMqServeFinishTimeInfoRequest;
import com.xmd.achievement.service.entity.request.MqServeFinishTimeInfoRequest;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.AchievementSourceEnum;
import com.xmd.achievement.support.constant.enums.TaskStatusEnum;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/14:25
 * @since 1.0
 */
@Service
@Slf4j
public class MqServeFinishTimeInfoServiceImpl implements MqServeFinishTimeInfoService {
    @Resource
    private IMqServeFinishTimeInfoRepository infoRepository;
    @Resource
    private IMqOrderPaymentInfoRepository orderPaymentInfoRepository;
    @Resource
    private IMqServeInprogressInfoRepository serviceInprogressInfoRepository;

    @Override
    @Lock("'MqserveFinishTime'+#progressDto.orderId+#progressDto.productId")
    public void saveInfo(ReceiveServeFinishInProgressDto progressDto) {
        //幂等校验
        MqServeFinishTimeInfoModel model = infoRepository.getOne(
                new LambdaQueryWrapper<MqServeFinishTimeInfoModel>()
                        .eq(MqServeFinishTimeInfoModel::getOrderId, progressDto.getOrderId())
                        .eq(MqServeFinishTimeInfoModel::getProductId, progressDto.getProductId()));
        if (ObjectUtil.isNotEmpty(model)) {
            log.warn("服务完成时间任务正在处理,重复订单不做处理，message:{}", JSONUtil.toJsonStr(progressDto));
            return;
        }

        //入库
        MqServeFinishTimeInfoModel saveModel = new MqServeFinishTimeInfoModel();
        BeanUtil.copyProperties(progressDto, saveModel);
        saveModel.setServeFinishTime(progressDto.getServeItemList().get(0).getExpirationTime());
        saveModel.setTaskId(IdUtil.getSnowflake().nextId());
        infoRepository.save(saveModel);
    }

    @Override
    public void executeSuccess(Long orderId, Long productId) {
        infoRepository.update(
                null,
                new LambdaUpdateWrapper<MqServeFinishTimeInfoModel>()
                        .eq(MqServeFinishTimeInfoModel::getOrderId, orderId)
                        .set(MqServeFinishTimeInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode()));

    }

    @Override
    public void executeFailed(Long orderId, Long productId, String message) {
        MqServeFinishTimeInfoModel model = infoRepository.getOne(
                new LambdaQueryWrapper<MqServeFinishTimeInfoModel>()
                        .eq(MqServeFinishTimeInfoModel::getOrderId, orderId)
                        .eq(MqServeFinishTimeInfoModel::getProductId, productId));
        infoRepository.update(
                null,
                new LambdaUpdateWrapper<MqServeFinishTimeInfoModel>()
                        .eq(MqServeFinishTimeInfoModel::getOrderId, orderId)
                        .eq(MqServeFinishTimeInfoModel::getProductId, productId)
                        .set(MqServeFinishTimeInfoModel::getFailReason, message)
                        .set(MqServeFinishTimeInfoModel::getFailCount, model.getFailCount() + 1));

    }

    @Override
    public List<MqServeFinishTimeInfoModel> queryExecuteTask() {
        return infoRepository.list(
                new LambdaQueryWrapper<MqServeFinishTimeInfoModel>()
                        .eq(MqServeFinishTimeInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                        .le(MqServeFinishTimeInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_10)
                        .orderByAsc(MqServeFinishTimeInfoModel::getCreateTime)
                        .last("LIMIT 100"));
    }

    @Override
    public Boolean checkTaskStatus(Long orderId, Long productId, String serveNo) {
        MqServeFinishTimeInfoModel model = infoRepository.getOne(
                new LambdaQueryWrapper<MqServeFinishTimeInfoModel>()
                        .eq(MqServeFinishTimeInfoModel::getOrderId, orderId)
                        .eq(MqServeFinishTimeInfoModel::getProductId, productId)
                        .eq(MqServeFinishTimeInfoModel::getServeNo, serveNo)
                        .eq(MqServeFinishTimeInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode()));
        return ObjectUtil.isNotEmpty(model);
    }

    @Override
    public Boolean infoSaveMqServeFinishTimeInfo(InfoSaveMqServeFinishTimeInfoRequest request) {
        List<MqServeFinishTimeInfoModel> models = new ArrayList<>();
        for (MqServeFinishTimeInfoRequest info : request.getInfoList()) {
            MqServeFinishTimeInfoModel one = infoRepository.getOne(
                    new LambdaQueryWrapper<MqServeFinishTimeInfoModel>()
                            .eq(MqServeFinishTimeInfoModel::getOrderId, info.getOrderId())
                            .eq(MqServeFinishTimeInfoModel::getProductId, info.getProductId())
                            .eq(MqServeFinishTimeInfoModel::getServeNo, info.getServeNo()));
            if (ObjectUtil.isNotEmpty(one)) {
                continue;
            }
            MqServeFinishTimeInfoModel model = new MqServeFinishTimeInfoModel();
            model.setTaskId(IdUtil.getSnowflake().nextId());
            model.setServeNo(info.getServeNo());
            model.setOrderId(info.getOrderId());
            model.setProductId(info.getProductId());
            model.setServeFinishTime(info.getServeFinishTime());
            models.add(model);
        }

        infoRepository.saveBatch(models);
        return null;
    }

    @Override
    public Boolean repairMqOrderPaymentInfo() {
        orderPaymentInfoRepository.update(null,
                new LambdaUpdateWrapper<MqOrderPaymentInfoModel>()
                        .eq(MqOrderPaymentInfoModel::getAchievementSource, AchievementSourceEnum.KUAJINFG.getCode())
                        .set(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                        .set(MqOrderPaymentInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_0)
                        .set(MqOrderPaymentInfoModel::getFailReason, null));

        //删除四张表信息    手动
        return true;
    }

    @Override
    public Boolean repairMqServeFinishTimeInfo() {
        infoRepository.update(null,
                new LambdaUpdateWrapper<MqServeFinishTimeInfoModel>()
                        .set(MqServeFinishTimeInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                        .set(MqServeFinishTimeInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_0)
                        .set(MqServeFinishTimeInfoModel::getFailReason, null));

        return true;
    }

    @Override
    public Boolean repairMqServeInprogressInfo() {
        serviceInprogressInfoRepository.update(null,
                new LambdaUpdateWrapper<MqServeInprogressInfoModel>()
                        .set(MqServeInprogressInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                        .set(MqServeInprogressInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_0)
                        .set(MqServeInprogressInfoModel::getFailReason, null));

        return true;
    }
}
