package com.xmd.achievement.handler.achievement;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.AchievementPolicyModel;
import com.xmd.achievement.dao.entity.PolicySpecDetailModel;
import com.xmd.achievement.service.IPolicyService;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.CurrentStatusEnum;
import com.xmd.achievement.support.constant.enums.PolicyRevenueNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 商代实发提成业绩计算<br/>
 * ●当岗位为商务代表/商务主管时，商代实发业绩提成(实发比例在商品中进行配置中):<br/>
 * ○若商品业绩计收节点为支付完成(付款完成），则：商代实发业绩提成=商代提成业绩x实发业绩提成比例（实发业绩提成比例固定为100%）；<br/>
 * ○若商品业绩计收节点为生产完成，则：<br/>
 * ■订单付款完成，商务实发业绩提成=商代提成业绩x实发业绩提成比例;<br/>
 * ■服务交付完成，商务实发业绩提成=商代提成业绩x（1-实发业绩提成比例);<br/>
 *
 * <AUTHOR>
 * @date: 2024/12/24 10:25
 */
@Slf4j
@Service
public class SpecCalculateAgentActCommAchv implements CalculateAmountHandler {
    @Resource
    private IPolicyService policyService;

    @Override
    public void calculate(CalculateFactInfo factInfo) {
        try {
            //获取商品业绩政策
            PolicySpecDetailModel policy = policyService.getPolicyDetailBySpecId(factInfo.getSpec().getSpecId());
            if (policy != null) {
                factInfo.getSpec().setRevenueNode(policy.getRevenueNode());
                //根据商品业绩计收节点计算商代实发提成业绩
                BigDecimal amount;
                if (CurrentStatusEnum.PAID.getStatus().equals(factInfo.getCurrentStatus())) {
                    if (PolicyRevenueNodeEnum.PAID_FINISHED.getType().equals(policy.getRevenueNode())) {
                        amount = NumberUtil.mul(factInfo.getSpec().getAgentCommAchv(), new BigDecimal(NumberConstants.INTEGER_VALUE_1));
                    } else {
                        amount = NumberUtil.mul(factInfo.getSpec().getAgentCommAchv(), NumberUtil.div(policy.getCommissionRatio(), NumberConstants.INTEGER_VALUE_100)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                    }
                } else {
                    amount = NumberUtil.mul(factInfo.getSpec().getAgentCommAchv(), (new BigDecimal(NumberConstants.INTEGER_VALUE_1).subtract(NumberUtil.div(policy.getCommissionRatio(), NumberConstants.INTEGER_VALUE_100)))).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                }
                factInfo.getSpec().setAgentActCommAchv(amount);
            } else {
                throw new RuntimeException(factInfo.getProduct().getProductId() + "未配置商品业绩政策");
            }
        } finally {
            log.warn("商代实发提成业绩计算业绩 规格id:{},参数:{} ", factInfo.getSpec().getSpecId(), JSON.toJSONString(factInfo));
        }
    }
}
