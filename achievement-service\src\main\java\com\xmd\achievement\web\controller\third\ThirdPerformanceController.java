package com.xmd.achievement.web.controller.third;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;

import com.xmd.achievement.dao.entity.OrganizationMonthlyReportModel;
import com.xmd.achievement.dao.entity.PositionModel;
import com.xmd.achievement.dao.entity.SeniorityModel;
import com.xmd.achievement.dao.entity.BusinessAchievementModel;
import com.xmd.achievement.dao.entity.CustomerSaasModel;
import com.xmd.achievement.dao.dto.SearchAchievementDetailsDto;
import com.xmd.achievement.service.*;
import com.xmd.achievement.service.entity.dto.ThirdAchievementDTO;
import com.xmd.achievement.service.entity.request.SearchMonthReportRequest;
import com.xmd.achievement.service.entity.request.BusinessIdsDateRangeRequest;
import com.xmd.achievement.service.entity.request.ExportKuajingAchievementRequest;
import com.xmd.achievement.service.entity.request.MonthFrozenRequest;
import com.xmd.achievement.service.entity.request.ThirdPerformanceRequest;
import com.xmd.achievement.service.entity.response.ExportKuajingAchievementResponse;
import com.xmd.achievement.service.entity.request.SearchBusinessAchievementRequest;
import com.xmd.achievement.service.entity.request.SearchProductDetailRequest;
import com.xmd.achievement.support.constant.enums.ThirdSourceEnum;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.CsvUtils;
import com.xmd.achievement.web.util.DateUtils;
import com.xmd.achievement.web.util.EasyExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 中小数据流水录入 导出 导入
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/15/09:17
 * @since 1.0
 */
@Tag(name = "API-TPC-中小数据流水")
@Slf4j
@RestController
@RequestMapping("api/achievement")
public class ThirdPerformanceController {
    @Resource
    private ThirdAchievementService thirdAchievementService;

    @Resource
    ExportPerformanceServcie exportPerformanceServcie;

    @Resource
    IAchievementProductDetailService achievementProductDetailService;

    @Resource
    private MqOrderPaymentInfoService marketOrderPaymentInfoService;

    @Resource
    private IBusinessMonthService businessMonthService;

    @Resource
    private IOrganizationReportService organizationReportService;

    @Resource
    private IBusinessAchievementService businessAchievementService;

    @Resource
    private PositionService positionService;

    @Resource
    private ISeniorityService seniorityService;

    @Resource
    private ICustomerSaasService customerSaasService;

    @Resource
    private AchievementFileService achievementFileService;

    @Operation(summary = "API-TPC-01:实时数据-中小数据流水信息")
    @PostMapping("flowing")
    public WebResult<Boolean> flowing(@RequestBody @Valid ThirdPerformanceRequest request) {
        log.info("API-TPC-01:中小数据流水录入,请求参数:{}", JSONUtil.toJsonStr(request));
        return WebResult.success(thirdAchievementService.flowing(request, ThirdSourceEnum.ONLINE));
    }


    @Operation(summary = "API-TPC-02:历史数据-导入中小数据")
    @PostMapping(value = "/importAchievement")
    public WebResult<Void> importAchievement(@RequestParam("file") MultipartFile file) {
        try {
            // 解析为 Java 对象列表
            List<ThirdAchievementDTO> dtoList = CsvUtils.parseCsvToBeansGB18030(file, ThirdAchievementDTO.class);
            List<ThirdPerformanceRequest> requests = BeanUtil.copyToList(dtoList, ThirdPerformanceRequest.class);
            thirdAchievementService.excelFlowing(requests, ThirdSourceEnum.EXCEL);
            return WebResult.success();
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
        } catch (Exception e) {
            log.error("其他异常", e);
            throw new RuntimeException(e);
        }
        return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
    }


    @Operation(summary = "API-TPC-03:跨境业务数据导出")
    @PostMapping(value = "/export")
    public void exportKuajingAchievement(@RequestBody @Valid ExportKuajingAchievementRequest request, HttpServletResponse response) {
        log.info("API-TPC-02:跨境业务数据导出，请求参数:{}", JSONUtil.toJsonStr(request));
        try {
            //查询数据
            List<ExportKuajingAchievementResponse> responseList = exportPerformanceServcie.exportKuajingAchievement(request);
            //导出
            EasyExcelUtil.download(response, responseList, "业绩统计-" + DateUtils.format(new Date(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss), Boolean.TRUE);
        } catch (Exception e) {
            log.error("跨境业务数据导出", e);
            throw new BusinessException("跨境业务数据导出");
        }
    }

    @Operation(summary = "API-TPC-04:重新跑数据(停掉所有定时任务才可执行)")
    @PostMapping("renewDate")
    public WebResult<Boolean> renewDate() {
        log.info("API-TPC-04-重新跑数据-停掉所有定时任务才可执行");
        return WebResult.success(marketOrderPaymentInfoService.renewDate());
    }

    @Operation(summary = "API-TPC-05:根据月份查询商务月是否已冻结")
    @PostMapping("/isMonthFrozen")
    public WebResult<Boolean> isMonthFrozen(@RequestBody MonthFrozenRequest request) {
        boolean frozen = businessMonthService.isMonthFrozen(request.getMonth());
        return WebResult.success(frozen);
    }

    @Operation(summary = "API-TPC-06:批量查询机构月报")
    @PostMapping("/searchMonthReport")
    public WebResult<List<OrganizationMonthlyReportModel>> searchMonthReport(@RequestBody @Valid SearchMonthReportRequest request) {
        return WebResult.success(organizationReportService.searchMonthReport(request.getOrgIds(), request.getMonths()));
    }

    @Operation(summary = "API-TPC-07:批量查询商务业绩")
    @PostMapping("/searchBusinessAchievement")
    public WebResult<List<BusinessAchievementModel>> searchBusinessAchievement(@RequestBody @Valid SearchBusinessAchievementRequest request) {
        List<BusinessAchievementModel> result = businessAchievementService.queryBusinessAchievements(request.getBusinessMonths(), request.getEmployeeIds());
        return WebResult.success(result);
    }

    @Operation(summary = "API-TPC-08:批量查询业绩明细")
    @PostMapping("/searchAchievementDetails")
    public WebResult<List<SearchAchievementDetailsDto>> searchAchievementDetails(@RequestBody @Valid SearchProductDetailRequest request) {
        return WebResult.success(achievementProductDetailService.searchAchievementDetails(request.getBusinessMonths(), request.getBusinessIds(), request.getCompanyId()));
    }

    @Operation(summary = "API-TPC-09:查询所有职级")
    @PostMapping("/allPositions")
    public WebResult<List<PositionModel>> getAllPositions() {
        List<PositionModel> list = positionService.getAllPositions();
        return WebResult.success(list);
    }
    
    @Operation(summary = "API-TPC-10:查询所有司龄段")
    @PostMapping("/allSeniorities")
    public WebResult<List<SeniorityModel>> getAllSeniorities() {
        List<SeniorityModel> list = seniorityService.getAllSeniorities();
        return WebResult.success(list);
    }

    @Operation(summary = "API-TPC-11:根据业务ID集合和日期区间查询客户SaaS信息")
    @PostMapping("/searchCustomerSaas")
    public WebResult<List<CustomerSaasModel>> searchCustomerSaas(@RequestBody @Valid BusinessIdsDateRangeRequest request) {
        List<CustomerSaasModel> result = customerSaasService.searchCustomerSaas(request.getBusinessIds(), request.getStartDate(), request.getEndDate());
        return WebResult.success(result);
    }

    // @Operation(summary = "API-TPC-12:导入中企数据-不经过mq")
    // @PostMapping(value = "/importZqAchievement")
    // public WebResult<Void> importZqAchievement(@RequestParam("file") MultipartFile file) {
    //     try {
    //         achievementFileService.importZqAchievementFromThird(file);
    //         return WebResult.success();
    //     } catch (IOException e) {
    //         log.error("解析Excel表格异常", e);
    //         return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "解析Excel表格异常");
    //     } catch (Exception e) {
    //         log.error("其他异常", e);
    //         return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
    //     }
    // }
}
