package com.xmd.achievement.web.controller.third;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.IPolicyService;
import com.xmd.achievement.service.entity.request.CheckPolicyRequest;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 业绩政策
 *
 * <AUTHOR>
 * @date: 2024/12/19 11:16
 * @version: 1.0.0
 * @return {@link }
 */
@Tag(name = "API-POC-业绩政策接口")
@Slf4j
@RestController
@RequestMapping("api/policy")
public class PolicyApiController {
    @Resource
    IPolicyService policyService;

    @Operation(summary = "API-POC-01:检查规格业绩政策配置是否完成")
    @PostMapping("checkPolicy")
    public WebResult<List<String>> checkPolicy(@RequestBody @Valid CheckPolicyRequest request) {
        log.info("API-POC-01:检查规格业绩政策配置是否完成,请求参数:{}", JSONUtil.toJsonStr(request));
        return WebResult.success(policyService.checkPolicy(request));
    }
}
