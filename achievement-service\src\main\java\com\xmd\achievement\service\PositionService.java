package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.PositionModel;
import com.xmd.achievement.service.entity.response.QueryPositionListResponse;

import java.util.List;

/**
 * 职级接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/10:35
 * @since 1.0
 */
public interface PositionService {
    /**
     * 查询职级集合
     *
     * @return List<QueryPositionListResponse>
     */
    List<QueryPositionListResponse> queryPositionList();

    /**
     *
     *  根据职级id和是否转正查询职级
     * @param jobGradeId 职级id
     * @param passFlag 是否转正
     * <AUTHOR>
     * @date: 2024/12/25 11:37
     * @version: 1.0.0
     * @return {@link }
     */
    PositionModel getByJobGradeIdAndPassFlag(Integer jobGradeId, Integer passFlag);

    /**
     * 查询所有职级全表数据
     * @return List<PositionModel>
     */
    List<PositionModel> getAllPositions();
}
