package com.xmd.achievement.service.entity.response;

import com.xmd.achievement.support.constant.enums.ProductChurnStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/10:47
 * @since 1.0
 */
@Data
public class ProductChurnResponse {
    //商品ID
    private Long productId;

    //订单ID
    private Long orderId;

    // CHURN-流失 NO_CHURN未流失
    private ProductChurnStatusEnum churnStatus;

    //流失时间
    private Date churnTime;
}
