package com.xmd.achievement.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xmd.achievement.service.IApiMockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class ApiMockServiceImpl implements IApiMockService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    public static final String MOCK_PREFIX = "bsp-performance:mock:";
    private static final long TIME_OUT = 3;

    @Override
    public void addMock(String apiName, String paramJson, Object value) {
        String key = buildMockKey(apiName, paramJson);
        String val = JSON.toJSONString(value);
        stringRedisTemplate.opsForValue().set(key, val, TIME_OUT, TimeUnit.DAYS);
    }

    @Override
    public void addMock(String apiName, Object value) {
        String key = buildMockKey(apiName, null);
        String val = JSON.toJSONString(value);
        stringRedisTemplate.opsForValue().set(key, val, TIME_OUT, TimeUnit.DAYS);
    }

    @Override
    public void addMockString(String apiName, String value) {
        String key = buildMockKey(apiName, null);
        stringRedisTemplate.opsForValue().set(key, value, TIME_OUT, TimeUnit.DAYS);
    }

    @Override
    public void addMockHash(String key, String hashKey, String value) {
        key = buildMockKey(key, null);
        stringRedisTemplate.opsForHash().put(key, hashKey, value);
        stringRedisTemplate.expire(key, TIME_OUT, TimeUnit.DAYS);
    }

    @Override
    public void addMockHash(String key, Map<String, String> map) {
        key = buildMockKey(key, null);
        stringRedisTemplate.opsForHash().putAll(key, map);
        stringRedisTemplate.expire(key, TIME_OUT, TimeUnit.DAYS);
    }

    @Override
    public void addMock(String apiName, String paramJson, String valueJson) {
        String key = buildMockKey(apiName, paramJson);
        stringRedisTemplate.opsForValue().set(key, valueJson, TIME_OUT, TimeUnit.DAYS);
    }

    @Override
    public String buildMockKey(String apiName, String paramJson) {
        String key = MOCK_PREFIX + apiName;
        if (StrUtil.isNotBlank(paramJson)) {
            paramJson = paramJson.replaceAll(":", "_");
            return key + ":" + paramJson;
        }
        return key;
    }

    @Override
    public <T> T getMock(String apiName, String paramJson, Class<T> clazz) {
        String key = buildMockKey(apiName, paramJson);
        String val = stringRedisTemplate.opsForValue().get(key);
        if (val == null) {
            return null;
        }
        return JSON.parseObject(val, clazz);
    }

    @Override
    public <T> T getMock(String apiName, Class<T> clazz) {
        return getMock(apiName, null ,clazz);
    }

    @Override
    public <T> T getMock(String apiName, String paramJson, TypeReference<T> typeRef) {
        String key = buildMockKey(apiName, paramJson);
        String val = stringRedisTemplate.opsForValue().get(key);
        if (val == null) {
            return null;
        }
        return JSON.parseObject(val, typeRef);
    }

    @Override
    public <T> T getMock(String apiName, TypeReference<T> typeRef) {
        return getMock(apiName, null, typeRef);
    }

    @Override
    public String getMockString(String apiName) {
        return getMockString(apiName, null);
    }

    @Override
    public String getMockString(String apiName, String paramJson) {
        String key = buildMockKey(apiName, paramJson);
        return stringRedisTemplate.opsForValue().get(key);
    }

    @Override
    public List<Object> getMockHash(String apiName) {
        String key = buildMockKey(apiName, null);
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(key);
        return ListUtil.toList(entries.values());
    }

    @Override
    public Map<Object, Object> getMockHashEntries(String apiName) {
        String key = buildMockKey(apiName, null);
        return stringRedisTemplate.opsForHash().entries(key);
    }

    @Override
    public Object getMockHash(String apiName, String hashKey) {
        String key = buildMockKey(apiName, null);
        return stringRedisTemplate.opsForHash().get(key, hashKey);
    }

    @Override
    public List<Object> getMockHash(String apiName, List<String> hashKeys) {
        String key = buildMockKey(apiName, null);
        List<Object> collect = hashKeys.stream().map(hk -> (Object) hk).collect(Collectors.toList());
        return stringRedisTemplate.opsForHash().multiGet(key, collect);
    }

    @Override
    public void deleteMock(String apiName) {
        String key = buildMockKey(apiName, null);
        stringRedisTemplate.delete(key);
    }

    @Override
    public void deleteMock(String apiName, String paramJson) {
        String key = buildMockKey(apiName, paramJson);
        stringRedisTemplate.delete(key);
    }

    @Override
    public void deleteMockHash(String apiName, String hashKey) {
        String key = buildMockKey(apiName, null);
        if (StrUtil.isBlank(hashKey)) {
            Set<Object> keys = stringRedisTemplate.opsForHash().keys(key);
            stringRedisTemplate.opsForHash().delete(key,
                    keys.stream().map(hk -> (String) hk).toArray());
            return;
        }
        stringRedisTemplate.opsForHash().delete(key, hashKey);
    }
}
