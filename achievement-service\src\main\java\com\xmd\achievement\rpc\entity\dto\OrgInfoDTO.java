package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
public class OrgInfoDTO implements Serializable {

    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "部门ID")
    private Long orgId;

    @Schema(description = "部门名称")
    private String name;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "市场分类")
    private Integer marketCategoryId;

    @Schema(description = "市场分类")
    private Integer marketCategoryName;

    @Schema(description = "级别")
    private Integer level;

    @Schema(description = "排序")
    private Integer sort;
}
