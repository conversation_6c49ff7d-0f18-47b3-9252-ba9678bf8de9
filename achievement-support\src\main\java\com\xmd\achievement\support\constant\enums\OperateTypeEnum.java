package com.xmd.achievement.support.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举
 */
@Getter
@AllArgsConstructor
public enum OperateTypeEnum {

    /**
     * 新增
     */
    CREATE("CREATE", "新增"),

    /**
     * 更新
     */
    UPDATE("UPDATE", "更新"),

    /**
     * 删除
     */
    DELETE("DELETE", "删除"),

    /**
     * 查询
     */
    QUERY("QUERY", "查询"),

    /**
     * 导入
     */
    IMPORT("IMPORT", "导入"),

    /**
     * 导出
     */
    EXPORT("EXPORT", "导出"),

    /**
     * 其他
     */
    OTHER("OTHER", "其他");

    /**
     * 操作类型编码
     */
    private final String code;

    /**
     * 操作类型描述
     */
    private final String desc;
}