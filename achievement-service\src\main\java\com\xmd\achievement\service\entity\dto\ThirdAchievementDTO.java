package com.xmd.achievement.service.entity.dto;

import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvDate;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * easyExcel
 */
@Data
public class ThirdAchievementDTO implements Serializable {

    private static final long serialVersionUID = 2387356132678099748L;

    @CsvBindByName(column = "thirdId")
    private String thirdId;

    @CsvBindByName(column = "businessMonth")
    private String businessMonth; // 商务月

    /**
     * 订单明细编号
     */
    @CsvBindByName(column = "orderDetailNo")
    private String orderDetailNo;

    @CsvBindByName(column = "productType")
    private Integer productType; // 产品类型

    @CsvBindByName(column = "productId")
    private Integer productId; // 商品ID

    @CsvBindByName(column = "productName")
    private String productName; // 商品名称

    @CsvBindByName(column = "webInfo")
    private Integer webInfo; // 是否网站

    @CsvBindByName(column = "businessType")
    private String businessType; //业务类型

    @CsvBindByName(column = "state")
    private Integer state; // 状态

    @CsvBindByName(column = "orderRecordCode")
    private String orderRecordCode; // 订单记录编号

    @CsvBindByName(column = "dataSource")
    private Integer dataSource; // 数据来源

    @CsvBindByName(column = "custId")
    private String custId; // 客户ID

    @CsvBindByName(column = "custName")
    private String custName; // 客户名称

    @CsvBindByName(column = "custType")
    private Integer custType; // 客户类型

    @CsvBindByName(column = "custCity")
    private String custCity; // 客户所在城市

    @CsvBindByName(column = "contractCode")
    private String contractCode; // 合同编号

    @CsvBindByName(column = "salerId")
    private String salerId; // 商务ID

    @CsvBindByName(column = "salerName")
    private String salerName; // 商务名称

    @CsvBindByName(column = "shareType")
    private Integer shareType; // 主副类型

    @CsvBindByName(column = "orgId")
    private String orgId; // 分司ID

    @CsvBindByName(column = "areaId")
    private String areaId; // 区域ID

    @CsvBindByName(column = "buId")
    private String buId; // BU ID

    @CsvBindByName(column = "deptId")
    private String deptId; // 部门ID

    @CsvBindByName(column = "singingAmount")
    private Double singingAmount; // 签单金额

    @CsvBindByName(column = "actualAccount")
    private Double actualAccount; // 实际到账

    @CsvBindByName(column = "discountAccount")
    private Double discountAccount; // 折扣金额

    @CsvBindByName(column = "singingDate")
    //@CsvDate("yyyy-MM-dd HH:mm:ss")
    @CsvDate("dd/MM/yyyy HH:mm:ss")
    private Date singingDate; // 签单日期

    @CsvBindByName(column = "toAccountDate")
    //@CsvDate("yyyy-MM-dd HH:mm:ss")
    @CsvDate("dd/MM/yyyy HH:mm:ss")
    private Date toAccountDate; // 到账日期

    @CsvBindByName(column = "firstStandardAccount")
    private Double firstStandardAccount; // 首年标准价

    @CsvBindByName(column = "firstActualAccount")
    private Double firstActualAccount; // 首年到账

    @CsvBindByName(column = "renewStandardAccount")
    private Double renewStandardAccount; // 续费标准价

    @CsvBindByName(column = "renewActualAccount")
    private Double renewActualAccount; // 续费到账

    @CsvBindByName(column = "netCashAccount")
    private Double netCashAccount; // 实发业绩

    @CsvBindByName(column = "saleHiredMoney")
    private Double saleHiredMoney; // 商务提成业绩

    @CsvBindByName(column = "relaySaleHiredMoney")
    private Double relaySaleHiredMoney; // 实发商务提成业绩

    @CsvBindByName(column = "delaySaleHiredMoney")
    private Double delaySaleHiredMoney; // 缓发商务提成业绩

    @CsvBindByName(column = "managerHiredMoney")
    private Double managerHiredMoney; // 经理提成业绩

    @CsvBindByName(column = "currentPrice")
    private Double currentPrice; // 当前价格

    @CsvBindByName(column = "subManagerHiredMoney")
    private Double subManagerHiredMoney; // 分总提成业绩

    @CsvBindByName(column = "dataState")
    private Integer dataState; // 数据状态

    @CsvBindByName(column = "dbInsertTime")
    //@CsvDate("yyyy-MM-dd HH:mm:ss")
    @CsvDate("dd/MM/yyyy HH:mm:ss")
    private Date dbInsertTime; // 数据库插入时间

    @CsvBindByName(column = "creater")
    private String creater; // 创建人

    @CsvBindByName(column = "dbUpdateTime")
    //@CsvDate("yyyy-MM-dd HH:mm:ss")
    @CsvDate("dd/MM/yyyy HH:mm:ss")
    private Date dbUpdateTime; // 数据库更新时间

    @CsvBindByName(column = "updater")
    private String updater; // 修改人

    /**
     * 是否展示 0 是，1 否
     */
    @CsvBindByName(column = "displayed")
    private Integer displayed;
}
