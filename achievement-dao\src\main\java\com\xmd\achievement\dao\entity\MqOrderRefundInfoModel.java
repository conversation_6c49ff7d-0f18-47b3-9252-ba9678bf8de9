package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 订单退转款消息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mq_order_refund_info")
public class MqOrderRefundInfoModel extends BaseModel {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;
    /**
     * task任务类型：ADD-新增 UPDATE-修改 DELETE-删除
     */
    @TableField("task_type")
    private String taskType;
    /**
     * 售后订单ID
     */
    @TableField("aftersale_order_id")
    private Long aftersaleOrderId;
    /**
     * 售后订单编号
     */
    @TableField("aftersale_order_no")
    private String aftersaleOrderNo;
    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;
    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;
    /**
     * 订单明细编码
     */
    @TableField("order_product_code")
    private String orderProductCode;
    /**
     * 客户ID
     */
    @TableField("customer_id")
    private String customerId;
    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;
    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购，5=转款，6=退款，7=高价赎回
     */
    @TableField("sale_type")
    private Integer saleType;
    /**
     * 任务状态：1-未完成 2-已完成
     */
    @TableField("task_status")
    private Integer taskStatus;
    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;
    /**
     * 失败次数
     */
    @TableField("fail_count")
    private Integer failCount;

    /**
     * 消息id
     */
    @TableField("message_id")
    private Long messageId;

    /**
     * 售后状态
     */
    @TableField("aftersale_status")
    private Integer aftersaleStatus;


    @TableField("transfer_in_order_id")
    private Long transferInOrderId;

    /**
     * 转入销售订单编号
     */
    @TableField("transfer_in_order_no")
    private String transferInOrderNo;
}