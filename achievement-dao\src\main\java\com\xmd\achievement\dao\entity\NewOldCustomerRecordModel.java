package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 新老客户记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@TableName("new_old_customer_record")
@Accessors(chain = true)
public class NewOldCustomerRecordModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 商务月id
     */
    @TableField(value = "business_month_id")
    private Long businessMonthId;
    /**
     * 员工id
     */
    @TableField(value = "employee_id")
    private String employeeId;
    /**
     * 新客户id
     */
    @TableField("new_customer_id")
    private String newCustomerId;
    /**
     * 老客户id
     */
    @TableField("old_customer_id")
    private String oldCustomerId;
}