package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 操作日志实体类
 * 用于记录系统中的数据变更操作，支持审计和追踪
 */
@Data
@TableName("operate_log")
public class OperateLogModel extends BaseModel{
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 操作日志ID
     */
    @TableField("operate_log_id")
    private Long operateLogId;
    
    /**
     * 操作类型: INSERT/UPDATE/DELETE
     */
    @TableField("operation_type")
    private String operationType;
    
    /**
     * 数据来源系统/模块
     */
    @TableField("source_system")
    private String sourceSystem;
    
    /**
     * 操作对象class
     */
    @TableField("class_name")
    private String className;
    
    /**
     * 变更前数据（JSON格式）
     */
    @TableField("before_data")
    private String beforeData;
    
    /**
     * 变更后数据（JSON格式）
     */
    @TableField("after_data")
    private String afterData;
    
    /**
     * 操作时间
     */
    @TableField("log_time")
    private LocalDateTime logTime;
    
    /**
     * 操作人ID
     */
    @TableField("log_user_id")
    private String logUserId;
    
    /**
     * 操作人名称
     */
    @TableField("log_user_name")
    private String logUserName;

    @TableField("remark")
    private String remark;

}