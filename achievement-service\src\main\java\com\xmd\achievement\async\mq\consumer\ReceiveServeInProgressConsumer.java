
package com.xmd.achievement.async.mq.consumer;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.MqServeInProgressInfoService;
import com.xmd.achievement.service.entity.dto.ReceiveServeInProgressDto;
import com.xmd.achievement.support.constant.NumberConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */

@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "${mq.group.serveInProgress}", topic = "${mq.topic.serveInProgress}", consumeMode = ConsumeMode.CONCURRENTLY, messageModel = MessageModel.CLUSTERING)
public class ReceiveServeInProgressConsumer implements RocketMQListener<String> {
    @Resource
    private MqServeInProgressInfoService serveInprogressInfoService;

    @Override
    public void onMessage(String dataJson) {
        log.info("服务中数据信息处理,接收数据:{}", dataJson);
        try {
            //解析
            ReceiveServeInProgressDto progressDto = JSONUtil.toBean(dataJson, ReceiveServeInProgressDto.class);
            log.info("new-服务中数据信息处理,数据转换:{}", JSONUtil.toJsonStr(progressDto));

            if (ObjectUtil.isNotEmpty(progressDto.getAcceptSource()) && Objects.equals(progressDto.getAcceptSource(), NumberConstants.INTEGER_VALUE_2)) {
                log.warn("服务中数据信息处理,来源为渠道，不落库");
                return;
            }
            //落库
            serveInprogressInfoService.saveInfo(progressDto);

        } catch (Exception e) {
            log.error("服务中数据信息处理异常，message:{},异常信息", dataJson, e);
            throw e;
        }
    }


/*  private boolean checkPolicyServeFinish(Long productId) {
        AchievementPolicyModel policy = policyService.getPolicyByProductId(productId);
        if (policy == null) {
            log.warn("商品{}业绩未配置政策", productId);
            return false;
        }
        return PolicyRevenueNodeEnum.SERV_FINISHED.getType().equals(policy.getRevenueNode());
    }*/

}

