package com.xmd.achievement.async.job.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.service.IMqOrderRefundInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class NotifyAftersaleJob {

    private final IMqOrderRefundInfoService mqOrderRefundInfoService;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.NOTIFY_AFTERSALE_JOB)
    public ReturnT<String> jobHandler(String param) {
        log.info("NotifyAftersaleJob jobHandler, param: {}", param);
        if (StrUtil.isBlank(param)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
        }
        JSONObject jsonObject = JSON.parseObject(param);
        String messageId = jsonObject.getString("messageId");
        if (StrUtil.isBlank(messageId)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "messageId不能为空");
        }
        MqOrderRefundInfoModel orderRefundInfoModel = mqOrderRefundInfoService.getByMessageId(messageId);
        mqOrderRefundInfoService.aftersaleCallback(orderRefundInfoModel);
        return ReturnT.SUCCESS;
    }
}
