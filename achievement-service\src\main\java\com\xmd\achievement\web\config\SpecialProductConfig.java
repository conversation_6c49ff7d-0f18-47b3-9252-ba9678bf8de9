package com.xmd.achievement.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 特殊商品走特殊的规格政策性成本
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "special-products")
public class SpecialProductConfig {

    private List<Product> items;

    private List<Spec> specs;

    @Data
    public static class Product {
        private Long id;
        private String name;
    }

    @Data
    public static class Spec {
        private Long id;
        private String name;
    }
}
