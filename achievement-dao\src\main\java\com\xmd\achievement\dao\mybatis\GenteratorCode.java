package com.xmd.achievement.dao.mybatis;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.converts.select.BranchBuilder;
import com.baomidou.mybatisplus.generator.config.converts.select.Selector;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.IColumnType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.keywords.MySqlKeyWordsHandler;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

import java.util.ResourceBundle;

import static com.baomidou.mybatisplus.generator.config.rules.DbColumnType.*;
import static com.baomidou.mybatisplus.generator.config.rules.DbColumnType.STRING;

/**
 * 生成代码的主类
 */
public class GenteratorCode {

    @SneakyThrows
    public static void main(String[] args) throws Exception {

        //用来获取Mybatis-Plus.properties文件的配置信息
        ResourceBundle rb = ResourceBundle.getBundle("mybatiesplus-config-system"); //不要加后缀
        String projectPath = System.getProperty("user.dir"); // 需要生成的表
        String tables = rb.getString("tables");
        if (StringUtils.isBlank(tables)) {
            throw new Exception("请配置需要生成的表");
        }
        // 数据源配置
        DataSourceConfig.Builder dataSourceConfig = new DataSourceConfig.Builder(rb.getString("jdbc.url"), rb.getString("jdbc.user"), rb.getString("jdbc.pwd"));
        dataSourceConfig.typeConvert(new MyMysqlTypeConvert());
        dataSourceConfig.keyWordsHandler(new MySqlKeyWordsHandler());
        dataSourceConfig.schema(rb.getString("jdbc.schema"));
//        dataSourceBuilder.dbQuery(rb.getString("jdbc.driver"));
        // 全局配置
        GlobalConfig gc = new GlobalConfig.Builder()
                .outputDir(projectPath + "/achievement-dao/src/main/java")
                .disableOpenDir()//是否打开输出目录
                .author("system")
                .dateType(DateType.ONLY_DATE)
                .build();
        // 包配置
        PackageConfig.Builder packageConfig = new PackageConfig.Builder();
        packageConfig.parent("com.xmd.achievement.dao");
        packageConfig.entity("entity");
        packageConfig.service("repository");
        packageConfig.serviceImpl("repository.impl");
        packageConfig.mapper("mapper");
        packageConfig.xml("mapper.xmls");
        // 自定义模板配置，可以 copy 源码 mybatis-plus/src/main/resources/templates 下面内容修改，
        TemplateConfig tc = new TemplateConfig.Builder()
                .serviceImpl("/templates/repositoryImpl.java.vm")
                .service("/templates/repository.java.vm")
                .controller(null)
                .build();
        // 策略配置
        StrategyConfig sc = new StrategyConfig.Builder()
                // 禁用SQL过滤
                .disableSqlFilter()
                // 表名
                .addInclude(tables.split(","))
                // 配置实体策略
                .entityBuilder()
                // 父类
                .superClass(Model.class)
                // 禁用序列化
                .disableSerialVersionUID()
                // 启用链式编程
                .enableChainModel()
                //启用Lombok
                .enableLombok()
                // 启用表字段注解
                .enableTableFieldAnnotation()
                // 表名驼峰
                .naming(NamingStrategy.underline_to_camel)
                // 列名驼峰命名
                .columnNaming(NamingStrategy.underline_to_camel)
                .idType(IdType.AUTO)
                // 格式化文件名称
                .formatFileName("%sModel")
                // 配置serveice 策略
                .serviceBuilder()
                // BaseRepository impl
                .formatServiceFileName("I%sRepository")
                .formatServiceImplFileName("%sRepositoryImpl")
                // 配置mapper策略
                .mapperBuilder()
                // 启用mapper注解
                .enableMapperAnnotation()
                // 启用 BaseResultMap
                .enableBaseResultMap()
                .enableBaseColumnList()
                .formatMapperFileName("%sMapper")
                .formatXmlFileName("%sMapper")
                .build();
        // 执行生成
        AutoGenerator template = new AutoGenerator(dataSourceConfig.build())
                // 全局配置
                .global(gc)
                .packageInfo(packageConfig.build())
                .strategy(sc)
                .template(tc);
        template.execute();
    }


    public static class MyMysqlTypeConvert extends MySqlTypeConvert {
        @Override
        public IColumnType processTypeConvert(GlobalConfig config, String fieldType) {
            Selector<String, IColumnType> selector = new Selector<>(fieldType.toLowerCase());
            return selector.test(MyTypeConverts.containsAny("char", "text", "json", "enum").then(STRING))
                    .test(MyTypeConverts.contains("bigint").then(LONG))
                    .test(MyTypeConverts.containsAny("tinyint(1)", "bit(1)").then(INTEGER))
                    .test(MyTypeConverts.contains("bit").then(BYTE))
                    .test(MyTypeConverts.contains("int").then(INTEGER))
                    .test(MyTypeConverts.contains("decimal").then(BIG_DECIMAL))
                    .test(MyTypeConverts.contains("clob").then(CLOB))
                    .test(MyTypeConverts.contains("blob").then(BLOB))
                    .test(MyTypeConverts.contains("binary").then(BYTE_ARRAY))
                    .test(MyTypeConverts.contains("float").then(FLOAT))
                    .test(MyTypeConverts.contains("double").then(DOUBLE))
                    .test(MyTypeConverts.containsAny("date", "time", "year").then(t -> toDateType(config, t)))
                    .or(STRING);

        }
    }

    /**
     * 该注册器负责注册并查询类型注册器
     *
     * <AUTHOR> hanchunlin
     * @since 3.3.1
     */
    public static class MyTypeConverts {

        /**
         * 使用指定参数构建一个选择器
         *
         * @param param 参数
         * @return 返回选择器
         */
        static Selector<String, IColumnType> use(String param) {
            return new Selector<>(param.toLowerCase());
        }

        /**
         * 这个分支构建器用于构建用于支持 {@link String#contains(CharSequence)} 的分支
         *
         * @param value 分支的值
         * @return 返回分支构建器
         * @see #containsAny(CharSequence...)
         */
        static BranchBuilder<String, IColumnType> contains(CharSequence value) {
            return BranchBuilder.of(s -> s.contains(value));
        }

        /**
         * @see #contains(CharSequence)
         */
        static BranchBuilder<String, IColumnType> containsAny(CharSequence... values) {
            return BranchBuilder.of(s -> {
                for (CharSequence value : values) {
                    if (s.contains(value)) {
                        return true;
                    }
                }
                return false;
            });
        }
    }
}