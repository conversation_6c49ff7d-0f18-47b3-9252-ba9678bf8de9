package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.dao.repository.*;
import com.xmd.achievement.rpc.entity.dto.UserLoginInfoDTO;
import com.xmd.achievement.service.IPolicyService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PolicySpecDetailResponse;
import com.xmd.achievement.service.entity.response.PolicySpecResponse;
import com.xmd.achievement.service.entity.response.QueryProductRuleConfigResponse;
import com.xmd.achievement.util.http.HttpResult;
import com.xmd.achievement.util.http.HttpResultEnum;
import com.xmd.achievement.util.http.OKHttpUtil;
import com.xmd.achievement.web.config.ThirdHttpUrlConfiguration;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@Slf4j
public class PolicyServiceImpl implements IPolicyService {
    @Resource
    private IPolicySpecDetailRepository policySpecDetailRepository;
    @Resource
    private IAchievementPolicyRepository achievementPolicyRepository;
    @Resource
    ThirdHttpUrlConfiguration thirdHttpUrlConfiguration;
    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;
    @Resource
    private IAchievementCategoryDetailRepository achievementCategoryDetailRepository;
    @Resource
    private IAchievementSpecDetailRepository achievementSpecDetailRepository;
    @Autowired
    private IAchievementProductRuleConfigRepository productRuleConfigRepository;
    @Autowired
    private IAchievementProductRuleRepository productRuleRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebResult<Boolean> savePolicy(SavePolicyRequest request) {
        //删除元数据
        policySpecDetailRepository.remove(new LambdaUpdateWrapper<PolicySpecDetailModel>().eq(PolicySpecDetailModel::getProductId, request.getProductId()));

        //新增
        List<PolicySpecDetailModel> policySpecDetailModels = new ArrayList<>();
        request.getPolicySpecDetailRequests().stream().sorted(Comparator.comparing(PolicySpecDetailRequest::getSpecId)).forEach(e -> {
            PolicySpecDetailModel detail = new PolicySpecDetailModel();
            BeanUtil.copyProperties(e, detail);
            detail.setPolicySpecDetailId(IdUtil.getSnowflake().nextId());
            detail.setProductId(request.getProductId());
            policySpecDetailModels.add(detail);
        });
        policySpecDetailRepository.saveBatch(policySpecDetailModels, policySpecDetailModels.size());
        // 添加商品流失规则
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        if (ObjectUtil.isEmpty(request.getOldRuleCode())) {
            AchievementProductRuleConfigModel ruleConfig = new AchievementProductRuleConfigModel();
            ruleConfig.setRuleCode(request.getRuleCode());
            ruleConfig.setProductId(request.getProductId());
            ruleConfig.setProductName(request.getProductName());
            ruleConfig.setProductCode(request.getProductCode());
            ruleConfig.setCreateUserId(userInfo.getUserId());
            ruleConfig.setCreateUserName(userInfo.getName());
            productRuleConfigRepository.save(ruleConfig);
        } else {
            String oldRuleCode = request.getOldRuleCode();
            String ruleCode = request.getRuleCode();
            Long productId = request.getProductId();
            AchievementProductRuleConfigModel productRuleConfigModel = productRuleConfigRepository.getOne(
                    new LambdaQueryWrapper<AchievementProductRuleConfigModel>()
                            .eq(AchievementProductRuleConfigModel::getRuleCode, oldRuleCode)
                            .eq(AchievementProductRuleConfigModel::getProductId, productId)
            );
            productRuleConfigModel.setRuleCode(ruleCode);
            productRuleConfigModel.setUpdateUserId(userInfo.getUserId());
            productRuleConfigModel.setUpdateUserName(userInfo.getName());
            productRuleConfigModel.setProductName(request.getProductName());
            productRuleConfigModel.setUpdateTime(new Date());
            productRuleConfigRepository.updateById(productRuleConfigModel);
        }

        return WebResult.success(true);
    }

    @Override
    public WebResult<PolicySpecResponse> queryPolicyList(QueryPolicyListRequest request) {
        PolicySpecResponse response = new PolicySpecResponse();
        //查询商品的规格
        Map<String, String> param = new HashMap<>();
        param.put("productId", request.getProductId());
        HttpResult<String> stringHttpResult = OKHttpUtil.getSync(thirdHttpUrlConfiguration.getItemManagement().getSpecDetail(), param);
        if (!HttpResultEnum.REQUEST_SUCCESS.getCode().equals(stringHttpResult.getCode())) {
            log.error("获取商品规格详情失败，请求参数:{},返回结果:{}", param, stringHttpResult);
            return WebResult.error(WebCodeMessageEnum.RPC_SERVER_EXCEPTION);
        }

        WebResult<List<PolicySpecDetailModel>> result = JSON.parseObject(stringHttpResult.getData(), new TypeReference<WebResult<List<PolicySpecDetailModel>>>() {
        });

        List<PolicySpecDetailModel> policySpecDetailModels = result.getData();
        if (ObjectUtils.isEmpty(policySpecDetailModels)) {
            return WebResult.success(null);
        }

        //组装数据
        List<PolicySpecDetailResponse> policySpecDetailResponses = new ArrayList<>();
        List<PolicySpecDetailModel> detailModels = policySpecDetailRepository.list(
                new LambdaQueryWrapper<PolicySpecDetailModel>()
                        .eq(PolicySpecDetailModel::getProductId, request.getProductId()));
        if (ObjectUtil.isNotEmpty(result.getData())) {
            // 合并并去重：如果specId相同，保留alist中的元素
            Map<Long, PolicySpecDetailModel> mergedMap = Stream.concat(detailModels.stream(), result.getData().stream())
                    .collect(Collectors.toMap(
                            PolicySpecDetailModel::getSpecId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            for (PolicySpecDetailModel mergedDetail : policySpecDetailModels) {
                PolicySpecDetailResponse specDetailResponse = new PolicySpecDetailResponse();
                BeanUtils.copyProperties(mergedDetail, specDetailResponse);
                PolicySpecDetailModel detailModel = mergedMap.get(mergedDetail.getSpecId());
                if (ObjectUtil.isNotEmpty(detailModel)) {
                    specDetailResponse.setId(detailModel.getSpecId());
                    specDetailResponse.setRevenueNode(detailModel.getRevenueNode());
                    specDetailResponse.setAchievementRatio(detailModel.getAchievementRatio());
                    specDetailResponse.setCommissionRatio(detailModel.getCommissionRatio());
                    specDetailResponse.setPolicySpecDetailId(detailModel.getPolicySpecDetailId());
                    specDetailResponse.setPolicyCostOpen(detailModel.getPolicyCostOpen());
                    specDetailResponse.setPolicyCostRenew(detailModel.getPolicyCostRenew());
                    specDetailResponse.setPolicyCostAdd(detailModel.getPolicyCostAdd());
                    specDetailResponse.setPolicyCostUpgrade(detailModel.getPolicyCostUpgrade());
                }
                policySpecDetailResponses.add(specDetailResponse);
            }
        }
        response.setPolicySpecDetailResponses(policySpecDetailResponses);
        // 查询商品流失规则配置
        QueryProductRuleConfigResponse ruleConfigResponse = new QueryProductRuleConfigResponse();
        AchievementProductRuleConfigModel productRuleConfigModel = productRuleConfigRepository.getOne(
                new LambdaQueryWrapper<AchievementProductRuleConfigModel>()
                        .eq(AchievementProductRuleConfigModel::getProductId, request.getProductId())
        );
        if (ObjectUtil.isNotEmpty(productRuleConfigModel)) {
            AchievementProductRuleModel achievementProductRuleModel = productRuleRepository.getOne(
                    new LambdaQueryWrapper<AchievementProductRuleModel>()
                            .eq(AchievementProductRuleModel::getRuleCode, productRuleConfigModel.getRuleCode())
            );
            if (ObjectUtil.isNotEmpty(achievementProductRuleModel)) {
                BeanUtil.copyProperties(achievementProductRuleModel, ruleConfigResponse);
            }
        }
        response.setRuleConfig(ruleConfigResponse);
        return WebResult.success(response);
    }

    @Override
    public WebResult<List<PolicySpecDetailResponse>> querySpecByProductId(QuerySpecByproductIdRequest request) {
        //查询商品的规格
        Map<String, String> param = new HashMap<>();
        param.put("productId", request.getProductId());
        HttpResult<String> stringHttpResult = OKHttpUtil.getSync(thirdHttpUrlConfiguration.getItemManagement().getSpecDetail(), param);
        if (!HttpResultEnum.REQUEST_SUCCESS.getCode().equals(stringHttpResult.getCode())) {
            log.error("获取商品规格详情失败，请求参数:{},返回结果:{}", param, stringHttpResult);
            return WebResult.error(WebCodeMessageEnum.RPC_SERVER_EXCEPTION);
        }

        WebResult<List<PolicySpecDetailModel>> result = JSON.parseObject(stringHttpResult.getData(), new TypeReference<WebResult<List<PolicySpecDetailModel>>>() {
        });
        if (ObjectUtil.isEmpty(result.getData())) {
            return WebResult.success(null);
        }

        List<PolicySpecDetailResponse> responses = result.getData().stream().map(e -> {
            PolicySpecDetailResponse response = new PolicySpecDetailResponse();
            BeanUtil.copyProperties(e, response);
            return response;
        }).collect(Collectors.toList());

        return WebResult.success(responses);
    }

    @Override
    public List<String> checkPolicy(CheckPolicyRequest request) {
        List<PolicySpecDetailModel> models = policySpecDetailRepository.list(
                new LambdaQueryWrapper<PolicySpecDetailModel>()
                        .in(PolicySpecDetailModel::getSpecId, request.getSpecIds()));

        List<String> existSpecIdList = models.stream().map(e -> e.getSpecId().toString()).collect(Collectors.toList());

        return Stream.concat(
                existSpecIdList.stream().filter(element -> !request.getSpecIds().contains(element)),
                request.getSpecIds().stream().filter(element -> !existSpecIdList.contains(element))
        ).collect(Collectors.toList());
    }

    @Override
    public PolicySpecDetailModel getPolicyDetailBySpecId(Long specId) {
        return policySpecDetailRepository.getOne(new LambdaQueryWrapper<PolicySpecDetailModel>().eq(PolicySpecDetailModel::getSpecId, specId));
    }

    @Override
    public List<PolicySpecDetailModel> getPolicyDetailList(List<Long> specIds) {
        return policySpecDetailRepository.list(new LambdaQueryWrapper<PolicySpecDetailModel>().in(PolicySpecDetailModel::getSpecId, specIds));
    }

    @Override
    public List<PolicySpecDetailModel> getPolicyDetailByOrderId(Long productId) {
        return policySpecDetailRepository.list(new LambdaQueryWrapper<PolicySpecDetailModel>().eq(PolicySpecDetailModel::getProductId, productId));
    }

    @Override
    public Boolean repairPolicyHistoryDate() {
        //检查新数据落入是否信息完整（新加了几个字段）

        //1.将商品上的业绩配置同步到规格上（计费节点等信息）
        List<AchievementPolicyModel> detailModels = achievementPolicyRepository.list(new LambdaQueryWrapper<>());
        Map<Long, List<AchievementPolicyModel>> policySpecDetailModelMap = detailModels.stream().collect(Collectors.groupingBy(AchievementPolicyModel::getProductId));

        for (AchievementPolicyModel detailModel : detailModels) {
            if (ObjectUtil.isEmpty(detailModel.getAchievementRatio())) {
                continue;
            }
            policySpecDetailRepository.update(
                    null,
                    new LambdaUpdateWrapper<PolicySpecDetailModel>()
                            .eq(PolicySpecDetailModel::getProductId, detailModel.getProductId())
                            .set(PolicySpecDetailModel::getRevenueNode, detailModel.getRevenueNode())
                            .set(PolicySpecDetailModel::getAchievementRatio, detailModel.getAchievementRatio())
                            .set(PolicySpecDetailModel::getCommissionRatio, detailModel.getCommissionRatio()));
        }
        log.info("1.修复业绩配置历史数据完成");
        return true;
    }
}
