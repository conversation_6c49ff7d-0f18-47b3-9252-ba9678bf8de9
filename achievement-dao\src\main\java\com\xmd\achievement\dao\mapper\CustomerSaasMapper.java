package com.xmd.achievement.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xmd.achievement.dao.dto.SearchCustomerSaasDto;
import com.xmd.achievement.dao.entity.CustomerSaasModel;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CustomerSaasMapper extends BaseMapper<CustomerSaasModel> {
      /**
     * 获取指定客户的最大流失时间churnDate
     * @param customerId 客户ID
     * @return 最大流失时间
     */
    Date selectMaxChurnDateByCustomerId(@Param("customerId") String customerId);

    /**
     * 获取指定客户在指定日期之前的最大流失时间churnDate
     * @param customerId 客户ID
     * @param date 截止日期（order_create_time < date）
     * @return 最大流失时间
     */
    Date selectMaxChurnDateByCustomerIdWithDate(@Param("customerId") String customerId, @Param("date") Date date,@Param("orderId")Long orderId);

    /**
     * 查询两个时间节点之间是否存在订单，起始时间可以为null，支持orderSource
     * @param customerId 客户ID
     * @param start 开始时间（可为null）
     * @param end 结束时间（可为null）
     * @param orderSource 订单来源
     * @return 订单数量
     */
    int countOrderBetween(@Param("customerId") String customerId, @Param("start") Date start, @Param("end") Date end, @Param("orderSource") Integer orderSource);

    List<SearchCustomerSaasDto> searchCustomerSaasCount(@Param("businessIds")List<String> businessIds,@Param("startDate") String startDate,@Param("endDate") String endDate);

}
