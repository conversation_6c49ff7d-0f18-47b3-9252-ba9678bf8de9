package com.xmd.achievement.dao.entity;

    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableField;
    import com.baomidou.mybatisplus.annotation.TableId;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.extension.activerecord.Model;
    import java.util.Date;
import lombok.Data;
    import lombok.EqualsAndHashCode;

    import java.io.Serializable;

/**
 * <p>
 * 销售任务审核表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sale_task_audit")
public class SaleTaskAuditModel extends BaseModel implements
        Serializable {
    private static final long serialVersionUID = 1L;

    public static final Integer AUDIT_PASS = 1;
    public static final Integer AUDIT_FAIL = 0;

    /**
    * 自增主键
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 任务id
    */
    @TableField("task_id")
    private Long taskId;
    /**
    * 审核结果：1通过；0未通过
    */
    @TableField("audit_result")
    private Integer auditResult;
    /**
    * 审核备注
    */
    @TableField("audit_remark")
    private String auditRemark;
}