package com.xmd.achievement.support.cat;

import java.lang.annotation.*;


/**
 * cat监控
 *
 * <AUTHOR>
 * @date: 2024/3/14 2:05 下午
 * @version: 1.0.0
 * @return:
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CatMonitor {

    /**
     * transaction.type 默认为类名
     *
     * @return
     */
    public String type() default "";

    /**
     * transaction.name 默认为方法名
     *
     * @return
     */
    public String name() default "";

}
