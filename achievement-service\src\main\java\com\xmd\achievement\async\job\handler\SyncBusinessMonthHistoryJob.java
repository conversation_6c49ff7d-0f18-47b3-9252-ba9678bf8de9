package com.xmd.achievement.async.job.handler;

import com.alibaba.fastjson.JSON;
import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.async.job.entity.JobParam;
import com.xmd.achievement.service.SyncBusinessMonthHistoryJobService;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 业绩商务月历史数据同步
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/5/31 9:40 上午
 */
@Slf4j
@Component
public class SyncBusinessMonthHistoryJob {

    @Resource
    private SyncBusinessMonthHistoryJobService syncBusinessMonthHistoryJobService;


    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SYNC_BUSINESS_MONTH_HISTORY_JOB)
    public ReturnT<String> syncBusinessMonthHistory(String param) {
        try {
            syncBusinessMonthHistoryJobService.syncBusinessMonthHistory();
        } catch (Exception e) {
            XxlJobLogger.log("SyncBusinessMonthHistoryJob error :",e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SYNC_ADVERTISING_PASS_HISTORY_JOB)
    @Lock("'sync'+#advertising+#pass")
    public ReturnT<String> syncAdvertisingPassHistory(String param) {
        XxlJobLogger.log("syncAdvertisingPassHistory param :{}",param);
        try {
            JobParam jobParam = JSON.parseObject(param, JobParam.class);
            syncBusinessMonthHistoryJobService.syncAdvertisingPassHistory(jobParam);
        } catch (Exception e) {
            XxlJobLogger.log("syncAdvertisingPassHistory error :",e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

}
