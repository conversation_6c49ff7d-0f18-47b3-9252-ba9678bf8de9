package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 岗位枚举
 */
@Getter
public enum PositionNameEnum {

    MANAGER("商务经理", "经理级"),

    DIRECTOR("分公司总监", "总监级"),

    CUSTOMER_REPRESENTATIVE("客户代表", "员工级"),

    BUSINESS_SUPERVISOR("商务主管", "员工级"),
    ;

    private final String name;
    private final String level;

    PositionNameEnum(String name, String level) {
        this.name = name;
        this.level = level;
    }
}
