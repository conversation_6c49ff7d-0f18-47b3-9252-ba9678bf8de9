package com.xmd.achievement.support.cat;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.constant.UtilConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;

import javax.validation.ConstraintViolationException;
import java.lang.reflect.Method;

/**
 * cat监控代理
 *
 * <AUTHOR>
 * @date: 2024/3/14 2:05 下午
 * @version: 1.0.0
 * @return:
 */
@Slf4j
@Aspect
@Component
@Order(Integer.MIN_VALUE)
public class CatMonitorProxy {

    @Value("${spring.profiles.active}")
    private String active;

    @Pointcut("@annotation(com.xmd.achievement.support.cat.CatMonitor)")
    public void catClientTransaction() {
    }

    @Around("catClientTransaction()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        if ("dev".equals(active)) {
            //本地环境关闭cat
            System.setProperty("CAT_ENABLED", "false");
            return joinPoint.proceed();
        }
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method targetMethod = methodSignature.getMethod();
        CatMonitor catClientTransaction = targetMethod.getAnnotation(CatMonitor.class);
        String type = catClientTransaction.type();
        String name = catClientTransaction.name();
        Class<?> declaringClass = targetMethod.getDeclaringClass();
        if (type == null || type.length() == 0) {
            type = "SERVICE";
        }
        if (name == null || name.length() == 0) {
            name = declaringClass.getSimpleName() + UtilConstant.CAT_SEPARATOR + targetMethod.getName();
        }
        Transaction t = Cat.newTransaction(type, name);
        try {
            t.setStatus(Transaction.SUCCESS);
            return joinPoint.proceed();
        } catch (Throwable e) {
            if (e instanceof BusinessException || e instanceof MethodArgumentNotValidException || e instanceof BindException || e instanceof ConstraintViolationException) {
                t.setStatus(Transaction.SUCCESS);
                throw e;
            }
            log.error(declaringClass.getSimpleName() + UtilConstant.CAT_SEPARATOR + targetMethod.getName() + " exception" + ExceptionUtils.getStackTrace(e));
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }
    }

}
