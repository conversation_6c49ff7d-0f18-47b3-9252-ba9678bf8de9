package com.xmd.achievement.support.constant.base;

/**
 * 业务类返回码枚举类 提示语改成英文能理解的
 *
 * @version 1.0.0
 * @date 2021/05/28 下午3:39
 */
public enum BusinessCodeEnum {
    /**
     * 成功返回码
     */
    REQUEST_SUCCESS("200", "success"),
    /**
     * 服务间的错误码-服务未授权
     */
    SERVER_AUTHORIZATION("401", "服务未授权"),
    /**
     * 请求参数异常
     */
    REQUEST_PARAM_EXCEPTION("100001", "Invalid character found in the request target"),
    REQUEST_PARAM_NOT_NULL("100002", "请求参数不能为空"),
    /**
     * 数据不存在
     */
    NO_DATA("100003", "no data"),
    LICENSE_ERROR("100003", "许可异常"),
    LICENSE_NO_DATA("100003", "许可不存在"),
    CHAT_LICENSE_NOT_ENOUGH("100003", "会话许可数量达到上限，请及时扩充"),
    SERVER_INTERNAL_EXCEPTION("100004", "网络超时，请稍后重试哦～"),
    REQUEST_ADDRESS_EXCEPTION("100005", "请求地址不存在"),
    /**
     * 服务异常
     */
    SERVER_EXCEPTION("100006", "service error"),
    /**
     * token不存在 ，提示语改成英文能理解的
     */
    TOKEN_NOT_EXIST("100007", "authentication failed"),
    /**
     * 当前社媒授权不存在
     */
    ACCOUNT_NOT_EXIST("100007", "当前社媒授权账号不存在"),

    //登录
    NOT_LOGIN("200001", "未登录或已失效"),
    ;

    private final String code;
    private final String msg;

    BusinessCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
