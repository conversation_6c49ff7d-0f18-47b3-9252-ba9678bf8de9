package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/10:42
 * @since 1.0
 */
@Data
public class CheckPolicyRequest implements Serializable {
    @Schema(description = "规格ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotEmpty(message = "规格ID不能为空")
    private List<String> specIds;
}
