# BSP Achievement System

BSP业绩系统 - 基于Spring Boot的多模块业绩计算和统计系统

## 项目概述
这是一个企业级业绩管理系统，主要用于处理订单支付、服务交付等场景下的业绩计算、统计和分析。

## 模块结构
- `achievement-dao`: 数据访问层，使用MyBatis Plus
- `achievement-cache`: Redis缓存层，使用Redisson  
- `achievement-util`: 工具类和常量定义
- `achievement-service`: 核心业务逻辑和REST API
- `achievement-support`: 通用支持组件（日志、消息队列、Redis等）

## 技术栈
- **框架**: Spring Boot 2.3.6、Spring 5.2.11
- **语言**: Java 8
- **数据库**: MySQL 8.0.29 + Druid连接池
- **缓存**: Redis + Redisson 3.17.7
- **消息队列**: Apache RocketMQ 2.0.3
- **ORM**: MyBatis Plus 3.5.2
- **构建工具**: Maven
- **其他**: FastJSON、Hutool、EasyExcel、XXL-JOB

## 核心业务流程

### 1. 业绩计算流程
- **入口**: `AchievementHandler.processAchievement()`
- **支付完成**: `handlerPaidAchievement()` - 处理订单支付完成后的业绩计算
- **服务中**: `newHandlerServiceAchievement()` - 处理服务交付中的业绩计算
- **第三方数据**: `handelThirdDataTask()` - 处理中小企业数据

### 2. 主要计算维度
- **商品维度**: `AchievementProductDetailModel`
- **分类维度**: `AchievementCategoryDetailModel` 
- **规格维度**: `AchievementSpecDetailModel`

### 3. 业绩类型
- 首年报价、续费报价
- 首年到账金额、续费到账金额
- 净现金、商代提成业绩
- 商代实发/缓发提成业绩
- 部门/事业部/分司提成业绩

## 关键文件路径

### 核心处理类
- **业绩处理器**: `achievement-service/src/main/java/com/xmd/achievement/handler/achievement/AchievementHandler.java`
- **业绩计算器**: `achievement-service/src/main/java/com/xmd/achievement/handler/achievement/CalculateAmountHandler.java`
- **客户类型计算**: `achievement-service/src/main/java/com/xmd/achievement/handler/calculateCustomer/`

### 数据模型
- **业绩商品**: `achievement-dao/src/main/java/com/xmd/achievement/dao/entity/AchievementProductDetailModel.java`
- **业绩分类**: `achievement-dao/src/main/java/com/xmd/achievement/dao/entity/AchievementCategoryDetailModel.java`
- **业绩规格**: `achievement-dao/src/main/java/com/xmd/achievement/dao/entity/AchievementSpecDetailModel.java`
- **订单信息**: `achievement-service/src/main/java/com/xmd/achievement/rpc/entity/dto/OrderSimpleInfoResponse.java`

### 服务接口
- **业绩服务**: `achievement-service/src/main/java/com/xmd/achievement/service/IAchievementService.java`
- **内部服务**: `achievement-service/src/main/java/com/xmd/achievement/rpc/InnerService.java`

### 控制器
- **业绩管理**: `achievement-service/src/main/java/com/xmd/achievement/web/controller/`

## 构建和测试命令
```bash
# 编译
mvn clean compile

# 测试
mvn test

# 打包
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests
```

## 环境配置
- **开发环境**: `application-dev.yml`
- **测试环境**: `application-test.yml`
- **预发环境**: `application-pre.yml`
- **生产环境**: `application-release.yml`

## 常用常量
- **数值常量**: `NumberConstants` - 常用数字常量
- **业务常量**: `SupportConstant` - 业务相关常量
- **工具常量**: `UtilConstant` - 工具类常量

## 复杂任务执行流程

### 1. 任务规划原则
- **简单任务**: 直接执行（单文件查看、简单修改、技术问答）
- **复杂任务**: 先规划后执行（多步骤任务、架构修改、新功能开发）

### 2. 复杂任务识别标准
- 涉及3个以上步骤的任务
- 需要修改多个文件或模块
- 架构设计或重构需求
- 用户明确提到"实现"、"开发"、"重构"等关键词

### 3. 执行流程

#### 步骤1: 需求分析和规划
1. 分析用户需求，理解要实现的功能
2. 创建/更新 `.claude/execution_plan.md` 文件
3. 记录当前需求（使用REQ-ID格式）
4. 制定详细执行计划（按阶段组织）
5. 使用 `TodoWrite` 工具创建任务跟踪

#### 步骤2: 计划确认
- 对于编码任务：使用 `ExitPlanMode` 工具展示计划
- 对于其他任务：直接开始执行

#### 步骤3: 执行和跟踪
1. 按计划逐步执行任务
2. 实时更新 `TodoWrite` 状态
3. 同步更新 `.claude/execution_plan.md` 中的进度
4. 遇到阻塞时记录问题和解决方案

#### 步骤4: 完成和归档
- 标记所有任务为完成状态
- 更新总进度为100%
- 将完成的需求移至历史记录

### 4. 执行计划文件格式

#### 需求记录
```markdown
## 当前需求
- [REQ-001] 需求描述1
- [REQ-002] 需求描述2
```

#### 计划格式
```markdown
### 阶段名称 [关联需求ID]
- [ ] 待执行任务
- [x] 已完成任务 ✅ 完成日期
- [!] 阻塞任务 ❌ 阻塞原因
- [~] 进行中任务 🔄
```

#### 状态标识说明
- `[ ]` 待执行
- `[x]` 已完成 ✅
- `[!]` 阻塞 ❌  
- `[~]` 进行中 🔄

#### 优先级标识
- 🔥 高优先级
- ⚡ 中优先级
- 📝 低优先级

### 5. 中断恢复机制
1. 读取 `.claude/execution_plan.md` 了解项目状态
2. 检查当前需求和执行进度
3. 从最后一个未完成任务继续执行
4. 更新任务状态和时间戳

### 6. 工具使用策略
- **TodoWrite**: 用于会话内的实时任务跟踪
- **execution_plan.md**: 用于持久化的项目状态管理
- **ExitPlanMode**: 用于编码任务的计划确认
- **常规工具**: Read、Edit、Bash等按需使用