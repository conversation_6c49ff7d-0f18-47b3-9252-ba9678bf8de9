# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

### Build & Run
```bash
# Full build with tests
mvn clean install

# Quick build (skip tests)
mvn clean install -DskipTests

# Run service locally
mvn spring-boot:run -pl achievement-service -Dspring.profiles.active=dev

# Run with specific config
java -jar achievement-service/target/achievement-service.jar --spring.profiles.active=dev
```

### Testing
```bash
# Run all tests
mvn test

# Run tests for specific module
mvn test -pl achievement-service

# Run single test class
mvn test -pl achievement-service -Dtest=ServiceTest

# Run specific test method
mvn test -pl achievement-service -Dtest=ServiceTest#testSpecificMethod
```

### Development Tools
```bash
# Generate API docs (Knife4j)
# Access at: http://localhost:8080/doc.html after starting the service

# Check code style
mvn checkstyle:check

# Package for deployment
mvn clean package -DskipTests -Prelease
```

## Architecture Overview

### Multi-Module Structure
```
bsp-achievement/
├── achievement-dao/          # Data layer (MyBatis Plus entities, mappers)
├── achievement-cache/        # Redis caching (Redisson)
├── achievement-util/         # Common utilities & constants
├── achievement-service/      # Main application (REST APIs, business logic)
└── achievement-support/      # Infrastructure (MQ, Redis, logging)
```

### Core Processing Flow
1. **Achievement Calculation Pipeline**:
   - `AchievementHandler.processAchievement()` → Entry point
   - `AchievementHandler.handlerPaidAchievement()` → Payment completion
   - `AchievementHandler.newHandlerServiceAchievement()` → Service delivery
   - `AchievementHandler.handelThirdDataTask()` → Third-party data processing

2. **Calculation Dimensions**:
   - Product: `AchievementProductDetailModel`
   - Category: `AchievementCategoryDetailModel`
   - Specification: `AchievementSpecDetailModel`

3. **Business Context**:
   - First-year vs renewal pricing
   - Net cash vs commission calculations
   - Department/division performance tracking
   - Agent commission (immediate/deferred)

### Key Service Endpoints
- **Internal APIs**: `/api/achievement/**` (Knife4j docs at `/doc.html`)
- **Third-party APIs**: `/api/third/**`
- **Management**: Various controller endpoints for configuration

## Environment Configuration

### Profiles & Config Files
```
achievement-service/src/main/resources/
├── application.yml          # Base config
├── application-dev.yml      # Development (H2, local Redis)
├── application-test.yml     # Testing environment
├── application-pre.yml      # Staging
└── application-release.yml  # Production (MySQL, Redis cluster)
```

### Key Configuration Properties
- `spring.datasource.*`: Database configuration per environment
- `spring.redis.*`: Redis configuration (cluster vs standalone)
- `rocketmq.*`: Message queue settings
- `logging.level.com.xmd.*`: Application logging levels

## Development Patterns

### Common File Locations
- **Entities**: `achievement-dao/src/main/java/com/xmd/achievement/dao/entity/`
- **Mappers**: `achievement-dao/src/main/java/com/xmd/achievement/dao/mapper/`
- **Services**: `achievement-service/src/main/java/com/xmd/achievement/service/`
- **Controllers**: `achievement-service/src/main/java/com/xmd/achievement/web/controller/`
- **Handlers**: `achievement-service/src/main/java/com/xmd/achievement/handler/`

### Adding New Achievement Types
1. Define entity in `achievement-dao`
2. Add mapper interface and XML
3. Create/update handler in `achievement-service`
4. Update constants if needed
5. Add API endpoints in controller

### Database Changes
- Entities use MyBatis Plus annotations
- Mappers extend `BaseMapper<T>`
- XML files in `achievement-dao/src/main/resources/mapper/`
- Flyway migrations not used - manual DB updates required

## Testing Strategy

### Test Structure
- **Unit Tests**: `achievement-service/src/test/java/`
- **Integration Tests**: Limited - mostly manual testing
- **Test Data**: Created via test classes, no test containers

### Running Tests Effectively
```bash
# Run all unit tests
mvn test

# Run with specific profile
mvn test -Dspring.profiles.active=test

# Debug tests
mvn -Dmaven.surefire.debug test
```

## Deployment Notes

### Build Artifacts
- Main JAR: `achievement-service/target/achievement-service.jar`
- Assembly: Created via maven-assembly-plugin for deployment

### Environment Variables
- `SPRING_PROFILES_ACTIVE`: Set to dev/test/pre/release
- Database URLs and credentials via environment-specific configs
- Redis and RocketMQ configuration via application-{profile}.yml

### Monitoring & Debugging
- **Health Check**: `/actuator/health` (if enabled)
- **API Docs**: `/doc.html` (Knife4j)
- **Logging**: Configured via logback-spring.xml per environment