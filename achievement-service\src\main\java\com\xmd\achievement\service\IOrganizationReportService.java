package com.xmd.achievement.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.OrganizationMonthlyReportModel;
import com.xmd.achievement.rpc.entity.dto.OrgFunctionResp;
import com.xmd.achievement.rpc.entity.dto.OrgInfoNodeResponse;
import com.xmd.achievement.service.entity.request.DailyReportQueryReq;
import com.xmd.achievement.service.entity.request.MonthlyReportQueryReq;
import com.xmd.achievement.service.entity.response.DailyReportResp;
import com.xmd.achievement.service.entity.response.MonthlyReportImportResultResponse;
import com.xmd.achievement.service.entity.response.MonthlyReportResp;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IOrganizationReportService {

    Page<DailyReportResp> queryDailyReport(DailyReportQueryReq req);

    Page<MonthlyReportResp> queryMonthlyReport(MonthlyReportQueryReq req);

    void exportDailyReport(DailyReportQueryReq req, HttpServletResponse response);

    void exportMonthlyReport(MonthlyReportQueryReq req, HttpServletResponse response);

    List<OrgInfoNodeResponse> getOrganizationRoleList();

    List<OrgFunctionResp> getOrgFunctionList();

    /**
     * 批量查询机构和月份冻结状态
     * @param orgIds 机构ID列表
     * @param months 月份字符串列表
     * @return 每组机构-月份是否冻结的布尔列表
     */
    List<OrganizationMonthlyReportModel> searchMonthReport(List<Long> orgIds, List<String> months);

    /**
     * 导入月报
     */
    MonthlyReportImportResultResponse excelImport(MultipartFile file);
}
