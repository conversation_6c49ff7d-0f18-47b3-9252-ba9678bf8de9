package com.xmd.achievement.service.entity.request;

import com.xmd.achievement.service.entity.page.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MonthlyReportQueryReq extends PageRequest {

    @ApiModelProperty("机构类型")
    private Integer type;

    @ApiModelProperty("组织机构id")
    private List<Long> organizationIds;

    @ApiModelProperty("市场类别id")
    private Long marketCategoryId;

    @ApiModelProperty("体系id")
    private Long systemId;

    @ApiModelProperty("商务月开始时间")
    private String businessMonthStart;

    @ApiModelProperty("商务月结束时间")
    private String businessMonthEnd;
}
