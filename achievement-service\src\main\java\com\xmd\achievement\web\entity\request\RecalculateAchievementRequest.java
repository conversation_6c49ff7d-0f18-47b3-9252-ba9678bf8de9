package com.xmd.achievement.web.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * 重算订单业绩接口入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/21
 * @since 1.0
 */
@Data
@Schema(description = "重算订单业绩接口参数")
public class RecalculateAchievementRequest {

    /**
     * 订单号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "订单编号")
    private String[] orderNos;
}
