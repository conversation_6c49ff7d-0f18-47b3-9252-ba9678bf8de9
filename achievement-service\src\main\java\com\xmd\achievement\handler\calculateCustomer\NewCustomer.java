package com.xmd.achievement.handler.calculateCustomer;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.util.enums.CustomerTypeEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *○新客户</br>
 * ■从第1笔订单付款时间计算，90天内所有业务类型为新开的订单累计实付金额≥3000元，则在满足当下实付金额≥3000的这笔订单中，该客户为新客户</br>
 * ■与公司所有服务结束时间超过180天，再次与公司发生业务往来的企业、机构或个人，且满足从下一笔订单付款时间计算，90天内所有业务类型为新开的订单累计实付金额≥3000元的这笔订单中，该客户为新客户，</br>
 * 此后从该订单起，后续订单付款时间至该客户与公司所有服务结束时间后180天内，所有订单客户类型为老客户</br>
 * <AUTHOR>
 * @date: 2024/12/18 13:38
 */
@Service
public class NewCustomer extends CustomerCalculateTemplate implements ICustomerCalculate {
    @Override
    public boolean calculateCustomerType(List<AchievementProductDetailModel> aches) {
        if (CollectionUtils.isEmpty(aches)) {
            return false;
        }
        return (calculateTotalPaidAmountLast90Days4New(aches).compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) >= 0 && in90DaysNotHaveNewCustomer(aches))
                || (calculateTotalPaidAmountLast180Days4New(aches).compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) >= 0 && in180DaysNotHaveNewCustomer(aches));
    }

    private boolean in180DaysNotHaveNewCustomer(List<AchievementProductDetailModel> aches) {
        Date firstPaymentTime = getMinTime(aches);
        Date serveFinishTimeAfter180Days = calculateDate(firstPaymentTime, NumberConstants.INTEGER_VALUE_180);
        return aches.stream()
                .noneMatch(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType())
                        && (ach.getPaymentTime().after(serveFinishTimeAfter180Days) || ach.getPaymentTime().equals(serveFinishTimeAfter180Days))
                        && ach.getPaymentTime().before(calculateDate(serveFinishTimeAfter180Days, NumberConstants.INTEGER_VALUE_90))
                        && CustomerTypeEnum.NEW.getCustomerType().equals(ach.getCustomerType()));
    }

    public boolean in90DaysNotHaveNewCustomer(List<AchievementProductDetailModel> aches) {
        Date firstPaymentTime = getMinTime(aches);
        return aches.stream()
                .noneMatch(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType())
                        && (ach.getPaymentTime().after(firstPaymentTime) || ach.getPaymentTime().equals(firstPaymentTime))
                        && ach.getPaymentTime().before(calculateDate(firstPaymentTime, NumberConstants.INTEGER_VALUE_90))
                        && CustomerTypeEnum.NEW.getCustomerType().equals(ach.getCustomerType()));
    }


    BigDecimal calculateTotalPaidAmountLast90Days4New(List<AchievementProductDetailModel> aches) {
        Date minTime = getMinTime(aches);
        Date inner90DaysTime = calculateDate(minTime, NumberConstants.INTEGER_VALUE_90);

        return aches.stream()
                .filter(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType())
                        && (ach.getPaymentTime().after(minTime) || ach.getPaymentTime().equals(minTime))
                        && ach.getPaymentTime().before(inner90DaysTime))
                .map(AchievementProductDetailModel::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    BigDecimal calculateTotalPaidAmountLast180Days4New(List<AchievementProductDetailModel> aches) {
        Date minTime = getMinTime(aches);
        Date after180DaysTime = calculateDate(minTime, NumberConstants.INTEGER_VALUE_180);
        Date inner90DaysTime = calculateDate(after180DaysTime, NumberConstants.INTEGER_VALUE_90);
        return aches.stream()
                .filter(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType())
                        && (ach.getPaymentTime().after(after180DaysTime) || ach.getPaymentTime().equals(after180DaysTime))
                        && ach.getPaymentTime().before(inner90DaysTime))
                .map(AchievementProductDetailModel::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
