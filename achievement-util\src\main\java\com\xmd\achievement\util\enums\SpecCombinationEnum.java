package com.xmd.achievement.util.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规格组合状态枚举
 *
 * <AUTHOR>
 * @date: 2024/12/18 11:04
 * @version: 1.0.0
 * @return {@link }
 */
@Getter
public enum SpecCombinationEnum {
    /**
     * 1 启用
     */
    ENABLED(1),
    /**
     * 2 禁用
     */
    DISABLED(2);

    private final Integer status;

    SpecCombinationEnum(Integer status) {
        this.status = status;
    }

    public static List<Integer> getStatusList() {
        return Arrays.stream(SpecCombinationEnum.values()).map(SpecCombinationEnum::getStatus).collect(Collectors.toList());
    }
}