package com.xmd.achievement.handler.statistics;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * ●商代提成业绩， (有效的)
 * ○该商务代表本月提成业绩之和；
 * ○只统付款时生成的业绩流水；
 * <AUTHOR>
 * @date: 2024/12/25 14:52
 * @version: 1.0.0
 * @return {@link }
 */
@Service
public class AgentCommissionAchievementHandler implements StatisticsHandler {
    @Override
    public void statistics(StatisticsFactInfo factInfo) {
        List<AchievementProductDetailModel> achList = factInfo.getAchList();
        BigDecimal agentCommissionAchievement = achList.stream()
                .map(AchievementProductDetailModel::getAgentCommissionAchievement)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        factInfo.setAgentCommissionAchievement(agentCommissionAchievement);
    }
}