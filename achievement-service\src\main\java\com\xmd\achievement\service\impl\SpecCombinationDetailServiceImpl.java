package com.xmd.achievement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.SpecCombinationDetailModel;
import com.xmd.achievement.dao.repository.ISpecCombinationDetailRepository;
import com.xmd.achievement.service.ISpecCombinationDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 组合规格详情
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class SpecCombinationDetailServiceImpl implements ISpecCombinationDetailService {

    @Resource
    private ISpecCombinationDetailRepository specCombinationDetailRepository;

    @Override
    public List<SpecCombinationDetailModel> getSpecCombinationDetailModels(Long specId) {
        return specCombinationDetailRepository.list(new LambdaQueryWrapper<SpecCombinationDetailModel>().eq(SpecCombinationDetailModel::getSpecId, specId));
    }

    @Override
    public List<SpecCombinationDetailModel> getSpecCombinationDetailModels(List<Long> combinationIds, List<Long> specIds) {
        return specCombinationDetailRepository.list(new LambdaQueryWrapper<SpecCombinationDetailModel>()
                .in(SpecCombinationDetailModel::getCombinationId, combinationIds)
                .in(SpecCombinationDetailModel::getSpecId, specIds));
    }


}
