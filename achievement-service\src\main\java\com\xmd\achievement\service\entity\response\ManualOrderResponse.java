package com.xmd.achievement.service.entity.response;

import lombok.Data;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class ManualOrderResponse {
    private String isSuccess;
    private String orderNo;
    private String orderProductCode;
    private String orderTime;
    private String message;

    public ManualOrderResponse(String orderNo, String orderProductCode,String orderTime,String isSuccess) {
        this.orderNo = orderNo;
        this.orderProductCode = orderProductCode;
        this.isSuccess = isSuccess;
        this.orderTime=orderTime;
    }
}
