<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.BusinessAchievementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.BusinessAchievementModel">
        <id column="id" property="id" />
        <result column="business_month_id" property="businessMonthId" />
        <result column="business_month" property="businessMonth" />
        <result column="employee_id" property="employeeId" />
        <result column="employee_name" property="employeeName" />
        <result column="position_name" property="positionName" />
        <result column="position" property="position" />
        <result column="position_code" property="positionCode"/>
        <result column="confirmed" property="confirmed" />
        <result column="tenure" property="tenure" />
        <result column="seniority_segment" property="senioritySegment" />
        <result column="region" property="region" />
        <result column="region_id" property="regionId" />
        <result column="company" property="company"/>
        <result column="company_id" property="companyId"/>
        <result column="division_id" property="divisionId" />
        <result column="division" property="division" />
        <result column="department" property="department" />
        <result column="dept_id" property="deptId" />
        <result column="net_cash_receipt" property="netCashReceipt" />
        <result column="site_net_cash_receipt" property="siteNetCashReceipt"/>
        <result column="position_code" property="positionCode"/>
        <result column="achievement_segment" property="achievementSegment" />
        <result column="actual_commission" property="actualCommission" />
        <result column="new_customer_count" property="newCustomerCount" />
        <result column="old_customer_count" property="oldCustomerCount" />
        <result column="non_renewal_orders" property="nonRenewalOrders" />
        <result column="new_website_count" property="newWebsiteCount" />
        <result column="website_non_renewal_customers" property="websiteNonRenewalCustomers" />
        <result column="agent_commission_achievement" property="agentCommissionAchievement" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , business_month_id, business_month, employee_id, employee_name, position_name,position_code, position, confirmed, tenure, seniority_segment, region, region_id, company, company_id, division_id, division, department, dept_id, net_cash_receipt, own_net_cash_receipt, achievement_segment, actual_commission, new_customer_count,old_customer_count, non_renewal_orders, new_website_count, website_non_renewal_customers, agent_commission_achievement,site_net_cash_receipt
    </sql>

</mapper>
