package com.xmd.achievement.service.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/17/17:26
 * @since 1.0
 */
@Data
public class AchievementProductDetailNodelRequst implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */

    private Long id;
    /**
     * 业绩流水ID
     */
    private Long achievementId;
    /**
     * 商务月ID
     */
    private Long businessMonthId;
    /**
     * 商务月
     */

    private String businessMonth;
    /**
     * 订单明细编号
     */

    private String orderProductId;
    /**
     * 服务编号
     */

    private String serveNo;
    /**
     * 商品id
     */

    private Long productId;
    /**
     * 是否网站：0=否 1=是
     */
    private Integer siteFlag;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品类型
     */
    private String productType;
    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购
     */

    private Integer saleType;
    /**
     * 业务类型 1=有效，2=已完成
     */

    private Integer status;
    /**
     * 订单id
     */

    private Long orderId;
    /**
     * 订单编号
     */

    private String orderNo;
    /**
     * 订单来源：1=商务签单，2=官网，3=驾驶舱-PC,4=驾驶舱移动端，5优化师工作台，6商务工作台，7优化师录单
     */

    private Integer orderSource;
    /**
     * 客户id
     */

    private String customerId;
    /**
     * 客户名称
     */

    private String customerName;
    /**
     * 客户类型 1=新客户，2=老客户，3=非新老
     */

    private Integer customerType;
    /**
     * 省id
     */

    private String provinceCode;
    /**
     * 省名称
     */

    private String provinceName;
    /**
     * 城市id
     */

    private String cityCode;
    /**
     * 城市名称
     */

    private String cityName;
    /**
     * 区县id
     */

    private String districtCode;
    /**
     * 区县名称
     */

    private String districtName;
    /**
     * 客户所在区
     */

    private String customerRegion;
    /**
     * 合同编号
     */

    private String contractNo;
    /**
     * 商务ID 注释
     */

    private String businessId;
    /**
     * 商务代表
     */

    private String businessRepresentative;
    /**
     * 主分单人 1=主，2=辅
     */

    private Integer mainSplitPerson;
    /**
     * 公司ID
     */

    private Long companyId;


    private Long regionId;

    /**
     * 公司
     */

    private String company;
    /**
     * 事业部ID
     */

    private Long divisionId;
    /**
     * 事业部
     */

    private String division;
    /**
     * 部门ID
     */

    private Long deptId;
    /**
     * 部门
     */

    private String department;
    /**
     * 标准价
     */

    private BigDecimal standardPrice;
    /**
     * 应付金额
     */

    private BigDecimal payableAmount;
    /**
     * 实付金额
     */

    private BigDecimal paidAmount;
    /**
     * 折扣比例
     */

    private BigDecimal discountRate;
    /**
     * 交付方式: 1-软件交付, 2-服务交付
     */

    private Integer deliveryMethod;
    /**
     * 订单类型：1=普通订单，2=折扣订单
     */

    private Integer orderType;
    /**
     * 业绩生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 签单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signedTime;
    /**
     * 付款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;
    /**
     * 首年报价
     */

    private BigDecimal firstYearQuote;
    /**
     * 首年到账金额
     */
    private BigDecimal firstYearRevenue;
    /**
     * 续费报价
     */
    private BigDecimal renewalQuote;
    /**
     * 续费到账金额
     */
    private BigDecimal renewalRevenue;
    /**
     * 净现金
     */
    private BigDecimal netCash;
    /**
     * 商代提成业绩
     */
    private BigDecimal agentCommissionAchievement;
    /**
     * 商代实发提成业绩
     */
    private BigDecimal agentActualCommission;
    /**
     * 商代缓发提成业绩
     */
    private BigDecimal agentDeferredCommission;
    /**
     * 部门提成业绩
     */
    private BigDecimal deptCommission;
    /**
     * 事业部提成业绩
     */
    private BigDecimal divCommission;
    /**
     * 分司提成业绩
     */
    private BigDecimal branchCommission;
    /**
     * 服务完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date serveFinishTime;
    /**
     * 业绩来源：1-跨境 2-中小
     */
    private Integer achievementSource;

    /**
     * 三方业绩流水ID
     */
    private String thirdAchievementId;
}
