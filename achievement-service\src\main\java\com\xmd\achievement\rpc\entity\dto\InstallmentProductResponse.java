package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 分期商品集合
 * <AUTHOR>
 * @date 2024年11月11日 下午1:29
 */
@Data

public class InstallmentProductResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;
    /**
     * 分期商品id
     */

    private Long installmentProductId;

    /**
     * 分期明细id
     */

    private Long installmentDetailId;

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 订单商品明细id
     */

    private String orderProductId;

    /**
     * 订单商品明细号
     */

    private String orderProductCode;


    /**
     * 商品id
     */

    private Long productId;

    /**
     * 商品名称
     */

    private String productName;

    /**
     * 分期期数
     */

    private Integer installmentNum;

    /**
     * 本期应付金额
     */

    private BigDecimal installmentPayableAmount;

    /**
     * 分期金额
     */

    private BigDecimal installmentAmount;

}
