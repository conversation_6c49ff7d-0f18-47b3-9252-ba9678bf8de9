package com.xmd.achievement.async.job.handler;

import com.xmd.achievement.async.constant.ServiceConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 会话统计
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/5/31 9:40 上午
 */
@Slf4j
@Component
public class ImChatStatisticsHandler {

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.TEST)
    public ReturnT<String> jobHandler(String param) {

        return ReturnT.SUCCESS;
    }

}
