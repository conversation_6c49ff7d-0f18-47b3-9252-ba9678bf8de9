package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 司龄表（入职公司年限）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("seniority")
public class SeniorityModel  extends BaseModel  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 司龄id
     */
    @TableField("seniority_id")
    private Long seniorityId;
    /**
     * 司龄名称
     */
    @TableField("seniority_name")
    private String seniorityName;
    /**
     * 最小值（年限）
     */
    @TableField("min_years")
    private Integer minYears;
    /**
     * 最大值（年限）
     */
    @TableField("max_years")
    private Integer maxYears;

}