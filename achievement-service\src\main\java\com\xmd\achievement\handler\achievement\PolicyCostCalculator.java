package com.xmd.achievement.handler.achievement;

import cn.hutool.core.collection.CollUtil;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.PolicySpecDetailModel;
import com.xmd.achievement.dao.entity.SpecCombinationDetailModel;
import com.xmd.achievement.dao.entity.SpecCombinationDetailModel;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductSpecItemResponse;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductSpecItemResponse;
import com.xmd.achievement.service.IPolicyService;
import com.xmd.achievement.service.ISpecCombinationDetailService;
import com.xmd.achievement.service.ISpecCombinationService;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import com.xmd.achievement.web.config.SpecialProductConfig;
import lombok.Getter;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 计算商代提成比例计算
 * <AUTHOR>
 */
@Service
public class PolicyCostCalculator {
    @Resource
    private ISpecCombinationService specCombinationService;
    @Resource
    private IPolicyService policyService;
    @Autowired
    private SpecialProductConfig specialProductConfig;
    @Autowired
    private InnerService innerService;
    @Autowired
    private ISpecCombinationDetailService specCombinationDetailService;


    public static void main(String[] args) {
        // 示例输入数据
        List<Long> specIds = Arrays.asList(1L, 2L, 3L); // 当前订单的规格 ID 集合

        // 组合规格 ID 映射
        Map<Long, List<Long>> combinationSpecIds = new HashMap<>();
        combinationSpecIds.put(101L, Arrays.asList(1L, 2L)); // A 和 B 的组合
        combinationSpecIds.put(102L, Arrays.asList(2L, 3L)); // B 和 C 的组合
        combinationSpecIds.put(103L, Collections.singletonList(1L)); // A、B,C,D 组合

        // 单规格政策性成本
        Map<Long, BigDecimal> specPolicyCosts = new HashMap<>();
        specPolicyCosts.put(1L, new BigDecimal("0.6")); // A 的政策性成本
        specPolicyCosts.put(2L, new BigDecimal("0.7")); // B 的政策性成本
        specPolicyCosts.put(3L, new BigDecimal("0.8")); // C 的政策性成本
        specPolicyCosts.put(4L, new BigDecimal("0.9")); // D 的政策性成本
        specPolicyCosts.put(5L, new BigDecimal("0.95")); // E 的政策性成本

        // 组合规格政策性成本
        Map<Long, BigDecimal> combinationPolicyCosts = new HashMap<>();
        combinationPolicyCosts.put(101L, new BigDecimal("0.95")); // A 和 B 的组合
        combinationPolicyCosts.put(102L, new BigDecimal("0.8")); // B 和 C 的组合
        combinationPolicyCosts.put(103L, new BigDecimal("0.7")); // A、B,C 的组合

        // 调用计算方法
       // Map<Long, BigDecimal> result = calculatePolicyCosts(specIds, combinationSpecIds, specPolicyCosts, combinationPolicyCosts);

        // 输出结果
        //result.forEach((specId, cost) -> System.out.println("规格 ID: " + specId + ", 政策性成本: " + cost));
    }

    public void calculatePolicyCosts(CalculateFactInfo factInfo) {
        if (factInfo.getCalculatePolicyCosts() == null) {
            // 根据orderId 获取所有规格集合
            List<Long> specIds = factInfo.getSpecList().stream().map(AchievementSpecDetailModel::getSpecId).collect(Collectors.toList());
            Map<Long, List<Long>> combinationSpecIds = specCombinationService.getAllCombinationSpecIds();
            OrderSaleTypeEnum orderSaleTypeEnum = OrderSaleTypeEnum.fromValue(factInfo.getOrderSaleType());
            Map<Long, BigDecimal> specPolicyCosts = policyService.getPolicyDetailList(factInfo.getSpecList()
                            .stream()
                            .map(AchievementSpecDetailModel::getSpecId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(PolicySpecDetailModel::getSpecId, v -> {
                        BigDecimal policyCost;
                        switch (orderSaleTypeEnum) {
                            case OPEN:
                                policyCost = v.getPolicyCostOpen();
                                break;
                            case RENEW:
                                policyCost = v.getPolicyCostRenew();
                                break;
                            case UPGRADE:
                                policyCost = v.getPolicyCostUpgrade();
                                break;
                            case ANOTHER_BUY:
                            default:
                                policyCost = v.getPolicyCostAdd();
                                break;
                        }
                        return policyCost;
                    }));
            Map<Long, List<SpecCombinationDetailModel>> combinationPolicyCosts = specCombinationService.getAllSpecCombinationPrice(factInfo.getOrderSaleType());
            Map<Long, BigDecimal> costs = calculatePolicyCosts(specIds, combinationSpecIds, specPolicyCosts, combinationPolicyCosts,orderSaleTypeEnum);
            factInfo.setCalculatePolicyCosts(costs);
        }
    }

    /**
     *  计算政策性成本
     * @param specIds 规格ID列表
     * @param combinationSpecIds 组合规格ID列表
     * @param specPolicyCosts 规格政策性成本
     * @param combinationPolicyCosts 组合规格政策性成本
     * <AUTHOR>
     * @date: 2024/12/23 14:54
     * @version: 1.0.0
     */
    public static Map<Long, BigDecimal> calculatePolicyCosts(
            List<Long> specIds,
            Map<Long, List<Long>> combinationSpecIds,
            Map<Long, BigDecimal> specPolicyCosts,
            Map<Long, List<SpecCombinationDetailModel>> combinationPolicyCosts,
            OrderSaleTypeEnum orderSaleTypeEnum
    ) {
        Map<Long, BigDecimal> result = new HashMap<>();
        Set<Long> processedSpecs = new HashSet<>();

        // 找到所有相关的组合规格及其政策性成本
        List<Combination> relevantCombinations = findRelevantCombinations(specIds, combinationSpecIds, combinationPolicyCosts,orderSaleTypeEnum);

        List<BigDecimal> specIdCostsList = new ArrayList<>();
        // 遍历所有规格ID，获取相关组合规格的政策性成本
        for (Long specId : specIds) {
            for (Combination relevantCombination : relevantCombinations) {
                BigDecimal specIdCosts = relevantCombination.getSpecPolicyCosts().get(specId);
                if(null != specIdCosts){
                    specIdCostsList.add(specIdCosts);
                }
            }
            if(CollectionUtils.isEmpty(specIdCostsList) && !processedSpecs.contains(specId)){
                result.put(specId, specPolicyCosts.get(specId));
            }
            if(!CollectionUtils.isEmpty(specIdCostsList) && !processedSpecs.contains(specId)){
                specIdCostsList.sort(Comparator.reverseOrder());
                result.put(specId, specIdCostsList.get(0));
            }
            processedSpecs.add(specId);
            specIdCostsList.clear();

        }



//        // 按政策性成本优先级排序（可以调整排序逻辑）
//        relevantCombinations.sort(Comparator.comparing(Combination::getCost));
//        // 使用 Comparator 按照 specs 集合的大小进行降序排序
//        relevantCombinations.sort((c1, c2) -> Integer.compare(c2.getSpecs().size(), c1.getSpecs().size()));
//
//        // 动态分配政策性成本
//        for (Combination combination : relevantCombinations) {
//            for (Long spec : combination.getSpecs()) {
//                if (!processedSpecs.contains(spec)) {
//                    result.put(spec, combination.getCost());
//                    processedSpecs.add(spec);
//                }
//            }
//        }
//
//        // 未处理的规格使用单规格政策性成本
//        for (Long specId : specIds) {
//            if (!processedSpecs.contains(specId)) {
//                result.put(specId, specPolicyCosts.get(specId));
//            }
//        }

        return result;
    }

    /**
     *  计算特殊商品政策性成本
     *  1.首年提成业绩=实付金额/购买年限 *业绩核算比例*（1-新开政策成本）
     * 2.续费提成业绩（此后每年）=实付金额/购买年限 *业绩核算比例*（1-续费政策成本）
     * 总续费提成业绩 实付金额/购买年限 *业绩核算比例*（1-续费政策成本） * （购买年限 - 1）
     * 总提成业绩 = 实付金额 * 业绩核算比例 * （1-新开政策成本 + （1-续费政策成本） *（购买年限 - 1） ）/购买年限
     * 实付金额 * 业绩核算比例  = 净现金
     * 商代提成业绩 = 净现金 （1 - 政策性成本）
     * 计算政策性成本 x = x = (C - (1 - A + (1 - B) * (C - 1))) / C
     * <AUTHOR>
     * @date: 2025/03/06 14:54
     * @version: 1.0.0
     */
    public void calculateSpecialProductPolicyCosts(CalculateFactInfo factInfo) {
        if (null != factInfo.getSpecialRecord() && factInfo.getSpecialRecord()) {
            return;
        }
        List<Long> specialSpecIds = factInfo.getSpecialSpecIds();
        if (CollUtil.isEmpty(specialSpecIds)) {
            factInfo.setSpecialRecord(true);
            return;
        }
        Map<Long, BigDecimal> result = new HashMap<>();
        // 查询组合规格 用来判断规格是否在组合规格里面
        Map<Long, List<Long>> combinationSpecIds = specCombinationService.getAllCombinationSpecIds();
        // 通过订单ID查询规格的购买年限 用来计算政策性成本
        OrderSimpleInfoResponse orderSimpleInfo = innerService.getOrderSimpleInfo(factInfo.getProduct().getOrderId());
        List<OrderSimpleProductSpecItemResponse> specItemResponseList = orderSimpleInfo.getSpecItemResponseList().stream().filter(v -> specialSpecIds.contains(v.getProductSpecId())).collect(Collectors.toList());;
        // 这个规格id集合用来判断是否满足组合规格
        List<Long> specIds = factInfo.getSpecList().stream().map(AchievementSpecDetailModel::getSpecId).collect(Collectors.toList());
        Set<Long> setSpecIds = new HashSet<>(specIds);
        // specItemResponseList中是这次订单购买的所有规格的集合
        for (OrderSimpleProductSpecItemResponse item : specItemResponseList) {
            // payableAmount == 0 表示是赠品，不参与计算
            if (BigDecimal.ZERO.compareTo(item.getPayableAmount()) == NumberConstants.INTEGER_VALUE_0) {
                continue;
            }
            // 规格ID
            Long productSpecId = item.getProductSpecId();
            // 这个方法只判断特殊的商品规格
            if (!specialSpecIds.contains(productSpecId)) {
                continue;
            }
            // 用来取规格政策性成本
            PolicySpecDetailModel specDetail = policyService.getPolicyDetailBySpecId(productSpecId);
            // 用来判断特殊规格是否存在组合规格 且用来取组合规格政策性成本
            List<SpecCombinationDetailModel> specCombinationDetailModels = specCombinationDetailService.getSpecCombinationDetailModels(productSpecId);

            int buyYear = item.getItemNum() / NumberConstants.INTEGER_VALUE_12;
            // 购买年限大于一年走额外的计算逻辑
            if (NumberConstants.INTEGER_VALUE_1.equals(buyYear)) {
                result.put(productSpecId, specDetail.getPolicyCostOpen());
                // 优先判断是否存在组合规格，否则取政策规格里面配置的值
                if (!CollUtil.isEmpty(specCombinationDetailModels)) {
                    //构建组合 -> 规格列表的映射
                    Map<Long, List<SpecCombinationDetailModel>> comboMap = specCombinationDetailModels.stream()
                            .collect(Collectors.groupingBy(SpecCombinationDetailModel::getCombinationId));
                    List<Long> combinationIds = Lists.newArrayList();
                    for (Map.Entry<Long, List<Long>> entry : combinationSpecIds.entrySet()) {
                        Long combinationId = entry.getKey();
                        List<Long> specsInCombination = entry.getValue();

                        if (new HashSet<>(specsInCombination).containsAll(setSpecIds) && specsInCombination.size() == setSpecIds.size()) {
                            //组合规格 A B C, 规格 A B C
                            Optional<SpecCombinationDetailModel> targetSpec = Optional.ofNullable(comboMap.get(combinationId))
                                    .flatMap(specs -> specs.stream().filter(spec -> spec.getSpecId().equals(productSpecId)).findFirst());
                            BigDecimal newPolicyCost = targetSpec.map(SpecCombinationDetailModel::getNewPolicyCost).orElse(BigDecimal.ZERO);
                            result.put(productSpecId, newPolicyCost);
                        }
                        if (setSpecIds.containsAll(specsInCombination)) {
                            //组合规格 (A B) (A C), 规格 A B C, A B 是特殊规格
                            combinationIds.add(combinationId);
                        }
                    }
                    if (!CollUtil.isEmpty(combinationIds)) {
                        Optional<SpecCombinationDetailModel> maxNewPolicyCost = comboMap.entrySet().stream()
                                .filter(entry -> combinationIds.contains(entry.getKey()))
                                .flatMap(entry -> entry.getValue().stream())
                                .max(Comparator.comparing(SpecCombinationDetailModel::getNewPolicyCost));
                        BigDecimal newPolicyCost = maxNewPolicyCost.map(SpecCombinationDetailModel::getNewPolicyCost).orElse(BigDecimal.ZERO);
                        result.put(productSpecId, newPolicyCost);
                    }
                }
            } else {
                BigDecimal newPolicyCost = specDetail.getPolicyCostOpen();
                BigDecimal renewalPolicyCost = specDetail.getPolicyCostRenew();
                result.put(productSpecId, specialFormula(newPolicyCost, renewalPolicyCost, buyYear));
                // 优先判断是否存在组合规格，否则取政策规格里面配置的值
                if (!CollUtil.isEmpty(specCombinationDetailModels)) {
                    //构建组合 -> 规格列表的映射
                    Map<Long, List<SpecCombinationDetailModel>> comboMap = specCombinationDetailModels.stream()
                            .collect(Collectors.groupingBy(SpecCombinationDetailModel::getCombinationId));
                    List<Long> combinationIds = Lists.newArrayList();
                    for (Map.Entry<Long, List<Long>> entry : combinationSpecIds.entrySet()) {
                        Long combinationId = entry.getKey();
                        List<Long> specsInCombination = entry.getValue();

                        if (new HashSet<>(specsInCombination).containsAll(setSpecIds) && specsInCombination.size() == setSpecIds.size()) {
                            //组合规格 A B C, 规格 A B C
                            Optional<SpecCombinationDetailModel> targetSpec = Optional.ofNullable(comboMap.get(combinationId))
                                    .flatMap(specs -> specs.stream().filter(spec -> spec.getSpecId().equals(productSpecId)).findFirst());
                            BigDecimal combinationNewPolicyCost = targetSpec.map(SpecCombinationDetailModel::getNewPolicyCost).orElse(BigDecimal.ZERO);
                            BigDecimal combinationRenewalPolicyCost = targetSpec.map(SpecCombinationDetailModel::getRenewalPolicyCost).orElse(BigDecimal.ZERO);
                            result.put(productSpecId, specialFormula(combinationNewPolicyCost, combinationRenewalPolicyCost, buyYear));
                        }
                        if (setSpecIds.containsAll(specsInCombination)) {
                            //组合规格 (A B) (A C), 规格 A B C, A B 是特殊规格
                            combinationIds.add(combinationId);
                        }
                    }
                    if (!CollUtil.isEmpty(combinationIds)) {
                        Optional<SpecCombinationDetailModel> maxNewPolicyCost = comboMap.entrySet().stream()
                                .filter(entry -> combinationIds.contains(entry.getKey()))
                                .flatMap(entry -> entry.getValue().stream())
                                .max(Comparator.comparing(SpecCombinationDetailModel::getNewPolicyCost));
                        Optional<SpecCombinationDetailModel> maxRenewalPolicyCost = comboMap.entrySet().stream()
                                .filter(entry -> combinationIds.contains(entry.getKey()))
                                .flatMap(entry -> entry.getValue().stream())
                                .max(Comparator.comparing(SpecCombinationDetailModel::getRenewalPolicyCost));
                        BigDecimal combinationNewPolicyCost = maxNewPolicyCost.map(SpecCombinationDetailModel::getNewPolicyCost).orElse(BigDecimal.ZERO);
                        BigDecimal combinationRenewalPolicyCost = maxRenewalPolicyCost.map(SpecCombinationDetailModel::getRenewalPolicyCost).orElse(BigDecimal.ZERO);
                        result.put(productSpecId, specialFormula(combinationNewPolicyCost, combinationRenewalPolicyCost, buyYear));
                    }
                }
            }
        }
        Map<Long, BigDecimal> calculatePolicyCosts = factInfo.getCalculatePolicyCosts();
        // 遍历  并替换值
        for (Map.Entry<Long, BigDecimal> entry : calculatePolicyCosts.entrySet()) {
            if (result.containsKey(entry.getKey())) {
                calculatePolicyCosts.put(entry.getKey(), result.get(entry.getKey()));
            }
        }
        factInfo.setCalculatePolicyCosts(calculatePolicyCosts);
        factInfo.setSpecialRecord(true);
    }

    private BigDecimal specialFormula(BigDecimal newPolicyCost, BigDecimal renewalPolicyCost, int year) {
        if (year==0) {
            return BigDecimal.ZERO;
        }
        // 定义 C 的值
        BigDecimal C = new BigDecimal(year);
        // 计算 x = (C - (1 - A + (1 - B) * (C - 1))) / C
        BigDecimal one = BigDecimal.ONE;
        // 1 - A
        BigDecimal term1 = one.subtract(newPolicyCost);
        // (1 - B) * (C - 1)
        BigDecimal term2 = one.subtract(renewalPolicyCost).multiply(C.subtract(one));
        // C - (1 - A + (1 - B) * (C - 1))
        BigDecimal numerator = C.subtract(term1.add(term2));
        return numerator.divide(C, NumberConstants.INTEGER_VALUE_10, RoundingMode.HALF_UP);
    }


    /**
     *
     * @param specIds 订单索引规格
     * @param combinationSpecIds 组合规则，规则详情 map
     * @param combinationPolicyCosts 组合规则，规则详情数据 map
     * @param orderSaleTypeEnum 类型枚举
     * @return
     */
    private static List<Combination> findRelevantCombinations(
            List<Long> specIds,
            Map<Long, List<Long>> combinationSpecIds,
            Map<Long, List<SpecCombinationDetailModel>> combinationPolicyCosts,
            OrderSaleTypeEnum orderSaleTypeEnum
    ) {
        Set<Long> setSpecIds = new HashSet<>(specIds);
        List<Combination> combinations = new ArrayList<>();
        for (Map.Entry<Long, List<Long>> entry : combinationSpecIds.entrySet()) {
            Long combinationId = entry.getKey();
            List<Long> specsInCombination = entry.getValue();

            //找到组合下所有规则明细的规则id和政策性成本值
            Map<Long, BigDecimal> specPolicyCosts = combinationPolicyCosts.get(combinationId)
                            .stream()
                       .collect(Collectors.toMap(SpecCombinationDetailModel::getSpecId, v -> {
                        BigDecimal policyCost;
                        switch (orderSaleTypeEnum) {
                            case OPEN:
                                policyCost = v.getNewPolicyCost();
                                break;
                            case RENEW:
                                policyCost = v.getRenewalPolicyCost();
                                break;
                            case UPGRADE:
                                policyCost = v.getUpgradePolicyCost();
                                break;
                            case ANOTHER_BUY:
                            default:
                                policyCost = v.getAdditionalPolicyCost();
                                break;
                        }
                        return policyCost;
                    }));


            if (specsInCombination.containsAll(setSpecIds) && specsInCombination.size() == setSpecIds.size()) {
                return Collections.singletonList(new Combination(combinationId, specsInCombination,specPolicyCosts));
            }
            if (setSpecIds.containsAll(specsInCombination)) {
                combinations.add(new Combination(combinationId, specsInCombination, specPolicyCosts));
            }
        }
        return combinations;
    }

    // 定义一个组合对象，用于存储组合信息
    @Getter
    static class Combination {
        private final Long id;
        private final List<Long> specs;
        private final Map<Long, BigDecimal> specPolicyCosts;

        public Combination(Long id, List<Long> specs, Map<Long, BigDecimal> specPolicyCosts) {
            this.id = id;
            this.specs = specs;
            this.specPolicyCosts = specPolicyCosts;
        }
    }
}
