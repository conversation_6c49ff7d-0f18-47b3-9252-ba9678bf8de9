package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class BusinessIdsDateRangeRequest {
    @Schema(description = "业务ID集合")
    @NotEmpty(message = "businessIds不能为空")
    private List<String> businessIds;

    @Schema(description = "开始日期，格式yyyy-MM-dd")
    @NotBlank(message = "startDate不能为空")
    private String startDate;

    @Schema(description = "结束日期，格式yyyy-MM-dd")
    @NotBlank(message = "endDate不能为空")
    private String endDate;
}
