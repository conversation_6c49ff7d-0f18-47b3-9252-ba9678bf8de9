package com.xmd.achievement.dao.entity;

    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableField;
    import com.baomidou.mybatisplus.annotation.TableId;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.extension.activerecord.Model;
    import java.math.BigDecimal;
    import java.util.Date;
import lombok.Data;
    import lombok.EqualsAndHashCode;

    import java.io.Serializable;

/**
 * <p>
 * 销售任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sale_task")
public class SaleTaskModel extends BaseModel implements
        Serializable {
    private static final long serialVersionUID = 1L;

    public static final Long ROOT_PARENT_ID = 0L;

    public static final int ORG_TYPE_HEAD = 1;// 总部
    public static final int ORG_TYPE_COMPANY = 2;// 分公司
    public static final int ORG_TYPE_DEPT = 3;// 部门或事业部
    public static final int ORG_TYPE_BUSINESS = 4;// 商务组

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Long orgId;
    /**
     * 机构名称
     */
    @TableField("org_name")
    private String orgName;
    /**
     * 机构级别类型
     */
    @TableField("org_type")
    private Integer orgType;
    /**
     * 商务月
     */
    @TableField("business_month")
    private String businessMonth;
    /**
     * 市场分类
     */
    @TableField("market_classification")
    private String marketClassification;
    /**
     * 基本任务（元）
     */
    @TableField("basic_task")
    private BigDecimal basicTask;
    /**
     * 分公司任务汇总（元）
     */
    @TableField("branch_office_total")
    private BigDecimal branchOfficeTotal;
    /**
     * 部门任务汇总（元）
     */
    @TableField("department_total")
    private BigDecimal departmentTotal;
    /**
     * 事业部任务汇总（元）
     */
    @TableField("business_unit_total")
    private BigDecimal businessUnitTotal;
    /**
     * 任务状态
     */
    @TableField("task_status")
    private Integer taskStatus;
    /**
     * 上级id
     */
    @TableField("parent_id")
    private Long parentId;
    /**
     * 任务业务ID
     */
    @TableField("task_id")
    private Long taskId;

}