# Qwen 使用说明

这是为 Qwen AI 助手准备的说明文档。

## 项目结构

本项目是一个基于 Spring Boot 的多模块 Maven 项目，包含以下模块：
- achievement-cache
- achievement-dao
- achievement-service
- achievement-support
- achievement-util

## 使用指南

当使用 Qwen 进行代码辅助时，请参考 `.qwen/prompts/` 目录下的提示模板：
- `java-codegen.md` - Java 代码生成规范
- `code-review.md` - 代码审查标准

## 注意事项

1. 本文件用于记录 Qwen 的使用方法和注意事项
2. 项目特定的配置信息请查看 `.qwen/config.json`
3. 更多详细信息请参考 `.qwen/README.md`