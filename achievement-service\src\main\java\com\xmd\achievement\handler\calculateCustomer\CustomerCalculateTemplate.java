package com.xmd.achievement.handler.calculateCustomer;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.CustomerType;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.*;

/**
 * 客户计算模板
 *
 * <AUTHOR>
 * @date: 2024/12/18 12:00
 */
public abstract class CustomerCalculateTemplate {

    /**
     * 计算客户90天内业绩总金额</br>
     * 释：</br>
     * 1.从第1笔订单付款时间计算，90天内所有业务类型为新开的订单累计实付金额</br>
     *
     * @param aches 业绩集合
     * @return {@link Double}
     * <AUTHOR>
     * @date: 2024/12/18 16:32
     * @version: 1.0.0
     */
    BigDecimal calculateTotalPaidAmountLast90Days4NewAndNoExisting(List<AchievementProductDetailModel> aches) {
        Date minTime = getMinTime(aches);
        Date inner180DaysTime = calculateDate(minTime, NumberConstants.INTEGER_VALUE_180);

        return aches.stream()
                .filter(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType())
                        && (ach.getPaymentTime().after(minTime) || ach.getPaymentTime().equals(minTime))
                        && ach.getPaymentTime().before(calculateDate(minTime, NumberConstants.INTEGER_VALUE_90))
                        && ach.getPaymentTime().before(inner180DaysTime))
                .map(AchievementProductDetailModel::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取客户第一笔支付时间
     *
     * @param aches 业绩集合
     * @return {@link java.util.Date}
     * <AUTHOR>
     * @date: 2024/12/18 16:33
     * @version: 1.0.0
     */
    @Nullable
    Date getMinPaymentTime(List<AchievementProductDetailModel> aches) {
       /* Date finishTimeAfter180Days = getFinishTimeAfter180Days(aches, null);
        if (finishTimeAfter180Days != null) {
            // 查找时间最晚的对象
            Optional<AchievementProductDetailModel> maxPaymentTimeOptional = aches.stream()
                    .filter(c -> c.getPaymentTime().after(finishTimeAfter180Days))
                    // 使用 Lambda 表达式，找出最大的对象（最早的时间）
                    .max(Comparator.comparing(AchievementProductDetailModel::getPaymentTime));
            return maxPaymentTimeOptional.map(AchievementProductDetailModel::getPaymentTime).orElse(null);
        } else {*/
        // 查找时间最早的对象
        Optional<AchievementProductDetailModel> minPaymentTimeOptional = aches.stream()
                // 使用 Lambda 表达式，找出最小的对象（最早的时间）
                .min(Comparator.comparing(AchievementProductDetailModel::getPaymentTime));
        return minPaymentTimeOptional.map(AchievementProductDetailModel::getPaymentTime).orElse(null);
//        }
    }

    Date getMaxPaymentTime(List<AchievementProductDetailModel> aches) {
        // 查找时间最早的对象
        Optional<AchievementProductDetailModel> minPaymentTimeOptional = aches.stream()
                // 使用 Lambda 表达式，找出最小的对象（最早的时间）
                .max(Comparator.comparing(AchievementProductDetailModel::getPaymentTime));
        return minPaymentTimeOptional.map(AchievementProductDetailModel::getPaymentTime).orElse(null);
        // 第一笔付款时间
    }

    /**
     * 计算日期
     *
     * @param time 给定日期 ，null 为当前日期
     * @param days 天数
     * @return {@link java.util.Date}
     * <AUTHOR>
     * @date: 2024/12/18 16:30
     * @version: 1.0.0
     */
    Date calculateDate(Date time, Integer days) {
        Calendar instance = Calendar.getInstance();
        if (null != time) {
            instance.setTime(time);
        }
        instance.add(Calendar.DAY_OF_MONTH, days);
        return instance.getTime();
    }

    /**
     * 计算客户服务结束180天后并且是90天内新开业绩</br>
     * 释：</br>
     * 1.所有服务结束时间超过180天</br>
     * 2.满足从下一笔订单付款时间计算，90天内所有业务类型为新开的订单累计实付金额
     *
     * @param aches 业绩集合
     * @return {@link java.lang.Double}
     * <AUTHOR>
     * @date: 2024/12/18 16:34
     * @version: 1.0.0
     */
    protected BigDecimal calculateTotalPaidAmountServiceAfter180Inner90Days(List<AchievementProductDetailModel> aches) {
        Date finishTimeAfter180Days = getFinishTimeAfter180Days(aches, null);
        if (finishTimeAfter180Days == null) {
            return null;
        }
        // 180天后第一笔订单支付时间
        Optional<AchievementProductDetailModel> closestPaymentTimeOptional = findClosestObjectAfter(finishTimeAfter180Days, aches);
        if (!closestPaymentTimeOptional.isPresent()) {
            return null;
        }
        Date minPaymentTime = closestPaymentTimeOptional.get().getPaymentTime();
        Date instance90Time = calculateDate(minPaymentTime, NumberConstants.INTEGER_VALUE_90);
        Date maxPaymentTime = getMaxPaymentTime(aches);
        if (instance90Time.before(maxPaymentTime)) {
            return null;
        }
        return aches.stream().filter(ach -> ach.getSaleType().equals(SaleTypeEnum.NEW_OPEN.getType())
                        && (ach.getPaymentTime().after(minPaymentTime) || ach.getPaymentTime().equals(minPaymentTime))
                        && (ach.getPaymentTime().before(instance90Time) || ach.getPaymentTime().equals(instance90Time)))
                .map(AchievementProductDetailModel::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // 计算最晚服务完成时间后180天的时间
    @Nullable
    Date getFinishTimeAfter180Days(List<AchievementProductDetailModel> aches, Date startTime) {

        Date lastServiceFinishTime;
        if (null == startTime) {
            lastServiceFinishTime = aches.stream().map(AchievementProductDetailModel::getServeFinishTime)
                    .filter(Objects::nonNull).max(Comparator.naturalOrder()).orElse(null);
            if (null == lastServiceFinishTime) {
                return null;
            }
        } else {
            lastServiceFinishTime = startTime;
        }
        // 计算最晚服务完成时间后180天的时间
        return calculateDate(lastServiceFinishTime, NumberConstants.INTEGER_VALUE_180);
    }

    /**
     * 查找最接近目标时间的对象
     *
     * @param targetTime 目标时间
     * @param aches      业绩集合
     * <AUTHOR>
     * @date: 2024/12/18 17:29
     * @version: 1.0.0
     */
    public static Optional<AchievementProductDetailModel> findClosestObjectAfter(Date targetTime, List<AchievementProductDetailModel> aches) {
        return aches.stream()
                // 过滤出时间晚于 targetTime 的对象
                .filter(obj -> obj.getPaymentTime().after(targetTime))
                // 找出离 targetTime 最近的对象
                .min(Comparator.comparingLong(o -> Math.abs(o.getPaymentTime().getTime() - targetTime.getTime())));
    }

    // 如果有服务完成时间取最大的服务完成时间 ，如果没有取最小的支付时间
    public Date getMinTime(List<AchievementProductDetailModel> aches) {
        Date minTime = aches.stream()
                .map(AchievementProductDetailModel::getServeFinishTime)
                .filter(Objects::nonNull)
                .max(Comparator.naturalOrder())
                .orElse(null);
        if (null == minTime) {
            minTime = aches.stream()
                    .filter(ach -> ach.getSaleType().equals(SaleTypeEnum.NEW_OPEN.getType()))
                    .map(AchievementProductDetailModel::getPaymentTime)
                    .min(Comparator.naturalOrder())
                    .orElse(null);
        }
        return minTime;
    }
}
