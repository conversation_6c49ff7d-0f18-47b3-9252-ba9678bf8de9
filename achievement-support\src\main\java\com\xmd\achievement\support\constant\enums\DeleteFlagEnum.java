package com.xmd.achievement.support.constant.enums;

/**
 * 删除标记枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 13:57
 **/
public enum DeleteFlagEnum {
    /**
     * 删除
     */
    DELETE(1),
    /**
     * 未删除
     */
    NOT_DELETE(0),
    ;

    private final Integer code;

    DeleteFlagEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }
}