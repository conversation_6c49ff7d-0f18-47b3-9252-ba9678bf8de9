package com.xmd.achievement.web.util;


import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.opencsv.bean.HeaderColumnNameMappingStrategy;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

public class CsvUtils {

    /**
     * 解析 CSV 文件为 Java 对象列表
     *
     * @param filePath CSV 文件路径
     * @param clazz    目标 Java 类
     * @param <T>      泛型类型
     * @return Java 对象列表
     * @throws IOException 如果文件读取失败
     */
    public static <T> List<T> parseCsvToBeans(String filePath, Class<T> clazz) throws IOException {
        try (Reader reader = Files.newBufferedReader(Paths.get(filePath))) {
            return parseCsvToBeans(reader, clazz);
        }
    }

    /**
     * 解析 MultipartFile 中的 CSV 文件为 Java 对象列表
     *
     * @param file  MultipartFile 文件
     * @param clazz 目标 Java 类
     * @param <T>   泛型类型
     * @return Java 对象列表
     * @throws IOException 如果文件读取失败
     */
    public static <T> List<T> parseCsvToBeans(MultipartFile file, Class<T> clazz) throws IOException {
        try (Reader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            return parseCsvToBeans(reader, clazz);
        }
    }

    /**
     * 解析 MultipartFile 中的 CSV 文件为 Java 对象列表
     *
     * @param file  MultipartFile 文件
     * @param clazz 目标 Java 类
     * @param <T>   泛型类型
     * @return Java 对象列表
     * @throws IOException 如果文件读取失败
     */
    public static <T> List<T> parseCsvToBeansGB18030(MultipartFile file, Class<T> clazz) throws Exception {
        // 创建 BufferedReader 并指定 UTF-8 编码，同时处理 BOM
        BufferedReader reader = createBOMAwareReader(file, Charset.forName("GB18030"));

        // 创建映射策略
        HeaderColumnNameMappingStrategy<T> strategy = new HeaderColumnNameMappingStrategy<>();
        strategy.setType(clazz);
        return parseCsvToBeans(reader, clazz);
    }

    /**
     * 创建支持 BOM 的 BufferedReader
     *
     * @param file 前端上传的 CSV 文件
     * @return 支持 BOM 的 BufferedReader
     * @throws Exception 如果读取文件失败
     */
    private static BufferedReader createBOMAwareReader(MultipartFile file, Charset code) throws Exception {
        // 使用 PushbackInputStream 检查并跳过 BOM
        PushbackInputStream pushbackInputStream = new PushbackInputStream(file.getInputStream(), 3);
        byte[] bom = new byte[3];
        int bytesRead = pushbackInputStream.read(bom, 0, bom.length);

        // 检查是否为 UTF-8 BOM (EF BB BF)
        boolean hasBOM = (bytesRead == 3 &&
                (bom[0] & 0xFF) == 0xEF &&
                (bom[1] & 0xFF) == 0xBB &&
                (bom[2] & 0xFF) == 0xBF);

        // 如果不是 BOM，则将读取的字节推回流中
        if (!hasBOM && bytesRead > 0) {
            pushbackInputStream.unread(bom, 0, bytesRead);
        }

        // 创建 InputStreamReader 和 BufferedReader
        InputStreamReader inputStreamReader = new InputStreamReader(pushbackInputStream, code);
        return new BufferedReader(inputStreamReader);
    }

    /**
     * 解析 MultipartFile 中的 CSV 文件为 Java 对象列表
     *
     * @param file  MultipartFile 文件
     * @param clazz 目标 Java 类
     * @param <T>   泛型类型
     * @return Java 对象列表
     * @throws IOException 如果文件读取失败
     */
    public static <T> List<T> parseCsvToBeansUtf8(MultipartFile file, Class<T> clazz) throws Exception {
        // 创建 BufferedReader 并指定 UTF-8 编码，同时处理 BOM
        BufferedReader reader = createBOMAwareReader(file);

        // 创建映射策略
        HeaderColumnNameMappingStrategy<T> strategy = new HeaderColumnNameMappingStrategy<>();
        strategy.setType(clazz);
        return parseCsvToBeans(reader, clazz);
    }

    /**
     * 创建支持 BOM 的 BufferedReader
     *
     * @param file 前端上传的 CSV 文件
     * @return 支持 BOM 的 BufferedReader
     * @throws Exception 如果读取文件失败
     */
    private static BufferedReader createBOMAwareReader(MultipartFile file) throws Exception {
        // 使用 PushbackInputStream 检查并跳过 BOM
        PushbackInputStream pushbackInputStream = new PushbackInputStream(file.getInputStream(), 3);
        byte[] bom = new byte[3];
        int bytesRead = pushbackInputStream.read(bom, 0, bom.length);

        // 检查是否为 UTF-8 BOM (EF BB BF)
        boolean hasBOM = (bytesRead == 3 &&
                (bom[0] & 0xFF) == 0xEF &&
                (bom[1] & 0xFF) == 0xBB &&
                (bom[2] & 0xFF) == 0xBF);

        // 如果不是 BOM，则将读取的字节推回流中
        if (!hasBOM && bytesRead > 0) {
            pushbackInputStream.unread(bom, 0, bytesRead);
        }

        // 创建 InputStreamReader 和 BufferedReader
        InputStreamReader inputStreamReader = new InputStreamReader(pushbackInputStream, StandardCharsets.UTF_8);
        return new BufferedReader(inputStreamReader);
    }

    /**
     * 解析 Reader 中的 CSV 文件为 Java 对象列表
     *
     * @param reader CSV 文件 Reader
     * @param clazz  目标 Java 类
     * @param <T>    泛型类型
     * @return Java 对象列表
     */
    private static <T> List<T> parseCsvToBeans(Reader reader, Class<T> clazz) {
        // 使用列名映射策略
        HeaderColumnNameMappingStrategy<T> strategy = new HeaderColumnNameMappingStrategy<>();
        strategy.setType(clazz);

        // 创建 CSV 解析器
        CsvToBean<T> csvToBean = new CsvToBeanBuilder<T>(reader)
                .withMappingStrategy(strategy)
                .withIgnoreLeadingWhiteSpace(true)
                .withIgnoreEmptyLine(true)
                .build();

        // 解析 CSV 文件并返回对象列表
        return csvToBean.parse();
    }

    /**
     * 解析 CSV 文件为字符串列表
     *
     * @param filePath CSV 文件路径
     * @return 字符串列表（每行作为一个字符串数组）
     * @throws IOException 如果文件读取失败
     */
    public static List<String[]> parseCsvToStrings(String filePath) throws IOException {
        try (Reader reader = Files.newBufferedReader(Paths.get(filePath))) {
            return parseCsvToStrings(reader);
        }
    }

    /**
     * 解析 MultipartFile 中的 CSV 文件为字符串列表
     *
     * @param file MultipartFile 文件
     * @return 字符串列表（每行作为一个字符串数组）
     * @throws IOException 如果文件读取失败
     */
    public static List<String[]> parseCsvToStrings(MultipartFile file) throws IOException {
        try (Reader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            return parseCsvToStrings(reader);
        }
    }

    /**
     * 解析 Reader 中的 CSV 文件为字符串列表
     *
     * @param reader CSV 文件 Reader
     * @return 字符串列表（每行作为一个字符串数组）
     */
    private static List<String[]> parseCsvToStrings(Reader reader) {
        // 创建 CSV 解析器
        CsvToBean<String[]> csvToBean = new CsvToBeanBuilder<String[]>(reader)
                .withType(String[].class)
                .withIgnoreLeadingWhiteSpace(true)
                .withIgnoreEmptyLine(true)
                .build();

        // 解析 CSV 文件并返回字符串列表
        return csvToBean.parse();
    }
}