package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

@Data
public class BillingItem {
    private Integer totalQuantity;   // 总数量（数值）
    private Integer expendQuantity;  // 已消耗数量（数值）
    private String itemUnit;        // 计量单位
    private Integer itemType;       // 项目类型（数值）
    private String itemName;        // 项目名称
    private String itemTypeName;    // 项目类型名称
    private String serveItemExpendId;// 服务项消耗ID（与父项关联）
    private String productItemId;   // 产品项ID（与父项关联）
    private String itemCode;        // 项目编码（与父项关联）
    private Integer billingItemRule; // 计费规则（数值）
}
