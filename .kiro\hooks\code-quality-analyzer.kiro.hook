{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.java", "**/*.xml", "**/*.properties", "**/*.yml", "**/*.yaml"]}, "then": {"type": "askAgent", "prompt": "Analyze the recently modified code files for potential improvements. Focus on:\n\n1. Code smells and anti-patterns\n2. Design pattern opportunities\n3. Best practices adherence\n4. Readability improvements\n5. Maintainability enhancements\n6. Performance optimization opportunities\n\nFor each suggestion, provide:\n- Clear explanation of the issue or improvement opportunity\n- Specific code examples showing before/after\n- Rationale for why the change improves code quality\n- Impact on readability, maintainability, or performance\n\nEnsure all suggestions maintain existing functionality while improving code quality."}}