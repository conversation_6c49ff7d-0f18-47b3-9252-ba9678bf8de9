package com.xmd.achievement.util.encrypt;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.*;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 数据脱敏用到的AES加解密类
 *
 * <AUTHOR>
 */
public class AESSupport {
    private final static Logger LOGGER = LoggerFactory.getLogger(AESSupport.class);
    /**
     * 默认密钥
     */
    protected final static String DEFAULT_KEY = "Ae1tS5c";
    /**
     * 算法
     */
    private static final String KEY_ALGORITHM = "AES";
    /**
     * 随机数生成算法
     */
    private static final String SECURE_RANDOM_ALGORITHM = "SHA1PRNG";
    /**
     * aes keysize: must be equal to 128, 192 or 256
     */
    private static final Integer KEY_SIZE = 128;
    /**
     * cipher算法
     */
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/GCM/NoPadding";
    /**
     * 指定字符集
     */
    private static final String CHARSET = "UTF-8";

    private SecretKeySpec secretKeySpec;

    /**
     * 解析密钥
     *
     * @param password 加密密钥（16进制），非原生密钥
     */
    public AESSupport(String password) {
        if (StringUtils.isEmpty(password)) {
            throw new IllegalArgumentException("password should not be null!");
        }
        this.secretKeySpec = new SecretKeySpec(HexUtil.hexStringToBytes(password), KEY_ALGORITHM);
    }

    /**
     * 加密
     *
     * @param content 加密内容
     * <AUTHOR>
     * @date: 2022/1/13 9:55 上午
     * @version: 1.0.0
     * @return: java.lang.String
     */
    public String encrypt(String content) {
        try {
            byte[] iv = new byte[12];
            SecureRandom secureRandom = new SecureRandom();
            secureRandom.nextBytes(iv);
            byte[] contentBytes = content.getBytes(CHARSET);
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            GCMParameterSpec params = new GCMParameterSpec(128, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, params);
            byte[] encryptData = cipher.doFinal(contentBytes);
            assert encryptData.length == contentBytes.length + 16;
            byte[] message = new byte[12 + contentBytes.length + 16];
            System.arraycopy(iv, 0, message, 0, 12);
            System.arraycopy(encryptData, 0, message, 12, encryptData.length);
            return Base64.getEncoder().encodeToString(message);
        } catch (InvalidKeyException | NoSuchAlgorithmException | NoSuchPaddingException | IllegalBlockSizeException
                 | BadPaddingException e) {
            LOGGER.error(e.getMessage(), e);
        } catch (UnsupportedEncodingException | InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 解密
     *
     * @param base64Content 解密内容
     * @return
     */
    public String decrypt(String base64Content) {
        try {
            byte[] content = Base64.getDecoder().decode(base64Content);
            if (content.length < 12 + 16)
                throw new IllegalArgumentException();
            GCMParameterSpec params = new GCMParameterSpec(128, content, 0, 12);
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, params);
            byte[] decryptData = cipher.doFinal(content, 12, content.length - 12);
            return new String(decryptData, CHARSET);
        } catch (InvalidKeyException | NoSuchAlgorithmException | NoSuchPaddingException
                 | InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException e) {
            LOGGER.error(e.getMessage(), e);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据密码生成密钥
     *
     * @param encryptPass 密码
     * @return
     * @throws NoSuchAlgorithmException
     */
    private static SecretKeySpec getSecretKey(String encryptPass) throws NoSuchAlgorithmException {
        KeyGenerator kg = KeyGenerator.getInstance(KEY_ALGORITHM);
        // 初始化密钥生成器，AES要求密钥长度为128位、192位、256位
        SecureRandom secureRandom = SecureRandom.getInstance(SECURE_RANDOM_ALGORITHM);
        secureRandom.setSeed(encryptPass.getBytes());
        kg.init(KEY_SIZE, secureRandom);
        SecretKey secretKey = kg.generateKey();
        return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM);// 转换为AES专用密钥
    }

    /**
     * 将密钥转换为16进制字符串
     *
     * @param password 密钥
     * @return
     * @see HexUtil#bytesToHexString(byte[])
     */
    public static String giveSecretKeyByHex(final String password) {
        try {
            SecretKeySpec secretKeySpec = getSecretKey(password);
            return HexUtil.bytesToHexString(secretKeySpec.getEncoded());
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("AES生成密钥异常", e);
        }
        return null;
    }

}
