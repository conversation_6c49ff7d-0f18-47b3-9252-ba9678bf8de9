package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xmd.achievement.dao.entity.PolicySpecDetailModel;
import com.xmd.achievement.dao.mapper.PolicySpecDetailMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IPolicySpecDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 政策规格明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@Slf4j
public class PolicySpecDetailRepositoryImpl extends ServiceImpl<PolicySpecDetailMapper, PolicySpecDetailModel> implements IPolicySpecDetailRepository {

    @Resource
    private PolicySpecDetailMapper policySpecDetailMapper;

    @Override
    public PolicySpecDetailModel getPolicySpecDetail(Long specId, Long specCategoryId) {
        LambdaQueryWrapper<PolicySpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicySpecDetailModel::getSpecId, specId);
        queryWrapper.eq(PolicySpecDetailModel::getSpecCategoryId, specCategoryId);
        return policySpecDetailMapper.selectOne(queryWrapper);
    }

    @Override
    public List<PolicySpecDetailModel> getPolicySpecDetailByCategoryId(Long specCategoryId) {
        LambdaQueryWrapper<PolicySpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicySpecDetailModel::getSpecCategoryId, specCategoryId);
        return policySpecDetailMapper.selectList(queryWrapper);
    }

    @Override
    public PolicySpecDetailModel getPolicySpecDetailBySpecId(Long specId) {
        LambdaQueryWrapper<PolicySpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicySpecDetailModel::getSpecId, specId);
        return policySpecDetailMapper.selectOne(queryWrapper);
    }
}