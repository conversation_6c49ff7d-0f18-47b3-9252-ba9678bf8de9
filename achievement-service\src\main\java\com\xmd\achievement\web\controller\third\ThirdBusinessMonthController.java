package com.xmd.achievement.web.controller.third;

import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "API-商务月接口")
@RequestMapping("/api/businessMonth")
public class ThirdBusinessMonthController {

    private final IBusinessMonthService businessMonthService;

    @Operation(summary = "获取上一个商务月")
    @PostMapping("/queryLastBusinessMonth")
    public WebResult<String> queryLastBusinessMonth() {
        String lastMonth = businessMonthService.queryLastBusinessMonth();
        return WebResult.success(lastMonth);
    }
}
