package com.xmd.achievement.service.entity.response;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * saas标签响应参数
 */
@Data
public class SaasTabResponse {

    @Schema(description = "saas标签ID", example = "1")
    private Long id;

    @Schema(description = "关联ID", example = "1")
    private Long associationId;

    @Schema(description = "关联名称", example = "示例名称")
    private String associationName;


    private String isSaas;

    private String saasType;

    @Schema(description = "备注", example = "备注信息")
    private String remark;

    private Date createTime;
    /**
     * 创建人名称
     */

    private String createUserName;

    /**
     * 更新时间
     */
 
    private Date updateTime;

    /**
     * 更新人名称
     */
    private String updateUserName;
} 