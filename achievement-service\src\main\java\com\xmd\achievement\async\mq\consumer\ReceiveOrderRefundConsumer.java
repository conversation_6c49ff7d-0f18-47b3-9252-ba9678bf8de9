package com.xmd.achievement.async.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.IMqOrderRefundInfoService;
import com.xmd.achievement.service.entity.dto.ReceiveOrderRefundDto;
import com.xmd.achievement.support.constant.enums.AfterSalesTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 退款
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(consumerGroup = "${mq.group.orderRefund}", topic = "${mq.topic.orderRefund}", consumeMode = ConsumeMode.CONCURRENTLY, messageModel = MessageModel.CLUSTERING)
public class ReceiveOrderRefundConsumer implements RocketMQListener<String> {

    private final IMqOrderRefundInfoService mqOrderRefundInfoService;

    @Override
    public void onMessage(String dataJson) {
        log.info("订单退款数据信息处理,接收数据:{}", dataJson);
        try {
            // 解析
            ReceiveOrderRefundDto receiveOrderRefundDto = JSONUtil.toBean(dataJson, ReceiveOrderRefundDto.class);
            log.info("订单退款数据信息处理,数据转换:{}", JSONUtil.toJsonStr(receiveOrderRefundDto));

            // 只有当afterSalesOrderType==1(退款单)和operateType="confirm" 时进行消费，消费完成回调，需带上各系统
            if (Objects.equals(receiveOrderRefundDto.getAfterSalesOrderType(), AfterSalesTypeEnum.REFUND.getCode())
                    && Objects.equals("confirm", receiveOrderRefundDto.getOperateType())) {
                log.warn("订单退款数据信息处理,是退款消息并且消息已确认");
                // 落库，更改为新的逻辑了
                // mqOrderRefundInfoService.saveInfo(receiveOrderRefundDto);
            }
        } catch (Exception e) {
            log.error("订单退款数据信息处理异常，message:{},异常信息", dataJson, e);
            throw e;
        }
    }

}
