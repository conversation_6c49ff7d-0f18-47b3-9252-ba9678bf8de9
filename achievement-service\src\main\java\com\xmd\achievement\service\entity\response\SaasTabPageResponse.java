package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * saas标签分页查询响应
 */
@Data
public class SaasTabPageResponse {

    @Schema(description = "saas标签id")
    private Long saasTabId;

    @Schema(description = "关联id")
    private Long associationId;

    @Schema(description = "关联名称")
    private String associationName;

    @Schema(description = "是否SAAS产品：0-非SAAS产品，1-SAAS产品")
    private Integer isSaas;

    @Schema(description = "SAAS类型：0=中企,1=跨境")
    private Integer saasType;

    @Schema(description = "备注")
    private String remark;
} 