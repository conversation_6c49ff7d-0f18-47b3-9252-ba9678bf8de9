package com.xmd.achievement.service.entity.request;

import com.xmd.achievement.service.entity.response.OrderProductResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 重算订单明细编号业绩请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class RecalculateOrderProductRequest implements Serializable {

    private static final long serialVersionUID = 7333332340191828323L;

    @Schema(description = "订单明细编号")
    List<OrderProductResponse> orderProducts;
}
