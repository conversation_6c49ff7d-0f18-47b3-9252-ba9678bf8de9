package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/13:55
 * @since 1.0
 */
@Data
public class PageQueryspecCombinationListResponse implements Serializable {
    @Schema(description = "组合ID")
    private String combinationId;

    @Schema(description = "组合名称")
    private String combinationName;

    @Schema(description = "新开政策性成本")
    private BigDecimal newPolicyCost;

    @Schema(description = "续费政策性成本")
    private BigDecimal renewalPolicyCost;

    @Schema(description = "升级政策性成本")
    private BigDecimal upgradePolicyCost;

    @Schema(description = "另购政策性成本")
    private BigDecimal additionalPolicyCost;

    @Schema(description = "状态 1=启用 2=禁用")
    private Integer status;

    @Schema(description = "创建者")
    private String createUserName;

    @Schema(description = "规格明细")
    private List<SpecCombinationDetaiResponse> specDetailList;

    @Schema(description = "备注")
    private String remark;
}
