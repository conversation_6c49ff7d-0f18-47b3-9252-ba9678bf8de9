package com.xmd.achievement.util.http;

/**
 * http返回结果枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/1/24 9:34 上午
 */
public enum HttpResultEnum {

    REQUEST_SUCCESS(200, "请求成功"),
    REQUEST_ERROR(100000, "http请求异常"),
    ;

    private Integer code;

    private String message;

    HttpResultEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
