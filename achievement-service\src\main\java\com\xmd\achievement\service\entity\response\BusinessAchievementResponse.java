package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <p>
 * 商务业绩表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class BusinessAchievementResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 商务月id
     */
    @Schema(description = "商务月id")
    private Long businessMonthId;

    /**
     * 商务月
     */
    @Schema(description = "商务月")
    private String businessMonth;
    /**
     * 员工ID
     */
    @Schema(description = "员工ID")
    private String employeeId;
    /**
     * 员工姓名
     */
    @Schema(description = "员工姓名")
    private String employeeName;

    @Schema(description = "职级类型")
    private String positionName;

    @Schema(description = "职级")
    private String position;

    @Schema(description = "是否转正")
    private String confirmed;
    /**
     * 在岗时长
     */
    @Schema(description = "在岗时长")
    private Integer tenure;

    /**
     * 司龄细分
     */
    @Schema(description = "司龄细分")
    private String senioritySegment;

    /**
     * 区域
     */
    @Schema(description = "区域")
    private String region;

    /**
     * 区域ID
     */
    @Schema(description = "区域ID")
    private Long regionId;

    /**
     * 分公司
     */
    @Schema(description = "分公司")
    private String company;

    /**
     * 分公司ID
     */
    @Schema(description = "分公司ID")
    private Long companyId;

    /**
     * 事业部ID
     */
    @Schema(description = "事业部ID")
    private Long divisionId;

    /**
     * 事业部
     */
    @Schema(description = "事业部")
    private String division;

    /**
     * 部门
     */
    @Schema(description = "部门")
    private String department;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 净现金到账
     */
    @Schema(description = "净现金到账")
    private BigDecimal netCashReceipt;

    /**
     * 自有净现金到账
     */
    @Schema(description = "自有净现金到账")
    private BigDecimal ownNetCashReceipt;

    /**
     * 业绩段
     */
    @Schema(description = "业绩段")
    private String achievementSegment;

    /**
     * 实发提成业绩
     */
    @Schema(description = "实发提成业绩")
    private BigDecimal actualCommission;

    /**
     * 新客户数
     */
    @Schema(description = "新客户数")
    private Integer newCustomerCount;
    /**
     * 老客户数
     */
    @Schema(description = "老客户数")
    private Integer oldCustomerCount;

    /**
     * 非续费单
     */
    @Schema(description = "非续费单")
    private Integer nonRenewalOrders;

    /**
     * 新开网站个数
     */
    @Schema(description = "新开网站个数")
    private Integer newWebsiteCount;

    /**
     * 网站非续费客户数
     */
    @Schema(description = "网站非续费客户数")
    private Integer websiteNonRenewalCustomers;

    /**
     * 商代提成业绩
     */
    @Schema(description = "商代提成业绩")
    private BigDecimal agentCommissionAchievement;

    /**
     * 网站净现金到账
     */
    @Schema(description = "网站净现金到账")
    private BigDecimal siteNetCashReceipt;

    @Schema(description = "saas净现金")
    private BigDecimal saasNetCash;

    @Schema(description = "saas新客户数")
    private Integer newSaasCustomerCount;
}
