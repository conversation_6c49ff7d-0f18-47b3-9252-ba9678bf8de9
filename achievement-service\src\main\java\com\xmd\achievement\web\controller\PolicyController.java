package com.xmd.achievement.web.controller;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.IPolicyService;
import com.xmd.achievement.service.entity.request.QueryPolicyListRequest;
import com.xmd.achievement.service.entity.request.QuerySpecByproductIdRequest;
import com.xmd.achievement.service.entity.request.SavePolicyRequest;
import com.xmd.achievement.service.entity.response.PolicySpecDetailResponse;
import com.xmd.achievement.service.entity.response.PolicySpecResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 业绩政策
 *
 * <AUTHOR>
 * @date: 2024/12/19 11:16
 * @version: 1.0.0
 * @return {@link }
 */
@Tag(name = "POC-业绩政策接口")
@Slf4j
@RestController
@RequestMapping("policy")
public class PolicyController {
    @Resource
    IPolicyService policyService;

    @Operation(summary = "POC-01:添加规格业绩政策配置")
    @PostMapping("savePolicy")
    public WebResult<Boolean> savePolicy(@RequestBody @Valid SavePolicyRequest request) {
        log.info("POC-01:添加规格业绩政策配置,请求参数:{}", JSONUtil.toJsonStr(request));
        return policyService.savePolicy(request);
    }

    @Operation(summary = "POC-02:查询规格业绩政策配置")
    @PostMapping("queryPolicyList")
    public WebResult<PolicySpecResponse> queryPolicyList(@RequestBody @Valid QueryPolicyListRequest request) {
        log.info("POC-02:查询规格业绩政策配置,请求参数:{}", JSONUtil.toJsonStr(request));
        return policyService.queryPolicyList(request);
    }


    @Operation(summary = "POC-03:查询规格通过productId")
    @PostMapping("querySpecByProductId")
    public WebResult<List<PolicySpecDetailResponse>> querySpecByProductId(@RequestBody @Valid QuerySpecByproductIdRequest request) {
        log.info("POC-03:查询规格通过productId,请求参数:{}", JSONUtil.toJsonStr(request));
        return policyService.querySpecByProductId(request);
    }

}
