package com.xmd.achievement.web.controller;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/09:48
 * @since 1.0
 */

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.AchievementProductRuleService;
import com.xmd.achievement.service.entity.request.AddProductRuleConfigRequest;
import com.xmd.achievement.service.entity.request.QueryRuleProductRequest;
import com.xmd.achievement.service.entity.response.QueryProductRuleConfigResponse;
import com.xmd.achievement.service.entity.response.QueryRuleProductResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 商品业绩类
 *
 * <AUTHOR>
 */
@Tag(name = "PRC-商品规则配置")
@Slf4j
@RestController
@RequestMapping("product/rule")
public class AchievementProductRuleController {
    @Resource
    AchievementProductRuleService achievementProductRuleService;

    @Operation(summary = "PRC-01-增加商品规则配置")
    @PostMapping("/addProductRuleConfig")
    public WebResult<Boolean> addProductRuleConfig(@RequestBody @Valid AddProductRuleConfigRequest request) {
        log.info("PRC-01-增加商品规则配置,请求参数:{}", JSONUtil.toJsonStr(request));
        return achievementProductRuleService.addProductRuleConfig(request);
    }


    @Operation(summary = "PRC-02-查询商品规则配置")
    @GetMapping("/queryProductRuleConfig")
    public WebResult<List<QueryProductRuleConfigResponse>> queryProductRuleConfig() {
        log.info("PRC-02-查询商品规则配置");
        return WebResult.success(achievementProductRuleService.queryProductRuleConfig());
    }

    @Operation(summary = "PRC-03-查询规则下商品信息")
    @PostMapping("/queryRuleProduct")
    public WebResult<List<QueryRuleProductResponse>> queryRuleProduct(@RequestBody @Valid QueryRuleProductRequest request) {
        log.info("PRC-03-查询规则下商品信息,请求参数:{}", JSONUtil.toJsonStr(request));
        return WebResult.success(achievementProductRuleService.queryRuleProduct(request));
    }


}
