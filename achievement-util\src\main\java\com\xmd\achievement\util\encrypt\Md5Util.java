package com.xmd.achievement.util.encrypt;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/19 上午11:08
 **/
class Md5Util {
    protected final static String MD5_KEY = "MD5";

    protected final static String SHA_KEY = "SHA1";
    /**
     * 指定编码，解决中文加密不一致的问题
     */
    protected final static String ENCODE = "UTF-8";

    /**
     * MD5加密
     *
     * @param value 待加密字符串
     * @param key   加密密钥
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午11:09
     * @version 1.0.0
     **/
    protected static String encrypt(String value, String key) {
        try {
            // 拿到一个MD5转换器（如果想要SHA1参数换成”SHA1”）
            MessageDigest messageDigest = MessageDigest.getInstance(key);
            // 输入的字符串转换成字节数组
            byte[] inputByteArray = value.getBytes(ENCODE);
            // inputByteArray是输入字符串转换得到的字节数组
            messageDigest.update(inputByteArray);
            // 转换并返回结果，也是字节数组，包含16个元素
            byte[] resultByteArray = messageDigest.digest();
            // 字符数组转换成字符串返回
            return byteArrayToHex(resultByteArray);
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            return null;
        }
    }

    /**
     * byte转hex
     *
     * @param byteArray byte数组
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午11:10
     * @version 1.0.0
     **/
    private static String byteArrayToHex(byte[] byteArray) {
        // 首先初始化一个字符数组，用来存放每个16进制字符
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        // new一个字符数组，这个就是用来组成结果字符串的（解释一下：一个byte是八位二进制，也就是2位十六进制字符（2的8次方等于16的2次方））
        char[] resultCharArray = new char[byteArray.length * 2];
        // 遍历字节数组，通过位运算（位运算效率高），转换成字符放到字符数组中去
        int index = 0;
        for (byte b : byteArray) {
            resultCharArray[index++] = hexDigits[b >>> 4 & 0xf];
            resultCharArray[index++] = hexDigits[b & 0xf];
        }
        // 字符数组组合成字符串返回
        return new String(resultCharArray);
    }
}
