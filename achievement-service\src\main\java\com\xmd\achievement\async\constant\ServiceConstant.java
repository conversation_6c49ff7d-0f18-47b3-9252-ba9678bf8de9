package com.xmd.achievement.async.constant;

import java.time.ZoneId;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/2/1 5:49 下午
 */
public class ServiceConstant {

    /**
     * 定时任务相关常量
     *
     * <AUTHOR>
     * @version 1.0.0
     * @date 2023/4/6 16:22
     **/
    public static class JobConstant {

        /**
         * 任务处理器
         */
        public static class JobHandlerConstant {
            /**
             *
             */
            public static final String TEST = "test";
            public static final String MQ_ORDER_PAYMENT_INFO_JOB = "MqOrderPaymentInfoJob";
            public static final String ACHIEVEMENT_DATA_TRANSFER_JOB = "AchievementDataTransferJob";
            public static final String MQ_SERVE_IN_PROGRESS_INFO_JOB = "MqServeInprogressInfoJob";
            public static final String MQ_SERVE_FINISH_INFO_JOB = "MqServeFinishInfoJob";
            public static final String THIRD_ACHIEVEMENT_JOB = "ThirdAchievementJob";
            public static final String THIRD_ACHIEVEMENT_TO_PAYMENT_ORDER_JOB = "BackUpThirdAchievementToPaymentOrderJob";
            public static final String SYNC_BUSINESS_MONTH_HISTORY_JOB = "syncBusinessMonthHistoryJob";
            public static final String SYNC_ADVERTISING_PASS_HISTORY_JOB = "syncAdvertisingPassHistoryJob";
            public static final String MONTHLY_REPORT_JOB = "monthlyReportJob";
            public static final String DAILY_REPORT_JOB = "dailyReportJob";
            public static final String SYNC_BUSINESS_ACHIEVEMENT_STATISTICS_JOB = "syncBusinessAchievementStatisticsJob";
            public static final String CUSTOMER_SAAS_JOB = "customerSaasJob";
            public static final String MQ_ORDER_REFUND_INFO_JOB = "MqOrderRefundInfoJob";
        }
    }

    /**
     * mq常量池
     *
     * <AUTHOR>
     * @version 1.0.0
     * @date 2023/4/6 16:23
     **/
    public static class MqConstant {

        /**
         * 主题标签分隔符
         */
        public final static String TOPIC_TAG_SEPARATOR = ":";

        /**
         * 主题
         */
        public static class Topic {
            public final static String _TOPIC = "_TOPIC";
        }

        /**
         * 标签
         */
        public static class Tag {
            public final static String _TAG = "_TAG";
        }

        /**
         * 订阅组常量
         */
        public static class Group {
            public final static String _GROUP = "_GROUP";
        }


        /**
         * 延迟消息级别
         * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
         */
        public static class Level {

            public static final int ONE_SECOND = 1;

            public static final int FIVE_SECOND = 2;

            public static final int TEN_SECOND = 3;

            public static final int THIRTY_SECOND = 4;

            public static final int ONE_MINUTE = 5;

            public static final int TWO_MINUTE = 6;

            public static final int THREE_MINUTE = 7;

            public static final int FOUR_MINUTE = 8;

            public static final int FIVE_MINUTE = 9;

            public static final int SIX_MINUTE = 10;

            public static final int SEVEN_MINUTE = 11;

            public static final int EIGHT_MINUTE = 12;

            public static final int NINE_MINUTE = 13;

            public static final int TEN_MINUTE = 14;

            public static final int TWENTY_MINUTE = 15;

            public static final int THIRTY_MINUTE = 16;

            public static final int ONE_HOUR = 17;

            public static final int TWO_HOUR = 18;

        }
    }

    public static class MothConstant {

        public static final ZoneId ZONE_ID = ZoneId.of("Asia/Shanghai");
    }
}
