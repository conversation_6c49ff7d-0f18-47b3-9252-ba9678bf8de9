package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.MqServeFinishTimeInfoModel;
import com.xmd.achievement.service.entity.dto.ReceiveServeFinishInProgressDto;
import com.xmd.achievement.service.entity.request.InfoSaveMqServeFinishTimeInfoRequest;

import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/14:25
 * @since 1.0
 */
public interface MqServeFinishTimeInfoService {
    /**
     * 保存信息
     *
     * @param progressDto 请求参数
     */
    void saveInfo(ReceiveServeFinishInProgressDto progressDto);

    /**
     * 执行成功
     *
     * @param orderId 请求参数
     */
    void executeSuccess(Long orderId, Long productId);

    /**
     * 执行失败
     *
     * @param orderId,productId,message 请求参数
     */
    void executeFailed(Long orderId, Long productId, String message);

    /**
     * 查询需要执行的任务
     *
     * @return List<MqServeInprogressInfoModel>
     */
    List<MqServeFinishTimeInfoModel> queryExecuteTask();

    /**
     * 检查任务状态
     *
     * @param orderId productId 请求参数
     * @param serveNo
     * @return Boolean
     */
    Boolean checkTaskStatus(Long orderId, Long productId, String serveNo);

    /**
     * 保存MqServeFinishTimeInfo信息
     *
     * @param request 请求参数
     * @return Boolean
     */
    Boolean infoSaveMqServeFinishTimeInfo(InfoSaveMqServeFinishTimeInfoRequest request);

    Boolean repairMqOrderPaymentInfo();

    Boolean repairMqServeFinishTimeInfo();

    Boolean repairMqServeInprogressInfo();
}
