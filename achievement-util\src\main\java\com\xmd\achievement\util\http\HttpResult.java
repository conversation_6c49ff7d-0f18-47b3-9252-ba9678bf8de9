package com.xmd.achievement.util.http;

import java.io.Serializable;

/**
 * http接口调用结果
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/1/24 9:23 上午
 */
public class HttpResult<T> implements Serializable {

    /**
     * 返回信息
     */
    private String msg;
    /**
     * 返回状态码
     */
    private Integer code;
    /**
     * 返回具体内容
     */
    private T data;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public HttpResult() {
    }

    private HttpResult(T data) {
        this.code = HttpResultEnum.REQUEST_SUCCESS.getCode();
        this.msg = HttpResultEnum.REQUEST_SUCCESS.getMessage();
        this.data = data;
    }

    private HttpResult(T data, String msg) {
        this.code = HttpResultEnum.REQUEST_SUCCESS.getCode();
        this.msg = msg;
        this.data = data;
    }

    private HttpResult(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private HttpResult(HttpResultEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMessage();
        }
    }

    private HttpResult(T t, HttpResultEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMessage();
        }
        data = t;
    }

    public static <T> HttpResult<T> success(T data) {
        return new HttpResult<>(data);
    }

    public static <T> HttpResult<T> success(T data, String msg) {
        return new HttpResult<>(data, msg);
    }

    public static <T> HttpResult<T> success() {
        return success(null);
    }

    public static <T> HttpResult<T> error(HttpResultEnum cm) {
        return new HttpResult<>(cm);
    }

    public static <T> HttpResult<T> error(HttpResultEnum cm, String msg) {
        return new HttpResult<>(cm.getCode(), msg);
    }

    public static <T> HttpResult<T> error(Integer code, String msg) {
        return new HttpResult<>(code, msg);
    }

    public static <T> HttpResult<T> error(T data, HttpResultEnum cm) {
        return new HttpResult<>(data, cm);
    }

    public static <T> HttpResult<T> error(HttpResult<?> HttpResult) {
        return new HttpResult<>(HttpResult.getCode(), HttpResult.getMsg());
    }

    public Boolean checkSuccess() {
        return HttpResultEnum.REQUEST_SUCCESS.getCode().equals(this.code);
    }
}
