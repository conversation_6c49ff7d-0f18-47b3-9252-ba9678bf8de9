package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 规格组合
 *
 * <AUTHOR>
 * @date: 2024/12/19 14:23
 * @version: 1.0.0
 * @return {@link }
 */
@Data
@Schema(description = "规格组合查询功能入参")
public class SpecCombinationSearch {
    /**
     * 组合id
     */
    @Schema(description = "组合id")
    private Long combinationId;
    /**
     * 组合名称
     */
    @Schema(description = "组合名称")
    private String combinationName;

    @Schema(description = "状态 1=启用 2=禁用")
    private Integer status;

}
