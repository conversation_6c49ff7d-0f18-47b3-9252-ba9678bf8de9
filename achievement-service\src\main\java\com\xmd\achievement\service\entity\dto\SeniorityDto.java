package com.xmd.achievement.service.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 司龄表（入职公司年限）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class SeniorityDto implements Serializable {
    private static final long serialVersionUID = 7944318837467623499L;
    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 司龄id
     */
    @Schema(description = "司龄id")
    private Long seniorityId;

    /**
     * 司龄名称
     */
    @Schema(description = "司龄名称")
    private String seniorityName;

    /**
     * 最小值（年限）
     */
    @Schema(description = "最小值（年限）")
    private Integer minYears;

    /**
     * 最大值（年限）
     */
    @Schema(description = "最大值（年限）")
    private Integer maxYears;

}