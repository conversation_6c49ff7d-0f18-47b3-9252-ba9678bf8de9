package com.xmd.achievement.dao.mapper;

import com.xmd.achievement.dao.entity.OrganizationMonthlyReportModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 机构月报 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface OrganizationMonthlyReportMapper extends BaseMapper<OrganizationMonthlyReportModel> {

    void batchInsertOrUpdate(@Param("list") List<OrganizationMonthlyReportModel> list);
}
