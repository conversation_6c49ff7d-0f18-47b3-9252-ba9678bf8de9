package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum MarketCategoryTypeEnum {

    /**
     * 一类
     */
    LEVEL_1(1, "一类"),
    /**
     * 二类
     */
    LEVEL_2(2, "二类"),
    /**
     * 三类
     */
    LEVEL_3(3, "三类"),
    ;

    private final Integer type;
    private final String name;

    MarketCategoryTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getNameByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        return Arrays.stream(MarketCategoryTypeEnum.values()).filter(flag -> Objects.equals(flag.getType(), type)).map(MarketCategoryTypeEnum::getName).findFirst().orElse("");
    }

}
