package com.xmd.achievement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.SaleTaskAuditModel;
import com.xmd.achievement.dao.entity.SaleTaskModel;
import com.xmd.achievement.dao.repository.ISaleTaskAuditRepository;
import com.xmd.achievement.service.SaleTaskAuditService;
import com.xmd.achievement.service.entity.request.SaleTaskAuditRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class SaleTaskAuditServiceImpl implements SaleTaskAuditService {

    @Autowired
    private ISaleTaskAuditRepository saleTaskAuditRepository;

    @Override
    public SaleTaskAuditModel getLatestAuditByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return null;
        }

        LambdaQueryWrapper<SaleTaskAuditModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaleTaskAuditModel::getTaskId, taskId);
        queryWrapper.orderByDesc(SaleTaskAuditModel::getCreateTime);
        queryWrapper.last("limit 1");
        return saleTaskAuditRepository.getOne(queryWrapper);
    }

    @Override
    public void saleTaskAudit(List<SaleTaskModel> saleTaskModels, SaleTaskAuditRequest auditRequest) {
        if (CollectionUtils.isEmpty(saleTaskModels)) {
            return;
        }

        List<SaleTaskAuditModel> models = Lists.newArrayList();
        for (SaleTaskModel taskModel : saleTaskModels) {
            SaleTaskAuditModel model = new SaleTaskAuditModel();
            model.setTaskId(taskModel.getTaskId());
            model.setAuditResult(auditRequest.getAuditResult());
            model.setAuditRemark(auditRequest.getAuditRemark());
            models.add(model);
        }

        saleTaskAuditRepository.saveBatch(models);
    }
}
