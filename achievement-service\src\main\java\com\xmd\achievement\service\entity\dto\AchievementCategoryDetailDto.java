package com.xmd.achievement.service.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 业绩规格分类明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@Accessors(chain = true)
public class AchievementCategoryDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 业绩规格分类id
     */
    @Schema(description = "业绩规格分类id")
    private Long achievementCategoryId;

    /**
     * 业绩流水ID
     */
    @Schema(description = "业绩流水ID")
    private Long achievementId;

    /**
     * 规格分类ID
     */
    @Schema(description = "规格分类ID")
    private Long categoryId;

    /**
     * 规格分类名称
     */
    @Schema(description = "规格分类名称")
    private String categoryName;

    /**
     * 标准价
     */
    @Schema(description = "标准价")
    private BigDecimal standardPrice;

    /**
     * 应付金额
     */
    @Schema(description = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额")
    private BigDecimal paidAmount;

    /**
     * 首年报价
     */
    @Schema(description = "首年报价")
    private BigDecimal firstYearQuote;

    /**
     * 首年到账金额
     */
    @Schema(description = "首年到账金额")
    private BigDecimal firstYearIncome;

    /**
     * 续费报价
     */
    @Schema(description = "续费报价")
    private BigDecimal renewalQuote;

    /**
     * 续费到账金额
     */
    @Schema(description = "续费到账金额")
    private BigDecimal renewalIncome;

    /**
     * 净现金
     */
    @Schema(description = "净现金")
    private BigDecimal netCash;

    /**
     * 商代提成业绩
     */
    @Schema(description = "商代提成业绩")
    private BigDecimal agentCommAchv;

    /**
     * 商代实发提成业绩
     */
    @Schema(description = "商代实发提成业绩")
    private BigDecimal agentActCommAchv;

    /**
     * 商代缓发提成业绩
     */
    @Schema(description = "商代缓发提成业绩")
    private BigDecimal agentDefCommAchv;

    /**
     * 部门提成业绩
     */
    @Schema(description = "部门提成业绩")
    private BigDecimal deptCommAchv;

    /**
     * 事业部提成业绩
     */
    @Schema(description = "事业部提成业绩")
    private BigDecimal buCommAchv;

    /**
     * 分公司提成业绩
     */
    @Schema(description = "分公司提成业绩")
    private BigDecimal branchCommAchv;
    /**
     * 商品ID (冗余字段)
     */
    private Long productId;
    /**
     * 商品名称 (冗余字段)
     */
    private String productName;

    public static AchievementCategoryDetailDto buildBaseDto(List<AchievementSpecDetailDto> specDetailDtoList) {
        AchievementSpecDetailDto specDetailDto = specDetailDtoList.get(0);
        String categoryName = specDetailDto.getCategoryName();
        return new AchievementCategoryDetailDto()
                .setCategoryId(specDetailDto.getCategoryId())
                .setCategoryName(categoryName)
                .setStandardPrice(specDetailDtoList.stream().map(AchievementSpecDetailDto::getStandardPrice).reduce(BigDecimal.ZERO, BigDecimal::add))
                .setPayableAmount(specDetailDtoList.stream().map(AchievementSpecDetailDto::getPayableAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .setPaidAmount(specDetailDtoList.stream().map(AchievementSpecDetailDto::getPaidAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .setProductId(specDetailDto.getProductId())
                .setProductName(specDetailDto.getProductName());
    }
}
