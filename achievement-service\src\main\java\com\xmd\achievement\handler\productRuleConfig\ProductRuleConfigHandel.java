package com.xmd.achievement.handler.productRuleConfig;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductRuleConfigModel;
import com.xmd.achievement.service.entity.response.ProductChurnResponse;
import com.xmd.achievement.service.entity.response.RechargeInfoResponse;
import com.xmd.achievement.service.entity.response.ServiceInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/11/15:12
 * @since 1.0
 */

public interface ProductRuleConfigHandel {
    void calculateProductChurnStatus(AchievementProductRuleConfigModel ruleConfig,AchievementProductDetailModel productDetailModel, RechargeInfoResponse rechargeInfo, Map<String, List<ServiceInfoResponse>> productServiceMap, List<ProductChurnResponse> churnResponseList, List<ProductChurnResponse> unChurnResponseList);
}
