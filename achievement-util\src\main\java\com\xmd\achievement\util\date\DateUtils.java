package com.xmd.achievement.util.date;

import cn.hutool.core.util.ObjectUtil;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/8 15:28
 **/
public class DateUtils {

    /**
     * xxl-job延迟秒数
     * 为了解决xxl-job在提交任务是如果距离当前时间过近，可能导致任务无法执行的问题
     */
    private final static int XXL_JOB_DURATION_SECOND = 1;
    /**
     * 秒转毫秒的进制
     */
    private final static int SECOND_TO_MILLIS = 1000;

    /**
     * 时间转换cron表达式（仅执行一次）
     *
     * @param localDateTime 任务执行时间
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/4/8 15:28
     **/
    public static String generateCron(LocalDateTime localDateTime) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        long second;
        if (currentDateTime.isAfter(localDateTime) && (second = Duration.between(currentDateTime, localDateTime).toMillis() / SECOND_TO_MILLIS) < XXL_JOB_DURATION_SECOND) {
            localDateTime = localDateTime.plusSeconds(XXL_JOB_DURATION_SECOND - second);
        }
        return localDateTime.format(DateTimeFormatter.ofPattern(DateTimeFormatUtil.XXL_JOB_CRON_FORMAT.getFormat()));
    }

    public static String getCurrentDay() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 定义格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTimeFormatStyleEnum.yyyy_MM.getCode());
        // 格式化日期为 yyyy-MM 格式
        return currentDate.format(formatter);
    }

    /**
     * 获取当天的起始日期
     * @return
     */
    public static Date getStartOfDay() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 将日期转换为当天的起始时间（00:00:00）
        LocalDateTime startOfDay = currentDate.atStartOfDay();
        return convertToDate(startOfDay);
    }

    /**
     * 获取当天的结束日期
     * @return
     */
    public static Date getEndOfDay() {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 将 LocalDate 转换为 Date（当天的结束时间）
        return convertToDate(today.atTime(LocalTime.MAX));
    }

    /**
     * 将 LocalDateTime 转换为 Date
     */
    public static Date convertToDate(LocalDateTime localDateTime) {
        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 将 LocalDateTime 转换为 ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        // 将 ZonedDateTime 转换为 Instant
        Instant instant = zonedDateTime.toInstant();
        // 将 Instant 转换为 Date
        return Date.from(instant);
    }

    /**
     * 获取当前月份
     * @return
     */
    public static String getCurrentMonth(Date date) {
        if (ObjectUtil.isEmpty(date)) {
            // 获取当前日期
            LocalDate now = LocalDate.now();
            // 格式化为 yyyy-MM 形式
            return now.format(DateTimeFormatter.ofPattern(DateTimeFormatStyleEnum.yyyy_MM.getCode()));
        } else {
            // 转换成 LocalDate
            LocalDate localDate = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            // 格式化输出
            return localDate.format(DateTimeFormatter.ofPattern(DateTimeFormatStyleEnum.yyyy_MM.getCode()));
        }

    }

    /**
     * 获取上个月月份
     * @return
     */
    public static String getPreviousMonth(Date startOfDay) {
        LocalDate today = LocalDate.now();
        if (ObjectUtil.isNotEmpty(startOfDay)) {
            today = startOfDay.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        }
        // 减去一个月
        LocalDate lastMonth = today.minusMonths(1);
        // 格式化为 yyyy-MM 格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTimeFormatStyleEnum.yyyy_MM.getCode());
        return lastMonth.format(formatter);
    }

    public static Date getBeforeDay(Date date, int day) {
        // 将 Date 转换为 LocalDate
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 获取前一天的 LocalDate
        LocalDate previousLocalDate = localDate.minusDays(day);
        // 将 LocalDate 转换回 Date
        return Date.from(previousLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将字符串转成日期
     * @return
     */
    public static Date stringToDate(String date, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        LocalDateTime localDateTime = LocalDateTime.parse(date, formatter);
        // 将 LocalDateTime 转换为 Date
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取两个 Date 之间每天的开始时间和结束时间
     *
     * @param start 起始时间
     * @param end   结束时间
     * @return List<Date[]>，每个元素包含两个 Date：当天的 00:00:00 和 23:59:59
     */
    public static List<Date[]> getDayTimeRanges(Date start, Date end) {
        List<Date[]> result = new ArrayList<>();

        if (start == null || end == null) {
            throw new IllegalArgumentException("Start and end dates must not be null.");
        }

        Calendar startDateCal = Calendar.getInstance();
        startDateCal.setTime(start);
        startDateCal.set(Calendar.HOUR_OF_DAY, 0);
        startDateCal.set(Calendar.MINUTE, 0);
        startDateCal.set(Calendar.SECOND, 0);
        startDateCal.set(Calendar.MILLISECOND, 0);

        Calendar endDateCal = Calendar.getInstance();
        endDateCal.setTime(end);
        endDateCal.set(Calendar.HOUR_OF_DAY, 0);
        endDateCal.set(Calendar.MINUTE, 0);
        endDateCal.set(Calendar.SECOND, 0);
        endDateCal.set(Calendar.MILLISECOND, 0);

        Calendar currentCal = (Calendar) startDateCal.clone();

        while (!currentCal.after(endDateCal)) {
            Date dayStart = currentCal.getTime();

            Calendar dayEndCal = (Calendar) currentCal.clone();
            dayEndCal.add(Calendar.DAY_OF_MONTH, 1);
            // 上一天的最后一毫秒（23:59:59.999）
            dayEndCal.add(Calendar.MILLISECOND, -1);

            Date dayEnd = dayEndCal.getTime();

            result.add(new Date[]{dayStart, dayEnd});

            currentCal.add(Calendar.DAY_OF_MONTH, 1);
        }

        return result;
    }

    /**
     * 获取指定日期的月份
     * 如果和当前时间在同一个月份，则取上一个月份
     * @return
     */
    public static String getPreviousOrTargetMonth(Date date) {
        Objects.requireNonNull(date);

        // 将 Date 转换为 LocalDate
        LocalDate inputDate = date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        LocalDate now = LocalDate.now();

        boolean isSameMonth = isSameMonth(inputDate, now);

        LocalDate targetDate;
        if (isSameMonth) {
            // 如果是当前月份，则取上个月
            targetDate = now.minusMonths(1);
        } else {
            // 否则取输入日期所在月份
            targetDate = inputDate;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTimeFormatStyleEnum.yyyy_MM.getCode());
        return targetDate.format(formatter);
    }

    private static boolean isSameMonth(LocalDate d1, LocalDate d2) {
        return d1.getYear() == d2.getYear() && d1.getMonth() == d2.getMonth();
    }
}
