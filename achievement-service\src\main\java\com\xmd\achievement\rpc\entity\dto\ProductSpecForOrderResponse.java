package com.xmd.achievement.rpc.entity.dto;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ProductSpecForOrderResponse {

    @Schema(description = "规格ID")
    private Long specId;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "规格名称")
    private String specName;

    @Schema(description = "规格状态")
    private Integer specStatus;

    @Schema(description = "规格分类ID")
    private Long specCategoryId;

    @Schema(description = "规格分类名称")
    private String specCategoryName;

    @Schema(description = "必选状态")
    private Integer mandatoryStatus;

    @Schema(description = "另购状态")
    private Integer additionalPurchaseStatus;

    @Schema(description = "计费类型")
    private Integer billingType;

    @Schema(description = "定价类型")
    private Integer pricingType;

    @Schema(description = "阶梯定价计费类型")
    private Integer tieredPricingBillingType;

    @Schema(description = "阶梯定价计费项ID")
    private Long tieredPricingBillingItemId;

    @Schema(description = "排斥规格ID列表")
    private List<Long> exclusionSpecIdList;

    @Schema(description = "计费项列表")
    private List<ProductSpecItemForOrderResponse> itemList;

    @Schema(description = "标准计费价格")
    private StandardBillingPriceResponse standardBillingPrice;

    @Schema(description = "阶梯计费价格")
    private TieredBillingPriceResponse tieredBillingPrice;

    @Schema(description = "广告标识")
    private Integer adFlag;

    @Schema(description = "特殊费用标识")
    private Integer specialFeeFlag;

    @Schema(description = "关联规格ID")
    private Long relationSpecId;

    @Schema(description = "价格比例")
    private BigDecimal priceRatio;

    @Schema(description = "计费项规则")
    private Integer billingItemRule;

    @Schema(description = "主规格标识")
    private Integer masterFlag;

    @Schema(description = "主规格关联规格ID")
    private Long materRelationSpecId;

    @Schema(description = "主规格价格比例")
    private BigDecimal masterPriceRatio;
}
