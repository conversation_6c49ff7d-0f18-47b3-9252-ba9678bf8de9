package com.xmd.achievement.async.mq.config;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.xmd.achievement.support.log.LogObject;
import com.xmd.achievement.support.log.MdcUtil;
import com.xmd.achievement.util.constant.UtilConstant;
import org.apache.rocketmq.common.message.MessageExt;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class MqAspect {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(MqAspect.class);

    /**
     * 执行成功消息
     */
    private final static String EXECUTE_SUCCESS = "success";

    private final static String MQ_INVOKER_NAME = "rocketMq";

    private final static String MQ_INVOKER_IP = "";

    @Pointcut("execution(public * com.xmd.achievement.async.mq.consumer.*.*(..))")
    public void requestMqConfigPointCut() {
    }

    @Around("requestMqConfigPointCut()")
    public Object aroundMq(ProceedingJoinPoint joinPoint) throws Throwable {
        //初始化MDC数据
        MdcUtil.initMdc();
        //添加cat监控
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Object[] params = joinPoint.getArgs();
        String messageId = null;
        String param = null;
        for (Object o : params) {
            if (o instanceof MessageExt) {
                messageId = ((MessageExt) o).getMsgId();
                param = new String(((MessageExt) o).getBody());
            }
        }
        String requestFlag = className + UtilConstant.CAT_SEPARATOR + methodName;
        Transaction t = Cat.newTransaction("SERVICE", requestFlag);
        t.setStatus(Transaction.SUCCESS);
        LogObject logObject = new LogObject().setInvokerName(MQ_INVOKER_NAME)
                .setInvokerIp(MQ_INVOKER_IP)
                .setEventName(requestFlag)
                .setTraceId(MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME))
                .setRequest(JSON.parse(param));
        String msg = EXECUTE_SUCCESS;
        Object proceed = null;
        try {
            //执行方法
            proceed = joinPoint.proceed();
        } catch (Throwable e) {
            msg = e.getMessage();
            t.setStatus(e);
            Cat.logError(e);
            LOGGER.error(requestFlag + "方法异常:", e);
            throw e;
        } finally {
            t.complete();
            logObject.setResponse(proceed)
                    .setMsg(msg + UtilConstant.DATA_SEPARATOR + messageId)
                    .setCostTime(System.currentTimeMillis() - Long.parseLong(MDC.get(UtilConstant.Mdc.START_TIME)));
            LOGGER.info(JSON.toJSONString(logObject));
            MdcUtil.clearMdc();
        }
        return proceed;
    }
}
