package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/14/14:35
 * @since 1.0
 */
@Data
public class InfoSaveMqServeFinishTimeInfoRequest implements Serializable {
    @Schema(description = "infoList", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotEmpty(message = "infoList不能为空")
    private List<MqServeFinishTimeInfoRequest> infoList;
}
