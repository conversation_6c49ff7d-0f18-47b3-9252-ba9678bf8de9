package com.xmd.achievement.service.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/17:28
 * @since 1.0
 */
@Data
public class ReceiveServeFinishInProgressDto implements Serializable {
    private Long orderId;
    private Long productId;
    private String serveNo;
    private Integer acceptSource;
    private List<ServeItem> serveItemList;

    @Data
    public class ServeItem {
        private String serveItemNo;
        private Long specId;
        private Date openTime;
        private Date expirationTime;
        private Integer acceptSource;
    }
}
