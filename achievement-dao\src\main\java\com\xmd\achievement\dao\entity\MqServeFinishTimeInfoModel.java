package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@TableName("mq_serve_finish_time_info")
public class MqServeFinishTimeInfoModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;
    /**
     * 服务编号
     */
    @TableField("serve_no")
    private String serveNo;
    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;
    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;
    /**
     * 服务完成时间
     */
    @TableField("serve_finish_time")
    private Date serveFinishTime;
    /**
     * 任务状态：1-未完成 2-已完成
     */
    @TableField("task_status")
    private Integer taskStatus;
    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;
    /**
     * 失败次数
     */
    @TableField("fail_count")
    private Integer failCount;
    /**
     * 删除标记: 0: 未删除, 1: 删除
     */
    @TableField("delete_flag")
    private Integer deleteFlag;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;
    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 更新人id
     */
    @TableField("update_user_id")
    private String updateUserId;
    /**
     * 更新人名称
     */
    @TableField("update_user_name")
    private String updateUserName;
}