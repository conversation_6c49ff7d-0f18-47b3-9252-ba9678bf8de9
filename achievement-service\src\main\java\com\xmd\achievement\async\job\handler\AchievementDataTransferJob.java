package com.xmd.achievement.async.job.handler;

import cn.hutool.core.util.ObjectUtil;
import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.handler.achievement.AchievementDataTransferHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static com.xmd.achievement.util.date.DateTimeFormatUtil.STANDARD_DATE_TIME;
import static com.xmd.achievement.web.util.DateUtils.stringToDate;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
public class AchievementDataTransferJob {

    @Autowired
    private AchievementDataTransferHandler handler;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.ACHIEVEMENT_DATA_TRANSFER_JOB)
    public ReturnT<String> jobHandler(String param) {
        if (ObjectUtil.isNotEmpty(param)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(STANDARD_DATE_TIME.getFormat());
            LocalDateTime localDateTime = LocalDateTime.parse(param, formatter);
            ZoneId zone = ZoneId.systemDefault();
            Date date = Date.from(localDateTime.atZone(zone).toInstant());
            extracted(date);
        } else {
            extracted(null);
        }
        return ReturnT.SUCCESS;
    }

    private void extracted(Date startTime) {
        log.info("执行AchievementDataTransferJob任务Start...");
        handler.process(startTime);
        log.info("执行AchievementDataTransferJob任务End...");
    }
}
