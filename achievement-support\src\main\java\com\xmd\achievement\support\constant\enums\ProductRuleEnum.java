package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

@Getter
public enum ProductRuleEnum {
    RULE_1("CS0001", "可单独购买及使用"),
    RULE_2("CS0002", "依赖于网站使用"), 
    RULE_3("CS0003", "广告通产品"),
    RULE_4("CS0004", "客户未流失");


    ProductRuleEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private String code;
    private String msg;

    public static ProductRuleEnum getByCode(String code) {
        for (ProductRuleEnum value : ProductRuleEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}