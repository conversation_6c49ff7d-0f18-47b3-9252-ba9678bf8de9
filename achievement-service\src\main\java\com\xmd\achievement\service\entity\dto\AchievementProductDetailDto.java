package com.xmd.achievement.service.entity.dto;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.AchStatus;
import com.xmd.achievement.support.constant.enums.LinkManTypeEnum;
import com.xmd.achievement.support.constant.enums.MainSubEnum;
import com.xmd.achievement.support.constant.enums.PositionNameEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 业绩商品明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Data
@Accessors(chain = true)
public class AchievementProductDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 业绩流水ID
     */
    @Schema(description = "业绩流水ID")
    private Long achievementId;

    /**
     * 商务月ID
     */
    @Schema(description = "商务月ID")
    private Long businessMonthId;

    /**
     * 商务月
     */
    @Schema(description = "商务月")
    private String businessMonth;

    /**
     * 订单明细编号
     */
    @Schema(description = "订单明细编号")
    private String orderProductId;

    /**
     * 服务编号
     */
    @Schema(description = "服务编号")
    private String serveNo;

    /**
     * 商品id
     */
    @Schema(description = "商品id")
    private Long productId;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String productName;

    /**
     * 商品类型
     */
    @Schema(description = "商品类型")
    private String productType;

    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购
     */
    @Schema(description = "业务类型 1=新开，2=续费，3=升级，4=另购")
    private Integer saleType;

    /**
     * 业务类型 1=有效，2=已完成
     */
    @Schema(description = "业务类型 1=有效，2=已完成")
    private Integer status;

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 订单编号
     */
    @Schema(description = "订单来源：1=商务签单，2=官网，3=驾驶舱-PC,4=驾驶舱移动端，5优化师工作台，6商务工作台，7优化师录单")
    private Integer orderSource;


    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private String customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 客户类型 1=新客户，2=老客户，3=非新老
     */
    @Schema(description = "客户类型 1=新客户，2=老客户，3=非新老")
    private Integer customerType;

    @Schema(description = "省份代码")
    private String provinceCode;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市代码")
    private String cityCode;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区县代码")
    private String districtCode;

    @Schema(description = "区县名称")
    private String districtName;

    /**
     * 客户所在区
     */
    @Schema(description = "客户所在区")
    private String customerRegion;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractNo;

    /**
     * 商务ID
     */
    @Schema(description = "商务ID")
    private String businessId;

    /**
     * 商务代表
     */
    @Schema(description = "商务代表")
    private String businessRepresentative;

    /**
     * 主分单人 1=主，2=辅
     */
    @Schema(description = "主分单人 1=主，2=辅")
    private Integer mainSplitPerson;

    /**
     * 公司ID
     */
    @Schema(description = "公司ID")
    private Long companyId;

    /**
     * 公司
     */
    @Schema(description = "公司")
    private String company;

    /**
     * 事业部ID
     */
    @Schema(description = "事业部ID")
    private Long divisionId;

    /**
     * 事业部
     */
    @Schema(description = "事业部")
    private String division;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 部门
     */
    @Schema(description = "部门")
    private String department;

    /**
     * 标准价
     */
    @Schema(description = "标准价")
    private BigDecimal standardPrice;

    /**
     * 应付金额
     */
    @Schema(description = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额")
    private BigDecimal paidAmount;

    /**
     * 业绩生成时间
     */
    @Schema(description = "业绩生成时间")
    private Date createTime;

    /**
     * 签单时间
     */
    @Schema(description = "签单时间")
    private Date signedTime;

    /**
     * 付款时间
     */
    @Schema(description = "付款时间")
    private Date paymentTime;

    /**
     * 首年报价
     */
    @Schema(description = "首年报价")
    private BigDecimal firstYearQuote;

    /**
     * 首年到账金额
     */
    @Schema(description = "首年到账金额")
    private BigDecimal firstYearRevenue;

    /**
     * 续费报价
     */
    @Schema(description = "续费报价")
    private BigDecimal renewalQuote;

    /**
     * 续费到账金额
     */
    @Schema(description = "续费到账金额")
    private BigDecimal renewalRevenue;

    /**
     * 净现金
     */
    @Schema(description = "净现金")
    private BigDecimal netCash;

    /**
     * 商代提成业绩
     */
    @Schema(description = "商代提成业绩")
    private BigDecimal agentCommissionAchievement;

    /**
     * 商代实发提成业绩
     */
    @Schema(description = "商代实发提成业绩")
    private BigDecimal agentActualCommission;

    /**
     * 商代缓发提成业绩
     */
    @Schema(description = "商代缓发提成业绩")
    private BigDecimal agentDeferredCommission;

    /**
     * 部门提成业绩
     */
    @Schema(description = "部门提成业绩")
    private BigDecimal deptCommission;

    /**
     * 事业部提成业绩
     */
    @Schema(description = "事业部提成业绩")
    private BigDecimal divCommission;

    /**
     * 分司提成业绩
     */
    @Schema(description = "分司提成业绩")
    private BigDecimal branchCommission;

    /**
     * 服务完成时间
     */
    @Schema(description = "服务完成时间")
    private Date serveFinishTime;
    /**
     * 折扣比例 (冗余字段)
     */
    private BigDecimal discountRate;
    /**
     * 交付方式: 1-软件交付, 2-服务交付 (冗余字段)
     */
    private Integer deliveryMethod;
    /**
     * 订单类型：1=普通订单，2=折扣订单 (冗余字段)
     */
    private Integer orderType;
    /**
     * 是否网站：0=否 1=是  (冗余字段)
     */
    private Integer siteFlag;



    /**
     * 构建业绩商品明细基础数据
     */
    public static List<AchievementProductDetailDto> buildBaseDtoList(OrderSimpleInfoResponse orderSimpleInfo, OrderSimpleProductResponse productResponse,
                                                                     List<AchievementCategoryDetailDto> categoryList, ProductListForAchievementResponse productListForAchievementResponse,InnerService innerService,IAchievementProductDetailRepository achievementProductDetailRepository,int installmentNum) {
        AchievementProductDetailDto dto = new AchievementProductDetailDto()
                .setAchievementId(IdUtil.getSnowflakeNextId())
                .setOrderProductId(productResponse.getOrderProductCode())
                .setOrderId(orderSimpleInfo.getOrderId())
                .setOrderNo(orderSimpleInfo.getOrderNo())
                .setProductId(productResponse.getProductId())
                .setProductName(productResponse.getProductName())
                .setSaleType(orderSimpleInfo.getOrderSaleType())
                .setStandardPrice(categoryList.stream().map(AchievementCategoryDetailDto::getStandardPrice).reduce(BigDecimal.ZERO, BigDecimal::add))
                .setPayableAmount(categoryList.stream().map(AchievementCategoryDetailDto::getPayableAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .setPaidAmount(categoryList.stream().map(AchievementCategoryDetailDto::getPaidAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .setSignedTime(orderSimpleInfo.getCreateTime())
                .setPaymentTime(productResponse.getUpdateTime())
                .setMainSplitPerson(MainSubEnum.MAIN.getType())
                .setOrderSource(orderSimpleInfo.getOrderSource())
                .setDiscountRate(productResponse.getDiscountRate())
                .setDeliveryMethod(productResponse.getDeliveryMethod())
                .setOrderType(orderSimpleInfo.getOrderType())
                .setStatus(AchStatus.VALID.getType())
                .setCreateTime(new Date())
                .setSiteFlag(productListForAchievementResponse.getSiteFlag())
                .setProductType(productListForAchievementResponse.getLevelThreeCategoryName());

        dto.setCustomerName(orderSimpleInfo.getCustomerName())
                .setCustomerId(orderSimpleInfo.getCustomerId());
        //通过客户id获取商务id
        ProtectByCustomer protectByCustomerId = innerService.getProtectByCustomerId(orderSimpleInfo.getCustomerId());

        if(null == protectByCustomerId){
            //客保关系为空  从订单获取
            log.info("buildBaseDtoList 客保关系为空:{}",orderSimpleInfo.getCustomerId());
            OrderContactResponse contract = orderSimpleInfo.getOrderContactResponseList().stream().filter(c -> LinkManTypeEnum.SALE.getType().equals(c.getType())).findFirst().orElse(null);
            dto.setBusinessId(contract.getUserId());
            dto.setBusinessRepresentative(contract.getName());
        }else{
            //商务id
            String salerId = protectByCustomerId.getSalerId();
            //商务信息
            UserInfoDetailResp userInfoDetail = innerService.getUserInfoDetail(salerId);
            dto.setBusinessId(salerId);
            dto.setBusinessRepresentative(userInfoDetail.getName());
        }

        List<AchievementProductDetailDto> list = ListUtil.toList(dto);
        // 判断是否分单
        boolean splitOrder = orderSimpleInfo.getOrderContactResponseList().stream().anyMatch(c -> Objects.equals(c.getType(), LinkManTypeEnum.SUB_SALE.getType()));
        if (splitOrder) {
            AchievementProductDetailDto subDto = new AchievementProductDetailDto();
            BeanUtils.copyProperties(dto, subDto);
            subDto.setAchievementId(IdUtil.getSnowflakeNextId());
            OrderContactResponse subContract = orderSimpleInfo.getOrderContactResponseList().stream().filter(c -> LinkManTypeEnum.SUB_SALE.getType().equals(c.getType())).findFirst().orElse(null);
            if (subContract == null) {
                throw new RuntimeException("卖方分单联系人不存在 , orderNo:" + orderSimpleInfo.getOrderNo() + "不能创建业绩流水");
            }
            //查询分单人是否是客户代表或商务主管 并且在职 给分单人
            UserInfoDetailResp userInfoDetail = innerService.getUserInfoDetailRedis(subContract.getUserId());
            if(null != userInfoDetail &&  (PositionNameEnum.CUSTOMER_REPRESENTATIVE.getName().equals(userInfoDetail.getPosition()) ||
                    PositionNameEnum.BUSINESS_SUPERVISOR.getName().equals(userInfoDetail.getPosition())) &&
                            NumberConstants.INTEGER_VALUE_0.equals(userInfoDetail.getStatus())){

                subDto.setBusinessId(subContract.getUserId())
                        .setBusinessRepresentative(subContract.getName())
                        .setMainSplitPerson(MainSubEnum.SUB.getType());
            }
            subDto.setMainSplitPerson(MainSubEnum.SUB.getType());
            // 离职或已不是客户代表或商务主管 给主单人
            //上面copy已赋值
            list.add(subDto);
        }
        return list;
    }
}
