package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/15:37
 * @since 1.0
 */
@Data
public class QuerySpecCombinationDetailRequest implements Serializable {
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "ID不能为空")
    private Long combinationId;
}
