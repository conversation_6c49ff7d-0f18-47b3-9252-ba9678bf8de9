package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 业绩商品明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@Accessors(chain = true)
public class AchievementDetailResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 业绩流水ID
     */
    @Schema(description = "业绩流水ID")
    private Long achievementId;

    /**
     * 商务月ID
     */
    @Schema(description = "商务月ID")
    private Long businessMonthId;

    /**
     * 商务月
     */
    @Schema(description = "商务月")
    private String businessMonth;

    /**
     * 订单明细编号
     */
    @Schema(description = "订单明细编号")
    private String orderProductId;

    /**
     * 服务编号
     */
    @Schema(description = "服务编号")
    private String serveNo;

    /**
     * 商品id
     */
    @Schema(description = "商品id")
    private Long productId;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String productName;

    /**
     * 商品类型
     */
    @Schema(description = "商品类型")
    private String productType;

    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购
     */
    @Schema(description = "业务类型 1=新开，2=续费，3=升级，4=另购")
    private String saleType;

    /**
     * 业务类型 1=有效，2=已完成
     */
    @Schema(description = "业务类型 1=有效，2=已完成")
    private String status;

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 订单编号
     */
    @Schema(description = "订单来源：1=商务签单，2=官网，3=驾驶舱-PC,4=驾驶舱移动端，5优化师工作台，6商务工作台，7优化师录单")
    private String orderSource;


    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private String customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 客户类型 1=新客户，2=老客户，3=非新老
     */
    @Schema(description = "客户类型 1=新客户，2=老客户，3=非新老")
    private String customerType;

    /**
     * 客户所在区id
     */
    @Schema(description = "客户所在区id")
    private Long customerRegionId;

    /**
     * 客户所在区
     */
    @Schema(description = "客户所在区")
    private String customerRegion;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractNo;

    /**
     * 商务ID
     */
    @Schema(description = "商务ID")
    private String businessId;

    /**
     * 商务代表
     */
    @Schema(description = "商务代表")
    private String businessRepresentative;

    /**
     * 主分单人 1=主，2=辅
     */
    @Schema(description = "主分单人 1=主，2=辅")
    private String mainSplitPerson;

    /**
     * 公司ID
     */
    @Schema(description = "公司ID")
    private Long companyId;

    /**
     * 公司
     */
    @Schema(description = "公司")
    private String company;

    /**
     * 事业部ID
     */
    @Schema(description = "事业部ID")
    private Long divisionId;

    /**
     * 事业部
     */
    @Schema(description = "事业部")
    private String division;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 部门
     */
    @Schema(description = "部门")
    private String department;

    /**
     * 标准价
     */
    @Schema(description = "标准价")
    private BigDecimal standardPrice;

    /**
     * 应付金额
     */
    @Schema(description = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额")
    private BigDecimal paidAmount;

    /**
     * 业绩生成时间
     */
    @Schema(description = "业绩生成时间")
    private Date createTime;

    /**
     * 签单时间
     */
    @Schema(description = "签单时间")
    private Date signedTime;

    /**
     * 付款时间
     */
    @Schema(description = "付款时间")
    private Date paymentTime;

    /**
     * 首年报价
     */
    @Schema(description = "首年报价")
    private BigDecimal firstYearQuote;

    /**
     * 首年到账金额
     */
    @Schema(description = "首年到账金额")
    private BigDecimal firstYearRevenue;

    /**
     * 续费报价
     */
    @Schema(description = "续费报价")
    private BigDecimal renewalQuote;

    /**
     * 续费到账金额
     */
    @Schema(description = "续费到账金额")
    private BigDecimal renewalRevenue;

    /**
     * 净现金
     */
    @Schema(description = "净现金")
    private BigDecimal netCash;

    /**
     * 商代提成业绩
     */
    @Schema(description = "商代提成业绩")
    private BigDecimal agentCommissionAchievement;

    /**
     * 商代实发提成业绩
     */
    @Schema(description = "商代实发提成业绩")
    private BigDecimal agentActualCommission;

    /**
     * 商代缓发提成业绩
     */
    @Schema(description = "商代缓发提成业绩")
    private BigDecimal agentDeferredCommission;

    /**
     * 部门提成业绩
     */
    @Schema(description = "部门提成业绩")
    private BigDecimal deptCommission;

    /**
     * 事业部提成业绩
     */
    @Schema(description = "事业部提成业绩")
    private BigDecimal divCommission;

    /**
     * 分司提成业绩
     */
    @Schema(description = "分司提成业绩")
    private BigDecimal branchCommission;

    /**
     * 服务完成时间
     */
    @Schema(description = "服务完成时间")
    private Date serveFinishTime;
    /**
     * 折扣比例 (冗余字段)
     */
    private BigDecimal discountRate;
    /**
     * 交付方式: 1-软件交付, 2-服务交付 (冗余字段)
     */
    private Integer deliveryMethod;
    /**
     * 订单类型：1=普通订单，2=折扣订单 (冗余字段)
     */
    private Integer orderType;

    @Schema(description = "业绩统计时间")
    private Date statisticsTime;
}
