package com.xmd.achievement.handler.productRuleConfig;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductRuleConfigModel;
import com.xmd.achievement.service.entity.response.ProductChurnResponse;
import com.xmd.achievement.service.entity.response.RechargeInfoResponse;
import com.xmd.achievement.service.entity.response.ServiceInfoResponse;
import com.xmd.achievement.support.constant.SupportConstant;
import com.xmd.achievement.support.constant.enums.ProductChurnStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/11/15:13
 * @since 1.0
 */
@Component
@Slf4j
public class ProductRuleCs004ServiceImpl implements ProductRuleConfigHandel {
    @Override
    public void calculateProductChurnStatus(AchievementProductRuleConfigModel ruleConfig, AchievementProductDetailModel productDetailModel, RechargeInfoResponse rechargeInfo, Map<String, List<ServiceInfoResponse>> productServiceMap, List<ProductChurnResponse> churnResponseList, List<ProductChurnResponse> unChurnResponseList) {
        if (!ruleConfig.getRuleCode().equals(SupportConstant.CS0004)) {
            return;
        }
        //配置的规则4的商品
        ProductChurnResponse response = new ProductChurnResponse();
        response.setChurnStatus(ProductChurnStatusEnum.NO_CHURN);
        response.setChurnTime(new Date());
        unChurnResponseList.add(response);
        log.info("customerId:{} V4新老客户计算-规则4,productId:{},计算结果:产品未流失", productDetailModel.getCustomerId(), productDetailModel.getProductId());
    }
}
