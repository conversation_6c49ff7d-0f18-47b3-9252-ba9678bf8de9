package com.xmd.achievement.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.rpc.entity.dto.OrgFunctionResp;
import com.xmd.achievement.rpc.entity.dto.OrgInfoNodeResponse;
import com.xmd.achievement.service.IOrganizationReportService;
import com.xmd.achievement.service.entity.request.DailyReportQueryReq;
import com.xmd.achievement.service.entity.request.MonthlyReportQueryReq;
import com.xmd.achievement.service.entity.response.DailyReportResp;
import com.xmd.achievement.service.entity.response.MonthlyReportResp;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 */
@Api(tags = "组织机构日报/月报")
@RestController
@RequestMapping("/organization")
@Slf4j
public class OrganizationReportController {

    @Autowired
    private IOrganizationReportService organizationReportService;

    @ApiOperation(value = "日报查询")
    @PostMapping("/daily/report")
    public WebResult<Page<DailyReportResp>> queryDailyReport(@RequestBody @Valid DailyReportQueryReq req) {
        return WebResult.success(organizationReportService.queryDailyReport(req));
    }

    @ApiOperation(value = "月报查询")
    @PostMapping("/monthly/report")
    public WebResult<Page<MonthlyReportResp>> queryMonthlyReport(@RequestBody @Valid MonthlyReportQueryReq req) {
        return WebResult.success(organizationReportService.queryMonthlyReport(req));
    }

    @ApiOperation(value = "日报查询导出")
    @PostMapping("/daily/report/export")
    public void exportDailyReport(@RequestBody @Valid DailyReportQueryReq req, HttpServletResponse response) {
        organizationReportService.exportDailyReport(req, response);
    }

    @ApiOperation(value = "月报查询导出")
    @PostMapping("/monthly/report/export")
    public void exportMonthlyReport(@RequestBody @Valid MonthlyReportQueryReq req, HttpServletResponse response) {
        organizationReportService.exportMonthlyReport(req, response);
    }

    @ApiOperation(value = "获取当前用户数据权限组织树")
    @GetMapping("/role/list")
    public WebResult<List<OrgInfoNodeResponse>> getOrganizationRoleList() {
        return WebResult.success(organizationReportService.getOrganizationRoleList());
    }

    @ApiOperation(value = "获取体系列表")
    @GetMapping("/function/list")
    public WebResult<List<OrgFunctionResp>> getOrgFunctionList() {
        return WebResult.success(organizationReportService.getOrgFunctionList());
    }


}
