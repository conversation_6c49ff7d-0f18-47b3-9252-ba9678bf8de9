package com.xmd.achievement.web.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

@Slf4j
public class AsyncUtil {

    private static final ConcurrentMap<AsyncScene, ExecutorService> threadPoolCacheMap = new ConcurrentHashMap<>(128);

    static {
        for (AsyncScene scene : AsyncScene.values()) {
            ThreadPoolExecutor executorService = new ThreadPoolExecutor(scene.corePoolSize, scene.maximumPoolSize,
                    scene.keepAliveTime, scene.unit, scene.workQueue, scene.threadFactory, scene.handler);
            executorService.allowCoreThreadTimeOut(true);
            threadPoolCacheMap.put(scene, executorService);
        }
    }

    public static void run(Runnable runnable) {
        run(runnable, AsyncScene.COMMON);
    }

    public static void run(Runnable runnable, AsyncScene asyncScene) {
        if (asyncScene == null) {
            asyncScene = AsyncScene.COMMON;
        }

        CompletableFuture.runAsync(() -> {
            try {
                runnable.run();
            } catch (Throwable t) {
                log.error(t.getMessage(), t);
            }
        }, threadPoolCacheMap.get(asyncScene));
    }

    public enum AsyncScene {
        /**
         * 通用
         */
        COMMON,
        /**
         * 服务中数据计算
         */
        SERVEINPROGRESS,

        /**
         * 订单完成服务计算
         */
        ORDERPAYMENT,
        /**
         * 服务完成时间
         */
        SERVEFINISHTIME,
        /**
         * 三方业绩流水
         */
        THIRDACHIEVEMENT,;

        AsyncScene() {
            this(8, 64, 60, TimeUnit.SECONDS, 1000, new ThreadPoolExecutor.CallerRunsPolicy());
        }

        AsyncScene(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, int workQueueCapacity, RejectedExecutionHandler handler) {
            this.corePoolSize = corePoolSize;
            this.maximumPoolSize = maximumPoolSize;
            this.keepAliveTime = keepAliveTime;
            this.unit = unit;
            this.workQueue = new LinkedBlockingQueue<>(workQueueCapacity);
            this.threadFactory = new ThreadFactoryBuilder().setNameFormat(String.format("async-scene-%s-%%d", this.name().toLowerCase())).build();
            this.handler = handler;
        }

        int corePoolSize;
        int maximumPoolSize;
        long keepAliveTime;
        TimeUnit unit;
        BlockingQueue<Runnable> workQueue;
        ThreadFactory threadFactory;
        RejectedExecutionHandler handler;
    }
}