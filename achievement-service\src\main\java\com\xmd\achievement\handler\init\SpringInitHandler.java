
package com.xmd.achievement.handler.init;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import com.xmd.achievement.service.ICustomerSaasService;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;



@Component
public class SpringInitHandler {

	@Resource
	private ICustomerSaasService customerSaasService;

	@Resource
	private RedissonClient redissonClient;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	/**
	 * Spring初始化完成后执行的方法
	 */
	@EventListener(ApplicationReadyEvent.class)
	public void onApplicationReady(ApplicationReadyEvent event) {
		retryRecalculateHistory();
	}


    private void retryRecalculateHistory() {
        // Spring初始化完成后，检测 REDIS_KEY_RETRY 是否存在，存在则尝试用 Redisson 获取锁，获取到锁后调用 recalculateHistory
		String REDIS_KEY_RETRY = "customer_saas:retry:";
		String retryValue = stringRedisTemplate.opsForValue().get(REDIS_KEY_RETRY);
		if (retryValue == null) {
			return;
		}
		String lockKey = "customer_saas:retry:recalculateHistory:lock";
		RLock lock = null;
		try {
			lock = redissonClient.getLock(lockKey);
			boolean acquired = lock.tryLock(0, -1, TimeUnit.SECONDS);
			if (acquired) {
				customerSaasService.recalculateHistory();
			}
		} catch (Exception e) {
		}
    }
}
