package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.AchievementSegmentModel;
import com.xmd.achievement.dao.repository.IAchievementSegmentRepository;
import com.xmd.achievement.service.ISegmentService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.*;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SegmentServiceImpl implements ISegmentService {
    @Resource
    IAchievementSegmentRepository segmentRepository;

    @Override
    public WebResult<Boolean> saveSegment(SaveSegmentRequest request) {
        //检查名称
        List<AchievementSegmentModel> checkName = segmentRepository.list(
                new LambdaQueryWrapper<AchievementSegmentModel>()
                        .eq(AchievementSegmentModel::getSegmentName, request.getSegmentName()));
        if (ObjectUtil.isNotEmpty(checkName)) {
            return WebResult.error(WebCodeMessageEnum.SEGMENT_NAME_EXIST);
        }

        if (request.getMinValue() >= request.getMaxValue()) {
            return WebResult.error(WebCodeMessageEnum.MIN_THEN_MAX_ERROR);
        }

        //检查最小值区间
        AchievementSegmentModel checkPart = segmentRepository.getOne(
                new LambdaQueryWrapper<AchievementSegmentModel>()
                        .orderByDesc(AchievementSegmentModel::getMaxValue)
                        .last("LIMIT 1"));
        if (ObjectUtil.isNotEmpty(checkPart) && request.getMaxValue() <= checkPart.getMinValue()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_DUPLICATE, "最大值必须大于:" + checkPart.getMaxValue());
        }
        if (ObjectUtil.isNotEmpty(checkPart) && !checkPart.getMaxValue().equals(request.getMinValue())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_DUPLICATE, "最小值必须从:" + checkPart.getMaxValue() + "设置");
        }

        //保存
        AchievementSegmentModel saveModel = new AchievementSegmentModel();
        BeanUtil.copyProperties(request, saveModel);
        saveModel.setSegmentId(IdUtil.getSnowflake().nextId());
        segmentRepository.save(saveModel);
        return WebResult.success(true);
    }

    @Override
    public WebResult<PageResponse<PageQuerySegmentResponse>> pageQuerySegment(PageRequest request) {
        Page<AchievementSegmentModel> page = new Page<>(request.getPageIndex(), request.getPageSize());
        Page<AchievementSegmentModel> result = segmentRepository.page(page, new LambdaQueryWrapper<>());
        PageResponse<PageQuerySegmentResponse> pageResponse = new PageResponse<>(result.getTotal(), result.getCurrent(), result.getSize());
        if (ObjectUtil.isEmpty(result.getRecords())) {
            return WebResult.success(pageResponse);
        }

        List<PageQuerySegmentResponse> list = result.getRecords().stream().map(e -> {
            PageQuerySegmentResponse response = new PageQuerySegmentResponse();
            BeanUtil.copyProperties(e, response);
            return response;
        }).collect(Collectors.toList());
        pageResponse.setList(list);
        return WebResult.success(pageResponse);
    }

    @Override
    public WebResult<QuerySegmentDetailResponse> querySegmentDetail(QuerySegmentDetailResquest request) {
        AchievementSegmentModel model = segmentRepository.getOne(
                new LambdaQueryWrapper<AchievementSegmentModel>()
                        .eq(AchievementSegmentModel::getSegmentId, request.getSegmentId()));
        if (ObjectUtil.isEmpty(model)) {
            return WebResult.success(null);
        }

        QuerySegmentDetailResponse response = new QuerySegmentDetailResponse();
        BeanUtil.copyProperties(model, response);
        return WebResult.success(response);
    }

    @Override
    public WebResult<List<QuerySegmentListResponse>> querySegmentList() {
        List<AchievementSegmentModel> models = segmentRepository.list();
        if (ObjectUtil.isEmpty(models)) {
            return WebResult.success(null);
        }

        List<QuerySegmentListResponse> responses = models.stream().map(e -> {
            QuerySegmentListResponse response = new QuerySegmentListResponse();
            BeanUtil.copyProperties(e, response);
            return response;
        }).collect(Collectors.toList());
        return WebResult.success(responses);
    }

    @Override
    public WebResult<Boolean> updateSegment(UpdateSegmentRequest request) {
        //检查名称
        List<AchievementSegmentModel> checkName = segmentRepository.list(
                new LambdaQueryWrapper<AchievementSegmentModel>()
                        .eq(AchievementSegmentModel::getSegmentName, request.getSegmentName())
                        .ne(AchievementSegmentModel::getSegmentId, request.getSegmentId()));
        if (ObjectUtil.isNotEmpty(checkName)) {
            return WebResult.error(WebCodeMessageEnum.SEGMENT_NAME_EXIST);
        }

        if (request.getMinValue() >= request.getMaxValue()) {
            return WebResult.error(WebCodeMessageEnum.MIN_THEN_MAX_ERROR);
        }

        //检查最大，最小值区间
        AchievementSegmentModel checkMinPart = segmentRepository.getOne(
                new LambdaQueryWrapper<AchievementSegmentModel>()
                        .eq(AchievementSegmentModel::getSegmentId, request.getSegmentId()));
        if (!checkMinPart.getMinValue().equals(request.getMinValue())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_DUPLICATE, "最小值必须从:" + checkMinPart.getMinValue() + "设置");
        }


        //检查最大值区间
        AchievementSegmentModel checkMaxPart = segmentRepository.getOne(
                new LambdaQueryWrapper<AchievementSegmentModel>()
                        .ge(AchievementSegmentModel::getMinValue, checkMinPart.getMaxValue())
                        .orderByAsc(AchievementSegmentModel::getMaxValue)
                        .last("LIMIT 1"));
        if (ObjectUtil.isNotEmpty(checkMaxPart) && request.getMaxValue() > checkMaxPart.getMinValue()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_DUPLICATE, "最大值必须小于:" + checkMaxPart.getMinValue());
        }

        //修改
        AchievementSegmentModel model = segmentRepository.getOne(new LambdaQueryWrapper<AchievementSegmentModel>().eq(AchievementSegmentModel::getSegmentId, request.getSegmentId()));
        AchievementSegmentModel updateModel = new AchievementSegmentModel();
        BeanUtil.copyProperties(request, updateModel);
        updateModel.setId(model.getId());
        segmentRepository.updateById(updateModel);
        return WebResult.success(true);
    }

    @Override
    public AchievementSegmentModel getSegment(BigDecimal amount) {
        return null == amount ? null : segmentRepository.getOne(new LambdaQueryWrapper<AchievementSegmentModel>().le(AchievementSegmentModel::getMinValue, amount).gt(AchievementSegmentModel::getMaxValue, amount));
    }
}
