<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.AchievementProductDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.AchievementProductDetailModel">
        <id column="id" property="id" />
        <result column="achievement_id" property="achievementId" />
        <result column="business_month_id" property="businessMonthId" />
        <result column="business_month" property="businessMonth" />
        <result column="order_product_id" property="orderProductId" />
        <result column="serve_no" property="serveNo" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="product_type" property="productType" />
        <result column="sale_type" property="saleType" />
        <result column="site_flag" property="siteFlag"/>
        <result column="status" property="status" />
        <result column="order_id" property="orderId" />
        <result column="order_no" property="orderNo" />
        <result column="order_source" property="orderSource"/>
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="customer_type" property="customerType" />
        <result column="customer_region" property="customerRegion" />
        <result column="contract_no" property="contractNo" />
        <result column="business_id" property="businessId" />
        <result column="business_representative" property="businessRepresentative" />
        <result column="main_split_person" property="mainSplitPerson" />
        <result column="company_id" property="companyId" />
        <result column="company" property="company" />
        <result column="division_id" property="divisionId" />
        <result column="division" property="division" />
        <result column="dept_id" property="deptId" />
        <result column="department" property="department" />
        <result column="standard_price" property="standardPrice" />
        <result column="payable_amount" property="payableAmount" />
        <result column="paid_amount" property="paidAmount" />
        <result column="discount_rate" property="discountRate"/>
        <result column="delivery_method" property="deliveryMethod"/>
        <result column="order_type" property="orderType"/>
        <result column="create_time" property="createTime" />
        <result column="signed_time" property="signedTime" />
        <result column="payment_time" property="paymentTime" />
        <result column="first_year_quote" property="firstYearQuote" />
        <result column="first_year_revenue" property="firstYearRevenue" />
        <result column="renewal_quote" property="renewalQuote" />
        <result column="renewal_revenue" property="renewalRevenue" />
        <result column="net_cash" property="netCash" />
        <result column="agent_commission_achievement" property="agentCommissionAchievement" />
        <result column="agent_actual_commission" property="agentActualCommission" />
        <result column="agent_deferred_commission" property="agentDeferredCommission" />
        <result column="dept_commission" property="deptCommission" />
        <result column="div_commission" property="divCommission" />
        <result column="branch_commission" property="branchCommission" />
        <result column="serve_finish_time" property="serveFinishTime" />
        <result column="installment_num" property="installmentNum"/>
        <result column="aftersale_order_id" property="aftersaleOrderId" />
        <result column="aftersale_order_no" property="aftersaleOrderNo" />
        <result column="aftersale_contract_no" property="aftersaleContractNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, achievement_id, business_month_id, business_month, order_product_id, serve_no, product_id,site_flag, product_name,
        product_type, sale_type, `status`, order_id, order_no, order_source, customer_id, customer_name, customer_type,
        customer_region_id, customer_region, contract_no, business_id, business_representative, main_split_person, company_id,
        company, division_id, division, dept_id, department, standard_price, payable_amount, paid_amount, discount_rate,
        delivery_method, order_type, create_time, signed_time, payment_time, first_year_quote, first_year_revenue,
        renewal_quote, renewal_revenue, net_cash, agent_commission_achievement, agent_actual_commission, agent_deferred_commission,
        dept_commission, div_commission, branch_commission, serve_finish_time,installment_num, aftersale_order_id, aftersale_order_no, aftersale_contract_no
    </sql>

    <!-- 查询业绩明细的自定义SQL，返回SearchAchievementDetailsResponse字段 -->
    <select id="searchAchievementDetails" resultType="com.xmd.achievement.dao.dto.SearchAchievementDetailsDto">
        SELECT 
            id,
            achievement_id,
            business_month_id,
            business_month,
            sale_type,
            status,
            customer_type,
            business_id,
            business_representative,
            main_split_person,
            company_id,
            company,
            dept_id,
            department,
            order_type,
            create_time,
            statistics_time,
            net_cash,
            agent_commission_achievement,
            agent_actual_commission,
            agent_deferred_commission,
            is_saas
        FROM achievement_product_detail
        WHERE delete_flag = 0
        <if test="businessMonths != null and businessMonths.size > 0">
            AND business_month IN
            <foreach collection="businessMonths" item="month" open="(" separator="," close=")">
                #{month}
            </foreach>
        </if>
        <if test="businessIds != null and businessIds.size > 0">
            AND business_id IN
            <foreach collection="businessIds" item="bid" open="(" separator="," close=")">
                #{bid}
            </foreach>
        </if>
        <if test="companyId != null">
            AND company_id = #{companyId}
        </if>
    </select>

    <!-- selectDistinctRefundOrderProduct --> 

    <select id="selectDistinctRefundOrderProduct" resultType="com.xmd.achievement.dao.entity.AchievementProductDetailModel">
        SELECT DISTINCT order_id, product_id,order_product_id
        FROM achievement_product_detail
        WHERE status = 4 AND delete_flag = 0
    </select>
</mapper>
