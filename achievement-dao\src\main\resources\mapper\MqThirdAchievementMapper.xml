<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.ThirdAchievementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.ThirdAchievementModel">
        <id column="id" property="id" />
        <result column="third_id" property="thirdId" />
        <result column="biz_month_id" property="bizMonthId" />
        <result column="biz_month" property="bizMonth" />
        <result column="order_detail_no" property="orderDetailNo" />
        <result column="serve_no" property="serveNo" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="product_type" property="productType" />
        <result column="is_site" property="isSite" />
        <result column="biz_type" property="bizType" />
        <result column="order_id" property="orderId" />
        <result column="order_record_code" property="orderRecordCode" />
        <result column="data_source" property="dataSource" />
        <result column="cust_id" property="custId" />
        <result column="cust_name" property="custName" />
        <result column="cust_type" property="custType" />
        <result column="province_code" property="provinceCode" />
        <result column="province_name" property="provinceName" />
        <result column="cust_city" property="custCity" />
        <result column="city_name" property="cityName" />
        <result column="district_code" property="districtCode" />
        <result column="district_name" property="districtName" />
        <result column="customer_region" property="customerRegion" />
        <result column="contract_code" property="contractCode" />
        <result column="saler_id" property="salerId" />
        <result column="saler_name" property="salerName" />
        <result column="share_type" property="shareType" />
        <result column="org_id" property="orgId" />
        <result column="area_id" property="areaId" />
        <result column="company" property="company" />
        <result column="bu_id" property="buId" />
        <result column="division" property="division" />
        <result column="dept_id" property="deptId" />
        <result column="department" property="department" />
        <result column="standard_price" property="standardPrice" />
        <result column="singing_amount" property="singingAmount" />
        <result column="actual_account" property="actualAccount" />
        <result column="discount_account" property="discountAccount" />
        <result column="delivery_method" property="deliveryMethod" />
        <result column="order_type" property="orderType" />
        <result column="singing_date" property="singingDate" />
        <result column="to_account_date" property="toAccountDate" />
        <result column="first_standard_account" property="firstStandardAccount" />
        <result column="first_actual_account" property="firstActualAccount" />
        <result column="renew_standard_account" property="renewStandardAccount" />
        <result column="renew_actual_account" property="renewActualAccount" />
        <result column="net_cash_account" property="netCashAccount" />
        <result column="sale_hired_money" property="saleHiredMoney" />
        <result column="relay_sale_hired_money" property="relaySaleHiredMoney" />
        <result column="delay_sale_hired_money" property="delaySaleHiredMoney" />
        <result column="manager_hired_money" property="managerHiredMoney" />
        <result column="current_price" property="currentPrice" />
        <result column="sub_manager_hired_money" property="subManagerHiredMoney" />
        <result column="serve_finish_time" property="serveFinishTime" />
        <result column="data_state" property="dataState" />
        <result column="db_insert_time" property="dbInsertTime" />
        <result column="creater" property="creater" />
        <result column="create_name" property="createName" />
        <result column="db_update_time" property="dbUpdateTime" />
        <result column="updater" property="updater" />
        <result column="update_name" property="updateName" />
        <result column="task_status" property="taskStatus" />
        <result column="fail_reason" property="failReason" />
        <result column="fail_count" property="failCount" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, third_id, biz_month_id, biz_month, order_detail_no, serve_no, product_id, product_name, product_type, is_site, biz_type, order_id, order_record_code, data_source, cust_id, cust_name, cust_type, province_code, province_name, cust_city, city_name, district_code, district_name, customer_region, contract_code, saler_id, saler_name, share_type, org_id, area_id, company, bu_id, division, dept_id, department, standard_price, singing_amount, actual_account, discount_account, delivery_method, order_type, singing_date, to_account_date, first_standard_account, first_actual_account, renew_standard_account, renew_actual_account, net_cash_account, sale_hired_money, relay_sale_hired_money, delay_sale_hired_money, manager_hired_money, current_price, sub_manager_hired_money, serve_finish_time, data_state, db_insert_time, creater, create_name, db_update_time, updater, update_name, task_status, fail_reason, fail_count, delete_flag, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

</mapper>
