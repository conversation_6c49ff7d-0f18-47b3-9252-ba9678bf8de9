<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.PositionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.PositionModel">
        <id column="id" property="id" />
        <result column="position_id" property="positionId" />
        <result column="position_name" property="positionName" />
        <result column="position_code" property="positionCode" />
        <result column="confirmed" property="confirmed" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, position_id, position_name, position_code, confirmed
    </sql>

</mapper>
