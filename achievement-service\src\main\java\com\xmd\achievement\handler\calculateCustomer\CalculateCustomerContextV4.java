package com.xmd.achievement.handler.calculateCustomer;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductRuleConfigModel;
import com.xmd.achievement.dao.repository.IAchievementProductRuleConfigRepository;
import com.xmd.achievement.handler.productRuleConfig.ProductRuleConfigHandel;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.service.IAchievementProductDetailService;
import com.xmd.achievement.service.entity.response.CustomerChurnResponse;
import com.xmd.achievement.service.entity.response.ProductChurnResponse;
import com.xmd.achievement.service.entity.response.RechargeInfoResponse;
import com.xmd.achievement.service.entity.response.ServiceInfoResponse;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.util.enums.MainSplitPersonEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 计算客户类型
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CalculateCustomerContextV4 {
    @Resource
    private InnerService innerService;

    @Resource
    private IAchievementProductDetailService productDetailService;

    @Resource
    private IAchievementProductRuleConfigRepository ruleConfigRepository;

    @Resource
    private List<ProductRuleConfigHandel> productRuleConfigHandels;

    public CustomerType calculateCustomerV4(Integer payType, Integer currentCount, String customerId, List<AchievementProductDetailModel> currentAchList) {
        log.info("customerId:{} V4新老客户计算,payType:{},currentCount:{},currentAchList:{}", customerId, payType, currentCount, JSONUtil.toJsonStr(currentAchList));
        //判断是否为分期
        if (PayTypeEnum.STAGES.getCode().equals(payType) && !currentCount.equals(NumberConstants.INTEGER_VALUE_1)) {
            return CustomerType.OLD;
        }

        //全款和首付逻辑=订单类型为续费、升级、另购时，生成的业绩流水中客户类型为老客户
        boolean allNewOpenType = currentAchList.stream().allMatch(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType()));
        if (allNewOpenType) {
            log.info("customerId:{} V4新老客户计算,订单类型为:{}", customerId, SaleTypeEnum.NEW_OPEN.getMessage());
            return calculateNewTypeCustomer(customerId, currentAchList);
        }
        return CustomerType.OLD;
    }

    private CustomerType calculateNewTypeCustomer(String customerId, List<AchievementProductDetailModel> currentAchList) {
        //1.判断是否客户流失
        CustomerChurnResponse response = innerService.getGetCustomerInfoByCondition(customerId);
        log.info("customerId:{} V4新老客户计算,三方客户流失状态:{}", customerId, JSONUtil.toJsonStr(response));

        //2.当前业绩创建时间
        Date currentAchievementCreateTime = currentAchList.get(0).getCreateTime();

        //3.如果为流失需要继续判断“跨境测”是否为客户流失
        if (CustomerChurnStatusEnum.CHURN_CUSTOMER.equals(response.getChurnStatus())) {
            //(1)继续判断客户流失状态（不是最终，还要判断跨境商品是否存在流失，如果都是流失才算流失，一方不流失按照不是流失客户进行计算）
            ProductChurnResponse productChurnResponse = productChurnStatus(customerId, currentAchievementCreateTime);

            //(2)判断是否为客户流失
            if (ProductChurnStatusEnum.CHURN.equals(productChurnResponse.getChurnStatus()) && null != productChurnResponse.getChurnTime() && null != response.getChurnTime()) {
                response.setChurnStatus(CustomerChurnStatusEnum.CHURN_CUSTOMER);
                response.setChurnTime(DateUtils.isDateAfter(productChurnResponse.getChurnTime(), response.getChurnTime()) ? productChurnResponse.getChurnTime() : response.getChurnTime());
            } else {
                response.setChurnStatus(CustomerChurnStatusEnum.NO_CHURN_CUSTOMER);
            }
        }

        log.info("customerId:{},V4新老客户计算,最终客户流失状态:{}", customerId, JSONUtil.toJsonStr(response));

        //3.未流失客户处理
        if (CustomerChurnStatusEnum.NO_CHURN_CUSTOMER.equals(response.getChurnStatus())) {
            return handlerUnChurnCustomerType(customerId, currentAchList, currentAchievementCreateTime);
        }

        //4.流失客户处理
        if (CustomerChurnStatusEnum.CHURN_CUSTOMER.equals(response.getChurnStatus())) {
            return handlerChurnCustomerType(customerId, currentAchList, response, currentAchievementCreateTime);
        }

        return null;
    }

    private CustomerType handlerChurnCustomerType(String customerId, List<AchievementProductDetailModel> currentAchList, CustomerChurnResponse response, Date currentAchievementCreateTime) {
        //当前时间和流失时间计算是否超过180天
        long distanceDayFor180Days = DateUtils.calculateDifference(currentAchievementCreateTime, response.getChurnTime()).getDays();

        //未超过 走未流失客户逻辑
        if (distanceDayFor180Days <= NumberConstants.INTEGER_VALUE_180) {
            return handlerUnChurnCustomerType(customerId, currentAchList, currentAchievementCreateTime);
        }

        //查询180天前的订单
        Date after180Date = DateUtils.calculateDate(response.getChurnTime(), TimeOperationEnum.ADD, NumberConstants.INTEGER_VALUE_180);
        List<AchievementProductDetailModel> firstAchievementFor180days = productDetailService.selectListByPayTime(customerId, response.getChurnTime(), after180Date, OrderRuleTypeEnum.ASC);

        //计算新老客户
        return calculateCustomer(customerId, firstAchievementFor180days.isEmpty() ? null : firstAchievementFor180days.get(0), currentAchList, currentAchievementCreateTime);
    }

    private CustomerType handlerUnChurnCustomerType(String customerId, List<AchievementProductDetailModel> currentAchList, Date currentAchievementCreateTime) {
        //获取客户之前单子是否有：新开类型 && 主单人 && 有效的 && 新客户
        List<AchievementProductDetailModel> newCustomerInfo = productDetailService.getNewCustomerInfo(customerId, currentAchievementCreateTime);

        //查询是否有新客户类型 有返回老客户
        if (ObjectUtil.isNotEmpty(newCustomerInfo)) {
            return CustomerType.OLD;
        }
        //获取客户的收笔业绩订单
        AchievementProductDetailModel firstAchievement = productDetailService.firstAchievement(customerId, currentAchievementCreateTime);

        //计算新老客户
        return calculateCustomer(customerId, firstAchievement, currentAchList, currentAchievementCreateTime);
    }

    private CustomerType calculateCustomer(String customerId, AchievementProductDetailModel firstAchievement, List<AchievementProductDetailModel> currentAchList, Date currentAchievementCreateTime) {
        BigDecimal currentTotalMoney = BigDecimal.ZERO;
        for (AchievementProductDetailModel model : currentAchList) {
            if (Objects.equals(model.getMainSplitPerson(), MainSplitPersonEnum.MAIN.getCode())) {
                BigDecimal paidAmount = model.getPaidAmount();
                currentTotalMoney = currentTotalMoney.add(paidAmount);
            }
        }

        //首单处理
        if (ObjectUtil.isEmpty(firstAchievement)) {
            return currentTotalMoney.compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) < 0 ? CustomerType.NON_EXISTENT : CustomerType.NEW;
        }
        //计算是否超过90天
        long distanceDay = DateUtils.calculateDifference(currentAchievementCreateTime, firstAchievement.getCreateTime()).getDays();

        //超过90天直接定义为老客户
        if (distanceDay > NumberConstants.INTEGER_VALUE_90) {
            return CustomerType.OLD;
        }

        //未超过90天将90天内的历史金额和当前金额相加
        List<AchievementProductDetailModel> historyProductDetailList = productDetailService.historyTotalMoney(customerId, firstAchievement.getCreateTime(), currentAchievementCreateTime, OrderRuleTypeEnum.ASC);
        BigDecimal historyTotalMoney = historyProductDetailList.stream()
                .map(AchievementProductDetailModel::getPaidAmount)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        BigDecimal totalMoney = currentTotalMoney.add(historyTotalMoney);
        return totalMoney.compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) < 0 ? CustomerType.NON_EXISTENT : CustomerType.NEW;
    }

    private ProductChurnResponse productChurnStatus(String customerId, Date currentAchievementCreateTime) {
        List<AchievementProductDetailModel> productDetailModels = productDetailService.queryCustomerAllProductInfo(customerId, currentAchievementCreateTime);
        List<AchievementProductDetailModel> kjProductDetailModels = productDetailModels.stream().filter(v -> v.getAchievementSource().equals(AchievementSourceEnum.KUAJINFG.getCode())).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(kjProductDetailModels)) {
            ProductChurnResponse response = new ProductChurnResponse();
            response.setChurnStatus(ProductChurnStatusEnum.CHURN);
            response.setChurnTime(new Date());
            return response;
        }

        //1.获取客户下所有的服务信息
        List<ServiceInfoResponse> serviceInfoResponseList = innerService.getServiceInfo(customerId);
        log.info("customerId:{} V4新老客户计算,获取客户服务信息:{}", customerId, JSONUtil.toJsonStr(serviceInfoResponseList));
        if (ObjectUtil.isEmpty(serviceInfoResponseList)) {
            ProductChurnResponse response = new ProductChurnResponse();
            response.setChurnStatus(ProductChurnStatusEnum.NO_CHURN);
            return response;
        }
        Map<String, List<ServiceInfoResponse>> productServiceMap = serviceInfoResponseList.stream().collect(Collectors.groupingBy(ServiceInfoResponse::getProductId));

        //2.获取客户下所有的服务信息
        RechargeInfoResponse rechargeInfo = innerService.getRechargeInfo(customerId);
        log.info("customerId:{} V4新老客户计算,获取客户充值信息:{}", customerId, JSONUtil.toJsonStr(rechargeInfo));

        //3.配置成第四条过期时间怎么
        List<ProductChurnResponse> churnResponseList = new ArrayList<>();
        List<ProductChurnResponse> unChurnResponseList = new ArrayList<>();

        log.info("customerId:{} V4新老客户计算完成,开始计算产品流失状态......", customerId);
        for (AchievementProductDetailModel productDetailModel : kjProductDetailModels) {
            log.info("customerId:{} V4新老客户计算,productId:{}", customerId, productDetailModel.getProductId());
            //查询规则
            AchievementProductRuleConfigModel ruleConfig = ruleConfigRepository.getOne(
                    new LambdaUpdateWrapper<AchievementProductRuleConfigModel>()
                            .eq(AchievementProductRuleConfigModel::getProductId, productDetailModel.getProductId()));
            if (ObjectUtil.isEmpty(ruleConfig)) {
                throw new RuntimeException(String.format("customerId:%s 新老客户计算错误,未配置商品规则，productId:%s,请联系销管人员配置商品流失规则!", customerId, productDetailModel.getProductId()));
            }
            log.info("customerId:{} V4新老客户计算,productId:{},获取置商品规则为:{}", customerId, productDetailModel.getProductId(), ruleConfig.getRuleCode());

            productRuleConfigHandels.forEach(e -> e.calculateProductChurnStatus(ruleConfig, productDetailModel, rechargeInfo, productServiceMap, churnResponseList, unChurnResponseList));
        }

        log.info("customerId:{} V4新老客户计算完成,结束计算产品流失状态.....，churnResponseList:{},unChurnResponseList:{}", customerId, JSONUtil.toJsonStr(churnResponseList), JSONUtil.toJsonStr(unChurnResponseList));

        //将churnResponseList按照流失时间升序排序取第一条返回
        if (ObjectUtil.isNotEmpty(churnResponseList)) {
            churnResponseList.sort(Comparator.comparing(ProductChurnResponse::getChurnTime));
            return churnResponseList.get(0);
        } else {
            return unChurnResponseList.get(0);
        }
    }
}
