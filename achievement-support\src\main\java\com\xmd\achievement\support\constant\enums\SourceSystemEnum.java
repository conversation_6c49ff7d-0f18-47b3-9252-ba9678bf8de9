package com.xmd.achievement.support.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 来源系统枚举
 */
@Getter
@AllArgsConstructor
public enum SourceSystemEnum {

    /**
     * 业绩系统
     */
    ACHIEVEMENT("ACHIEVEMENT", "业绩系统"),

    THIRDACHIEVEMENT("THIRDACHIEVEMENT", "第三方");

    /**
     * 来源系统编码
     */
    private final String code;

    /**
     * 来源系统描述
     */
    private final String desc;
}