package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 第三方业绩状态枚举
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
public enum ThirdAchievementStateEnum {
    /**
     * 有效
     */
    VALID(0, "有效"),
    /**
     * 失效
     */
    INVALID(1, "失效"),
    /**
     * 已退
     */
    REFUNDED(2, "已退"),
    /**
     * 已完成
     */
    COMPLETED(3, "已完成");

    private final Integer code;
    private final String desc;

    ThirdAchievementStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
