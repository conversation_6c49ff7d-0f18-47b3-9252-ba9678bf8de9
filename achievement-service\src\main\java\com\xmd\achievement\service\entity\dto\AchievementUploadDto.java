package com.xmd.achievement.service.entity.dto;

import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvDate;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 业绩文件上传对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class AchievementUploadDto implements Serializable {


    private static final long serialVersionUID = -4065072346617858151L;
    /**
     * 订单明细编号
     */
    @CsvBindByName(column = "orderProductCode")
    private String orderProductCode;

    /**
     * 订单编号
     */
    @CsvBindByName(column = "orderNo")
    private String orderNo;

    /**
     * 规格分类id
     */
    @CsvBindByName(column = "specCategoryId")
    private Long specCategoryId;

    /**
     * 规格id
     */
    @CsvBindByName(column = "specId")
    private Long specId;

    /**
     * 客户类型 1=新客户，2=老客户，3=非新老
     */
    @CsvBindByName(column = "customerType")
    private Integer customerType;

    /**
     * 主辅分单人
     */
    @CsvBindByName(column = "mainSplitPerson")
    private Integer mainSplitPerson;

    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购
     */
    @CsvBindByName(column = "saleType")
    private Integer saleType;

    /**
     * 商务代表ID
     */
    @CsvBindByName(column = "businessId")
    private String businessId;

    /**
     * 业绩统计时间
     */
    @CsvBindByName(column = "statisticsTime")
    @CsvDate("yyyy/MM/dd HH:mm")
    private Date statisticsTime;

    /**
     * 净现金
     */
    @CsvBindByName(column = "netCash")
    private Double netCash;

    /**
     * 商代提成业绩
     */
    @CsvBindByName(column = "saleHiredMoney")
    private Double saleHiredMoney;

    /**
     * 实发商务提成业绩
     */
    @CsvBindByName(column = "relaySaleHiredMoney")
    private Double relaySaleHiredMoney;

    /**
     * 商代缓发提成业绩
     */
    @CsvBindByName(column = "delaySaleHiredMoney")
    private Double delaySaleHiredMoney;

    /**
     * 部门提成业绩
     */
    @CsvBindByName(column = "deptCommission")
    private Double deptCommission;

    /**
     * 事业部提成业绩
     */
    @CsvBindByName(column = "divCommission")
    private Double divCommission;

    /**
     * 分司提成业绩
     */
    @CsvBindByName(column = "branchCommission")
    private Double branchCommission;

    /**
     * 数据更新原因
     */
    private String remark;

    /**
     * 标准价
     */
    @CsvBindByName(column = "standardPrice")
    private Double standardPrice;

    /**
     * 应付金额
     */
    @CsvBindByName(column = "payableAmount")
    private Double payableAmount;

    /**
     * 实付金额
     */
    @CsvBindByName(column = "paidAmount")
    private Double paidAmount;

    /**
     * 首年到账金额
     */
    @CsvBindByName(column = "firstYearRevenue")
    private Double firstYearRevenue;

    /**
     * 首年报价
     */
    @CsvBindByName(column = "firstYearQuote")
    private Double firstYearQuote;

    /**
     * 续费到账金额
     */
    @CsvBindByName(column = "renewalRevenue")
    private Double renewalRevenue;

    /**
     * 续费报价
     */
    @CsvBindByName(column = "renewalQuote")
    private Double renewalQuote;

    /**
     * 业绩状态 1=有效，2=已完成，3=失效，4=退款
     */
    @CsvBindByName(column = "status")
    private Integer status;
}
