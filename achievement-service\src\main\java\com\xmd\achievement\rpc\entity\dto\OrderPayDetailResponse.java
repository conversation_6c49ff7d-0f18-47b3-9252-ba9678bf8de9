package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年11月11日 下午1:42
 */
@Data

public class OrderPayDetailResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;


    /**
     * 订单id
     * */

    private Long orderId;

    /**
     * 订单支付详情id
     */

    private String orderPayDetailId;

    /**
     * 支付类型： 1 钱包支付，2 线上支付
     */

    private Integer payType;

    /**
     * 交易流水号
     */

    private Long transactionId;

    /**
     * 支付金额-单位：元
     */

    private BigDecimal payAmount;

    /**
     * 支付人id
     */

    private Long payUserId;

    /**
     * 支付人名称
     */

    private String payUserName;

    /**
     * 支付状态：3支付完成
     */

    private Integer status;

    /**
     * 删除标记: 0-未删除|1-删除
     */

    private Integer deleteFlag;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 创建人id
     */

    private String createUserId;

    /**
     * 创建人名称
     */

    private String createUserName;

    /**
     * 更新时间
     */

    private Date updateTime;

    /**
     * 更新人id
     */

    private String updateUserId;

    /**
     * 更新人名称
     */

    private String updateUserName;
}
