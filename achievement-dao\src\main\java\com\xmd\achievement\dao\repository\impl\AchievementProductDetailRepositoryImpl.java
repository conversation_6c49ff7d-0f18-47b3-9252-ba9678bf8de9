package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.dto.SearchAchievementDetailsDto;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.BaseModel;
import com.xmd.achievement.dao.mapper.AchievementProductDetailMapper;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业绩商品明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Service
@Slf4j
public class AchievementProductDetailRepositoryImpl extends ServiceImpl<AchievementProductDetailMapper, AchievementProductDetailModel> implements IAchievementProductDetailRepository {

    @Resource
    private AchievementProductDetailMapper achievementProductDetailMapper;

    @Override
    public List<AchievementProductDetailModel> selectAchievementProductByAchievementIds(List<Long> achievementIdList , Integer source) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(achievementIdList),AchievementProductDetailModel::getAchievementId,achievementIdList);
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag,0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed, 0);
        queryWrapper.eq(null != source ,AchievementProductDetailModel::getAchievementSource,source);
        return this.list(queryWrapper);
    }


    @Override
    public void updateAchievementStatisticsTime(AchievementProductDetailModel model) {
        LambdaUpdateWrapper<AchievementProductDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AchievementProductDetailModel::getId, model.getId())
                .eq(AchievementProductDetailModel::getDeleteFlag, 0)
                .set(AchievementProductDetailModel::getDataChangeType, model.getDataChangeType())
                .set(AchievementProductDetailModel::getLatestRemark, model.getLatestRemark())
                .set(AchievementProductDetailModel::getRemarkHistory, model.getRemarkHistory());
        if (ObjectUtils.isNotEmpty(model.getStatisticsTime())) {
            updateWrapper.set(AchievementProductDetailModel::getStatisticsTime, model.getStatisticsTime());
        }
        if (ObjectUtils.isNotEmpty(model.getCustomerType())) {
            updateWrapper.set(AchievementProductDetailModel::getCustomerType, model.getCustomerType());
        }
        if (ObjectUtils.isNotEmpty(model.getBusinessMonth())) {
            updateWrapper.set(AchievementProductDetailModel::getBusinessMonth, model.getBusinessMonth());
            updateWrapper.set(AchievementProductDetailModel::getBusinessMonthId, model.getBusinessMonthId());
        }
        this.update(null, updateWrapper);
    }

    @Override
    public AchievementProductDetailModel getAchievementProductDetailModelById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<AchievementProductDetailModel> selectAchievementProductByBetweenDate(Date startDate , Date endDate,List<Integer> saleTypeList,String customerId,String productId,String productName,Integer installmentNum,Long orderId) {
        List<String> productIdList = Arrays.asList(productId.split(","));

        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();

        if(StringUtils.isEmpty(productName)){
            queryWrapper.between(AchievementProductDetailModel::getStatisticsTime,startDate,endDate);
            queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag,0);
            queryWrapper.eq(AchievementProductDetailModel::getDisplayed,0);
            queryWrapper.in(AchievementProductDetailModel::getSaleType,saleTypeList);
            queryWrapper.eq(AchievementProductDetailModel::getStatus,1);
            queryWrapper.eq(AchievementProductDetailModel::getCustomerId,customerId);
            queryWrapper.in(!CollectionUtils.isEmpty(productIdList),AchievementProductDetailModel::getProductId,productIdList);
            queryWrapper.le(null != installmentNum,AchievementProductDetailModel::getInstallmentNum,installmentNum);
            queryWrapper.ne(null != orderId,AchievementProductDetailModel::getOrderId,orderId);
        }else{
            queryWrapper
                    .between(AchievementProductDetailModel::getStatisticsTime,
                            startDate,
                            endDate)
                    .ne(null != orderId,AchievementProductDetailModel::getOrderId,orderId)
                    .eq(AchievementProductDetailModel::getDeleteFlag, 0)
                    .eq(AchievementProductDetailModel::getDisplayed,0)
                    .in(AchievementProductDetailModel::getSaleType, saleTypeList)
                    .eq(AchievementProductDetailModel::getStatus, 1)
                    .eq(AchievementProductDetailModel::getCustomerId, customerId)
                    .le(null != installmentNum,AchievementProductDetailModel::getInstallmentNum, installmentNum)
                    .and(wrapper -> wrapper
                            .in(!CollectionUtils.isEmpty(productIdList),AchievementProductDetailModel::getProductId,productIdList)
                            .or()
                            .eq(AchievementProductDetailModel::getProductName, productName));
        }

        return this.list(queryWrapper);
    }

    @Override
    public void updateByOrderIdAndProductId(List<Long> orderIdList, List<Long> productIdList) {
        LambdaUpdateWrapper<AchievementProductDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AchievementProductDetailModel::getCalculateAll,0);
        updateWrapper.in(!CollectionUtils.isEmpty(orderIdList),AchievementProductDetailModel::getOrderId,orderIdList);
        updateWrapper.in(!CollectionUtils.isEmpty(productIdList),AchievementProductDetailModel::getProductId,productIdList);
        this.update(updateWrapper);
    }

    @Override
    public List<AchievementProductDetailModel> selectListByDateAndName(String startDateStr, String endDateStr, String websiteName,List<Integer> saleTypeList, List<String> productIdList) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        if(CollectionUtils.isEmpty(productIdList)){
            queryWrapper.in(!CollectionUtils.isEmpty(saleTypeList),AchievementProductDetailModel::getSaleType,saleTypeList);
            queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag,0);
            queryWrapper.eq(AchievementProductDetailModel::getDisplayed,0);
            queryWrapper.eq(AchievementProductDetailModel::getStatus, 1);
            queryWrapper.eq(AchievementProductDetailModel::getProductName,websiteName);
            queryWrapper.eq(AchievementProductDetailModel::getAchievementSource,2);
            queryWrapper.le(AchievementProductDetailModel::getStatisticsTime,endDateStr);
            queryWrapper.ge(AchievementProductDetailModel::getStatisticsTime,startDateStr);
            queryWrapper.le(AchievementProductDetailModel::getInstallmentNum,1);
            return this.list(queryWrapper);
        }
        queryWrapper
                .in(!CollectionUtils.isEmpty(saleTypeList),AchievementProductDetailModel::getSaleType,saleTypeList)
                .le(AchievementProductDetailModel::getStatisticsTime,endDateStr)
                .ge(AchievementProductDetailModel::getStatisticsTime,startDateStr)
                .eq(AchievementProductDetailModel::getDeleteFlag, 0)
                .eq(AchievementProductDetailModel::getDisplayed,0)
                .eq(AchievementProductDetailModel::getStatus, 1)
                .le(AchievementProductDetailModel::getInstallmentNum,1)
                .and(wrapper -> wrapper
                        .in(!CollectionUtils.isEmpty(productIdList),AchievementProductDetailModel::getProductId,productIdList)
                        .or()
                        .eq(AchievementProductDetailModel::getProductName, websiteName));
        return this.list(queryWrapper);
    }

    @Override
    public List<AchievementProductDetailModel> listByUpdateTime(Date startTime) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(BaseModel::getUpdateTime,startTime);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed, 0);
        return this.list(queryWrapper);
    }

    @Override
    public List<AchievementProductDetailModel> listByStatisticsTime(Date startTime, Date endTime) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag, 0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed, 0);
        queryWrapper.le(AchievementProductDetailModel::getStatisticsTime, endTime);
        queryWrapper.ge(AchievementProductDetailModel::getStatisticsTime, startTime);
        return null == startTime || null == endTime ? Collections.emptyList() : this.list(queryWrapper);
    }

    @Override
    public List<AchievementProductDetailModel> listByBusinessMonthId(Long businessMonthId) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag, 0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed, 0);
        queryWrapper.eq(AchievementProductDetailModel::getBusinessMonthId, businessMonthId);
        return this.list(queryWrapper);
    }

    @Override
    public AchievementProductDetailModel selectByOrderProductIdAndInstallmentNum(String orderProductCode, int installmentNum,int mainSplitPerson) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AchievementProductDetailModel::getBusinessId,AchievementProductDetailModel::getBusinessRepresentative);
        queryWrapper.eq(AchievementProductDetailModel::getOrderProductId,orderProductCode);
        queryWrapper.eq(AchievementProductDetailModel::getInstallmentNum,installmentNum);
        queryWrapper.eq(AchievementProductDetailModel::getMainSplitPerson,mainSplitPerson);
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag,0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed, 0);
        queryWrapper.orderByDesc(AchievementProductDetailModel::getId);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public List<AchievementProductDetailModel> selectAchievementProductDetailListByDate(Date startDate, Date endDate,String employeeId) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag,0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed,0);
        queryWrapper.ge(null != startDate , AchievementProductDetailModel::getStatisticsTime,startDate);
        queryWrapper.le(null != endDate ,AchievementProductDetailModel::getStatisticsTime,endDate);
        queryWrapper.eq(StringUtils.isNotEmpty(employeeId) ,AchievementProductDetailModel::getBusinessId,employeeId);
        return this.list(queryWrapper);
    }

    @Override
    public List<AchievementProductDetailModel> selectByOrderIdAndOrderProductId(Long orderId, List<String> orderProductId) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getOrderId, orderId);
        queryWrapper.in(AchievementProductDetailModel::getOrderProductId, orderProductId);
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag, 0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed, 0);
        return this.list(queryWrapper);
    }

    @Override
    public void logicBatchDelete(List<Long> ids) {
        LambdaUpdateWrapper<AchievementProductDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AchievementProductDetailModel::getId, ids);
        updateWrapper.eq(BaseModel::getDeleteFlag, 0);
        updateWrapper.set(AchievementProductDetailModel::getDeleteFlag, 1);
        achievementProductDetailMapper.update(null,updateWrapper);
    }


    @Override
    public List<SearchAchievementDetailsDto> searchAchievementDetails(List<String> businessMonths,
            List<String> businessIds, Long companyId) {
        return achievementProductDetailMapper.searchAchievementDetails(businessMonths, businessIds, companyId);
    }

    @Override
    public List<AchievementProductDetailModel> selectDistinctRefundOrderProduct() {
        return achievementProductDetailMapper.selectDistinctRefundOrderProduct();
    }

    @Override
    public void deleteByAchievementId(List<Long> achivevementIdList) {
        LambdaUpdateWrapper<AchievementProductDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AchievementProductDetailModel::getAchievementId, achivevementIdList);
        updateWrapper.eq(BaseModel::getDeleteFlag, 0);
        updateWrapper.set(AchievementProductDetailModel::getDeleteFlag, 1);
        achievementProductDetailMapper.update(null,updateWrapper);
    }

    @Override
    public void updateCalculateByIdList(List<Long> refundIdList) {
        LambdaUpdateWrapper<AchievementProductDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AchievementProductDetailModel::getId,refundIdList);
        updateWrapper.set(AchievementProductDetailModel::getCalculateAll,0);
        achievementProductDetailMapper.update(null,updateWrapper);
    }

    @Override
    public List<AchievementProductDetailModel> selectAchRefundListByOrderProductId(List<String> orderProductIdList) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag,0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed,0);
        queryWrapper.eq(AchievementProductDetailModel::getStatus, 4);
        queryWrapper.in(AchievementProductDetailModel::getOrderProductId,orderProductIdList);
        return this.list(queryWrapper);
    }
    @Override
    public List<AchievementProductDetailModel> selectByOrderIdAndOrderProductIdAndInstallmentNum(Long orderId,String orderProductCode, int installmentNum,int mainSplitPerson) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getOrderId, orderId);
        queryWrapper.eq(AchievementProductDetailModel::getOrderProductId,orderProductCode);
        queryWrapper.eq(AchievementProductDetailModel::getInstallmentNum,installmentNum);
        queryWrapper.eq(AchievementProductDetailModel::getMainSplitPerson,mainSplitPerson);
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag,0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed,0);
        return this.list(queryWrapper);
    }

    @Override
    public List<AchievementProductDetailModel> selectByOrderNo(String orderNo) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getOrderNo, orderNo);
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag,0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed,0);
        return this.list(queryWrapper);
    }

    @Override
    public List<AchievementProductDetailModel> selectAchievement(List<Long> companyIds, List<Long> deptIds,
            List<Long> businessMonthIds, List<String> businessIds) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag, 0);
        queryWrapper.eq(AchievementProductDetailModel::getDisplayed, 0);
        if (companyIds != null && !companyIds.isEmpty()) {
            queryWrapper.in(AchievementProductDetailModel::getCompanyId, companyIds);
        }
        if (deptIds != null && !deptIds.isEmpty()) {
            queryWrapper.in(AchievementProductDetailModel::getDeptId, deptIds);
        }
        if (businessMonthIds != null && !businessMonthIds.isEmpty()) {
            queryWrapper.in(AchievementProductDetailModel::getBusinessMonthId, businessMonthIds);
        }
        if (businessIds != null && !businessIds.isEmpty()) {
            queryWrapper.in(AchievementProductDetailModel::getBusinessId, businessIds);
        }
        return achievementProductDetailMapper.selectList(queryWrapper);
    }
}