<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.OrganizationDailyReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.OrganizationDailyReportModel">
        <id column="id" property="id" />
        <result column="business_month_id" property="businessMonthId" />
        <result column="business_month" property="businessMonth" />
        <result column="report_create_time" property="reportCreateTime" />
        <result column="organization_id" property="organizationId" />
        <result column="organization_name" property="organizationName" />
        <result column="system_category" property="systemCategory" />
        <result column="market_category" property="marketCategory" />
        <result column="daily_signing_amount" property="dailySigningAmount" />
        <result column="daily_net_cash" property="dailyNetCash" />
        <result column="monthly_signing_amount" property="monthlySigningAmount" />
        <result column="monthly_net_cash" property="monthlyNetCash" />
        <result column="basic_task" property="basicTask" />
        <result column="task_completion_rate" property="taskCompletionRate" />
        <result column="website_net_cash" property="websiteNetCash" />
        <result column="daily_new_customer_count" property="dailyNewCustomerCount" />
        <result column="monthly_new_customer_count" property="monthlyNewCustomerCount" />
        <result column="daily_website_new_customer_count" property="dailyWebsiteNewCustomerCount" />
        <result column="monthly_website_new_customer_count" property="monthlyWebsiteNewCustomerCount" />
        <result column="daily_new_website_count" property="dailyNewWebsiteCount" />
        <result column="monthly_new_website_count" property="monthlyNewWebsiteCount" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="saas_net_cash" property="saasNetCash" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business_month_id, business_month, report_create_time, organization_id, organization_name, system_category, market_category, daily_signing_amount, daily_net_cash, monthly_signing_amount, monthly_net_cash, basic_task, task_completion_rate, website_net_cash, daily_new_customer_count, monthly_new_customer_count, daily_website_new_customer_count, monthly_website_new_customer_count, daily_new_website_count, monthly_new_website_count, create_user_id, create_user_name, create_time, update_user_id, update_user_name, update_time, delete_flag,saas_net_cash
    </sql>

    <!-- 批量插入或更新 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO organization_daily_report (
        business_month_id,
        business_month,
        current_day_date,
        organization_id,
        organization_name,
        system_id,
        system_category,
        market_category_id,
        market_category,
        daily_signing_amount,
        daily_net_cash,
        monthly_signing_amount,
        monthly_net_cash,
        basic_task,
        task_completion_rate,
        website_net_cash,
        daily_new_customer_count,
        monthly_new_customer_count,
        daily_website_new_customer_count,
        monthly_website_new_customer_count,
        daily_new_website_count,
        monthly_new_website_count,
        organization_type,
        create_user_id,
        create_user_name,
        update_user_id,
        update_user_name,
        saas_net_cash
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.businessMonthId},
            #{item.businessMonth},
            #{item.currentDayDate},
            #{item.organizationId},
            #{item.organizationName},
            #{item.systemId},
            #{item.systemCategory},
            #{item.marketCategoryId},
            #{item.marketCategory},
            #{item.dailySigningAmount},
            #{item.dailyNetCash},
            #{item.monthlySigningAmount},
            #{item.monthlyNetCash},
            #{item.basicTask},
            #{item.taskCompletionRate},
            #{item.websiteNetCash},
            #{item.dailyNewCustomerCount},
            #{item.monthlyNewCustomerCount},
            #{item.dailyWebsiteNewCustomerCount},
            #{item.monthlyWebsiteNewCustomerCount},
            #{item.dailyNewWebsiteCount},
            #{item.monthlyNewWebsiteCount},
            #{item.organizationType},
            #{item.createUserId},
            #{item.createUserName},
            #{item.updateUserId},
            #{item.updateUserName},
            #{item.saasNetCash}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        business_month_id = VALUES(business_month_id),
        business_month = VALUES(business_month),
        organization_name = VALUES(organization_name),
        system_id = VALUES(system_id),
        system_category = VALUES(system_category),
        market_category_id = VALUES(market_category_id),
        market_category = VALUES(market_category),
        daily_signing_amount = VALUES(daily_signing_amount),
        daily_net_cash = VALUES(daily_net_cash),
        monthly_signing_amount = VALUES(monthly_signing_amount),
        monthly_net_cash = VALUES(monthly_net_cash),
        basic_task = VALUES(basic_task),
        task_completion_rate = VALUES(task_completion_rate),
        website_net_cash = VALUES(website_net_cash),
        daily_new_customer_count = VALUES(daily_new_customer_count),
        monthly_new_customer_count = VALUES(monthly_new_customer_count),
        daily_website_new_customer_count = VALUES(daily_website_new_customer_count),
        monthly_website_new_customer_count = VALUES(monthly_website_new_customer_count),
        daily_new_website_count = VALUES(daily_new_website_count),
        monthly_new_website_count = VALUES(monthly_new_website_count),
        organization_type = VALUES(organization_type),
        update_user_id = VALUES(update_user_id),
        update_user_name = VALUES(update_user_name),
        update_time = CURRENT_TIMESTAMP,
        saas_net_cash= VALUES(saas_net_cash);
    </insert>

</mapper>
