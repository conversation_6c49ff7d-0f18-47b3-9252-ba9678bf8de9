package com.xmd.achievement.util.enums;
import lombok.Getter;

@Getter
public enum DataChangeTypeEnum {
    NORMAL(1, "正常数据"),
    ARTIFICIAL_NEW(2, "人工新增"),
    ARTIFICIAL_UPDATE(3, "人工修改");

    private final Integer changeType;
    private final String description;

    DataChangeTypeEnum(Integer changeType, String description) {
        this.changeType = changeType;
        this.description = description;
    }
}
