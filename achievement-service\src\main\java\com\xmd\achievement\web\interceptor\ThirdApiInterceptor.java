package com.xmd.achievement.web.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 日志拦截器
 *
 * @version 1.0.0
 * @date 2021/10/25 下午5:19
 */
@Component
@Slf4j
public class ThirdApiInterceptor implements HandlerInterceptor {
    private static final List<String> URL_LIST = Arrays.asList("uc-hb2-kj-ulb.online.local", "************","achievement.gboss.tech");

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            StringBuffer requestUrl = request.getRequestURL();
            String parts = requestUrl.toString().split("://")[1];
            String ipAddress;
            if (!parts.contains(":")) {
                ipAddress = parts.split("/")[0];
            } else {
                ipAddress = parts.split(":")[0];
            }
            log.info("内部调用接口拦截验证,requestUrl:{},ipAddress：{},status:{}", requestUrl, ipAddress, URL_LIST.contains(ipAddress));
            if (!URL_LIST.contains(ipAddress)) {
                writeResponse(response, WebResult.error(WebCodeMessageEnum.ROLE_NO_PERMISSION));
                return false;
            }
            return true;
        } catch (Exception e) {
            log.warn("登录拦截器异常", e);
            return false;
        }
    }


    @SneakyThrows
    private void responseWriter(HttpServletResponse response, String code, String msg) {
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().print(JSONObject.toJSONString(WebResult.error(code, msg)));
        response.getWriter().flush();
    }

    @SneakyThrows
    private static void writeResponse(HttpServletResponse response, WebResult webResult) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().print(JSONObject.toJSONString(webResult));
        response.getWriter().flush();
        response.getWriter().close();
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        MDC.clear();
    }
}
