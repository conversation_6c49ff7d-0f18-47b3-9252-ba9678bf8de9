package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class OrgInfoNodeResponse implements Serializable {


    private static final long serialVersionUID = -3366744410847406736L;
    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long orgId;


    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String name;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Long parentId;

    /**
     * 级别
     */
    @Schema(description = "级别")
    private Integer level;

    /**
     * 1总部；2区域；3分公司；4商业部；5商务组
     */
    @Schema(description = "机构类型1总部；2区域；3分公司；4商业部；5商务组")
    private Integer type;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "是否商务机构:0-否 1-是")
    private Integer commerceFlag;

    @Schema(description = "子部门树")
    private List<OrgInfoNodeResponse> childOrgInfoNodeRespList;

    public List<OrgInfoNodeResponse> getChildOrgInfoNodeRespList() {
        return childOrgInfoNodeRespList != null ? childOrgInfoNodeRespList : Collections.emptyList();
    }
}
