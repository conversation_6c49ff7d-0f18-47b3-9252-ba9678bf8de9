package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.util.Date;

/**
 * 售后操作记录
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class AfterSalesRecordResp {

    /**
     * 售后订单操作日志id
     */
    private Long afterSalesOrderOpeLogId;

    /**
     * 处理时间
     */
    private Date handlerTime;

    /**
     * 操作事项
     */
    private String content;

    /**
     * 售后状态描述
     */
    private String afterSalesStatusDesc;

}
