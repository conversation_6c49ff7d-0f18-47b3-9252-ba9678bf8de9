package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrgInfoResp {

    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long orgId;


    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String name;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Long parentId;

    /**
     * 父级名称
     */
    @Schema(description = "父级名称")
    private String parentName;

    /**
     * 级别
     */
    @Schema(description = "级别")
    private Integer level;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;
}
