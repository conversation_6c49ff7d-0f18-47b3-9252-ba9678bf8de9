package com.xmd.achievement.rpc;

import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.service.entity.response.CustomerChurnResponse;
import com.xmd.achievement.service.entity.response.RechargeInfoResponse;
import com.xmd.achievement.service.entity.response.ServiceInfoResponse;

import java.util.List;
import java.util.Map;

/**
 * 业绩需要调用的二方接口汇总
 *
 * <AUTHOR>
 * @date: 2024/12/18 14:12
 * @version: 1.0.0
 * @return {@link }
 */
public interface InnerService {

    /**
     * 发送微信消息
     * @param achievementId 业绩流水ID
     * @return 发送结果
     */
    Boolean sendWxMessage(Long achievementId);

    CustomerChurnResponse getGetCustomerInfoByCondition(String customerId);

    OrderSimpleInfoResponse getOrderSimpleInfo(Long orderId);

    OrderSimpleInfoResponse getOrderSimpleInfoByOrderNo(String orderNo);

    List<SpecStatisticsResponse> getSpecStatistics(String customerId, List<Long> productIds);

    QueryContractDetailResponse getContractDetail(Long orderId);

    UserInfoDetailResp getUserInfoDetail(String userId);

    UserInfoDetailResp getUserInfoDetailRedis(String userId);

    List<ProductListForAchievementResponse> getProductListForAchievement(List<Long> specIds, Integer specStatus);

    List<ProductDiscountRulesListForAchievementResponse> getProductDiscountRulesListForAchievement(List<Long> productIds);

    QueryCustomerResponse queryCustomerInfo(String customerId);

    List<ServiceInfoResponse>  getServiceInfo(String customerId);

    RechargeInfoResponse getRechargeInfo(String customerId);

    List<ProductForAchievementResponse> getAllProductForAchievement();

    OrgInfoResponse queryOrgInfoById(Long orgId);

    List<OrgInfoNodeResponse> queryKjOrgTree();

    List<OrgInfoResp> queryListOrg(List<Long> orgIds);

    QueryLeveRelationResponse queryLeveRelation(Long orgId);

    List<OrgFunctionResp> queryOrgFunctionList(String requestSource);

    OrgFunctionResp getOrgFunctionById(String orgFunctionId, String source);

    OrgBusinessResponse getOrgBusiness(Long orgId, String dateStr, String source);

    List<ProductInfoForOrderResponse> getProductListForOrder(List<Long> productIds);

    ProtectByCustomer getProtectByCustomerId(String customerId);

    List<String> getCustomerLossDateList(String customerId);

    List<String> getCustomerLossDateListCrm(String customerId);

    List<QueryCustomerAllRechargeTimeDto> queryCustomerAllRechargeTime(String serveNo);

    List<ServiceInfoResponse> getServiceInfoByServeNo(String serveNo);

    List<ServeProductConfigResponse> getConfigList(Long productId);

    /**
     * 根据id查询组织名称，缓存中没有则调用三方接口，缓存调用方自己提供，不是全局缓存
     *
     * @param orgId       组织ID
     * @param description 描述：区域/公司/部门
     * @param cache       缓存map
     * @return {@link String }
     */
    String queryOrgNameByIdWithCache(Long orgId, String description, Map<String, String> cache);

    /**
     * 查询售后订单信息
     *
     * @param aftersaleOrderNo 售后单号
     * @return {@link AfterSalesOrderResp }
     * <AUTHOR>
     * @since 1.0
     */
    AfterSalesOrderResp queryAfterSalesOrderInfo(String aftersaleOrderNo);

    /**
     * 获取售后订单详情
     *
     * @param aftersaleOrderNo 售后单号
     * @return {@link AfterSalesOrderDetailResp }
     * <AUTHOR>
     * @since 1.0
     */
    AfterSalesOrderDetailResp queryAfterSalesOrderDetail(String aftersaleOrderNo);
}
