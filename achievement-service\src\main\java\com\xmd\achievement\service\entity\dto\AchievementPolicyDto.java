package com.xmd.achievement.service.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 业绩政策表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class AchievementPolicyDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业绩政策ID
     */
    @Schema(description = "业绩政策ID")
    private Long policyId;

    /**
     * 签单方式 1=电子签单，2=其他
     */
    @Schema(description = "签单方式 1=电子签单，2=其他")
    private Integer signingMethod;

    /**
     * 业绩计收节点 1=支付完成，2=生产完成
     */
    @Schema(description = "业绩计收节点 1=支付完成，2=生产完成")
    private Integer revenueNode;

    /**
     * 业绩核算比例
     */
    @Schema(description = "业绩核算比例")
    private BigDecimal achievementRatio;

    /**
     * 实发业绩提成比例
     */
    @Schema(description = "实发业绩提成比例")
    private BigDecimal commissionRatio;

    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
    private Long productId;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String productName;


    @Schema(description = "政策明细")
    private List<PolicySpecDetailDto> policySpecDetailList;
}
