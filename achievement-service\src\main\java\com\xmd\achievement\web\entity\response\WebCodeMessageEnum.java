package com.xmd.achievement.web.entity.response;

/**
 * 返回码枚举类
 *
 * @version 1.0.0
 * @date 2021/05/28 下午3:39
 */
public enum WebCodeMessageEnum {

    //成功返回码
    REQUEST_SUCCESS("200", "请求成功"),
    //会员登录异常，特殊处理，兼容之前的校验码
    //异常返回码，从100001开始
    SERVER_INTERNAL_EXCEPTION("100001", "网络超时，请稍后重试哦～"),
    REQUEST_METHOD_EXCEPTION("100002", "请求方式异常"),
    REQUEST_ADDRESS_EXCEPTION("100003", "请求地址不存在"),
    REQUEST_PARAM_EXCEPTION("100004", "请求参数异常"),
    RPC_EXCEPTION("100005", "网络连接超时，请稍后重试哦～"),
    REQUEST_PARAM_NOT_NULL("100006", "请求参数不能为空"),
    REQUEST_PARAM_DUPLICATE("100010", "请求参数冲突"),
    NOT_LOGIN("200001", "未登录或已失效"),
    BIZ_COMMON_FREQUENT_OPERATION_ERROR("200000", "操作频繁, 请稍后再试"),
    ROLE_NO_PERMISSION("300003", "您无权限操作"),
    RPC_SERVER_EXCEPTION("500101", "RPC服务器异常"),

    //============================商品规格40开头==============================
    PRODUCT_COMBINATION_NAME_EXIST("400001", "商品组合规格名称重复"),
    PRODUCT_COMBINATION_EXIST("400002", "组合下已经存在该规格组合"),

    //============================司龄60开头==============================
    SENIORITY_NAME_EXIST("600001", "司龄名称重复"),
    SENIORITY_MIN_PART_EXIST("600002", "最小值已设置区间，不可重复设置"),
    SENIORITY_MAX_PART_EXIST("600003", "最大值已设置区间，不可重复设置"),

    //============================司龄70开头==============================
    SEGMENT_NAME_EXIST("700001", "业绩分段名称重复"),
    SEGMENT_MIN_PART_EXIST("700002", "最小值已设置区间，不可重复设置"),
    SEGMENT_MAX_PART_EXIST("700002", "最大值已设置区间，不可重复设置"),
    MIN_THEN_MAX_ERROR("700003", "最小值必须小于最大值"),
    SEGMENT_MAX_PART_DEL_EXIST("700004", "区间分段数据不可修改，请先删除大于此分段配置后修改"),


    //============================司龄70开头==============================
    MONTH_NAME_EXIST("800001", "商务月已经存在"),
    THE_START_DATE_CANNOT_BE_A_PAST_DATE("800002", "开始日期不能是过去日期"),
    MONTH_HALF_DATE_START_DATE("800003", "月半日期必须大于开始日期"),
    END_DATE_HALF_MONTH_DATE("800004", "结束日期必须大于月半日期"),
    ID_CANNOT_BE_EMPTY("800005", "id不能为空"),
    MONTH_HALF_DATE_CUR_DATE("800006", "月半日期必须大于当前日期"),
    END_DATE_EXIT_MONTH_DATE("800007", "已经存在下一个商务月，不允许修改"),
    EDIT_MID_DATE_TIME_ERROR("800008", "修改月半时间，必须在月半未生效前修改"),
    //============================商品业绩政策90开头==============================
    POLICY_NOT_EXIST("900001", "商品业绩未配置政策"),
    DATA_NOT_EXIST("900002", "数据未落库"),
    DATA_EXIST("900003", "数据已落库"),
    MANUAL_ORDER_ERROR("900003","当前订单商品不满足生产完成条件，请确认！"),

    //============================商品业绩政策90开头==============================
    CRM_CUSTOMER_CHURN_ERROR("1000001", "获取CRM客户流失状态异常"),
    PRODUCT_CONFIG_RULE_ERROR("1000002", "商品配置业绩规则已存在"),
    RECALCULATE_ACHIEVEMENT_ERROR("1000003", "重算订单业绩异常"),

    COMPANY_EXIST_COMMIT_SALE_TASK("1100001", "分公司下存在下属机构下发任务，请先确认后再提交"),
    DEPT_EXIST_COMMIT_SALE_TASK("1100002", "事业部下存在下属机构下发任务，请先确认后再提交"),
    BASIC_TASK_GREATER_THAN_REFERENCE_TASK("1100003", "分司基本任务必须≥分司参考任务！"),
    HIGHER_DATA_NOT_EXIST("1100004", "上级机构数据不存在"),
    NO_DATA_EDIT_PERMISSION("1100005", "无数据编辑权限"),
    PASSED_DATA_EDIT_ERROR("1100006", "已通过状态，不允许编辑"),

    RECALCULATE_ACHIEVEMENT_PAGE_ERROR("1000003", "重算订单业绩异常"),
    RECALCULATE_ACHIEVEMENT_EXCEL_ERROR("1000004", "重算订单业绩【excel导入】异常"),
    BUSINESS_MONTH_FROZEN_ERROR("1000005", "商务月已冻结"),
    FILE_PARSING_ABNORMAL("10000001","解析Excel表格异常"),
    ;

    private final String code;
    private final String msg;

    WebCodeMessageEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
