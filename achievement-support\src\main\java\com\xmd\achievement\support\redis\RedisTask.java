package com.xmd.achievement.support.redis;

import com.xmd.achievement.support.cache.handler.RedisHeartbeatCacheHandler;
import com.xmd.achievement.support.constant.SupportConstant;
import com.xmd.achievement.cache.constant.CacheConstant;
import com.xmd.achievement.util.constant.UtilConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

/**
 * redis定时任务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 17:18
 */
@Configuration
public class RedisTask {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(RedisTask.class);

    @Resource
    private RedisHeartbeatCacheHandler redisHeartbeatCacheHandler;

    /**
     * 发送心跳
     * 解决lettuce没有维护心跳的问题（会导致链接超时）
     *
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Scheduled(fixedDelay = SupportConstant.REDIS_KEEP_HEARTBEAT_TIME)
    @Retryable(value = Exception.class, maxAttempts = UtilConstant.MethodRetry.MAX_ATTEMPTS, backoff = @Backoff(delay = UtilConstant.MethodRetry.DELAY, multiplier = UtilConstant.MethodRetry.MULTIPLIER, maxDelay = UtilConstant.MethodRetry.MAX_DELAY))
    public void sendHeartBeat() {
        redisHeartbeatCacheHandler.get(CacheConstant.CACHE_HEARTBEAT);
    }

    @Recover
    public void recover(Exception exception) {
        LOGGER.debug("redis心跳维持异常，异常信息为:", exception);
    }
}
