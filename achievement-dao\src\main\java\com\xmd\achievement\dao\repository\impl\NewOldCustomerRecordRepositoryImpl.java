package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.NewOldCustomerRecordModel;
import com.xmd.achievement.dao.mapper.NewOldCustomerRecordMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.INewOldCustomerRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 新老客户记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Service
@Slf4j
public class NewOldCustomerRecordRepositoryImpl extends ServiceImpl<NewOldCustomerRecordMapper, NewOldCustomerRecordModel> implements INewOldCustomerRecordRepository {

    @Resource
    private NewOldCustomerRecordMapper newOldCustomerRecordMapper;

}