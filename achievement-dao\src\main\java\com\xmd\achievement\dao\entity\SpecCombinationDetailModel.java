package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 规格组合明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("spec_combination_detail")
public class SpecCombinationDetailModel extends BaseModel  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 组合id
     */
    @TableField("combination_id")
    private Long combinationId;
    /**
     *商品ID
     */
    @TableField("product_id")
    private Long productId;
    /**
     * 规格id
     */
    @TableField("spec_id")
    private Long specId;


    // spec_combination_detail表新增规格下政策性成本字段
    /**
     * 新开政策性成本
     */
    @TableField("new_policy_cost")
    private BigDecimal newPolicyCost;
    /**
     * 续费政策性成本
     */
    @TableField("renewal_policy_cost")
    private BigDecimal renewalPolicyCost;
    /**
     * 升级政策性成本
     */
    @TableField("upgrade_policy_cost")
    private BigDecimal upgradePolicyCost;
    /**
     * 另购政策性成本
     */
    @TableField("additional_policy_cost")
    private BigDecimal additionalPolicyCost;

}