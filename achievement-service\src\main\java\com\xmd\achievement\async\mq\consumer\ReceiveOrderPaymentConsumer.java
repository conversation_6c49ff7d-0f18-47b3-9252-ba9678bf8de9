package com.xmd.achievement.async.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.MqOrderPaymentInfoService;
import com.xmd.achievement.service.entity.dto.ReceiveOrderPaymentDto;
import com.xmd.achievement.support.constant.enums.OrderViewStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "${mq.group.orderChangeStatus}", topic = "${mq.topic.orderChangeStatus}", consumeMode = ConsumeMode.CONCURRENTLY, messageModel = MessageModel.CLUSTERING, consumeThreadMax = 10)
public class ReceiveOrderPaymentConsumer implements RocketMQListener<String> {
    @Resource
    private MqOrderPaymentInfoService orderPaymentInfoService;

    @Override
    public void onMessage(String dataJson) {
        log.info("订单支付完成数据信息处理 接收数据:{}", dataJson);
        try {
            //解析
            ReceiveOrderPaymentDto paymentDto = JSONUtil.toBean(dataJson, ReceiveOrderPaymentDto.class);
            log.info("订单支付完成数据信息处理,数据转换:{}", JSONUtil.toJsonStr(paymentDto));

            //只接受订单完成状态数据
            if (OrderViewStatusEnum.FINISHED.getCode() != paymentDto.getOrderStatus() && OrderViewStatusEnum.PARTIAL_PAYMENT.getCode() != paymentDto.getOrderStatus()) {
                return;
            }
            //落库
            orderPaymentInfoService.saveInfo(paymentDto);
        } catch (Exception e) {
            log.error("订单支付完成数据信息处理异常，message:{},异常信息", dataJson, e);
            throw e;
        }
    }
}
