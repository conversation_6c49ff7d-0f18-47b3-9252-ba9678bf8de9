package com.xmd.achievement.web.config.ppk;

import com.alibaba.fastjson.JSON;
import com.xmd.achievement.support.constant.base.BusinessCodeEnum;
import com.xmd.achievement.support.constant.base.BusinessResult;
import com.xmd.achievement.util.encrypt.RSAUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * RSA请求拦截器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/1/12 3:22 下午
 **/
@Aspect
@Component
@Slf4j
public class PpkRequestProxy {

    @Autowired
    private PpkConfiguration ppkConfiguration;


    @Pointcut("@annotation(com.xmd.achievement.web.config.ppk.PPK)")
    public void ppkRequest() {
    }


    @Around("ppkRequest()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        BusinessResult<String> check = check(joinPoint);
        if (!check.checkSuccess()) {
            return check;
        }
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method targetMethod = methodSignature.getMethod();
        PPK ppk = targetMethod.getAnnotation(PPK.class);
        BusinessResult<Object> resultEntity = decryptData(check.getData(), ppk);
        if (!resultEntity.checkSuccess()) {
            return resultEntity;
        }
        //验证通过则放行
        return handel(joinPoint, JSON.toJSONString(resultEntity.getData()));
    }


    /**
     * 服务通行
     *
     * @param joinPoint
     * <AUTHOR>
     * @date: 2022/1/13 4:01 下午
     * @version: 1.0.0
     * @return: java.lang.Object
     */
    private Object handel(ProceedingJoinPoint joinPoint, String obj) throws Throwable {
        //执行被拦截方法
        Object[] args = new Object[1];
        args[0] = obj;
        Object result = joinPoint.proceed(args);
        return result;

    }


    /**
     * 验证
     *
     * @param joinPoint
     * <AUTHOR>
     * @date: 2022/1/13 4:30 下午
     * @version: 1.0.0
     * @return:
     */
    private BusinessResult<String> check(ProceedingJoinPoint joinPoint) {
        //获取当前请求的非空参数
        Object[] args = joinPoint.getArgs();
        if (ObjectUtils.isEmpty(args) || !Objects.equals(args.length, 1) || (args[0]) == null || !(args[0] instanceof String)) {
            log.warn("ppk--参数错误,encryptData={}", args);
            return BusinessResult.error(BusinessCodeEnum.REQUEST_PARAM_EXCEPTION);
        }
        String params = args[0].toString();
        return BusinessResult.success(params);
    }


    /**
     * 验证解密
     *
     * @param encryptData
     * @param ppk
     * <AUTHOR>
     * @date: 2024/5/28 7:31 下午
     * @version: 1.0.0
     * @return: com.xmd.cs.visitor.ws.support.constant.base.BusinessResult<D>
     */
    private <D> BusinessResult<D> decryptData(String encryptData, PPK ppk) {
        String requestDecryptKey = PpkManager.getRequestDecryptKey(ppk.decryptRequestKey(), ppkConfiguration);
        if (StringUtils.isEmpty(requestDecryptKey)) {
            log.warn("ppk--未授权解密key,encryptData={}", encryptData);
            return BusinessResult.error(BusinessCodeEnum.SERVER_AUTHORIZATION);
        }
        //RSA 数据解密
        String decryptData = RSAUtil.decryptByPrivateKey(requestDecryptKey, encryptData);
        if (StringUtils.isEmpty(decryptData)) {
            log.warn("ppk--数据解密失败,encryptData={}", encryptData);
            return BusinessResult.error(BusinessCodeEnum.REQUEST_PARAM_EXCEPTION);
        }
        log.info("ppk--decryptData={}", decryptData);
        PpkRequest<D> callback = JSON.parseObject(decryptData, PpkRequest.class);
        if (Objects.isNull(callback) || StringUtils.isEmpty(callback.getNonce())) {
            log.warn("ppk--请求参数缺失,decryptData={}", decryptData);
            return BusinessResult.error(BusinessCodeEnum.REQUEST_PARAM_EXCEPTION);
        }
        Class<D> decrypt = ppk.clazz();
        return BusinessResult.success(JSON.parseObject(JSON.toJSONString(callback.getData()), decrypt));
    }
}
