package com.xmd.achievement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.repository.IAchievementCategoryDetailRepository;
import com.xmd.achievement.service.IAchievementCategoryDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 业绩商品分类详情接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AchievementCategoryDetailServiceImpl implements IAchievementCategoryDetailService {
    @Resource
    private IAchievementCategoryDetailRepository achievementCategoryDetailRepository;

    @Override
    public void save(AchievementCategoryDetailModel category) {

    }

    @Override
    public void saveBatch(List<AchievementCategoryDetailModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        achievementCategoryDetailRepository.saveBatch(models, models.size());
    }

    @Override
    public void updateBatch(List<AchievementCategoryDetailModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        achievementCategoryDetailRepository.updateBatchById(models, models.size());
    }

    @Override
    public List<AchievementCategoryDetailModel> listByAchievementId(Long achievementId) {
        if (null == achievementId) {
            return Collections.emptyList();
        }
        return achievementCategoryDetailRepository.list(new LambdaQueryWrapper<AchievementCategoryDetailModel>().eq(AchievementCategoryDetailModel::getAchievementId, achievementId));
    }

    @Override
    public void saveOrUpdateBatch(List<AchievementCategoryDetailModel> categoryList) {
        if (CollectionUtils.isNotEmpty(categoryList)) {
            achievementCategoryDetailRepository.saveOrUpdateBatch(categoryList, categoryList.size());
        }
    }

    @Override
    public List<AchievementCategoryDetailModel> getCategoryByProductCategoryId(Long orderId, Long productId, Long productCategoryId,Integer mainSplitPerson,Integer installmentNum) {
        return achievementCategoryDetailRepository.list(
                new LambdaQueryWrapper<AchievementCategoryDetailModel>()
                        .eq(AchievementCategoryDetailModel::getOrderId, orderId)
                        .eq(AchievementCategoryDetailModel::getProductId, productId)
                        .eq(AchievementCategoryDetailModel::getCategoryId, productCategoryId)
                        .eq(AchievementCategoryDetailModel::getInstallmentNum, installmentNum)
                        .eq(AchievementCategoryDetailModel::getMainSplitPerson, mainSplitPerson));
    }
}
