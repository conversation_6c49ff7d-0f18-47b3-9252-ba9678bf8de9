package com.xmd.achievement.support.log;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 统一日志对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 17:18
 */
public class LogObject {

    @JSONField(ordinal = 1)
    private String invokerName;

    @JSONField(ordinal = 2)
    private String invokerIp;

    @JSONField(ordinal = 3)
    private String eventName;

    @JSONField(ordinal = 4)
    private String traceId;

    @JSONField(ordinal = 5)
    private String msg;

    @JSONField(ordinal = 6)
    private long costTime;

    @JSONField(ordinal = 7)
    private Object request;

    @JSONField(ordinal = 8)
    private Object response;

    public String getInvokerName() {
        return invokerName;
    }

    public LogObject setInvokerName(String invokerName) {
        this.invokerName = invokerName;
        return this;
    }

    public String getInvokerIp() {
        return invokerIp;
    }

    public LogObject setInvokerIp(String invokerIp) {
        this.invokerIp = invokerIp;
        return this;
    }

    public String getEventName() {
        return eventName;
    }

    public LogObject setEventName(String eventName) {
        this.eventName = eventName;
        return this;
    }

    public Object getRequest() {
        return request;
    }

    public LogObject setRequest(Object request) {
        this.request = request;
        return this;
    }

    public Object getResponse() {
        return response;
    }

    public LogObject setResponse(Object response) {
        this.response = response;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public LogObject setMsg(String msg) {
        this.msg = msg;
        return this;
    }


    public long getCostTime() {
        return costTime;
    }

    public LogObject setCostTime(long costTime) {
        this.costTime = costTime;
        return this;
    }

    public String getTraceId() {
        return traceId;
    }

    public LogObject setTraceId(String traceId) {
        this.traceId = traceId;
        return this;
    }
}
