package com.xmd.achievement.handler.statistics;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.enums.AchStatus;
import com.xmd.achievement.support.constant.enums.MainSubEnum;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 *  非续费单处理器</br>
 *  ●非续费单</br>
 * ○所生成的首付款且业务类型为新开或升级且净现金大于0的业绩条数，</br>
 * ○只统付款时生成的业绩流水。</br>
 * ○统计主分单人为主单人的业绩条数，辅分单人业绩则不统计；</br>
 * <AUTHOR>
 * @date: 2024/12/25 14:01
 * @version: 1.0.0
 * @return {@link }
 */
@Service
public class NonRenewalOrdersHandler implements StatisticsHandler {
    @Override
    public void statistics(StatisticsFactInfo factInfo) {
        List<AchievementProductDetailModel> achList = factInfo.getAchList();
        factInfo.setNonRenewalOrders((int) achList.stream().filter(ach -> MainSubEnum.MAIN.getType().equals(ach.getMainSplitPerson())
                        && (ach.getSaleType().equals(OrderSaleTypeEnum.OPEN.getType()) || ach.getSaleType().equals(OrderSaleTypeEnum.UPGRADE.getType()))
                        && ach.getNetCash().compareTo(BigDecimal.ZERO) > 0)
                .count());
    }
}