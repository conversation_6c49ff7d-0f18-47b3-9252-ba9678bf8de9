package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementCategoryDetailSelectModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xmd.achievement.dao.entity.AchievementProductDetailSelectModel;

import java.util.List;

/**
 * <p>
 * 业绩规格分类明细表(查询) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IAchievementCategoryDetailSelectRepository extends IService<AchievementCategoryDetailSelectModel> {

    List<AchievementCategoryDetailSelectModel> selectAchievementCategoryByAchievementIds(List<Long> achievementIdList);

    void insertOrUpdate(List<AchievementCategoryDetailSelectModel> models);
}
