package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/11:29
 * @since 1.0
 */
@Getter
public enum CustomerChurnStatusEnum {
    /**
     * 新客户
     */
    NEW_CUSTOMER(1, "新客户"),
    /**
     * 未流失客户
     */
    NO_CHURN_CUSTOMER(2, "未流失客户"),
    /**
     * 流失客户
     */
    CHURN_CUSTOMER(3, "流失客户");

    private final Integer code;
    private final String msg;

    CustomerChurnStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
