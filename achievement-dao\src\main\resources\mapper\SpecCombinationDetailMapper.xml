<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.SpecCombinationDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.SpecCombinationDetailModel">
        <id column="id" property="id" />
        <result column="combination_id" property="combinationId" />
        <result column="spec_id" property="specId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, combination_id, spec_id
    </sql>

</mapper>
