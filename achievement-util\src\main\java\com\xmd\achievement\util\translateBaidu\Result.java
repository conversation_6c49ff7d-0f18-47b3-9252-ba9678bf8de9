package com.xmd.achievement.util.translateBaidu;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/3/5 3:21 下午
 */
@Getter
@Setter
public class Result<T> implements Serializable {
    /**
     * 返回信息
     */
    private String msg;
    /**
     * 返回状态码
     */
    private Integer code;
    /**
     * 返回具体内容
     */
    private T data;

    public Result() {
    }

    private Result(T data) {
        this.code = ResultEnum.REQUEST_SUCCESS.getCode();
        this.msg = ResultEnum.REQUEST_SUCCESS.getMessage();
        this.data = data;
    }

    private Result(T data, String msg) {
        this.code = ResultEnum.REQUEST_SUCCESS.getCode();
        this.msg = msg;
        this.data = data;
    }

    private Result(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Result(ResultEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMessage();
        }
    }

    private Result(T t, ResultEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMessage();
        }
        data = t;
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(data);
    }

    public static <T> Result<T> success(T data, String msg) {
        return new Result<>(data, msg);
    }

    public static <T> Result<T> success() {
        return success(null);
    }

    public static <T> Result<T> error(ResultEnum cm) {
        return new Result<>(cm);
    }

    public static <T> Result<T> error(ResultEnum cm, String msg) {
        return new Result<>(cm.getCode(), msg);
    }

    public static <T> Result<T> error(Integer code, String msg) {
        return new Result<>(code, msg);
    }

    public static <T> Result<T> error(T data, ResultEnum cm) {
        return new Result<>(data, cm);
    }

    public static <T> Result<T> error(Result<?> Result) {
        return new Result<>(Result.getCode(), Result.getMsg());
    }

    public Boolean checkSuccess() {
        return ResultEnum.REQUEST_SUCCESS.getCode().equals(this.code);
    }

}
