package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class PolicySpecResponse implements Serializable {

    private static final long serialVersionUID = -5422414776118573206L;

    @Schema(description = "流失规则")
    private QueryProductRuleConfigResponse ruleConfig;

    @Schema(description = "政策规格明细")
    private List<PolicySpecDetailResponse> policySpecDetailResponses;
}
