package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xmd.achievement.dao.entity.AchievementProductRuleConfigModel;
import com.xmd.achievement.dao.entity.AchievementProductRuleModel;
import com.xmd.achievement.dao.repository.IAchievementProductRuleConfigRepository;
import com.xmd.achievement.dao.repository.IAchievementProductRuleRepository;
import com.xmd.achievement.service.AchievementProductRuleService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.QueryProductRuleConfigResponse;
import com.xmd.achievement.service.entity.response.QueryRuleProductResponse;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/09:45
 * @since 1.0
 */
@Service
public class AchievementProductRuleServiceImpl implements AchievementProductRuleService {
    @Resource
    private IAchievementProductRuleConfigRepository productRuleConfigRepository;

    @Resource
    private IAchievementProductRuleRepository productRuleRepository;

    public AchievementProductRuleServiceImpl(IAchievementProductRuleConfigRepository productRuleConfigRepository) {
        this.productRuleConfigRepository = productRuleConfigRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebResult<Boolean> addProductRuleConfig(AddProductRuleConfigRequest request) {
        productRuleConfigRepository.remove(
                new LambdaUpdateWrapper<AchievementProductRuleConfigModel>()
                        .eq(AchievementProductRuleConfigModel::getRuleCode, request.getRuleCode()));
        if (ObjectUtil.isEmpty(request.getProductConfigInfoRequests())) {
            return WebResult.success(true);
        }

        List<AchievementProductRuleConfigModel> ruleConfigModelList = new ArrayList<>();
        for (ProductConfigInfoRequest configInfoRequest : request.getProductConfigInfoRequests()) {
            List<AchievementProductRuleConfigModel> list = productRuleConfigRepository.list(
                    new LambdaUpdateWrapper<AchievementProductRuleConfigModel>()
                            .eq(AchievementProductRuleConfigModel::getProductId, configInfoRequest.getProductId())
                            .ne(AchievementProductRuleConfigModel::getRuleCode, request.getRuleCode()));
            if (ObjectUtil.isNotEmpty(list)) {
                return WebResult.error(WebCodeMessageEnum.PRODUCT_CONFIG_RULE_ERROR);
            }

            AchievementProductRuleConfigModel ruleConfig = new AchievementProductRuleConfigModel();
            ruleConfig.setRuleCode(request.getRuleCode());
            ruleConfig.setProductId(configInfoRequest.getProductId());
            ruleConfig.setProductName(configInfoRequest.getProductName());
            ruleConfig.setProductCode(configInfoRequest.getProductCode());
            ruleConfigModelList.add(ruleConfig);
        }
        productRuleConfigRepository.saveBatch(ruleConfigModelList);
        return WebResult.success(true);
    }

    @Override
    public Boolean checkProductRuleStatus(CheckProductRuleStatusRequest request) {
        AchievementProductRuleConfigModel ruleConfigRepositoryOne = productRuleConfigRepository.getOne(
                new LambdaUpdateWrapper<AchievementProductRuleConfigModel>()
                        .eq(AchievementProductRuleConfigModel::getProductId, request.getProductId()));
        return !ObjectUtil.isEmpty(ruleConfigRepositoryOne);
    }

    @Override
    public List<QueryProductRuleConfigResponse> queryProductRuleConfig() {
        List<AchievementProductRuleModel> productRuleModels = productRuleRepository.list(
                new LambdaUpdateWrapper<>());
        if (ObjectUtil.isEmpty(productRuleModels)) {
            return null;
        }

        List<QueryProductRuleConfigResponse> responseList = new ArrayList<>();
        BeanUtil.copyProperties(productRuleModels, responseList);
        return productRuleModels.stream().map(e -> {
            QueryProductRuleConfigResponse response = new QueryProductRuleConfigResponse();
            BeanUtil.copyProperties(e, response);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public List<QueryRuleProductResponse> queryRuleProduct(QueryRuleProductRequest request) {
        List<AchievementProductRuleConfigModel> ruleConfigRepositoryList = productRuleConfigRepository.list(
                new LambdaUpdateWrapper<AchievementProductRuleConfigModel>()
                        .eq(AchievementProductRuleConfigModel::getRuleCode, request.getRuleCode()));
        if (ObjectUtil.isEmpty(ruleConfigRepositoryList)) {
            return new ArrayList<>();
        }
        return ruleConfigRepositoryList.stream().map(e -> {
            QueryRuleProductResponse response = new QueryRuleProductResponse();
            BeanUtil.copyProperties(e, response);
            return response;
        }).collect(Collectors.toList());
    }
}
