package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品列表返回体for业绩
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class ProductListForAchievementResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品 ID")
    private Long productId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品三级分类 ID")
    private Long levelThreeCategoryId;

    @Schema(description = "商品三级分类名称，就是商品类型")
    private String levelThreeCategoryName;

    @Schema(description = "是否网站：0否 1是")
    private Integer siteFlag;

    @Schema(description = "是否电商：0否 1是")
    private Integer commerceFlag;

    @Schema(description = "是否广告：0否 1是")
    private Integer adFlag;

    @Schema(description = "规格id")
    private Long specId;

    @Schema(description = "规格名称")
    private String specName;

    @Schema(description = "计费价格")
    private BigDecimal billingPrice;

    @Schema(description = "续费价格")
    private BigDecimal renewalPrice;

    @Schema(description = "规格分类Id")
    private Long specCategoryId;

    @Schema(description = "规格分类名称")
    private String specCategoryName;

    @Schema(description = "上下架状态:1上架 2下架")
    private Integer specStatus;
}
