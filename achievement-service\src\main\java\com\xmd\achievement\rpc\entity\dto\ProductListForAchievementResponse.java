package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品列表返回体for业绩
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class ProductListForAchievementResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品 ID")
    private Long productId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品三级分类 ID")
    private Long levelThreeCategoryId;

    @Schema(description = "商品三级分类名称，就是商品类型")
    private String levelThreeCategoryName;

    @Schema(description = "是否网站：0否 1是")
    private Integer siteFlag;

    @Schema(description = "是否电商：0否 1是")
    private Integer commerceFlag;

    @Schema(description = "是否广告：0否 1是")
    private Integer adFlag;

    @Schema(description = "规格id")
    private Long specId;

    @Schema(description = "规格名称")
    private String specName;

    @Schema(description = "规格分类Id")
    private Long specCategoryId;

    @Schema(description = "规格分类名称")
    private String specCategoryName;

    @Schema(description = "上下架状态:1上架 2下架")
    private Integer specStatus;

    @Schema(description = "计费类型")
    private Integer billingType;

    @Schema(description = "定价类型")
    private Integer pricingType;

    @Schema(description = "阶梯定价计费类型")
    private Integer tieredPricingBillingType;

    @Schema(description = "阶梯定价计费项ID")
    private Long tieredPricingBillingItemId;

    @Schema(description = "简单规格计费列表")
    private List<SimpleSpecBillingResponse> simpleSpecBillingList;

    @Schema(description = "标准计费价格")
    private StandardBillingPriceResponse standardBillingPrice;

    @Schema(description = "阶梯计费价格")
    private TieredBillingPriceResponse tieredBillingPrice;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "特殊费用标识")
    private Integer specialFeeFlag;

    @Schema(description = "关联规格ID")
    private Long relationSpecId;

    @Schema(description = "价格比例")
    private BigDecimal priceRatio;

    @Schema(description = "计费项类型列表")
    private List<Integer> billingItemTypeList;
}
