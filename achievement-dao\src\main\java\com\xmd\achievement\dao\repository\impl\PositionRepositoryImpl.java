package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.PositionModel;
import com.xmd.achievement.dao.mapper.PositionMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IPositionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 职级表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Service
@Slf4j
public class PositionRepositoryImpl extends ServiceImpl<PositionMapper,PositionModel> implements IPositionRepository {

@Resource
private PositionMapper positionMapper;

}