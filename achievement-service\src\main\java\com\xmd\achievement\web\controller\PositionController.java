package com.xmd.achievement.web.controller;

import com.xmd.achievement.service.PositionService;
import com.xmd.achievement.service.entity.response.QueryPositionListResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 职级接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/10:35
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/position")
@Tag(name = "PC-职级接口")
public class PositionController {

    @Resource
    private PositionService positionService;

    @Operation(summary = "PC-01-查询职级集合")
    @GetMapping("queryPositionList")
    public WebResult<List<QueryPositionListResponse>> queryPositionList() {
        log.info("PC-01-查询职级集合");
        return WebResult.success(positionService.queryPositionList());
    }

}
