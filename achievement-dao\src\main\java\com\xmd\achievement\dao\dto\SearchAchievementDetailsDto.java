package com.xmd.achievement.dao.dto;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;

@Data
public class SearchAchievementDetailsDto {
  /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业绩流水ID
     */
    @TableField("achievement_id")
    private Long achievementId;
    /**
     * 商务月ID
     */
    @TableField("business_month_id")
    private Long businessMonthId;
    /**
     * 商务月
     */
    @TableField("business_month")
    private String businessMonth;
    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购
     */
    @TableField("sale_type")
    private Integer saleType;
    /**
     * 业绩状态 1=有效，2=已完成，3=失效，4=退款
     */
    @TableField("status")
    private Integer status;

    /**
     * 客户类型 1=新客户，2=老客户，3=非新老
     */
    @TableField("customer_type")
    private Integer customerType;
    /**
     * 商务ID 注释
     */
    @TableField("business_id")
    private String businessId;
    /**
     * 商务代表
     */
    @TableField("business_representative")
    private String businessRepresentative;
    /**
     * 主分单人 1=主，2=辅
     */
    @TableField("main_split_person")
    private Integer mainSplitPerson;
    /**
     * 公司ID
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 公司
     */
    @TableField("company")
    private String company;
    /**
     * 部门ID
     */
    @TableField("dept_id")
    private Long deptId;
    /**
     * 部门
     */
    @TableField("department")
    private String department;
   
    /**
     * 订单类型：1=普通订单，2=折扣订单
     */
    @TableField("order_type")
    private Integer orderType;
    /**
     * 业绩生成时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 业绩统计时间
     */
    @TableField("statistics_time")
    private Date statisticsTime;
  
    /**
     * 净现金
     */
    @TableField("net_cash")
    private BigDecimal netCash;
    /**
     * 商代提成业绩
     */
    @TableField("agent_commission_achievement")
    private BigDecimal agentCommissionAchievement;
    /**
     * 商代实发提成业绩
     */
    @TableField("agent_actual_commission")
    private BigDecimal agentActualCommission;
    /**
     * 商代缓发提成业绩
     */
    @TableField("agent_deferred_commission")
    private BigDecimal agentDeferredCommission;

    @TableField("is_saas")
    private Integer isSaas;
}
