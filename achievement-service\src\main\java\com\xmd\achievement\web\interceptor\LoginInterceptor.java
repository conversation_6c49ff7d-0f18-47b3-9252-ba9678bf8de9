package com.xmd.achievement.web.interceptor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.xmd.achievement.rpc.ManagementService;
import com.xmd.achievement.rpc.entity.dto.UserLoginInfoDTO;
import com.xmd.achievement.web.annotate.Permission;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.UserContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 用户拦截器
 *
 * <AUTHOR>
 * @date 2024/11/15
 * @since 1.0
 */
@Slf4j
@Component
public class LoginInterceptor implements HandlerInterceptor {
    @Resource
    private ManagementService managementService;

    /**
     * 定义token参数名称
     */
    @Override
    @SneakyThrows
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        StringBuffer requestUrl = request.getRequestURL();
        // 获取用户上下文
        WebResult<UserLoginInfoDTO> tokenCheckResult = this.initUserContext(request, handler);
        log.info(">>>>>>>>>>>>>>>>>>>>>>>请求地址:{},用户信息:{}", requestUrl, JSONObject.toJSONString(tokenCheckResult));
        if (!tokenCheckResult.checkSuccess()) {
            this.responseWriter(response, tokenCheckResult.getCode(), tokenCheckResult.getMsg());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public void postHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) {
        UserContext.removeUserInfo();
    }


    private WebResult<UserLoginInfoDTO> initUserContext(HttpServletRequest request, Object handler) {
        UserLoginInfoDTO userLoginInfoDTO = new UserLoginInfoDTO();
        String token = request.getHeader("authorization");
        String functionCode = getPermissionFunctionCode(handler);
        if (StrUtil.isBlank(token)) {
            return WebResult.error(WebCodeMessageEnum.NOT_LOGIN);
        }
        userLoginInfoDTO.setToken(token);
        Map<String, String> map = Maps.newHashMap();
        map.put("authorization", token);
        if (StrUtil.isNotBlank(functionCode)) {
            map.put("functionCode", functionCode);
        }
        WebResult<UserLoginInfoDTO> businessResult = managementService.getLoginUserInfo(map);
        if (!businessResult.checkSuccess()) {
            return WebResult.error(businessResult.getCode(), businessResult.getMsg());
        }
        UserLoginInfoDTO userLoginInfoDtoResult = businessResult.getData();
        //如果无权限直接返回
        if (!userLoginInfoDtoResult.getCheckPermissionFlag()) {
            return WebResult.error(WebCodeMessageEnum.ROLE_NO_PERMISSION);
        }
        UserContext.setUserInfo(userLoginInfoDtoResult);
        return WebResult.success(userLoginInfoDtoResult);
    }

    @SneakyThrows
    private void responseWriter(HttpServletResponse response, String code, String msg) {
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().print(JSONObject.toJSONString(WebResult.error(code, msg)));
        response.getWriter().flush();
    }

    private String getPermissionFunctionCode(Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod method = (HandlerMethod) handler;
            // 判断方法或者类上是否存在@Permission
            Permission permission = method.getMethodAnnotation(Permission.class);
            if (permission == null) {
                permission = method.getMethod().getDeclaringClass().getAnnotation(Permission.class);
            }
            if (permission != null) {
                // 从注解上拿到功能码
                return permission.code();
            }
        }
        return null;
    }
}
