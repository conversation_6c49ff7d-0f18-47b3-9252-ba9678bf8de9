package com.xmd.achievement.service.entity.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 业绩黑名单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
public class AchievementBlacklistRequest extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 订单编号
     */
    private String orderNo;
}

