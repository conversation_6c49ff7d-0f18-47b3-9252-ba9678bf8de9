package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.ThirdAchievementModel;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.*;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 商品业绩服务接口
 *
 * <AUTHOR>
 * @date: 2024/12/19 15:11
 * @version: 1.0.0
 * @return {@link }
 */
public interface IAchievementService {
    PageResponse<AchievementProductDetailResponse> achievementProductList(AchievementProductPageRequest pageRequest);

    AchievementDetailResponse getAchievementProductDetail(Long achievementId);

    AchievementProductDetailModel getAchievementProductDetailModel(Long achievementId);

    List<AchievementCategoryResponse> getAchievementCategoryDetail(Long achievementId);

    List<AchievementSpecDetailResponse> getAchievementSpecDetail(Long achievementCategoryId,String orderProductId);

    PageResponse<BusinessAchievementResponse> businessAchievementList(BusinessSearchRequest pageRequest);

    void exportBusinessAchievement(BusinessSearch search, HttpServletResponse response);

    void updateServeFinishTime(Long orderId, Long productId, String serveNo, Date serveFinishTime);

    void handelThirdAchievement(ThirdAchievementModel model, MqOrderPaymentInfoModel mqModel);

    boolean selectAchievementInfo(Long orderId, String orderProductId);

    void updateAchievementAndSpec(AchievementUpdateRequest updateRequest);

    /**
     * 解析重算订单明细编号文件
     * @throws Exception
     */
    List<OrderProductResponse> importOrderProduct(MultipartFile file) throws Exception;

    /**
     * 根据订单编号和订单明细编号重算业绩
     */
    void recalculateOrderProductAchievement(RecalculateOrderProductRequest request);

    String[] recalculateAchievementByExcel(MultipartFile file);

    void recalculateAchievement(String[] orderNos, String orderNoStr);

    /**
     * 业绩开放接口 查询业绩
     * @return
     */
    List<OpenAchievementResponse> openAchievementList(OpenAchievementRequest request);


    /**
     * 删除商品业绩列表接口
     * @param request
     * @return
     */
    WebResult<Boolean> deleteAchievement(@Valid DeleteAchievementProductRequest request);

    /**
     * 重算退款
     * @param achievementId 业绩ID
     * @return 处理结果
     */
    Boolean recalculateRefund(Long achievementId);
}
