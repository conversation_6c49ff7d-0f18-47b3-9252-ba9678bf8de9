package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 业绩规格分类明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("achievement_category_detail")
public class AchievementCategoryDetailModel extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业绩规格分类id
     */
    @TableField("achievement_category_id")
    private Long achievementCategoryId;
    /**
     * 业绩流水ID
     */
    @TableField("achievement_id")
    private Long achievementId;
    /**
     * 规格分类ID
     */
    @TableField("category_id")
    private Long categoryId;
    /**
     * 规格分类名称
     */
    @TableField("category_name")
    private String categoryName;
    /**
     * 标准价
     */
    @TableField("standard_price")
    private BigDecimal standardPrice;
    /**
     * 应付金额
     */
    @TableField("payable_amount")
    private BigDecimal payableAmount;
    /**
     * 实付金额
     */
    @TableField("paid_amount")
    private BigDecimal paidAmount;
    /**
     * 首年报价
     */
    @TableField("first_year_quote")
    private BigDecimal firstYearQuote;
    /**
     * 首年到账金额
     */
    @TableField("first_year_income")
    private BigDecimal firstYearIncome;
    /**
     * 续费报价
     */
    @TableField("renewal_quote")
    private BigDecimal renewalQuote;
    /**
     * 续费到账金额
     */
    @TableField("renewal_income")
    private BigDecimal renewalIncome;
    /**
     * 净现金
     */
    @TableField("net_cash")
    private BigDecimal netCash;
    /**
     * 商代提成业绩
     */
    @TableField("agent_comm_achv")
    private BigDecimal agentCommAchv;
    /**
     * 商代实发提成业绩
     */
    @TableField("agent_act_comm_achv")
    private BigDecimal agentActCommAchv;
    /**
     * 商代缓发提成业绩
     */
    @TableField("agent_def_comm_achv")
    private BigDecimal agentDefCommAchv;
    /**
     * 部门提成业绩
     */
    @TableField("dept_comm_achv")
    private BigDecimal deptCommAchv;
    /**
     * 事业部提成业绩
     */
    @TableField("bu_comm_achv")
    private BigDecimal buCommAchv;
    /**
     * 分公司提成业绩
     */
    @TableField("branch_comm_achv")
    private BigDecimal branchCommAchv;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 业务类型 1=有效，2=已完成
     */
    @TableField("status")
    private Integer status;

    /**
     * 主分单人 1=主，2=辅
     */
    @TableField("main_split_person")
    private Integer mainSplitPerson;

    /**
     * 数据改动标识  1 - 正常数据、2 - 人工新增、3 - 人工修改
     */
    @TableField("data_change_type")
    private Integer dataChangeType;

    /**
     * 分期期数
     */
    @TableField("installment_num")
    private Integer installmentNum;

    @TableField(exist = false)
    private Long groupById;
}