package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementCategoryDetailSelectModel;
import com.xmd.achievement.dao.mapper.AchievementCategoryDetailSelectMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IAchievementCategoryDetailSelectRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 业绩规格分类明细表(查询) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@Slf4j
public class AchievementCategoryDetailSelectRepositoryImpl extends ServiceImpl<AchievementCategoryDetailSelectMapper,AchievementCategoryDetailSelectModel> implements IAchievementCategoryDetailSelectRepository {

@Resource
private AchievementCategoryDetailSelectMapper achievementCategoryDetailSelectMapper;

    @Override
    public List<AchievementCategoryDetailSelectModel> selectAchievementCategoryByAchievementIds(List<Long> achievementIdList) {
        LambdaQueryWrapper<AchievementCategoryDetailSelectModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AchievementCategoryDetailSelectModel::getAchievementId,achievementIdList);
        queryWrapper.eq(AchievementCategoryDetailSelectModel::getDeleteFlag,0);
        return this.list(queryWrapper);

    }

    @Override
    public void insertOrUpdate(List<AchievementCategoryDetailSelectModel> models) {
        if (ObjectUtils.isNotEmpty(models)) {
            achievementCategoryDetailSelectMapper.batchInsertOrUpdate(models);
        }
    }

}