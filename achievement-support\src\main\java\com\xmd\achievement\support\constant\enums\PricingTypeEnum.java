package com.xmd.achievement.support.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 定价类型枚举
 * 
 * <AUTHOR> Generated
 * @date 2025-01-30
 */
@Getter
@AllArgsConstructor
public enum PricingTypeEnum {

    /**
     * 标准定价
     */
    STANDARD(1, "标准定价"),

    /**
     * 阶梯定价
     */
    TIERED(2, "阶梯定价");

    /**
     * 定价类型代码
     */
    private final Integer type;

    /**
     * 定价类型描述
     */
    private final String desc;

    /**
     * 根据类型获取枚举
     *
     * @param type 定价类型
     * @return PricingTypeEnum
     */
    public static PricingTypeEnum fromType(Integer type) {
        if (type == null) {
            return null;
        }
        for (PricingTypeEnum pricingType : PricingTypeEnum.values()) {
            if (pricingType.getType().equals(type)) {
                return pricingType;
            }
        }
        return null;
    }

    /**
     * 根据类型获取描述
     *
     * @param type 定价类型
     * @return 描述信息
     */
    public static String getDescByType(Integer type) {
        PricingTypeEnum pricingType = fromType(type);
        return pricingType != null ? pricingType.getDesc() : null;
    }

    /**
     * 判断是否为标准定价
     *
     * @param type 定价类型
     * @return true-标准定价，false-非标准定价
     */
    public static boolean isStandard(Integer type) {
        return STANDARD.getType().equals(type);
    }

    /**
     * 判断是否为阶梯定价
     *
     * @param type 定价类型
     * @return true-阶梯定价，false-非阶梯定价
     */
    public static boolean isTiered(Integer type) {
        return TIERED.getType().equals(type);
    }
}
