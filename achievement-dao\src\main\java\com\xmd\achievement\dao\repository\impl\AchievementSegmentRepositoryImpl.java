package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.AchievementSegmentModel;
import com.xmd.achievement.dao.mapper.AchievementSegmentMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IAchievementSegmentRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 业绩分段表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@Slf4j
public class AchievementSegmentRepositoryImpl extends ServiceImpl<AchievementSegmentMapper, AchievementSegmentModel> implements IAchievementSegmentRepository {

    @Resource
    private AchievementSegmentMapper achievementSegmentMapper;

}