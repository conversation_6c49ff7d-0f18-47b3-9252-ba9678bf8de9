package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 删除标记枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 13:57
 **/
@Getter
public enum DataPermitEnum {
    /**
     * 无权限
     */
    NO_PERMIT(1, "无权限"),
    /**
     * 本人及公司
     */
    SELF_AND_COMPANY(2, "本人及公司"),
    /**
     * 本部门
     */
    DEPT(3, "本部门"),
    /**
     * 事业部
     */
    CAREER(4, "事业部"),
    /**
     * 本公司
     */
    COMPANY(5, "本公司"),
    /**
     * 不需要权限（所有权限）
     */
    ALL(6, "不需要权限（所有权限）"),

    SELF(7,"本人")   ;

    private final Integer code;
    private final String msg;

    DataPermitEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static DataPermitEnum getDataPermitEnumByCode(Integer code) {
        for (DataPermitEnum value : DataPermitEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }

}