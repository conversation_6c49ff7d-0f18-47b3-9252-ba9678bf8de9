package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * whatsapp计费标准
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("test")
public class TestModel extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * ID（主键）
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

}