package com.xmd.achievement.util.string;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * 字符串处理工具
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class StringUtils {

    /**
     * 将异常的堆栈信息转成字符串
     * @return
     */
    public static String getStackTraceAsString(Throwable throwable) {
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        throwable.printStackTrace(printWriter);
        return stringWriter.toString();
    }
}
