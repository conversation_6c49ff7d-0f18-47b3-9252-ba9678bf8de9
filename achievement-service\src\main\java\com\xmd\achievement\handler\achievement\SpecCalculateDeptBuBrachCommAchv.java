package com.xmd.achievement.handler.achievement;

import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 *
 *  //部门提成业绩=商代提成业绩
 *  //事业部提成业绩=商代提成业绩
 *  //公司提成业绩=商代提成业绩
 * <AUTHOR>
 * @date: 2024/12/24 14:10
 */
@Slf4j
@Service
public class SpecCalculateDeptBuBrachCommAchv implements CalculateAmountHandler {
    @Override
    public void calculate(CalculateFactInfo factInfo) {
        try {
            AchievementSpecDetailModel spec = factInfo.getSpec();
            BigDecimal amount = spec.getAgentCommAchv();
            spec.setDeptCommAchv(amount);
            spec.setBuCommAchv(amount);
            spec.setBranchCommAchv(amount);
        } finally {
            log.warn("首年到账金额业绩 规格id:{},参数:{}", factInfo.getSpec().getSpecId(), JSON.toJSONString(factInfo));
        }
    }
}
