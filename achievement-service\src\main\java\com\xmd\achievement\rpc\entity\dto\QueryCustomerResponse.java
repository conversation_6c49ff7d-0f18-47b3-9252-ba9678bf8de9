package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/13/13:41
 * @since 1.0
 */
@Data
public class QueryCustomerResponse implements Serializable {

    @Schema(description = "客户ID")
    private String customerId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "客户类型")
    private Integer customerType;

    @Schema(description = "销售人员ID")
    private String salerId;

    @Schema(description = "部门ID")
    private String deptId;

    @Schema(description = "子公司ID")
    private String subId;

    @Schema(description = "区域ID")
    private String areaId;

    @Schema(description = "省份代码")
    private String provinceCode;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市代码")
    private String cityCode;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区县代码")
    private String districtCode;

    @Schema(description = "区县名称")
    private String districtName;

}
