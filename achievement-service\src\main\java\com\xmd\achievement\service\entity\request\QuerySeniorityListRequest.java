package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/17:30
 * @since 1.0
 */
@Data
public class QuerySeniorityListRequest implements Serializable {
    @Schema(description = "司龄ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "司龄ID不能为空")
    private Long seniorityId;
}
