package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.ThirdAchievementModel;
import com.xmd.achievement.service.entity.request.ThirdPerformanceRequest;
import com.xmd.achievement.support.constant.enums.ThirdSourceEnum;

import java.util.Date;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/15/10:18
 * @since 1.0
 */
public interface ThirdAchievementService {
    /**
     * 三方流水信息保存
     *
     * @param request 请求参数
     * @return Boolean
     */
    Boolean flowing(ThirdPerformanceRequest request, ThirdSourceEnum thirdSourceEnum);

    /**
     * 查询可执行的任务
     *
     * @return List<ThirdAchievementModel>
     */
    List<ThirdAchievementModel> queryExecuteTask();

    /**
     * excel解析
     *
     * @return List<ThirdAchievementModel>
     */
    void excelFlowing(List<ThirdPerformanceRequest> requests,ThirdSourceEnum thirdSourceEnum);

    /**
     * 按任务id获取任务
     *
     * @param taskId 任务ID
     * @return {@link ThirdAchievementModel }
     */
    ThirdAchievementModel getInfoByTaskId(Long taskId);

    ThirdAchievementModel getInfoByThirdId(String thirdId);

    /**
     * 按订单号获取任务列表
     *
     * @param orderNo 订单号
     * @return {@link List<ThirdAchievementModel> }
     */
    List<ThirdAchievementModel> getInfoByOrderNo(String orderNo);

    /**
     * 同步数据
     *
     * @return List<ThirdAchievementModel>
     */
    void syncDtae();

    void updatePayTime();

    /**
     * 根据订单记录编号获取最小的到账日期
     *
     * @param orderRecordCode 订单记录编号
     * @return 最小的到账日期
     */
    Date getMinToAccountDateByOrderRecordCode(String orderRecordCode);
}
