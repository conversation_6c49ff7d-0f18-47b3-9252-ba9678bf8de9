package com.xmd.achievement.util.http;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Slf4j
public class RestTemplateUtil {

    public static RestTemplate restTemplate;

    private static final String ycloud_api_key = "b21548f0d4163e3296f410e4a9af8c27";


    public static RestTemplate getProxyTemplate() {
        if (restTemplate == null) {
            synchronized (RestTemplateUtil.class) {
                restTemplate = new RestTemplate();
                SimpleClientHttpRequestFactory clientHttpRequestFactory = new SimpleClientHttpRequestFactory();
                clientHttpRequestFactory.setConnectTimeout(30000);
                clientHttpRequestFactory.setReadTimeout(20000);
                restTemplate.setRequestFactory(clientHttpRequestFactory);
            }
        }
        return restTemplate;
    }

    public static <T> T getForObj(String url, Map<String, String> paramMap, Class<T> responseType) {
        log.info("请求地址 url： {}", url);
        log.info("请求参数 param: {}", JSONObject.toJSONString(paramMap));
        return getProxyTemplate().getForObject(url, responseType, paramMap);
    }

    public static HttpResult getForObjWithJSONObject(String url, Map<String, String> paramMap) {
        log.info("请求地址 url： {}", url);
        log.info("请求参数 param: {}", JSONObject.toJSONString(paramMap));
        return getProxyTemplate().getForObject(url, HttpResult.class, paramMap);
    }

    public static void delete(String url, Map<String, String> paramMap) {
        log.info("请求地址 url： {}", url);
        log.info("请求参数 param: {}", JSONObject.toJSONString(paramMap));
        getProxyTemplate().delete(url, paramMap);
    }


    public static String getForObjWhatsApp(String url, Map<String, String> paramMap) {
        log.info("请求地址 url： {}", url);
        log.info("请求参数 param: {}", JSONObject.toJSONString(paramMap));
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-Key", ycloud_api_key);
        // 创建HttpEntity对象，设置请求头和请求体
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<JSONObject> responseEntity = getProxyTemplate().exchange(url, HttpMethod.GET, requestEntity, JSONObject.class);
        return responseEntity.getBody().getString("te");
    }

    public static String postForObjWhatsApp(String url, Map<String, String> paramMap) {
        log.info("请求地址 url： {}", url);
        log.info("请求参数 param: {}", JSONObject.toJSONString(paramMap));
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-Key", ycloud_api_key);
        // 创建HttpEntity对象，设置请求头和请求体
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<String> responseEntity = getProxyTemplate().exchange(url, HttpMethod.POST, requestEntity, String.class);
        return responseEntity.getBody();
    }
}
