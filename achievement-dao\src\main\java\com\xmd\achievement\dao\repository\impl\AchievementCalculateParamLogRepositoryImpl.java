package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.AchievementCalculateParamLogModel;
import com.xmd.achievement.dao.mapper.AchievementCalculateParamLogMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IAchievementCalculateParamLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 业绩计算参数日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Service
@Slf4j
public class AchievementCalculateParamLogRepositoryImpl extends ServiceImpl<AchievementCalculateParamLogMapper,AchievementCalculateParamLogModel> implements IAchievementCalculateParamLogRepository {

@Resource
private AchievementCalculateParamLogMapper achievementCalculateParamLogMapper;

    @Override
    public void saveOrUpdateFactInfo(String achievementParam, Long orderId, String remark) {
        achievementCalculateParamLogMapper.insertOrUpdate(achievementParam, orderId, remark);
    }
}