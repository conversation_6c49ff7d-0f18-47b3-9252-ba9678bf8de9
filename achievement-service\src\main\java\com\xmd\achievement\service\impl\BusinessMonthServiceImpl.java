package com.xmd.achievement.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.BusinessAchievementModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.repository.IBusinessMonthRepository;
import com.xmd.achievement.handler.achievement.ReportHandler;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.SyncBusinessAchievementStatisticsService;
import com.xmd.achievement.service.entity.dto.UpdateBusinessMonthDto;
import com.xmd.achievement.service.entity.request.BusinessMonthSaveRequest;
import com.xmd.achievement.service.entity.request.MonthPageRequest;
import com.xmd.achievement.service.entity.request.OpenBusinessMonthRequest;
import com.xmd.achievement.service.entity.response.BusinessMonthInfoResponse;
import com.xmd.achievement.service.entity.response.BusinessMonthListResponse;
import com.xmd.achievement.service.entity.response.BusinessStartTimeResponse;
import com.xmd.achievement.service.entity.response.OpenBusinessesMonthResponse;
import com.xmd.achievement.support.constant.enums.BusinessMonthFreezeEnum;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.SpringFestivalMonthEnum;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.xmd.achievement.async.constant.ServiceConstant.MothConstant.ZONE_ID;
import static com.xmd.achievement.web.entity.response.WebCodeMessageEnum.*;
import static com.xmd.achievement.web.util.DateUtils.convertToLocalDateTime;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/19 16:49
 * @version: 1.0.0
 * @return {@link }
 */
@Service
public class BusinessMonthServiceImpl implements IBusinessMonthService {
    @Resource
    private IBusinessMonthRepository businessMonthRepository;

    @Resource
    private SyncBusinessAchievementStatisticsService syncBusinessAchievementStatisticsService;


    @Resource
    private ReportHandler reportHandler;

    @Override
    public WebResult<Boolean> saveMonth(BusinessMonthSaveRequest request) {

        List<BusinessMonthModel> checkName = businessMonthRepository.list(
                new LambdaQueryWrapper<BusinessMonthModel>()
                        .eq(BusinessMonthModel::getMonth, request.getMonth()));
        if (ObjectUtil.isNotEmpty(checkName)) {
            return WebResult.error(MONTH_NAME_EXIST);
        }

        // 如果设置为春节月，需要验证同一年是否已存在春节月
        if (request.getIsSpringFestivalMonth() != null && request.getIsSpringFestivalMonth().equals(SpringFestivalMonthEnum.SPRING_FESTIVAL.getCode())) {
            if (!isSpringFestivalMonth(request.getMonth(), null)) {
                return WebResult.error(SPRING_FESTIVAL_MONTH_EXIST);
            }
        }

        BusinessMonthModel businessMonthModel = new BusinessMonthModel();
        BeanUtils.copyProperties(request, businessMonthModel);
        businessMonthModel.setMonthId(IdUtil.getSnowflake().nextId());

        return WebResult.success(businessMonthRepository.save(businessMonthModel));
    }

    @Override
    public WebResult<Boolean> updateMonth(UpdateBusinessMonthDto updateBusinessMonthDto) {

        BusinessMonthModel monthRepositoryOne = businessMonthRepository.getOne(new LambdaQueryWrapper<BusinessMonthModel>().eq(BusinessMonthModel::getMonthId, updateBusinessMonthDto.getMonthId()));
        Date startDate = monthRepositoryOne.getStartDate();
        LocalDateTime startDateTime = convertToLocalDateTime(startDate);
        LocalDateTime localMidDateTime = convertToLocalDateTime(updateBusinessMonthDto.getMidDate());
        if (localMidDateTime.toLocalDate().isBefore(startDateTime.toLocalDate())) {
            return WebResult.error(MONTH_HALF_DATE_START_DATE);
        }

        //月半时间修改，如果月半时间已经小于当前时间不支持修改
        Date midDate = monthRepositoryOne.getMidDate();
        LocalDateTime dbdMidDateTime = convertToLocalDateTime(midDate);
        if (!dbdMidDateTime.equals(localMidDateTime)
                && dbdMidDateTime.toLocalDate().isBefore(LocalDateTime.now().toLocalDate())) {
            return WebResult.error(EDIT_MID_DATE_TIME_ERROR);
        }

        //查一下是否 有下一个 商务月了 如果有开始时间 大于原来结束时间的时间就不让改了
        List<BusinessMonthModel> nextMonth = businessMonthRepository.list(new LambdaQueryWrapper<BusinessMonthModel>().ge(BusinessMonthModel::getStartDate, monthRepositoryOne.getEndDate()));
        if (!CollectionUtils.isEmpty(nextMonth)) {
            return WebResult.error(END_DATE_EXIT_MONTH_DATE);
        }

        // 如果设置为春节月，需要验证同一年是否已存在春节月
        if (updateBusinessMonthDto.getIsSpringFestivalMonth() != null && updateBusinessMonthDto.getIsSpringFestivalMonth().equals(SpringFestivalMonthEnum.SPRING_FESTIVAL.getCode())) {
            if (!isSpringFestivalMonth(monthRepositoryOne.getMonth(), updateBusinessMonthDto.getMonthId())) {
                return WebResult.error(SPRING_FESTIVAL_MONTH_EXIST);
            }
        }

        BusinessMonthModel businessMonthModel = new BusinessMonthModel();
        BeanUtils.copyProperties(updateBusinessMonthDto, businessMonthModel);
        businessMonthModel.setId(monthRepositoryOne.getId());

        return WebResult.success(businessMonthRepository.updateById(businessMonthModel));
    }

    @Override
    public PageResponse<BusinessMonthListResponse> monthList(MonthPageRequest monthPageRequest) {
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(monthPageRequest.getMonth()), BusinessMonthModel::getMonth, monthPageRequest.getMonth());
        queryWrapper.orderByDesc(BusinessMonthModel::getMonth);
        Page<BusinessMonthModel> pageOf = new Page<>(monthPageRequest.getPageIndex(), monthPageRequest.getPageSize());
        Page<BusinessMonthModel> page = businessMonthRepository.page(pageOf, queryWrapper);

        PageResponse<BusinessMonthListResponse> response = new PageResponse<>(page.getTotal(), page.getCurrent(), page.getSize());

        response.setList(page.getRecords().stream().map(businessMonthModel -> {
            BusinessMonthListResponse businessMonthListResponse = new BusinessMonthListResponse();
            BeanUtils.copyProperties(businessMonthModel, businessMonthListResponse);
            return businessMonthListResponse;
        }).collect(Collectors.toList()));

        return response;
    }

    @Override
    public BusinessMonthInfoResponse getMonth(Long monthId) {
        BusinessMonthModel monthRepositoryOne = businessMonthRepository.getOne(new LambdaQueryWrapper<BusinessMonthModel>().eq(BusinessMonthModel::getMonthId, monthId));
        BusinessMonthInfoResponse businessMonthInfoResponse = new BusinessMonthInfoResponse();
        BeanUtils.copyProperties(monthRepositoryOne, businessMonthInfoResponse);
        return businessMonthInfoResponse;
    }


    @Override
    public BusinessMonthModel getMonthInfo(Date paidTime) {
        return null == paidTime ? null : businessMonthRepository.getOne(new LambdaQueryWrapper<BusinessMonthModel>().le(BusinessMonthModel::getStartDate, paidTime).ge(BusinessMonthModel::getEndDate, paidTime));
    }

    @Override
    public BusinessStartTimeResponse getMonthStartDate() {
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(BusinessMonthModel::getEndDate);
        queryWrapper.last("limit 1");
        BusinessMonthModel businessMonthModel = businessMonthRepository.getOne(queryWrapper);
        BusinessStartTimeResponse businessStartTimeResponse = new BusinessStartTimeResponse();

        if (ObjectUtils.isNotEmpty(businessMonthModel)) {
            ZonedDateTime currentDate = businessMonthModel.getEndDate().toInstant().atZone(ZONE_ID);
            ZonedDateTime newDate = currentDate.plusDays(1);
            businessStartTimeResponse.setStartTime(Date.from(newDate.toInstant()));
        }
        return businessStartTimeResponse;
    }

    @Override
    public Long getMonthInfoByBusinessMonth(String businessMonth) {
        BusinessMonthModel one = businessMonthRepository.getOne(
                new LambdaQueryWrapper<BusinessMonthModel>()
                        .eq(BusinessMonthModel::getMonth, businessMonth)
                        .eq(BusinessMonthModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        if (ObjectUtil.isEmpty(one)) {
            return null;
        }
        return one.getMonthId();

    }

    @Override
    public void saveOrUpdate(BusinessAchievementModel model) {

    }

    @Override
    public String getCurrentBusinessesMonth() {
        DateTime nowDate = DateUtil.date();
        BusinessMonthModel one = businessMonthRepository.lambdaQuery()
                .le(BusinessMonthModel::getStartDate, nowDate)
                .ge(BusinessMonthModel::getEndDate, nowDate)
                .last("limit 1").one();
        if (Objects.nonNull(one)) {
            return one.getMonth();
        }
        return null;
    }

    @Override
    public OpenBusinessesMonthResponse openBusinessesMonth(OpenBusinessMonthRequest request) {
        String currentDates = request.getCurrentDate();
        if (ObjectUtil.isEmpty(currentDates)) {
            return null;
        }
        BusinessMonthModel businessMonthModel = businessMonthRepository.selectBusinessMonthByCurrentDate(currentDates);
        OpenBusinessesMonthResponse target = new OpenBusinessesMonthResponse();
        BeanUtils.copyProperties(businessMonthModel, target);
        return target;
    }

    /**
     * 获取上一个工作月
     *
     * @return {@link String }
     * <AUTHOR>
     * @since 1.0
     */
    @Override
    public String queryLastBusinessMonth() {
        BusinessMonthModel monthModel = businessMonthRepository.lambdaQuery()
                .le(BusinessMonthModel::getEndDate, DateUtil.date())
                .orderByDesc(BusinessMonthModel::getEndDate)
                .last("limit 1").one();
        if (Objects.nonNull(monthModel)) {
            return monthModel.getMonth();
        }
        return null;
    }

    @Override
    public Boolean freezeMonth(Long id) {
        BusinessMonthModel monthModel = businessMonthRepository.getById(id);
        if (monthModel == null || monthModel.getEndDate() == null|| BusinessMonthFreezeEnum.FREEZE.getCode().equals(monthModel.getIsFreeze()))  {
            return false;
        }
        LocalDate today = LocalDate.now();
        LocalDate endDate = monthModel.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 当前日期必须大于结束日期至少一天
        if (!today.isAfter(endDate)) {
            return false;
        }
        BusinessMonthModel updateModel = new BusinessMonthModel();
        updateModel.setId(id);
        updateModel.setIsFreeze(BusinessMonthFreezeEnum.FREEZE.getCode());
        if(!businessMonthRepository.updateById(updateModel)){
            return false;
        }
        syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(null,monthModel.getMonth(), null);
        reportHandler.processMonthlyReport(monthModel.getStartDate());
        reportHandler.processDailyReport(monthModel.getMonth());
        return true;
    }

    /**
     * 判断商务月是否已冻结
     */
    @Override
    public boolean isMonthFrozen(Long monthId) {
         LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
         queryWrapper.eq(BusinessMonthModel::getMonthId, monthId)
         .eq(BusinessMonthModel::getIsFreeze, BusinessMonthFreezeEnum.FREEZE.getCode())
         .eq(BusinessMonthModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        return businessMonthRepository.count(queryWrapper) > 0;
    }

    /**
     * 判断是否为春节月
     * 根据商务月字段判断，一年只允许有一个春节月
     * @param month 商务月，格式为 YYYY-MM
     * @param currentMonthId 当前商务月ID（修改时传入，新增时传null）
     * @return true-是春节月，false-不是春节月
     */
    private boolean isSpringFestivalMonth(String month, Long currentMonthId) {
        if (ObjectUtil.isEmpty(month)) {
            return false;
        }

        // 提取年份
        String year = month.substring(0, 4);

        // 查询同一年是否已存在春节月
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(BusinessMonthModel::getMonth, year + "-")
                .eq(BusinessMonthModel::getIsSpringFestivalMonth, SpringFestivalMonthEnum.SPRING_FESTIVAL.getCode())
                .eq(BusinessMonthModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        if (currentMonthId != null) {
            queryWrapper.ne(BusinessMonthModel::getMonthId, currentMonthId);
        }

        return businessMonthRepository.count(queryWrapper) == 0;
    }

    /**
     * 判断商务月是否已冻结（通过月份字符串）
     */
    @Override
    public boolean isMonthFrozen(String month) {
        if (StringUtils.isBlank(month)) {
            month=queryLastBusinessMonth();            
        }
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessMonthModel::getMonth, month)
            .eq(BusinessMonthModel::getIsFreeze, BusinessMonthFreezeEnum.FREEZE.getCode())
            .eq(BusinessMonthModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        return businessMonthRepository.count(queryWrapper) > 0;
    }

    @Override
    public boolean isMonthFrozen(Date paidTime) {
        BusinessMonthModel monthModel=getMonthInfo(paidTime);
        return isMonthFrozen(monthModel);
    }

    @Override
    public boolean isMonthFrozen(BusinessMonthModel model){
         if (Objects.isNull(model)||model.getIsFreeze().equals(BusinessMonthFreezeEnum.NOT_FREEZE.getCode())) {
            return false;
        }
        return model.getIsFreeze().equals(BusinessMonthFreezeEnum.FREEZE.getCode());
    }
}

