package com.xmd.achievement.util.date;

/**
 * 日期/时间格式枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/28 下午5:26
 */
public enum DateTimeFormatUtil {
    /**
     * 例如:2020-12-28
     */
    STANDARD_DATE("yyyy-MM-dd"),
    /**
     * 例如:2020-12-28 10:00:00
     */
    STANDARD_DATE_TIME("yyyy-MM-dd HH:mm:ss"),
    /**
     * 例如:20201228100000
     */
    SIMPLE_DATE_TIME("yyyyMMddHHmmss"),
    /**
     * 例如:20201228
     */
    UNSIGNED_DATE("yyyyMMdd"),
    /**
     * 例如:10:00:00
     */
    STANDARD_TIME("HH:mm:ss"),
    /**
     * 例如:10:00
     */
    TIME_WITHOUT_SECOND("HH:mm"),
    /**
     * 例如:2020-12-28 10:00
     */
    DATE_TIME_WITHOUT_SECONDS("yyyy-MM-dd HH:mm"),
    /**
     * xxl-job任务时间转换格式
     */
    XXL_JOB_CRON_FORMAT("ss mm HH dd MM ? yyyy"),
    /**
     * 每天凌晨0点执行
     */
    ZERO_CLOCK_CRON_FORMAT("0 0 0 * * ?"),
    ;
    /**
     * 时间/日期格式
     */
    private final String format;

    DateTimeFormatUtil(String format) {
        this.format = format;
    }

    public String getFormat() {
        return format;
    }
}
