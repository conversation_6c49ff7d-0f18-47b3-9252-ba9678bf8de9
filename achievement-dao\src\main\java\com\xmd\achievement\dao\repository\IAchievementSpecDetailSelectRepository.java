package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.AchievementCategoryDetailSelectModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailSelectModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 业绩规格明细表(查询) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IAchievementSpecDetailSelectRepository extends IService<AchievementSpecDetailSelectModel> {
    List<AchievementSpecDetailSelectModel> selectAchievementSpecListByCategoryIds(List<Long> achievementCategoryIdList);
    void insertOrUpdate(List<AchievementSpecDetailSelectModel> models);
}
