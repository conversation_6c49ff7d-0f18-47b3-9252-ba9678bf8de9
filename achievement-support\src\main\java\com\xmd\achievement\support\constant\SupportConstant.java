package com.xmd.achievement.support.constant;


/**
 * 业务常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 16:21
 **/
public class SupportConstant {

    /**
     * redis保持心跳的间隔时间，单位:毫秒
     **/
    public final static int REDIS_KEEP_HEARTBEAT_TIME = 3000;


    /**
     * 数组分割符
     */
    public static final String SPLIT_DELIMITER = ",";

    /**
     * 匹配所有
     */
    public static final String SYMBOL_ALL = "**";

    /**
     * 表单模板ID
     */
    public final static String FORM_TEMPLATE_ID = "1716359955909";

    public final static String IM_RULE_TEMPLATE_ID = "im_rule_template_id";

    public final static String CS0001 = "CS0001";
    public final static String CS0002 = "CS0002";
    public final static String CS0003 = "CS0003";
    public final static String CS0004 = "CS0004";
}