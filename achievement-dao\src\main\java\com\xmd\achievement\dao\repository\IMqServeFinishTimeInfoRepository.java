package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.MqServeFinishTimeInfoModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface IMqServeFinishTimeInfoRepository extends IService<MqServeFinishTimeInfoModel> {

    List<MqServeFinishTimeInfoModel> selectByOrderIdAndProductId(Long orderId, List<Long> productId);
}
