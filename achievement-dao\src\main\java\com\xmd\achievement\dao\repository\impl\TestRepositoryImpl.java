package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.entity.TestModel;
import com.xmd.achievement.dao.mapper.TestMapper;
import com.xmd.achievement.dao.repository.ITestRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * whatsapp计费标准 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Service
@Slf4j
public class TestRepositoryImpl extends ServiceImpl<TestMapper, TestModel> implements ITestRepository {


    @Resource
    private TestMapper wspPriceStandardMapper;
}