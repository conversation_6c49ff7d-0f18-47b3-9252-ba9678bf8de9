package com.xmd.achievement.handler.achievement;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.QueryContractDetailResponse;
import com.xmd.achievement.support.constant.enums.AchStatus;
import com.xmd.achievement.support.constant.enums.AdnormalEnum;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class AchievementRefundCommonHandler {

    @Resource
    private InnerService innerService;

    // ==================== 数据创建方法 ====================

    /**
     * 创建商品明细退款记录
     */
    public AchievementProductDetailModel createRefundProduct(AchievementProductDetailModel original,
            BusinessMonthModel businessMonth,
            Date refundCreateTime,
            MqOrderRefundInfoModel refundTask) {
        BigDecimal multiplier = new BigDecimal("1");
        return createRefundProduct(original, businessMonth, refundCreateTime, refundTask, multiplier);
    }

    public AchievementProductDetailModel createRefundProduct(AchievementProductDetailModel original,
            BusinessMonthModel businessMonth,
            Date refundCreateTime,
            MqOrderRefundInfoModel refundTask,
            BigDecimal multiplier) {
        AchievementProductDetailModel refundProduct = new AchievementProductDetailModel();
        BeanUtil.copyProperties(original, refundProduct);

        // 清空 id 和设置新的 achievement_id
        refundProduct.setId(null);
        refundProduct.setAchievementId(IdUtil.getSnowflakeNextId());

        // 设置状态为退款
        refundProduct.setStatus(AchStatus.REFUND.getType());

        // 设置商务月信息
        setBusinessMonthInfo(refundProduct, businessMonth);

        // 设置金额字段：部分字段直接取负值，部分字段取负值乘以倍数
        refundProduct.setStandardPrice(negateIfNotNull(original.getStandardPrice()));
        refundProduct.setPayableAmount(negateIfNotNull(original.getPayableAmount()));
        refundProduct.setPaidAmount(negateIfNotNull(original.getPaidAmount()));
        refundProduct.setFirstYearQuote(negateIfNotNull(original.getFirstYearQuote()));
        refundProduct.setFirstYearRevenue(negateIfNotNull(original.getFirstYearRevenue()));
        refundProduct.setRenewalQuote(negateIfNotNull(original.getRenewalQuote()));
        refundProduct.setRenewalRevenue(negateIfNotNull(original.getRenewalRevenue()));
        refundProduct.setNetCash(negateAndMultiply(original.getNetCash(), multiplier));
        refundProduct.setAgentCommissionAchievement(negateAndMultiply(original.getAgentCommissionAchievement(), multiplier));
        refundProduct.setAgentActualCommission(negateAndMultiply(original.getAgentActualCommission(), multiplier));
        refundProduct.setAgentDeferredCommission(negateAndMultiply(original.getAgentDeferredCommission(), multiplier));
        refundProduct.setDeptCommission(negateAndMultiply(original.getDeptCommission(), multiplier));
        refundProduct.setDivCommission(negateAndMultiply(original.getDivCommission(), multiplier));
        refundProduct.setBranchCommission(negateAndMultiply(original.getBranchCommission(), multiplier));

        // 设置创建时间和更新时间为null，让MySQL使用当前时间戳
        refundProduct.setCreateTime(null);
        refundProduct.setUpdateTime(null);

        // 设置统计时间为退款任务的创建时间
        refundProduct.setStatisticsTime(refundCreateTime);

        // 设置售后订单信息
        refundProduct.setAftersaleOrderId(refundTask.getAftersaleOrderId());
        refundProduct.setAftersaleOrderNo(refundTask.getAftersaleOrderNo());
        refundProduct.setSaleType(refundTask.getSaleType());
        refundProduct.setIsAbnormal(AdnormalEnum.NORMAL.getCode());
        // 设置售后合同编号
        try {
            QueryContractDetailResponse contractDetail = innerService.getContractDetail(refundTask.getOrderId());
            if (contractDetail != null && contractDetail.getContractDto() != null) {
                refundProduct.setAftersaleContractNo(contractDetail.getContractDto().getContractCode());
            }
        } catch (Exception e) {
            log.info("获取合同信息失败，订单ID: {}, 错误: {}", refundTask.getOrderId(), e.getMessage());
        }

        return refundProduct;
    }

    /**
     * 创建分类明细退款记录（不带倍数，默认为1）
     */
    public AchievementCategoryDetailModel createRefundCategory(AchievementCategoryDetailModel original,
            Long newAchievementId,
            Date refundCreateTime) {
        BigDecimal multiplier = new BigDecimal("1");
        return createRefundCategory(original, newAchievementId, refundCreateTime, multiplier);
    }

    /**
     * 创建分类明细退款记录（带倍数）
     */
    public AchievementCategoryDetailModel createRefundCategory(AchievementCategoryDetailModel original,
            Long newAchievementId,
            Date refundCreateTime,
            BigDecimal multiplier) {
        AchievementCategoryDetailModel refundCategory = new AchievementCategoryDetailModel();
        BeanUtil.copyProperties(original, refundCategory);

        // 清空 id 和设置新的 achievement_category_id
        refundCategory.setId(null);
        refundCategory.setAchievementCategoryId(IdUtil.getSnowflakeNextId());

        // 设置新的 achievement_id，与商品明细保持一致
        refundCategory.setAchievementId(newAchievementId);

        // 设置状态为退款
        refundCategory.setStatus(AchStatus.REFUND.getType());

        // 设置金额字段：前面字段直接取负值，从NetCash开始乘以倍数
        refundCategory.setStandardPrice(negateIfNotNull(original.getStandardPrice()));
        refundCategory.setPayableAmount(negateIfNotNull(original.getPayableAmount()));
        refundCategory.setPaidAmount(negateIfNotNull(original.getPaidAmount()));
        refundCategory.setFirstYearQuote(negateIfNotNull(original.getFirstYearQuote()));
        refundCategory.setFirstYearIncome(negateIfNotNull(original.getFirstYearIncome()));
        refundCategory.setRenewalQuote(negateIfNotNull(original.getRenewalQuote()));
        refundCategory.setRenewalIncome(negateIfNotNull(original.getRenewalIncome()));
        // 从NetCash开始，需要乘以倍数
        refundCategory.setNetCash(negateAndMultiply(original.getNetCash(), multiplier));
        refundCategory.setAgentCommAchv(negateAndMultiply(original.getAgentCommAchv(), multiplier));
        refundCategory.setAgentActCommAchv(negateAndMultiply(original.getAgentActCommAchv(), multiplier));
        refundCategory.setAgentDefCommAchv(negateAndMultiply(original.getAgentDefCommAchv(), multiplier));
        refundCategory.setDeptCommAchv(negateAndMultiply(original.getDeptCommAchv(), multiplier));
        refundCategory.setBuCommAchv(negateAndMultiply(original.getBuCommAchv(), multiplier));
        refundCategory.setBranchCommAchv(negateAndMultiply(original.getBranchCommAchv(), multiplier));

        // 设置创建时间和更新时间为null，让MySQL使用当前时间戳
        refundCategory.setCreateTime(null);
        refundCategory.setUpdateTime(null);

        return refundCategory;
    }

    /**
     * 创建规格明细退款记录（不带倍数，默认为1）
     */
    public AchievementSpecDetailModel createRefundSpec(AchievementSpecDetailModel original,
            Long newAchievementCategoryId,
            Date refundCreateTime) {
        BigDecimal multiplier = new BigDecimal("1");
        return createRefundSpec(original, newAchievementCategoryId, refundCreateTime, multiplier);
    }

    /**
     * 创建规格明细退款记录（带倍数）
     */
    public AchievementSpecDetailModel createRefundSpec(AchievementSpecDetailModel original,
            Long newAchievementCategoryId,
            Date refundCreateTime,
            BigDecimal multiplier) {
        AchievementSpecDetailModel refundSpec = new AchievementSpecDetailModel();
        BeanUtil.copyProperties(original, refundSpec);

        // 清空 id 和设置新的 achievement_spec_id
        refundSpec.setId(null);
        refundSpec.setAchievementSpecId(IdUtil.getSnowflakeNextId());

        // 设置新的 achievement_category_id，与分类明细保持一致
        refundSpec.setAchievementCategoryId(newAchievementCategoryId);

        // 设置状态为退款
        refundSpec.setStatus(AchStatus.REFUND.getType());

        // 设置金额字段：前面字段直接取负值，从NetCash开始乘以倍数
        refundSpec.setStandardPrice(negateIfNotNull(original.getStandardPrice()));
        refundSpec.setPayableAmount(negateIfNotNull(original.getPayableAmount()));
        refundSpec.setPaidAmount(negateIfNotNull(original.getPaidAmount()));
        refundSpec.setFirstYearQuote(negateIfNotNull(original.getFirstYearQuote()));
        refundSpec.setFirstYearIncome(negateIfNotNull(original.getFirstYearIncome()));
        refundSpec.setRenewalQuote(negateIfNotNull(original.getRenewalQuote()));
        refundSpec.setRenewalIncome(negateIfNotNull(original.getRenewalIncome()));
        // 从NetCash开始，需要乘以倍数
        refundSpec.setNetCash(negateAndMultiply(original.getNetCash(), multiplier));
        refundSpec.setAgentCommAchv(negateAndMultiply(original.getAgentCommAchv(), multiplier));
        refundSpec.setAgentActCommAchv(negateAndMultiply(original.getAgentActCommAchv(), multiplier));
        refundSpec.setAgentDefCommAchv(negateAndMultiply(original.getAgentDefCommAchv(), multiplier));
        refundSpec.setDeptCommAchv(negateAndMultiply(original.getDeptCommAchv(), multiplier));
        refundSpec.setBuCommAchv(negateAndMultiply(original.getBuCommAchv(), multiplier));
        refundSpec.setBranchCommAchv(negateAndMultiply(original.getBranchCommAchv(), multiplier));

        // 设置创建时间和更新时间为null，让MySQL使用当前时间戳
        refundSpec.setCreateTime(null);
        refundSpec.setUpdateTime(null);

        return refundSpec;
    }

    /**
     * 将 BigDecimal 值取负值，如果为 null 则返回 null
     *
     * @param value 原始值
     * @return 负值或 null
     */
    public BigDecimal negateIfNotNull(BigDecimal value) {
        return value != null ? value.negate() : null;
    }

    /**
     * 将 BigDecimal 值取负值并乘以倍数，如果为 null 则返回 null
     *
     * @param value      原始值
     * @param multiplier 倍数
     * @return 负值乘以倍数或 null
     */
    public BigDecimal negateAndMultiply(BigDecimal value, BigDecimal multiplier) {
        if (value == null || multiplier == null) {
            return null;
        }
        return value.negate().multiply(multiplier);
    }

    /**
     * 创建反向规格明细退款记录（100%逻辑专用）
     * 取net_cash值的50%（四舍五入），其他数值字段取0
     */
    public AchievementSpecDetailModel createReverseRefundSpec(AchievementSpecDetailModel original,
            Long newAchievementCategoryId,
            Date refundCreateTime) {
        AchievementSpecDetailModel refundSpec = new AchievementSpecDetailModel();
        BeanUtil.copyProperties(original, refundSpec);

        // 清空 id 和设置新的 achievement_spec_id
        refundSpec.setId(null);
        refundSpec.setAchievementSpecId(IdUtil.getSnowflakeNextId());

        // 设置新的 achievement_category_id
        refundSpec.setAchievementCategoryId(newAchievementCategoryId);

        // 设置状态为退款
        refundSpec.setStatus(AchStatus.REFUND.getType());

        // 计算net_cash的50%并取反值，四舍五入到3位小数
        BigDecimal originalNetCash = original.getNetCash();
        BigDecimal halfNetCash = BigDecimal.ZERO;
        if (originalNetCash != null) {
            halfNetCash = originalNetCash.multiply(new BigDecimal("0.5"))
                    .setScale(3, RoundingMode.HALF_UP) // 四舍五入到3位小数
                    .negate(); // 取反值
        }
        refundSpec.setNetCash(halfNetCash);

        // 其他数值字段设置为0
        refundSpec.setStandardPrice(BigDecimal.ZERO);
        refundSpec.setPayableAmount(BigDecimal.ZERO);
        refundSpec.setPaidAmount(BigDecimal.ZERO);
        refundSpec.setFirstYearQuote(BigDecimal.ZERO);
        refundSpec.setFirstYearIncome(BigDecimal.ZERO);
        refundSpec.setRenewalQuote(BigDecimal.ZERO);
        refundSpec.setRenewalIncome(BigDecimal.ZERO);
        refundSpec.setAgentCommAchv(BigDecimal.ZERO);
        refundSpec.setAgentActCommAchv(BigDecimal.ZERO);
        refundSpec.setAgentDefCommAchv(BigDecimal.ZERO);
        refundSpec.setDeptCommAchv(BigDecimal.ZERO);
        refundSpec.setBuCommAchv(BigDecimal.ZERO);
        refundSpec.setBranchCommAchv(BigDecimal.ZERO);

        // 设置创建时间和更新时间
        refundSpec.setCreateTime(null);
        refundSpec.setUpdateTime(null);

        return refundSpec;
    }

    /**
     * 创建反向分类明细退款记录（100%逻辑专用）
     * 规则与spec相同：net_cash取50%反值，其他数值字段取0
     */
    public AchievementCategoryDetailModel createReverseRefundCategory(AchievementCategoryDetailModel original,
            Long newAchievementCategoryId,
            Long newAchievementId,
            Date refundCreateTime,
            AchievementSpecDetailModel refundSpec) {
        AchievementCategoryDetailModel refundCategory = new AchievementCategoryDetailModel();
        BeanUtil.copyProperties(original, refundCategory);

        // 清空 id 和设置新的 achievement_category_id
        refundCategory.setId(null);
        refundCategory.setAchievementCategoryId(newAchievementCategoryId);

        // 设置新的 achievement_id
        refundCategory.setAchievementId(newAchievementId);

        // 设置状态为退款
        refundCategory.setStatus(AchStatus.REFUND.getType());

        // net_cash直接取refundSpec的netCash
        refundCategory.setNetCash(refundSpec != null ? refundSpec.getNetCash() : BigDecimal.ZERO);

        // 其他数值字段设置为0
        refundCategory.setStandardPrice(BigDecimal.ZERO);
        refundCategory.setPayableAmount(BigDecimal.ZERO);
        refundCategory.setPaidAmount(BigDecimal.ZERO);
        refundCategory.setFirstYearQuote(BigDecimal.ZERO);
        refundCategory.setFirstYearIncome(BigDecimal.ZERO);
        refundCategory.setRenewalQuote(BigDecimal.ZERO);
        refundCategory.setRenewalIncome(BigDecimal.ZERO);
        refundCategory.setAgentCommAchv(BigDecimal.ZERO);
        refundCategory.setAgentActCommAchv(BigDecimal.ZERO);
        refundCategory.setAgentDefCommAchv(BigDecimal.ZERO);
        refundCategory.setDeptCommAchv(BigDecimal.ZERO);
        refundCategory.setBuCommAchv(BigDecimal.ZERO);
        refundCategory.setBranchCommAchv(BigDecimal.ZERO);

        // 设置创建时间和更新时间为null，让MySQL使用当前时间戳
        refundCategory.setCreateTime(null);
        refundCategory.setUpdateTime(null);

        return refundCategory;
    }

    /**
     * 创建反向商品明细退款记录（100%逻辑专用）
     * 规则与spec相同：net_cash取50%反值，其他数值字段取0，calculate_all与50%的设置规则相同
     */
    public AchievementProductDetailModel createReverseRefundProduct(AchievementProductDetailModel original,
            BusinessMonthModel businessMonth,
            Date refundCreateTime,
            MqOrderRefundInfoModel refundTask,
            AchievementSpecDetailModel refundSpec) {
        AchievementProductDetailModel refundProduct = new AchievementProductDetailModel();
        BeanUtil.copyProperties(original, refundProduct);

        // 清空 id 和设置新的 achievement_id
        refundProduct.setId(null);
        refundProduct.setAchievementId(IdUtil.getSnowflakeNextId());

        // 设置状态为退款
        refundProduct.setStatus(AchStatus.REFUND.getType());

        // 设置商务月信息
        setBusinessMonthInfo(refundProduct, businessMonth);
        refundProduct.setStatisticsTime(refundCreateTime);

        // 设置售后相关字段
        refundProduct.setAftersaleOrderId(refundTask.getAftersaleOrderId());
        refundProduct.setAftersaleOrderNo(refundTask.getAftersaleOrderNo());

        // net_cash直接取refundSpec的netCash
        refundProduct.setNetCash(refundSpec != null ? refundSpec.getNetCash() : BigDecimal.ZERO);

        // 其他数值字段设置为0
        refundProduct.setStandardPrice(BigDecimal.ZERO);
        refundProduct.setPayableAmount(BigDecimal.ZERO);
        refundProduct.setPaidAmount(BigDecimal.ZERO);
        refundProduct.setFirstYearQuote(BigDecimal.ZERO);
        refundProduct.setFirstYearRevenue(BigDecimal.ZERO);
        refundProduct.setRenewalQuote(BigDecimal.ZERO);
        refundProduct.setRenewalRevenue(BigDecimal.ZERO);
        refundProduct.setAgentCommissionAchievement(BigDecimal.ZERO);
        refundProduct.setAgentActualCommission(BigDecimal.ZERO);
        refundProduct.setAgentDeferredCommission(BigDecimal.ZERO);
        refundProduct.setDeptCommission(BigDecimal.ZERO);
        refundProduct.setDivCommission(BigDecimal.ZERO);
        refundProduct.setBranchCommission(BigDecimal.ZERO);

        // 设置calculate_all与50%的设置规则相同（设置为1）
        refundProduct.setCalculateAll(1);

        // 设置sale_type为新开
        refundProduct.setSaleType(OrderSaleTypeEnum.OPEN.getType());
        refundProduct.setIsAbnormal(AdnormalEnum.NORMAL.getCode());
        // 设置创建时间和更新时间
        refundProduct.setCreateTime(null);
        refundProduct.setUpdateTime(null);

        return refundProduct;
    }

    /**
     * 设置商务月信息的通用方法
     *
     * @param product       产品明细对象
     * @param businessMonth 商务月信息
     */
    private void setBusinessMonthInfo(AchievementProductDetailModel product, BusinessMonthModel businessMonth) {
        if (businessMonth != null) {
            product.setBusinessMonthId(businessMonth.getMonthId());
            product.setBusinessMonth(businessMonth.getMonth());
        }
    }

    /**
     * 检测异常业绩并发送微信消息
     *
     * @param productDetailList 业绩商品明细列表
     */
    public void checkAbnormalAndSendWxMessage(List<AchievementProductDetailModel> productDetailList) {
        if (productDetailList == null || productDetailList.isEmpty()) {
            return;
        }

        for (AchievementProductDetailModel productDetail : productDetailList) {
            if (AdnormalEnum.ABNORMAL.getCode().equals(productDetail.getIsAbnormal())) {
                log.info("检测到异常业绩，发送微信消息 - 业绩ID: {}", productDetail.getAchievementId());
                try {
                    innerService.sendWxMessage(productDetail.getAchievementId());
                } catch (Exception e) {
                    log.error("发送微信消息异常 - 业绩ID: {}, 错误: {}", productDetail.getAchievementId(), e.getMessage(), e);
                }
            }
        }
    }
}
