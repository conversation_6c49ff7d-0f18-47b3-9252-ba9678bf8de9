package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 业绩政策表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("achievement_policy")
public class AchievementPolicyModel extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业绩政策ID
     */
    @TableField("policy_id")
    private Long policyId;
    /**
     * 签单方式 1=电子签单，2=其他
     */
    @TableField("signing_method")
    private Integer signingMethod;
    /**
     * 业绩计收节点 1=支付完成，2=生产完成
     */
    @TableField("revenue_node")
    private Integer revenueNode;
    /**
     * 业绩核算比例
     */
    @TableField("achievement_ratio")
    private BigDecimal achievementRatio;
    /**
     * 实发业绩提成比例
     */
    @TableField("commission_ratio")
    private BigDecimal commissionRatio;
    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;
    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;


}