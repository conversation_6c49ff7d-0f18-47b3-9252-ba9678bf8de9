package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.MqServeInprogressInfoModel;
import com.xmd.achievement.dao.mapper.MqServeInprogressInfoMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IMqServeInprogressInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Service
@Slf4j
public class MqServeInprogressInfoRepositoryImpl extends ServiceImpl<MqServeInprogressInfoMapper,MqServeInprogressInfoModel> implements IMqServeInprogressInfoRepository {

@Resource
private MqServeInprogressInfoMapper mqServeInprogressInfoMapper;

}