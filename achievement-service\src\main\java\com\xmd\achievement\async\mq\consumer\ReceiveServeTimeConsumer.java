package com.xmd.achievement.async.mq.consumer;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.IAchievementService;
import com.xmd.achievement.service.MqServeFinishTimeInfoService;
import com.xmd.achievement.service.entity.dto.ReceiveServeFinishInProgressDto;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "${mq.group.serveFinishTime}", topic = "${mq.topic.serveFinishTime}", consumeMode = ConsumeMode.CONCURRENTLY, messageModel = MessageModel.CLUSTERING)
public class ReceiveServeTimeConsumer implements RocketMQListener<String> {
    @Resource
    private IAchievementService achievementService;
    @Resource
    private MqServeFinishTimeInfoService finishTimeInfoService;

    @Override
    public void onMessage(String dataJson) {
        log.info("服务时间信息处理,接收数据:{}", dataJson);
        try {
            //解析
            ReceiveServeFinishInProgressDto progressDto = JSONUtil.toBean(dataJson, ReceiveServeFinishInProgressDto.class);
            log.info("服务时间信息处理,数据转换:{}", JSONUtil.toJsonStr(progressDto));

            if (ObjectUtil.isNotEmpty(progressDto.getAcceptSource()) && Objects.equals(progressDto.getAcceptSource(), NumberConstants.INTEGER_VALUE_2)) {
                log.warn("服务时间信息处理,来源为渠道，不落库");
                return;
            }
            //落库
            finishTimeInfoService.saveInfo(progressDto);

        } catch (Exception e) {
            log.error("服务时间信息处理异常，message:{},异常信息", dataJson, e);
            throw new BusinessException(WebCodeMessageEnum.RPC_SERVER_EXCEPTION.getMsg());
        }
    }
}
