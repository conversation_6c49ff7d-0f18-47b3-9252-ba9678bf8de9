package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/27/09:34
 * @since 1.0
 */
@Getter
public enum ProcessType {
    /**
     * 订单支付完成计算
     */
    PAYMENT(1, "订单支付完成计算"),

    /**
     * 服务中计算
     */
    SERVE_IN_PROGRESS(2, "服务中计算");

    private final Integer code;
    private final String msg;

    ProcessType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
