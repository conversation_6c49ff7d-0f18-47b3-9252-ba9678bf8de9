package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.BaseModel;
import com.xmd.achievement.dao.mapper.AchievementCategoryDetailMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IAchievementCategoryDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业绩规格分类明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@Slf4j
public class AchievementCategoryDetailRepositoryImpl extends ServiceImpl<AchievementCategoryDetailMapper, AchievementCategoryDetailModel> implements IAchievementCategoryDetailRepository {

    @Resource
    private AchievementCategoryDetailMapper achievementCategoryDetailMapper;

    @Override
    public List<AchievementCategoryDetailModel> selectAchievementCategoryByCategoryIds(List<Long> achievementCategoryIdList) {
        LambdaQueryWrapper<AchievementCategoryDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AchievementCategoryDetailModel::getAchievementCategoryId,achievementCategoryIdList);
        queryWrapper.eq(AchievementCategoryDetailModel::getDeleteFlag,0);
        return this.list(queryWrapper);
    }

    @Override
    public List<AchievementCategoryDetailModel> selectAchievementCategoryByAchievementIds(List<Long> achievementIdList) {
        LambdaQueryWrapper<AchievementCategoryDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AchievementCategoryDetailModel::getAchievementId,achievementIdList);
        queryWrapper.eq(AchievementCategoryDetailModel::getDeleteFlag,0);
        return this.list(queryWrapper);

    }

    @Override
    public void updateChangeTypeByAchievementId(Long achievementId, Integer changeType) {
        LambdaUpdateWrapper<AchievementCategoryDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AchievementCategoryDetailModel::getAchievementId, achievementId);
        updateWrapper.eq(BaseModel::getDeleteFlag, 0);
        updateWrapper.set(AchievementCategoryDetailModel::getDataChangeType, changeType);
        achievementCategoryDetailMapper.update(null, updateWrapper);
    }

    @Override
    public List<AchievementCategoryDetailModel> listByUpdateTime(Date startTime) {
        LambdaQueryWrapper<AchievementCategoryDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(BaseModel::getUpdateTime,startTime);
        return this.list(queryWrapper);
    }

    @Override
    public void logicDeleteByAchievementId(List<Long> achievementId) {
        LambdaUpdateWrapper<AchievementCategoryDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AchievementCategoryDetailModel::getAchievementId,achievementId);
        updateWrapper.eq(BaseModel::getDeleteFlag, 0);
        updateWrapper.set(AchievementCategoryDetailModel::getDeleteFlag, 1);
        achievementCategoryDetailMapper.update(null, updateWrapper);
    }

    @Override
    public List<AchievementCategoryDetailModel> selectCategoryByAchievementIdsAndProductCategoryId(List<Long> achievementIdList, Long productCategoryId) {
        LambdaQueryWrapper<AchievementCategoryDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AchievementCategoryDetailModel::getAchievementId,achievementIdList);
        queryWrapper.eq(AchievementCategoryDetailModel::getDeleteFlag,0);
        queryWrapper.eq(AchievementCategoryDetailModel::getCategoryId, productCategoryId);
        return this.list(queryWrapper);
    }
}