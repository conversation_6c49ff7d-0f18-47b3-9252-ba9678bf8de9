package com.xmd.achievement.web.entity.base;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
public class PageResponse<T> implements Serializable {

    /**
     * 总条数
     */
    private long total;

    /**
     * 当前页
     */
    private long current;

    /**
     * 每页条数
     */
    private long size;

    /**
     * 分页数据
     */
    List<T> list;


    public PageResponse(long total, long current, long size) {
        this.total = total;
        this.current = current;
        this.size = size;
    }


}
