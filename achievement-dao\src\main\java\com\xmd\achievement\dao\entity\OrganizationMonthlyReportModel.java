package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 机构月报
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@TableName("organization_monthly_report")
public class OrganizationMonthlyReportModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 商务月id
     */
    @TableField("business_month_id")
    private Long businessMonthId;
    /**
     * 商务月
     */
    @TableField("business_month")
    private String businessMonth;
    /**
     * 当前月份 yyyy-MM
     */
    @TableField("current_day_month")
    private String currentDayMonth;
    /**
     * 机构ID
     */
    @TableField("organization_id")
    private Long organizationId;
    /**
     * 机构名称
     */
    @TableField("organization_name")
    private String organizationName;
    /**
     * 机构领导ID
     */
    @TableField("organization_leader_id")
    private Long organizationLeaderId;
    /**
     * 机构领导名称
     */
    @TableField("organization_leader_name")
    private String organizationLeaderName;
    /**
     * 任职本机构日期
     */
    @TableField("appointment_date")
    private Date appointmentDate;
    /**
     * 体系
     */
    @TableField("system_category")
    private String systemCategory;
    /**
     * 市场类别
     */
    @TableField("market_category")
    private String marketCategory;
    /**
     * 考核部门数
     */
    @TableField("examination_dept_count")
    private BigDecimal examinationDeptCount;
    /**
     * 部门数
     */
    @TableField("dept_count")
    private BigDecimal deptCount;
    /**
     * 商务代表数量
     */
    @TableField("business_representative_count")
    private Long businessRepresentativeCount;
    /**
     * 月签单金额
     */
    @TableField("monthly_signing_amount")
    private BigDecimal monthlySigningAmount;
    /**
     * 月净现金
     */
    @TableField("monthly_net_cash")
    private BigDecimal monthlyNetCash;
    /**
     * 月半净现金
     */
    @TableField("monthly_half_net_cash")
    private BigDecimal monthlyHalfNetCash;
    /**
     * 基本任务（元）
     */
    @TableField("basic_task")
    private BigDecimal basicTask;
    /**
     * 任务完成率
     */
    @TableField("task_completion_rate")
    private BigDecimal taskCompletionRate;
    /**
     * 总出单人数
     */
    @TableField("total_sign_person_count")
    private Long totalSignPersonCount;
    /**
     * 出单率
     */
    @TableField("sign_rate")
    private BigDecimal signRate;
    /**
     * 转正商代人数
     */
    @TableField("formal_business_representative_count")
    private Long formalBusinessRepresentativeCount;
    /**
     * 在岗人均净现金
     */
    @TableField("net_cash_per_employee")
    private BigDecimal netCashPerEmployee;
    /**
     * 非续费客户数
     */
    @TableField("non_renewal_customer_count")
    private Long nonRenewalCustomerCount;
    /**
     * 月新客户数
     */
    @TableField("monthly_new_customer_count")
    private Long monthlyNewCustomerCount;
    /**
     * 月网站新客户数
     */
    @TableField("monthly_website_new_customer_count")
    private Long monthlyWebsiteNewCustomerCount;
    /**
     * 月新网站数
     */
    @TableField("monthly_new_website_count")
    private Long monthlyNewWebsiteCount;
    /**
     * 部门提成业绩
     */
    @TableField("dept_commission")
    private BigDecimal deptCommission;
    /**
     * 分公司提成业绩
     */
    @TableField("branch_commission")
    private BigDecimal branchCommission;
    /**
     * 月网站净现金
     */
    @TableField("monthly_website_net_cash")
    private BigDecimal monthlyWebsiteNetCash;
    /**
     * 业绩段
     */
    @TableField("achievement_segment")
    private String achievementSegment;
    /**
     * 业绩段id
     */
    @TableField("achievement_segment_id")
    private Long achievementSegmentId;
    /**
     * 月老客户数
     */
    @TableField("monthly_old_customer_count")
    private Long monthlyOldCustomerCount;
    /**
     * 体系id
     */
    @TableField("system_id")
    private Long systemId;
    /**
     * 市场类别id
     */
    @TableField("market_category_id")
    private Long marketCategoryId;
    /**
     * 机构类型
     * 机构类型1总部；2区域；3分公司；4事业部；5商务组
     */
    @TableField("organization_type")
    private Integer organizationType;
    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;
    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新人id
     */
    @TableField("update_user_id")
    private String updateUserId;
    /**
     * 更新人名称
     */
    @TableField("update_user_name")
    private String updateUserName;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 删除标记: 0: 未删除, 1: 删除
     */
    @TableField("delete_flag")
    private Integer deleteFlag;

    @TableField("saas_net_cash")
    private BigDecimal saasNetCash;
}