package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 业绩黑名单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("achievement_blacklist")
public class AchievementBlacklistModel extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 黑名单id
     */
    @TableField("blacklist_id")
    private Long blacklistId;
    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 订单明细编号
     */
    @TableField("order_info_no")
    private String orderInfoNo;

}