package com.xmd.achievement.service.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 商品业绩修改
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Schema(description = "商品业绩修改参数")
public class AchievementUpdateRequest {

    /**
     * 商品业绩id
     */
    @NotNull(message = "商品业绩id不能为空")
    @Schema(description = "商品业绩id")
    private Long id;

    /**
     * 业绩统计时间
     */
    @Schema(description = "业绩统计时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date statisticsTime;

    /**
     * 客户类型(枚举 1-新客户、2-老客户、3-非新老)
     */
    @Schema(description = "客户类型(枚举 1-新客户、2-老客户、3-非新老)")
    private Integer customerType;

    /**
     * 数据更新原因
     */
    @NotBlank(message = "数据更新原因不能为空")
    @Schema(description = "数据更新原因")
    private String remark;

    /**
     * 主分单人 1=主，2=辅
     */
    @Schema(description = "主分单人 1=主，2=辅")
    private Integer mainSplitPerson;

    /**
     * 商务ID
     */
    @Schema(description = "商务ID")
    private String businessId;

    /**
     * 商务代表
     */
    @Schema(description = "商务代表")
    private String businessRepresentative;

    @Schema(description = "规格列表")
    private List<AchievementSpecUpdateRequest> specList;
}
