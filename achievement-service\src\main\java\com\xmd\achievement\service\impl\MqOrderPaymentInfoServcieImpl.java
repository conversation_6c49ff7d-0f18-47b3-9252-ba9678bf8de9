package com.xmd.achievement.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.dao.repository.*;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContextV4;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.InstallmentDetailResponse;
import com.xmd.achievement.rpc.entity.dto.OrderPayDetailResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
import com.xmd.achievement.service.MqOrderPaymentInfoService;
import com.xmd.achievement.service.entity.dto.ReceiveOrderPaymentDto;
import com.xmd.achievement.service.entity.request.InfoSaveMqOrderPaymentInfoRequest;
import com.xmd.achievement.service.entity.request.UpdatePaycompleteTestRequest;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.CalculateTypeEnum;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/14:26
 * @since 1.0
 */
@Service
@Slf4j
public class MqOrderPaymentInfoServcieImpl implements MqOrderPaymentInfoService {
    @Resource
    IMqOrderPaymentInfoRepository mqOrderPaymentRepository;

    @Resource
    IMqServeInprogressInfoRepository mqServeInprogressInfoRepository;

    @Resource
    IMqServeFinishTimeInfoRepository mqServeFinishTimeInfoRepository;

    @Resource
    IThirdAchievementRepository thirdAchievementRepository;

    @Resource
    IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    IAchievementCategoryDetailRepository achievementCategoryDetailRepository;

    @Resource
    IAchievementSpecDetailRepository achievementSpecDetailRepository;

    @Resource
    IBusinessAchievementRepository businessAchievementRepository;

    @Resource
    InnerService innerService;

    @Resource
    CalculateCustomerContextV4 calculateCustomerContextV4;

    @Override
    @Lock("'MqServeInProgress'+#paymentDto.orderId")
    public boolean saveInfo(ReceiveOrderPaymentDto paymentDto) {
        //幂等校验
//        MqOrderPaymentInfoModel model = mqOrderPaymentRepository.getOne(
//                new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
//                        .eq(MqOrderPaymentInfoModel::getOrderId, paymentDto.getOrderId()));
//        if (ObjectUtil.isNotEmpty(model)) {
//            log.warn("订单支付完成数据信息处理,重复订单不做处理，message:{}", JSONUtil.toJsonStr(paymentDto));
//            return false;
//        }

        OrderSimpleInfoResponse orderSimpleInfoResponse = innerService.getOrderSimpleInfo(paymentDto.getOrderId());
        if(null == orderSimpleInfoResponse || CollectionUtils.isEmpty(orderSimpleInfoResponse.getPayDetailResponses())){
            return false;
        }
        Optional<OrderPayDetailResponse> optionalOrderPayDetail = orderSimpleInfoResponse.getPayDetailResponses().stream()
                .filter(e -> e.getStatus().equals(NumberConstants.INTEGER_VALUE_3))
                .findFirst();

        Integer installment ;
        //订单分期状态
        Integer installmentStatus = orderSimpleInfoResponse.getInstallmentStatus();
        if(InstallmentEnum.INSTALLMENT.getCode().equals(installmentStatus) || InstallmentEnum.NOT_INSTALLMENT.getCode().equals(installmentStatus)){
            //不分期
            installment = 1;
        }else{
            installment = 2;
        }
        if(CollectionUtils.isEmpty(orderSimpleInfoResponse.getInstallmentDetailResponseList())){
                //没有分期
                //幂等校验
            MqOrderPaymentInfoModel model = mqOrderPaymentRepository.getOne(
                    new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                            .eq(MqOrderPaymentInfoModel::getOrderId, paymentDto.getOrderId()).last("limit 1"));
            if (ObjectUtil.isNotEmpty(model)) {
                log.warn("订单支付完成数据信息处理,重复订单不做处理，message:{}", JSONUtil.toJsonStr(paymentDto));
                return false;
            }

            MqOrderPaymentInfoModel saveModel = new MqOrderPaymentInfoModel();
            BeanUtil.copyProperties(paymentDto, saveModel);
            saveModel.setTaskId(IdUtil.getSnowflake().nextId());
            saveModel.setAchievementSource(AchievementSourceEnum.KUAJINFG.getCode());
            saveModel.setTaskType(TaskTypeEnum.ADD.getMsg());
            saveModel.setCustomerId(orderSimpleInfoResponse.getCustomerId());
            saveModel.setCalculateType(CalculateTypeEnum.PAYMENT.getCode());
            saveModel.setInstallmentStatus(NumberConstants.INTEGER_VALUE_1);
            saveModel.setInstallmentNum(NumberConstants.INTEGER_VALUE_0);
            if (optionalOrderPayDetail.isPresent()) {
                OrderPayDetailResponse o = optionalOrderPayDetail.get();
                saveModel.setPaymentTime(o.getCreateTime());
            } else {
                throw new BusinessException("未查询到支付时间，roderId:" + paymentDto.getOrderStatus());
            }
            mqOrderPaymentRepository.save(saveModel);
            return true;
        }
        //已支付的分期集合
        List<InstallmentDetailResponse> installmentPayDetailList = orderSimpleInfoResponse.getInstallmentDetailResponseList().stream().filter(e -> e.getInstallmentPayStatus().equals(NumberConstants.INTEGER_VALUE_3)).collect(Collectors.toList());
        //已支付的分期集合分期数
        List<Integer> installmentNumList = installmentPayDetailList.stream().map(InstallmentDetailResponse::getInstallmentNum).collect(Collectors.toList());
        //已入库分期订单
        List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModelList = mqOrderPaymentRepository.selectListByOrderIdAndInstallment(paymentDto.getOrderId(),installmentNumList,null,null);

        List<Integer> notInsertDBInstallmentNum = new ArrayList<>();

        for (Integer installmentNum : installmentNumList) {
            boolean  flag = true;
            for (MqOrderPaymentInfoModel model : mqOrderPaymentInfoModelList) {
                if(installmentNum.equals(model.getInstallmentNum())){
                    flag = false;
                    break;
                }
            }
            if(flag){
                notInsertDBInstallmentNum.add(installmentNum);
            }
        }

        for (Integer num : notInsertDBInstallmentNum) {
            MqOrderPaymentInfoModel saveModel = new MqOrderPaymentInfoModel();
            BeanUtil.copyProperties(paymentDto, saveModel);
            saveModel.setTaskId(IdUtil.getSnowflake().nextId());
            saveModel.setAchievementSource(AchievementSourceEnum.KUAJINFG.getCode());
            saveModel.setTaskType(TaskTypeEnum.ADD.getMsg());
            saveModel.setCustomerId(orderSimpleInfoResponse.getCustomerId());
            saveModel.setCalculateType(CalculateTypeEnum.PAYMENT.getCode());
            saveModel.setInstallmentStatus(installment);
            saveModel.setInstallmentNum(num);
            if (optionalOrderPayDetail.isPresent()) {
                OrderPayDetailResponse o = optionalOrderPayDetail.get();
                saveModel.setPaymentTime(o.getCreateTime());
            } else {
                throw new BusinessException("未查询到支付时间，roderId:" + paymentDto.getOrderStatus());
            }
            mqOrderPaymentRepository.save(saveModel);
        }

        return true;
    }

    @Override
    public void executeSuccess(MqOrderPaymentInfoModel model) {
        mqOrderPaymentRepository.update(
                null,
                new LambdaUpdateWrapper<MqOrderPaymentInfoModel>()
                        .eq(MqOrderPaymentInfoModel::getId, model.getId())
                        .set(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode()));
    }

    @Override
    public void executeFailed(MqOrderPaymentInfoModel model, String message) {
        MqOrderPaymentInfoModel historyModel = mqOrderPaymentRepository.getOne(
                new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                        .eq(MqOrderPaymentInfoModel::getId, model.getId()));

        mqOrderPaymentRepository.update(
                null,
                new LambdaUpdateWrapper<MqOrderPaymentInfoModel>()
                        .eq(MqOrderPaymentInfoModel::getId, model.getId())
                        .set(MqOrderPaymentInfoModel::getFailReason, message)
                        .set(MqOrderPaymentInfoModel::getFailCount, historyModel.getFailCount() + 1));
    }

    @Override
    public List<MqOrderPaymentInfoModel> queryExcutTask() {
        return mqOrderPaymentRepository.list(
                new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                        .eq(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                        .le(MqOrderPaymentInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_10)
                        .orderByAsc(MqOrderPaymentInfoModel::getCreateTime)
                        .orderByAsc(MqOrderPaymentInfoModel::getId)
                        .last("LIMIT 200"));
    }

    @Override
    public Boolean checkTaskStatus(MqOrderPaymentInfoModel model) {
        MqOrderPaymentInfoModel result = mqOrderPaymentRepository.getOne(
                new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                        .eq(MqOrderPaymentInfoModel::getId, model.getId())
                        .eq(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode()));
        return ObjectUtil.isNotEmpty(result);
    }

    @Override
    public Boolean infoSaveMqOrderPaymentInfo(InfoSaveMqOrderPaymentInfoRequest request) {
        List<MqOrderPaymentInfoModel> models = new ArrayList<>();
        for (Long orderId : request.getOrderIds()) {
            MqOrderPaymentInfoModel one = mqOrderPaymentRepository.getOne(
                    new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                            .eq(MqOrderPaymentInfoModel::getOrderId, orderId));
            if (ObjectUtil.isNotEmpty(one)) {
                continue;
            }
            MqOrderPaymentInfoModel model = new MqOrderPaymentInfoModel();
            model.setTaskId(IdUtil.getSnowflake().nextId());
            model.setOrderId(orderId);
            models.add(model);
        }

        mqOrderPaymentRepository.saveBatch(models);
        return true;
    }

    @Override
    public void saveModel(MqOrderPaymentInfoModel mqOrderPaymentInfoModel) {
        mqOrderPaymentRepository.save(mqOrderPaymentInfoModel);
    }

    @Override
    public void saveModels(List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModels) {
        mqOrderPaymentRepository.saveBatch(mqOrderPaymentInfoModels, mqOrderPaymentInfoModels.size());
    }

    @Override
    public void updatePayTime() {
        List<MqOrderPaymentInfoModel> list = mqOrderPaymentRepository.list(
                new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                        .isNull(MqOrderPaymentInfoModel::getPaymentTime));

        if (ObjectUtil.isEmpty(list)) {
            return;
        }

        for (MqOrderPaymentInfoModel model : list) {
            OrderSimpleInfoResponse orderSimpleInfoResponse = innerService.getOrderSimpleInfo(model.getOrderId());
            Optional<OrderPayDetailResponse> optionalOrderPayDetail = orderSimpleInfoResponse.getPayDetailResponses().stream()
                    .filter(e -> e.getStatus().equals(NumberConstants.INTEGER_VALUE_3))
                    .findFirst();

            if (optionalOrderPayDetail.isPresent()) {
                OrderPayDetailResponse o = optionalOrderPayDetail.get();
                mqOrderPaymentRepository.update(null,
                        new LambdaUpdateWrapper<MqOrderPaymentInfoModel>()
                                .eq(MqOrderPaymentInfoModel::getId, model.getId())
                                .set(MqOrderPaymentInfoModel::getPaymentTime, o.getCreateTime())
                                .set(MqOrderPaymentInfoModel::getCustomerId, orderSimpleInfoResponse.getCustomerId()));
            } else {
                throw new BusinessException("未查询到支付时间，roderId:" + model.getOrderId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handelThirdAchievementToPaymentOrder(ThirdAchievementModel model) {
        MqOrderPaymentInfoModel saveModel = new MqOrderPaymentInfoModel();
        saveModel.setTaskId(IdUtil.getSnowflake().nextId());
        saveModel.setTaskType(TaskTypeEnum.ADD.getMsg());
        saveModel.setOrderId(Long.valueOf(model.getOrderId())); //todo
        saveModel.setTaskStatus(TaskStatusEnum.NO.getCode().toString());
        saveModel.setAchievementSource(AchievementSourceEnum.ZHONGXIAO.getCode());
        saveModel.setThirdAchievementId(model.getThirdId());
        saveModel.setPaymentTime(model.getToAccountDate());
        saveModel.setCustomerId(model.getCustId());
        mqOrderPaymentRepository.save(saveModel);

        thirdAchievementRepository.update(
                null,
                new LambdaUpdateWrapper<ThirdAchievementModel>()
                        .eq(ThirdAchievementModel::getId, model.getId())
                        .set(ThirdAchievementModel::getTaskStatus, TaskStatusEnum.YES.getCode()));
        log.info("执行ThirdAchievementToPaymentOrderJob任务,完成ThirdId:{}的录入", model.getThirdId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean renewDate() {
        mqOrderPaymentRepository.update(
                null,
                new LambdaUpdateWrapper<MqOrderPaymentInfoModel>()
                        .set(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                        .set(MqOrderPaymentInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_0)
                        .set(MqOrderPaymentInfoModel::getDeleteFlag, YesOrNotEnum.NO.getCode()));
        log.info("1.重新计算业绩数据前置操作，完成表mq_order_payment_info的任务状态修改");

        mqServeInprogressInfoRepository.update(
                null,
                new LambdaUpdateWrapper<MqServeInprogressInfoModel>()
                        .set(MqServeInprogressInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                        .set(MqServeInprogressInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_0)
                        .set(MqServeInprogressInfoModel::getDeleteFlag, YesOrNotEnum.NO.getCode()));
        log.info("2.重新计算业绩数据前置操作，完成表mq_serve_inprogress_info的任务状态修改");

        mqServeFinishTimeInfoRepository.update(
                null,
                new LambdaUpdateWrapper<MqServeFinishTimeInfoModel>()
                        .set(MqServeFinishTimeInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                        .set(MqServeFinishTimeInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_0)
                        .set(MqServeFinishTimeInfoModel::getDeleteFlag, YesOrNotEnum.NO.getCode()));
        log.info("3.重新计算业绩数据前置操作，完成表mq_serve_finish_time_info的任务状态修改");

        achievementProductDetailRepository.remove(new LambdaQueryWrapper<>());
        log.info("4.重新计算业绩数据前置操作，完成表achievement_product_detail的全量删除操作");

        achievementCategoryDetailRepository.remove(new LambdaQueryWrapper<>());
        log.info("5.重新计算业绩数据前置操作，完成表achievement_category_detail的全量删除操作");

        achievementSpecDetailRepository.remove(new LambdaQueryWrapper<>());
        log.info("6.重新计算业绩数据前置操作，完成表achievement_spec_detail的全量删除操作");

        businessAchievementRepository.remove(new LambdaQueryWrapper<>());
        log.info("7.重新计算业绩数据前置操作，完成表business_achievement的全量删除操作");

        log.info("8.重新计算业绩数据前置操作完成，执行任务进行计算");
        return true;

    }

    @Override
    public Boolean addPayTime() {
        //分页获取支付时间为空的额数据 定义分页开始 每次查询100条数据
        int pageSize = 1;
        int size = 100;
        boolean hasMoreData = true;
        //总计完成条数
        int totalCount = 0;
        while (hasMoreData) {
            log.info("同步三方数据到支付完成表中,pageSize:{},size:{},totalCount:{}", pageSize, 100, totalCount);
            //分页查询
            Page<MqOrderPaymentInfoModel> page = new Page<>(pageSize, size);
            Page<MqOrderPaymentInfoModel> list = mqOrderPaymentRepository.page(page,
                    new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                            .isNull(MqOrderPaymentInfoModel::getPaymentTime));

            if (ObjectUtil.isEmpty(list.getRecords())) {
                hasMoreData = false; // 没有更多数据，退出循环
            } else {
                // 处理查询到的数据
                for (MqOrderPaymentInfoModel model : list.getRecords()) {
                    ThirdAchievementModel one = thirdAchievementRepository.getOne(
                            new LambdaQueryWrapper<ThirdAchievementModel>()
                                    .eq(ThirdAchievementModel::getThirdId, model.getThirdAchievementId())
                                    .eq(ThirdAchievementModel::getTaskType, model.getTaskType()));
                    if (ObjectUtil.isNotEmpty(one)) {
                        mqOrderPaymentRepository.update(
                                null,
                                new LambdaUpdateWrapper<MqOrderPaymentInfoModel>()
                                        .eq(MqOrderPaymentInfoModel::getId, model.getId())
                                        .set(MqOrderPaymentInfoModel::getPaymentTime, one.getToAccountDate()));
                        log.info("同步三方数据到支付完成表中,同步成功,ThirdId:{}", model.getThirdAchievementId());
                        totalCount++;
                    }
                }
            }
            pageSize++;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePayCompleteTest(UpdatePaycompleteTestRequest request) {
        if (ObjectUtil.isEmpty(request)) {
            return;
        }
        for (Long taskId : request.getTaskId()) {
            MqOrderPaymentInfoModel model = mqOrderPaymentRepository.getOne(
                    new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                            .eq(MqOrderPaymentInfoModel::getTaskId, taskId));

            if (ObjectUtil.isEmpty(model)) {
                return;
            }
            //修改状态
            mqOrderPaymentRepository.update(
                    new LambdaUpdateWrapper<MqOrderPaymentInfoModel>()
                            .eq(MqOrderPaymentInfoModel::getTaskId, taskId)
                            .set(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode()));

            //删除相关表信息
            achievementProductDetailRepository.remove(
                    new LambdaUpdateWrapper<AchievementProductDetailModel>()
                            .eq(AchievementProductDetailModel::getOrderId, model.getOrderId())
                            .eq(AchievementProductDetailModel::getProductId, model.getProductId())
                            .eq(AchievementProductDetailModel::getStatus, NumberConstants.INTEGER_VALUE_2));

            achievementCategoryDetailRepository.remove(
                    new LambdaUpdateWrapper<AchievementCategoryDetailModel>()
                            .eq(AchievementCategoryDetailModel::getOrderId, model.getOrderId())
                            .eq(AchievementCategoryDetailModel::getProductId, model.getProductId())
                            .eq(AchievementCategoryDetailModel::getStatus, NumberConstants.INTEGER_VALUE_2));

            achievementSpecDetailRepository.remove(
                    new LambdaUpdateWrapper<AchievementSpecDetailModel>()
                            .eq(AchievementSpecDetailModel::getOrderId, model.getOrderId())
                            .eq(AchievementSpecDetailModel::getProductId, model.getProductId())
                            .eq(AchievementSpecDetailModel::getStatus, NumberConstants.INTEGER_VALUE_2));
            log.info("TaskId:{},修改支付完成状态及删除相关表信息,orderId:{},productId:{}", model.getTaskId(), model.getOrderId(), model.getProductId());
        }
    }

    @Override
    public MqOrderPaymentInfoModel queryExcutTaskByTaskId(Long taskId) {
        return mqOrderPaymentRepository.getOne(
                new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                        .eq(MqOrderPaymentInfoModel::getTaskId, taskId)
                        .eq(MqOrderPaymentInfoModel::getCalculateType, CalculateTypeEnum.SERVEINPROGRESS.getCode()));
    }


    @Override
    public void repairCalculateCustomerType() {
        //循环获取mq_order_payment_info中的数据 每次取100条 进行处理 直到数据为空停止分页查询
        int countFlag = 0;
        boolean hasMoreData = true;
        int pageSize = 1;
        int size = 100;
        while (hasMoreData) {
            log.info("修复计算客户类型,pageSize:{},size:{}", pageSize, size);
            Page<MqOrderPaymentInfoModel> page = new Page<>(pageSize, size);
            Page<MqOrderPaymentInfoModel> list = mqOrderPaymentRepository.page(page,
                    new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                            .eq(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode())
                            //.ge(MqOrderPaymentInfoModel::getCreateTime, DateUtils.stringToDate("2025-01-13 20:04:29", DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss))
                            .orderByAsc(MqOrderPaymentInfoModel::getCreateTime));
            if (ObjectUtil.isEmpty(list.getRecords())) {
                log.info("修复计算客户类型已完成,请检查数据");
                hasMoreData = false;
                continue;
            }

            // 处理查询到的数据
            for (MqOrderPaymentInfoModel model : list.getRecords()) {
                countFlag ++;
                log.info("当前已修复条数：{}", countFlag);
                log.info("修复计算客户类型,orderId:{},productId:{},calculateType:{}", model.getOrderId(), model.getProductId(), model.getCalculateType());
                List<AchievementProductDetailModel> models = new ArrayList<>();
                if (CalculateTypeEnum.PAYMENT.getCode().equals(model.getCalculateType())) {
                    if (AchievementSourceEnum.KUAJINFG.getCode().equals(model.getAchievementSource())) {
                        models = achievementProductDetailRepository.list(
                                new LambdaQueryWrapper<AchievementProductDetailModel>()
                                        .eq(AchievementProductDetailModel::getOrderId, model.getOrderId())
                                        .eq(AchievementProductDetailModel::getStatus, NumberConstants.INTEGER_VALUE_1)
                                        .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));

                    } else {
                        models = achievementProductDetailRepository.list(
                                new LambdaQueryWrapper<AchievementProductDetailModel>()
                                        .eq(AchievementProductDetailModel::getThirdAchievementId, model.getThirdAchievementId())
                                        .eq(AchievementProductDetailModel::getStatus, NumberConstants.INTEGER_VALUE_1)
                                        .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
                    }
                }

                if (CalculateTypeEnum.SERVEINPROGRESS.getCode().equals(model.getCalculateType())) {
                    if (AchievementSourceEnum.KUAJINFG.getCode().equals(model.getAchievementSource())) {
                        models = achievementProductDetailRepository.list(
                                new LambdaQueryWrapper<AchievementProductDetailModel>()
                                        .eq(AchievementProductDetailModel::getOrderId, model.getOrderId())
                                        .eq(AchievementProductDetailModel::getProductId, model.getProductId())
                                        .eq(AchievementProductDetailModel::getStatus, NumberConstants.INTEGER_VALUE_2)
                                        .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
                    } else {
                        models = achievementProductDetailRepository.list(
                                new LambdaQueryWrapper<AchievementProductDetailModel>()
                                        .eq(AchievementProductDetailModel::getThirdAchievementId, model.getThirdAchievementId())
                                        .eq(AchievementProductDetailModel::getStatus, NumberConstants.INTEGER_VALUE_2)
                                        .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
                    }
                }
                if (models.isEmpty()) {
                    log.info("修复计算客户类型,orderId:{},productId:{},calculateType:{},商品业绩为空不处理！", model.getOrderId(), model.getProductId(), model.getCalculateType());
                    continue;
                }
                CustomerType customerType = calculateCustomerContextV4.calculateCustomerV4(model.getInstallmentStatus(), model.getInstallmentNum(), models.get(0).getCustomerId(), models);
                log.info("修复计算客户类型,orderId:{},productId:{},calculateType:{},修复结果:{}", model.getOrderId(), model.getProductId(), model.getCalculateType(), customerType.getDescription());
                if (CalculateTypeEnum.PAYMENT.getCode().equals(model.getCalculateType())) {
                    if (AchievementSourceEnum.KUAJINFG.getCode().equals(model.getAchievementSource())) {
                        achievementProductDetailRepository.update(
                                new LambdaUpdateWrapper<AchievementProductDetailModel>()
                                        .eq(AchievementProductDetailModel::getOrderId, model.getOrderId())
                                        .eq(AchievementProductDetailModel::getStatus, NumberConstants.INTEGER_VALUE_1)
                                        .set(AchievementProductDetailModel::getCustomerType, customerType.getType()));
                    } else {
                        achievementProductDetailRepository.update(
                                new LambdaUpdateWrapper<AchievementProductDetailModel>()
                                        .eq(AchievementProductDetailModel::getThirdAchievementId, model.getThirdAchievementId())
                                        .eq(AchievementProductDetailModel::getStatus, NumberConstants.INTEGER_VALUE_1)
                                        .set(AchievementProductDetailModel::getCustomerType, customerType.getType()));
                    }

                }

                if (CalculateTypeEnum.SERVEINPROGRESS.getCode().equals(model.getCalculateType())) {
                    achievementProductDetailRepository.update(
                            new LambdaUpdateWrapper<AchievementProductDetailModel>()
                                    .eq(AchievementProductDetailModel::getOrderId, model.getOrderId())
                                    .eq(AchievementProductDetailModel::getProductId, model.getProductId())
                                    .eq(AchievementProductDetailModel::getStatus, NumberConstants.INTEGER_VALUE_2)
                                    .set(AchievementProductDetailModel::getCustomerType, customerType.getType()));
                }
            }
            pageSize++;
        }
    }

    @Override
    public Boolean repairThirdDataCreateTime() {
        boolean hasMoreData = true;
        int pageSize = 1;
        int size = 1000;
        while (hasMoreData) {
            log.info("V4版本修复mq支付表中计算类型,pageSize:{},size:{}", pageSize, size);
            Page<ThirdAchievementModel> page = new Page<>(pageSize, size);
            Page<ThirdAchievementModel> result = thirdAchievementRepository.page(
                    page,
                    new LambdaQueryWrapper<ThirdAchievementModel>()
                            .eq(ThirdAchievementModel::getTaskType, TaskTypeEnum.ADD.getMsg())
                            .orderByAsc(ThirdAchievementModel::getDbInsertTime));
            if (ObjectUtil.isEmpty(result.getRecords())) {
                log.info("V4版本修复mq支付表中计算类型已完成，请检查数据");
                hasMoreData = false;
                continue;
            }

            for (ThirdAchievementModel model : result.getRecords()) {
                MqOrderPaymentInfoModel paymentInfoModel = mqOrderPaymentRepository.getOne(
                        new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                                .eq(MqOrderPaymentInfoModel::getThirdAchievementId, model.getThirdId()));
                if (ObjectUtil.isEmpty(paymentInfoModel)) {
                    log.info("V4版本修复mq支付表计算类型已完成，检测数据为空，thirdId:{}", model.getThirdId());
                    continue;
                }

                Integer calculateType = null;
                if (Objects.equals(model.getState(), NumberConstants.INTEGER_VALUE_0)) {
                    calculateType = NumberConstants.INTEGER_VALUE_1;
                }
                if (Objects.equals(model.getState(), NumberConstants.INTEGER_VALUE_3)) {
                    calculateType = NumberConstants.INTEGER_VALUE_2;
                }
                if (calculateType == null) {
                    log.info("V4版本修复mq支付表计算类型已完成，三方状态不为有效及已完成，thirdId:{}", model.getThirdId());
                    continue;
                }
                mqOrderPaymentRepository.update(
                        new LambdaUpdateWrapper<MqOrderPaymentInfoModel>()
                                .eq(MqOrderPaymentInfoModel::getThirdAchievementId, model.getThirdId())
                                .set(MqOrderPaymentInfoModel::getCalculateType, calculateType)
                );
            }
            pageSize++;
        }
        return true;
    }
}
