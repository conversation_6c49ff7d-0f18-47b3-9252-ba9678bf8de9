package com.xmd.achievement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.SaasTabModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IAchievementSpecDetailRepository;
import com.xmd.achievement.dao.repository.ISaasTabRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.ProductInfoForOrderResponse;
import com.xmd.achievement.service.ISaasTabService;
import com.xmd.achievement.service.entity.request.SaasTabAddRequest;
import com.xmd.achievement.service.entity.request.SaasTabPageRequest;
import com.xmd.achievement.service.entity.request.SaasTabUpdateRequest;
import com.xmd.achievement.service.entity.response.SaasTabResponse;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.SaasEnum;
import com.xmd.achievement.support.constant.enums.SaasTypeEnum;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.web.entity.base.PageResponse;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * saas标签表服务实现类
 */
@Slf4j
@Service
public class SaasTabServiceImpl implements ISaasTabService {

    @Resource
    private ISaasTabRepository saasTabRepository;

    @Resource
    private InnerService innerService;

    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    private IAchievementSpecDetailRepository achievementSpecDetailRepository;

    @Override
    public PageResponse<SaasTabResponse> pageQuery(SaasTabPageRequest request) {
        LambdaQueryWrapper<SaasTabModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(request.getAssociationId() != null, SaasTabModel::getAssociationId, request.getAssociationId())
                    .like(request.getAssociationName() != null, SaasTabModel::getAssociationName, request.getAssociationName())
                    .eq(SaasTabModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        Page<SaasTabModel> page = new Page<>(request.getPageIndex(), request.getPageSize());
        Page<SaasTabModel> result = saasTabRepository.page(page, queryWrapper);
        
        List<SaasTabResponse> responseList = result.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        PageResponse<SaasTabResponse> pageResponse = new PageResponse<>(result.getTotal(), result.getCurrent(), result.getSize());
        pageResponse.setList(responseList);
        return pageResponse;
    }

    /**
     * 将Model转换为Response
     */
    private SaasTabResponse convertToResponse(SaasTabModel model) {
        SaasTabResponse response = new SaasTabResponse();
        BeanUtils.copyProperties(model, response);
        response.setIsSaas(SaasEnum.getByCode(model.getIsSaas()).getMsg());
        response.setSaasType(SaasTypeEnum.getByCode(model.getSaasType()).getMsg());
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(SaasTabAddRequest request) {
        // 检查是否已存在相同地记录（未删除的）
        LambdaQueryWrapper<SaasTabModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaasTabModel::getAssociationId, request.getAssociationId())
                .eq(SaasTabModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(SaasTabModel::getSaasType, request.getSaasType());
        
        if (saasTabRepository.count(queryWrapper) > 0) {
            throw new BusinessException("该记录已存在");
        }

        SaasTabModel model = new SaasTabModel();
        BeanUtils.copyProperties(request, model);
        model.setSaasTabId(IdUtil.getSnowflake().nextId());
        return saasTabRepository.save(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(SaasTabUpdateRequest request) {
        LambdaUpdateWrapper<SaasTabModel> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(SaasTabModel::getId, request.getId())
                .eq(SaasTabModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .set(SaasTabModel::getIsSaas, request.getIsSaas());
        return saasTabRepository.update(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        LambdaUpdateWrapper<SaasTabModel> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(SaasTabModel::getId, id)
                .set(SaasTabModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode());
        return saasTabRepository.update(wrapper);
    }

    @Override
    public boolean checkIsSaas(Long productId) {
        if (productId == null){
            return false;
        }
        return checkIsSaasKuaJing(productId) || checkIsSaasZhongQi(productId);
    }

    @Override
    public boolean checkIsSaasKuaJing(Long productId) {
        if (productId == null) {
            return false;
        }
        List<ProductInfoForOrderResponse> productList = innerService.getProductListForOrder(Collections.singletonList(productId));
        if (CollectionUtils.isEmpty(productList)) {
            return false;
        }
        Long categoryId = productList.get(0).getLevelOneCategoryId();
        if (categoryId == null) {
            return false;
        }
        return saasTabRepository.count(buildQueryWrapper(SaasTypeEnum.KUAJING,categoryId)) == 1;
    }

    @Override
    public boolean checkIsSaasZhongQi(Long productId) {
        if (productId == null){
            return false;
        }
        return saasTabRepository.count(buildQueryWrapper(SaasTypeEnum.ZHONGQI,productId)) == 1;
    }

    private LambdaQueryWrapper<SaasTabModel> buildQueryWrapper(SaasTypeEnum type,  Long associationId){
        LambdaQueryWrapper<SaasTabModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaasTabModel::getAssociationId, associationId)
                .eq(SaasTabModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(SaasTabModel::getSaasType, type.getCode())
                .eq(SaasTabModel::getIsSaas, SaasEnum.YES.getCode());
        return queryWrapper;
    }

    @Override
    public boolean batchRefresh() {
        log.info("开始批量刷新SAAS状态");
        refreshProduct();
        refreshSpec();
        return true;
    }

    private void refreshProduct(){
        int pageSize = 500;
        Long lastId = 0L; // 起始 ID，假设 ID 是 Long 类型且大于 0
        // 如果允许，可以在循环外定义时间
        while (true) {
            LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .isNull(AchievementProductDetailModel::getIsSaas)
                    .orderByAsc(AchievementProductDetailModel::getId)
                    .gt(AchievementProductDetailModel::getId, lastId)
                    .last("LIMIT " + pageSize); // MyBatis-Plus 的 .last() 追加 LIMIT
            Date now = new Date();
            List<AchievementProductDetailModel> productList = achievementProductDetailRepository.list(queryWrapper);
            if (CollectionUtils.isEmpty(productList)) {
                break;
            }
            for (AchievementProductDetailModel product : productList) {
                product.setIsSaas(checkIsSaas(product.getProductId()) ? SaasEnum.YES.getCode() : SaasEnum.NO.getCode());
                product.setUpdateTime(now); // 或使用循环外时间 now，根据需要选择
            }
            achievementProductDetailRepository.saveOrUpdateBatch(productList);
            // 更新 lastId 为当前批次最后一条记录的 ID
            lastId = productList.get(productList.size() - 1).getId();
        }
    }

    private void refreshSpec(){
        int pageSize = 500;
        Long lastId = 0L; // 起始 ID，假设 ID 是 Long 类型且大于 0
        // 如果允许，可以在循环外定义时间
        while (true) {
            LambdaQueryWrapper<AchievementSpecDetailModel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AchievementSpecDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .isNull(AchievementSpecDetailModel::getIsSaas)
                    .orderByAsc(AchievementSpecDetailModel::getId)
                    .gt(AchievementSpecDetailModel::getId, lastId)
                    .last("LIMIT " + pageSize); // MyBatis-Plus 的 .last() 追加 LIMIT
            Date now = new Date();
            List<AchievementSpecDetailModel> specList = achievementSpecDetailRepository.list(queryWrapper);
            if (CollectionUtils.isEmpty(specList)) {
                break;
            }
            for (AchievementSpecDetailModel product : specList) {
                product.setIsSaas(checkIsSaas(product.getProductId()) ? SaasEnum.YES.getCode() : SaasEnum.NO.getCode());
                product.setUpdateTime(now); // 或使用循环外时间 now，根据需要选择
            }
            achievementSpecDetailRepository.saveOrUpdateBatch(specList);
            // 更新 lastId 为当前批次最后一条记录的 ID
            lastId = specList.get(specList.size() - 1).getId();
        }
    }
}

