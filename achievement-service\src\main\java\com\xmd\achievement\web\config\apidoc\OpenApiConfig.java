package com.xmd.achievement.web.config.apidoc;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.HeaderParameter;
import io.swagger.v3.oas.models.parameters.Parameter;
import org.springdoc.core.GroupedOpenApi;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 接口文档配置
 */
@Configuration
public class OpenApiConfig {

    private static final String TOKEN = "Authorization";

    private static String BUS_PACKAGE = "com.xmd.achievement.web.controller";

    @Autowired
    private OpenApiProperties openApiProperties;

    /**
     * token请求头参数
     */
    private Parameter TOKEN_PARAM = new HeaderParameter().name(TOKEN).required(false).schema(new StringSchema()._default("").name(TOKEN));


    @Bean
    public OpenAPI openApi() {
        return new OpenAPI()
                .info(new Info()
                        .title(openApiProperties.getTitle())
                        .description(openApiProperties.getDescription())
                        .termsOfService(openApiProperties.getTermsOfService())
                        .contact(new Contact().name(openApiProperties.getContactName()).url(openApiProperties.getContactUrl()).email(openApiProperties.getContactEmail()))
                        .version(openApiProperties.getVersion()))
                .externalDocs(new ExternalDocumentation().description(openApiProperties.getExternalDescription()).url(openApiProperties.getExternalUrl()));
    }


    @Bean
    public GroupedOpenApi busApi() {
        String[] packagedToMatch = {BUS_PACKAGE};
        return api("1.业务接口文档", packagedToMatch);
    }

    /**
     * 除了上面的接口之外，其它的接口都在项目接口文档中
     * 请根据实际情况进行自定义
     *
     * @return
     */
/*    @Bean
    public GroupedOpenApi projectApi() {
        return GroupedOpenApi.builder()
                .group("1.项目接口文档")
                .addOperationCustomizer(getOperationCustomizer())
                .pathsToMatch("/**")
                .packagesToExclude(BUS_PACKAGE)
                .build();
    }*/

    /**
     * 配置接口
     *
     * @param group
     * @param packagedToMatch
     * @return
     */
    private GroupedOpenApi api(String group, String[] packagedToMatch) {
        return GroupedOpenApi.builder()
                .group(group)
                .addOperationCustomizer(getOperationCustomizer())
                .pathsToMatch("/**")
                .packagesToScan(packagedToMatch).build();
    }

    /**
     * 配置自定义请求头
     *
     * @return
     */
    public OperationCustomizer getOperationCustomizer() {
        return (operation, handlerMethod) -> {
            operation.addParametersItem(TOKEN_PARAM);
            return operation;
        };
    }

}
