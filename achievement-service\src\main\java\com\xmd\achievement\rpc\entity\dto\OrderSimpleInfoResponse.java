package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单简单信息
 * <AUTHOR>
 * @date 2024年11月11日 下午1:27
 *
 */
@Data

public class OrderSimpleInfoResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 订单编号
     */

    private String orderNo;

    /**
     * 收款公司id
     */

    private String payeeCompanyId;

    /**
     * 收款公司名称
     */

    private String payeeCompanyName;

    /**
     * 订单类型：1=普通订单，2=折扣订单
     */

    private Integer orderType;


    private String orderTypeName;

    /**
     * 订单销售类型：1=新开，2=续费，3=升级，4=另购
     */

    private Integer orderSaleType;


    private String orderSaleTypeName;

    /**
     * 是否是vip订单：0否 1是
     */

    private Integer vipOrder;

    /**
     * 折扣单原因
     */

    private String discountDesc;

    /**
     * 折扣单图片url
     */

    private String discountImgUrl;

    /**
     * 客户id
     */

    private String customerId;

    /**
     * 客户名称
     */

    private String customerName;

    /**
     * 签约类型：1=企业法人/个人 2=企业授权人 3=电子合同
     */

    private Integer signType;


    private String signTypeName;

    /**
     * 订单状态：1创建完成，2已完成，3已取消，4已退款
     */

    private Integer orderStatus;


    private String orderStatusName;

    /**
     * 订单来源：1=代客下单，2=官网，3=驾驶舱-PC,4=驾驶舱移动端，5优化师工作台，6商务工作台，7线下录单
     */

    private Integer orderSource;


    private String orderSourceName;

    /**
     * 标准价
     */

    private BigDecimal basePrice;

    /**
     * 优惠金额
     */

    private BigDecimal discountAmount;

    /**
     * 应付金额
     */

    private BigDecimal payableAmount;

    /**
     * 实付金额
     */

    private BigDecimal paidAmount;

    /**
     * 签单金额
     */

    private BigDecimal signAmount;

    /**
     * 折扣审核状态：0无需审核，1待审核，2审核通过，3审核失败
     */

    private Integer discountAuditStatus;


    private String discountAuditStatusName;

    /**
     * 推送状态：0未推送，1已推送
     */

    private Integer pushStatus;

    /**
     * 支付状态：1待付款，2付款中，3已付款，4未付款
     */

    private Integer payStatus;


    private String payStatusName;

    /**
     * 合同状态：0-无需审核，1-审核待发起，2-审核中，3审核通过，4-审核失败
     */

    private Integer contractStatus;


    private String contractStatusName;

    /**
     * 确认状态：0待确认，1已确认
     */

    private Integer confirmStatus;

    /**
     * 支付类型：1 一次性全额支付，2分批支付
     */

    private Integer payType;

    /**
     * 视图状态：1-待审核，2-待推送，3-待确认，4-合同审核待发起，5-合同审核中，6-已确认待支付，7-已完成，8-取消
     */

    private Integer viewStatus;


    private String viewStatusName;

    /**
     * 订单备注
     */

    private String orderDesc;

    /**
     * 订单所属销售部门id
     */

    private Long deptId;

    /**
     * 订单所属事业中心部门ID
     */

    private Long careerId;

    /**
     * 订单所属销售公司id
     */

    private Long companyId;

    /**
     * 订单所属区域id
     */

    private Long regionId;

    /**
     * 删除标记: 0-未删除|1-删除
     */

    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 更新人名称
     */
    private String updateUserName;

    /**
     * 完成时间
     */

    private Date finishTime;

    /**
     * 订单商品信息
     * */

    private List<OrderSimpleProductResponse> productResponseList;


    private List<OrderSimpleProductSpecResponse> specResponseList;


    private List<OrderSimpleProductSpecItemResponse> specItemResponseList;

    /**
     *相关联系人
     * */

    private List<OrderContactResponse> orderContactResponseList;

    /**
     * 支付记录
     * */

    private List<OrderPayDetailResponse> payDetailResponses;

    /**
     * 客户类型 2:个人 1:企业
     **/

    private Integer customerType;

    /**
     * 分期状态：1 不可分期，2 可分期 ，3 分期申请中 ，4 分期成功，5 分期申请失败
     */
    private Integer installmentStatus;

    /**
     * 分期详情集合
     */
    private List<InstallmentDetailResponse> installmentDetailResponseList;

    /**
     * 分期商品集合
     */
    private List<InstallmentProductResponse> installmentProductResponseList;

    /**
     * 分期规格集合
     */
    private List<InstallmentSpecResponse> installmentSpecResponseList;

}
