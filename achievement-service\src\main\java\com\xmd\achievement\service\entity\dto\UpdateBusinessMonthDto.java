package com.xmd.achievement.service.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/19 10:06
 * @version: 1.0.0
 * @return {@link }
 */
@Data
@Schema(description = "商务月修改功能入参")
public class UpdateBusinessMonthDto implements Serializable {
    private static final long serialVersionUID = -783312549637218645L;
    /**
     * 商务月id
     */
    @NotNull
    @Schema(description = "商务月id")
    private Long monthId;

    /**
     * 月半日期
     */
    @NotNull
    @Schema(description = "月半日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date midDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 是否为春节月 0-否 1-是
     */
    @Schema(description = "是否为春节月 0-否 1-是")
    private Integer isSpringFestivalMonth;
}
