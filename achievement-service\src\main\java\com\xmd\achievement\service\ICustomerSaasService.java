package com.xmd.achievement.service;

import java.util.List;
import java.util.Map;

import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.dao.entity.CustomerSaasModel;
import com.xmd.achievement.service.entity.response.CustomerChurnResponse;

public interface ICustomerSaasService {
    public void customerSaasJob();
    
    public void recalculateHistory();

    public void handler(MqOrderPaymentInfoModel model);

    public void  refundHandler(Long orderId,Long productId,String orderProductCode);

    public void refundHandler(MqOrderRefundInfoModel refundTask);

    public void deleteHandler(Long orderId, Long productId,String orderProductCode);

    public void updateHandler(Long orderId, Long productId,String orderProductCode, String businessId,String businessRepresentative);

    public List<CustomerSaasModel> searchCustomerSaas(List<String> businessIds, String startDate, String endDate);

    public int searchCount(String businessId, Long monthId);

    public void recalculateAllSaasStatus();

    /**
     * 获取客户流失日期列表（带缓存）
     * @param customerId 客户ID
     * @return 客户流失日期列表
     */
    public List<String> getCustomerLossDateListWithCache(String customerId);

    /**
     * 获取客户流失信息（带缓存）
     * @param customerId 客户ID
     * @return 客户流失信息
     */
    public CustomerChurnResponse getCustomerChurnInfoWithCache(String customerId);

    /**
     * 获取SaaS相关Redis值
     * @return 包含两个key的值的Map
     */
    public Map<String, String> getSaasRedisValues();

    /**
     * 清除客户SaaS Redis值
     * @return 是否清除成功
     */
    public boolean clearCustomerSaasRedisValue();

    /**
     * 清除SaaS状态重算 Redis值
     * @return 是否清除成功
     */
    public boolean clearSaasStatusRecalcRedisValue();

    boolean clearRetryRedisValue();

    /**
     * 获取修复orderProductId的进度状态
     */
    String getFixOrderProductIdProgress();

    /**
     * 清除修复orderProductId的进度状态
     */
    boolean clearFixOrderProductIdProgress();

    /**
     * 修复MqOrderPaymentInfo表中缺失的orderProductId
     */
    void fixMissingOrderProductId();
}
