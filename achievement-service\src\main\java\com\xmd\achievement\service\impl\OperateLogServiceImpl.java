package com.xmd.achievement.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.OperateLogModel;
import com.xmd.achievement.dao.repository.IOperateLogRepository;
import com.xmd.achievement.rpc.entity.dto.UserLoginInfoDTO;
import com.xmd.achievement.service.OperateLogService;
import com.xmd.achievement.support.constant.enums.OperateTypeEnum;
import com.xmd.achievement.support.constant.enums.SourceSystemEnum;
import com.xmd.achievement.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志服务实现类
 */
@Service
@Slf4j
public class OperateLogServiceImpl implements OperateLogService {

    @Autowired
    private IOperateLogRepository operateLogRepository;

    /**
     * 默认的系统来源
     */
    private static final SourceSystemEnum DEFAULT_SOURCE_SYSTEM = SourceSystemEnum.ACHIEVEMENT;

    private static final String DEFAULT_REMARK = "";

    @Override
    public boolean save(Object beforeObj, Object afterObj, OperateTypeEnum operationType, SourceSystemEnum sourceSystem,String remark) {
        try {
            OperateLogModel operateLog = createOperateLog(beforeObj, afterObj, operationType, sourceSystem, remark);
            if (checkAndSave(operateLog)) {
                return operateLogRepository.save(operateLog);
            }
            return false;
        } catch (Exception e) {
            log.error("保存操作日志失败", e);
            return false;
        }
    }
    
    @Override
    public boolean save(Object beforeObj, Object afterObj, OperateTypeEnum operationType) {
        return save(beforeObj, afterObj, operationType, DEFAULT_SOURCE_SYSTEM, DEFAULT_REMARK);
    }

    @Override
    public boolean saveBatch(List<OperateLogModel> operateLogs) {
        if (CollectionUtils.isEmpty(operateLogs)) {
            return false;
        }
        try {
            for (OperateLogModel operateLog : operateLogs) {
                if (!checkAndSave(operateLog)) {
                    return false;
                }
            }
            return operateLogRepository.saveBatch(operateLogs);
        } catch (Exception e) {
            log.error("批量保存操作日志失败", e);
            return false;
        }
    }

    @Override
    public boolean save(Object beforeObj, Object afterObj, OperateTypeEnum operationType, String remark) {
        return save(beforeObj, afterObj, operationType, DEFAULT_SOURCE_SYSTEM, remark);
    }

    @Override
    public boolean save(Class<?> clazz, String beforeJson, String afterJson, OperateTypeEnum operationType, SourceSystemEnum sourceSystem, String remark) {
        try {
            OperateLogModel operateLog = createOperateLog(clazz, beforeJson, afterJson, operationType, sourceSystem, remark);
            if (checkAndSave(operateLog)) {
                return operateLogRepository.save(operateLog);
            }
            return false;
        } catch (Exception e) {
            log.error("保存操作日志失败", e);
            return false;
        }
    }

    private boolean checkAndSave(OperateLogModel operateLog) {
        if (operateLog == null) {
            return false;
        }
        if (operateLog.getBeforeData() == null && operateLog.getAfterData() == null) {
            return false;
        }
        if (operateLog.getOperationType() == null) {
            return false;
        }
        if (operateLog.getClassName() == null) {
            return false;
        }
        if (operateLog.getSourceSystem() == null) {
            operateLog.setSourceSystem(DEFAULT_SOURCE_SYSTEM.getCode());
        }
        operateLog.setLogTime(LocalDateTime.now());            
        // 获取当前用户信息
        UserLoginInfoDTO currentUser = UserContext.getCurrentUserInfo();
        if (currentUser != null) {
            operateLog.setLogUserId(currentUser.getUserId());
            operateLog.setLogUserName(currentUser.getName());
        }            
        return true;
    }

    @Override
    public OperateLogModel createOperateLog(Object beforeObj, Object afterObj, OperateTypeEnum operationType, SourceSystemEnum sourceSystem,String remark) {
        OperateLogModel operateLog = new OperateLogModel();
        operateLog.setOperateLogId(IdUtil.getSnowflakeNextId());
        operateLog.setBeforeData(beforeObj != null ? JSON.toJSONString(beforeObj) : null);
        operateLog.setAfterData(afterObj != null ? JSON.toJSONString(afterObj) : null);
        String className = null;
        if (beforeObj != null) {
            className = beforeObj.getClass().getName();
        } else if (afterObj != null) {
            className = afterObj.getClass().getName();
        }
        operateLog.setOperationType(operationType.getCode());
        operateLog.setClassName(className);
        operateLog.setSourceSystem(sourceSystem==null?DEFAULT_SOURCE_SYSTEM.getCode():sourceSystem.getCode());
        operateLog.setRemark(remark);
        return operateLog;
    }

    @Override
    public OperateLogModel createOperateLog(Object beforeObj, Object afterObj, OperateTypeEnum operationType) {
        return createOperateLog(beforeObj, afterObj, operationType, DEFAULT_SOURCE_SYSTEM,DEFAULT_REMARK);
    }

    @Override
    public OperateLogModel createOperateLog(Class<?> clazz, String beforeJson, String afterJson, OperateTypeEnum operationType, SourceSystemEnum sourceSystem, String remark) {
        OperateLogModel operateLog = new OperateLogModel();
        operateLog.setOperateLogId(IdUtil.getSnowflakeNextId());
        operateLog.setBeforeData(beforeJson);
        operateLog.setAfterData(afterJson);
        operateLog.setOperationType(operationType.getCode());
        operateLog.setClassName(clazz.getName());
        operateLog.setSourceSystem(sourceSystem == null ? DEFAULT_SOURCE_SYSTEM.getCode() : sourceSystem.getCode());
        operateLog.setRemark(remark);
        return operateLog;
    }
}