package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单退转款消息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface IMqOrderRefundInfoRepository extends IService<MqOrderRefundInfoModel> {
        List<MqOrderRefundInfoModel> selectByOrderNo(String orderNo);

        MqOrderRefundInfoModel getByMessageId(String messageId);
}
