package com.xmd.achievement.util.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/18 10:59
 * @version: 1.0.0
 * @return {@link }
 */
@Getter
public enum MainSplitPersonEnum {
    /**
     * 1 主
     */
    MAIN(1,"主"),
    /**
     * 2 辅
     */
    ASSISTANT(2,"辅");

    private final Integer mainSplitPerson;
    private final String message;

    MainSplitPersonEnum(Integer mainSplitPerson, String message) {
        this.mainSplitPerson = mainSplitPerson;
        this.message = message;
    }

    public static List<Integer> getMainSplitPersonList() {
        return Arrays.stream(MainSplitPersonEnum.values()).map(MainSplitPersonEnum::getMainSplitPerson).collect(Collectors.toList());
    }



    public Integer getCode() {
        return mainSplitPerson;
    }

    public String getMessage() {
        return message;
    }

    public static MainSplitPersonEnum fromCode(int code) {
        for (MainSplitPersonEnum value : MainSplitPersonEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("MainSplitPersonEnum code: " + code);
    }
}