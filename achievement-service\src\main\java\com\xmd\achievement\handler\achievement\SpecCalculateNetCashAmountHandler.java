package com.xmd.achievement.handler.achievement;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.PolicySpecDetailModel;
import com.xmd.achievement.service.IPolicyService;
import com.xmd.achievement.support.constant.NumberConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * * ●净现金(业绩)
 * ○只有主单人时：净现金(业绩)=实付金额x业绩核算比例；
 * ○主单人分单人都有时，每个人的净现金=实付金额x业绩核算比例x50%；
 *
 * <AUTHOR>
 * @date: 2024/12/20 14:49
 */
@Slf4j
@Service
public class SpecCalculateNetCashAmountHandler implements CalculateAmountHandler {
    @Resource
    private IPolicyService policyService;

    @Override
    public void calculate(CalculateFactInfo factInfo) {
        try {
            AchievementSpecDetailModel spec = factInfo.getSpec();
            if (factInfo.getZeroNetCash()) {
                spec.setNetCash(BigDecimal.ZERO);
            } else {
                calculateProcess(factInfo, spec);
            }
        } finally {
            log.warn("净现金业绩 规格id:{},参数:{}", factInfo.getSpec().getSpecId(), JSON.toJSONString(factInfo));
        }
    }

    private void calculateProcess(CalculateFactInfo factInfo, AchievementSpecDetailModel spec) {
        if (spec.getPaidAmount().compareTo(BigDecimal.ZERO) == 0) {
            spec.setNetCash(BigDecimal.ZERO);
        } else {
            boolean splitOrder = factInfo.isSplitOrder();
            PolicySpecDetailModel policy = policyService.getPolicyDetailBySpecId(spec.getSpecId());
            if (null == policy) {
                throw new RuntimeException("规格ID:" + spec.getSpecId() + "未配置商品业绩政策");
            }
            BigDecimal amount = spec.getPaidAmount().multiply(NumberUtil.div(policy.getAchievementRatio(), NumberConstants.INTEGER_VALUE_100)).multiply(splitOrder ? BigDecimal.valueOf(NumberConstants.DOUBLE_VALUE_0_5) : BigDecimal.ONE).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
            spec.setNetCash(amount);
        }
    }
}
