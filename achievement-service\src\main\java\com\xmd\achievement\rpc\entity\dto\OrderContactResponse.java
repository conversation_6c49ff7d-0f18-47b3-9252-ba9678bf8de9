package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 联系人信息
 * <AUTHOR>
 * @date 2024年11月11日 下午1:30
 */
@Data

public class OrderContactResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;

    /**
     * 订单联系人id
     */

    private Long orderContactId;

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 用户id
     */

    private String userId;

    /**
     * 联系人名称
     */

    private String name;

    /**
     * 联系人类型：1=客户联系人，2=卖方联系人，3=卖方分单人
     */

    private Integer type;

    /**
     * 所属机构类型：1=基础平台 2=中企
     */

    private Integer orgType;

    /**
     * 机构编码
     */

    private String orgCode;

    /**
     * 职位编码
     */

    private String positionCode;

    /**
     * 职位名称
     */

    private String positionName;

    /**
     * 联系人手机
     */

    private String mobile;

    /**
     * 联系人邮箱
     */

    private String email;

    /**
     * 删除标记: 0-未删除|1-删除
     */

    private Integer deleteFlag;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 创建人id
     */

    private String createUserId;

    /**
     * 创建人名称
     */

    private String createUserName;

    /**
     * 更新时间
     */

    private Date updateTime;

    /**
     * 更新人id
     */

    private String updateUserId;

    /**
     * 更新人名称
     */

    private String updateUserName;
}
