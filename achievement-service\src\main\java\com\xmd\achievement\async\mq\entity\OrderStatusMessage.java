package com.xmd.achievement.async.mq.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 订单消息状态
 * <AUTHOR>
 * @date 2024年11月14日 下午5:18
 */
@Data
public class OrderStatusMessage implements Serializable {
    private static final long serialVersionUID = -2366170322904213967L;
    /**
     * 订单id
     */
    private Long orderId;


    /**
     * 订单状态：1创建完成，2已完成，3已取消，4已退款
     */
    private Integer orderStatus;

    public enum OrderStatusEnum {
        WAITING_AUDIT(1, "待审核"),
        WAITING_PUSH(2, "待推送"),
        WAITING_CONFIRM(3, "待确认"),
        WAITING_START_CONTRACT_AUDIT(4, "合同审核待发起"),
        CONTRACT_AUDITING(5, "合同审核中"),
        CONFIRM_WAITING_PAY(6, "已确认待支付"),
        FINISHED(7, "已完成"),
        CANCEL(8, "取消");

        private final Integer code;
        private final String name;

        OrderStatusEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

}
