package com.xmd.achievement.service.entity.response;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import cn.idev.excel.annotation.format.NumberFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/25 14:37
 * @version: 1.0.0
 * @return {@link }
 */
@Data
public class AchievementProductDetailExcel {
    @ExcelProperty("商务月")
    private String businessMonth;

    @ExcelProperty("业绩流水ID")
    private String achievementId;

    @ExcelProperty("订单明细编号")
    private String orderProductId;

    @ExcelProperty("商品名称")
    private String productName;

    @ExcelProperty("商品类型")
    private String productType;

    @ExcelProperty("订单编号")
    private String orderNo;

    @ExcelProperty("客户名称")
    private String customerName;

    @ExcelProperty("客户类型")
    private String customerType;

    @ExcelProperty("客户所在地区")
    private String customerRegion;

    @ExcelProperty("合同编号")
    private String contractNo;

    @ExcelProperty("业绩流水生成时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date statisticsTime;

    @ExcelProperty("签单日期")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date signedTime;

    @ExcelProperty("到账时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    @ExcelProperty("主分单人")
    private String mainSplitPerson;

    @ExcelProperty("分司ID")
    private String companyId;

    @ExcelProperty("分司")
    private String company;

    @ExcelProperty("事业部ID")
    private String divisionId;

    @ExcelProperty("事业部名称")
    private String division;

    @ExcelProperty("商务部门ID")
    private String deptId;

    @ExcelProperty("商务部门")
    private String department;

    @ExcelProperty("商务ID")
    private String businessId;

    @ExcelProperty("商务名称")
    private String businessRepresentative;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("业务类型")
    private String saleType;

    @ExcelProperty("标准报价")
    @NumberFormat("0.000")
    private BigDecimal standardPrice;

    @ExcelProperty("应付金额")
    @NumberFormat("0.000")
    private BigDecimal payableAmount;

    @ExcelProperty("实际到账")
    @NumberFormat("0.000")
    private BigDecimal paidAmount;

    @ExcelProperty("首年到账")
    @NumberFormat("0.000")
    private BigDecimal firstYearRevenue;

    @ExcelProperty("首年报价")
    @NumberFormat("0.000")
    private BigDecimal firstYearQuote;

    @ExcelProperty("续费到账")
    @NumberFormat("0.000")
    private BigDecimal renewalRevenue;

    @ExcelProperty("续费报价")
    @NumberFormat("0.000")
    private BigDecimal renewalQuote;

    @ExcelProperty("净现金")
    @NumberFormat("0.000")
    private BigDecimal netCash;

    @ExcelProperty("商代提成")
    @NumberFormat("0.000")
    private BigDecimal agentCommissionAchievement;

    @ExcelProperty("实发商代提成业绩")
    @NumberFormat("0.000")
    private BigDecimal agentActualCommission;

    @ExcelProperty("缓发商代提成业绩")
    @NumberFormat("0.000")
    private BigDecimal agentDeferredCommission;

    @ExcelProperty("部门提成业绩")
    @NumberFormat("0.000")
    private BigDecimal deptCommission;

    @ExcelProperty("事业部提成业绩")
    @NumberFormat("0.000")
    private BigDecimal divCommission;

    @ExcelProperty("分公司提成业绩")
    @NumberFormat("0.000")
    private BigDecimal branchCommission;

    @ExcelProperty("数据改动标识")
    private String dataChangeType;

    @ExcelProperty("saas商品")
    private String isSaas;

    @ExcelProperty("数据更新备注")
    private String latestRemark;
}
