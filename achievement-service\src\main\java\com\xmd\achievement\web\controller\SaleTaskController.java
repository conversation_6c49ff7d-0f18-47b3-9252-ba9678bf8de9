package com.xmd.achievement.web.controller;

import com.xmd.achievement.service.SaleTaskService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.OpenSaleTaskResponse;
import com.xmd.achievement.service.entity.response.QuerySaleTaskListResponse;
import com.xmd.achievement.service.entity.response.SaleTaskBranchResponse;
import com.xmd.achievement.service.entity.response.SaleTaskListInfoResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 销售任务
 *
 * <AUTHOR>
 * @date 2024/3/27/10:35
 */
@Slf4j
@RestController
@RequestMapping("/saleTask")
@Tag(name = "销售任务")
public class SaleTaskController {

    @Autowired
    private SaleTaskService saleTaskService;

    @Operation(summary = "销售任务列表")
    @PostMapping("queryList")
    public WebResult<QuerySaleTaskListResponse> queryList(@RequestBody @Valid QuerySaleTaskListRequest listRequest) {
        log.info("销售任务列表");
        return WebResult.success(saleTaskService.queryList(listRequest));
    }

    @Operation(summary = "导入销售任务")
    @PostMapping("/importSaleTask")
    public WebResult<Void> importSaleTask(@RequestParam("file") MultipartFile file) {
        try {
            saleTaskService.excelImport(file);
        } catch (IOException e) {
            log.error("导入销售任务失败", e);
        }
        return WebResult.success();
    }

    @Operation(summary = "提交任务")
    @PostMapping("commit")
    public WebResult<Void> commit(@RequestBody @Valid SaleTaskCommitRequest taskList) {
        log.info("提交任务");
        return saleTaskService.taskCommit(taskList);
    }

    @Operation(summary = "任务提交弹窗信息")
    @GetMapping("getPreCommitInfo")
    public WebResult<List<QuerySaleTaskListResponse>> getPreCommitInfo(@RequestParam("businessMonth") String businessMonth) {
        log.info("任务提交弹窗信息");
        return WebResult.success(saleTaskService.getPreCommitInfo(businessMonth));
    }

    @Operation(summary = "撤销任务")
    @PostMapping("revoke/{id}")
    public WebResult<Void> revoke(@PathVariable("id") Long id) {
        log.info("撤销任务");
        saleTaskService.revoke(id);
        return WebResult.success();
    }

    @Operation(summary = "任务查看")
    @GetMapping("view/{id}")
    public WebResult<SaleTaskListInfoResponse> view(@PathVariable("id") Long id) {
        log.info("任务查看");
        return WebResult.success(saleTaskService.view(id));
    }

    @Operation(summary = "任务审核")
    @PostMapping("audit")
    public WebResult<Void> audit(@RequestBody @Valid SaleTaskAuditRequest auditRequest) {
        log.info("任务审核");
        saleTaskService.audit(auditRequest);
        return WebResult.success();
    }

    @Operation(summary = "获取编辑信息")
    @GetMapping("editInfo/{id}")
    public WebResult<SaleTaskListInfoResponse> editInfo(@PathVariable("id") Long id) {
        log.info("获取任务编辑信息");
        return WebResult.success(saleTaskService.editInfo(id));
    }

    @Operation(summary = "任务编辑")
    @PostMapping("edit")
    public WebResult<Void> edit(@RequestBody @Valid SaleTaskEditRequest request) {
        log.info("任务编辑");
        return saleTaskService.edit(request);
    }

    @Operation(summary = "特殊任务添加查询分司部门信息")
    @GetMapping("queryCompanyList")
    public WebResult<List<SaleTaskBranchResponse>> queryCompanyList(@RequestParam("businessMonth") String businessMonth) {
        return WebResult.success(saleTaskService.queryCompanyList(businessMonth));
    }

    @Operation(summary = "批量提交任务")
    @PostMapping("commitBatch")
    public WebResult<Void> commitBatch(@RequestBody SaleTaskBranchCommitRequest taskList) {
        return saleTaskService.commitBatch(taskList);
    }

    @Operation(summary = "修改任务")
    @PostMapping("updateTask")
    public WebResult<Boolean> updateTask(@RequestBody @Valid SaleTaskEditRequest task) {
        return saleTaskService.updateTask(task);
    }
}
