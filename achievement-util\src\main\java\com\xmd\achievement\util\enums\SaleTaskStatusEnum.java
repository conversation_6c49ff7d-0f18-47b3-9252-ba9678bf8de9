package com.xmd.achievement.util.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-28
 */
@Getter
@AllArgsConstructor
public enum SaleTaskStatusEnum {

    INIT(0, "初始化"),
    ISSUED(1, "已下发"),
    UNCOMMITTED(2, "未提交"),
    REVOKED(3, "已撤销"),
    PENDING_APPROVAL(4, "待审核"),
    PASSED(5, "已通过"),
    REJECTED(6, "被驳回"),
    COMMITTED(7, "已提交")
    ;

    final int key;
    final String value;

}
