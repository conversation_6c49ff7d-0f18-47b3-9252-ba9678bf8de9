package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 订单类型枚举
 * <AUTHOR>
 * @date: 2024/12/20 15:21
 */
@Getter
public enum OrderTypeEnum {
    NORMAL(1, "普通订单"), DISCOUNT(2, "折扣订单");
    private final Integer type;
    private final String description;

    OrderTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }
}
