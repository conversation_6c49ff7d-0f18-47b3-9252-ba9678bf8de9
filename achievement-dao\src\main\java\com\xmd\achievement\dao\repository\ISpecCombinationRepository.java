package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.SpecCombinationModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规格组合表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface ISpecCombinationRepository extends IService<SpecCombinationModel> {

    List<SpecCombinationModel> selectAllSpecCombination();
}
