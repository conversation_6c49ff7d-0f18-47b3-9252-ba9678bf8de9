package com.xmd.achievement.web.controller;

/*
 * 业绩导出
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/10/17:05
 * @since 1.0
 */

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.ExportPerformanceServcie;
import com.xmd.achievement.service.entity.request.ExportPerformancRequest;
import com.xmd.achievement.service.entity.response.PrformanceInfoResponse;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;
import com.xmd.achievement.web.util.DateUtils;
import com.xmd.achievement.web.util.EasyExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Tag(name = "API-EPC-销管需求-业绩导出")
@Slf4j
@RestController
@RequestMapping("/achievement/export")
public class ExportPerformanceController {
    @Resource
    ExportPerformanceServcie exportPerformanceServcie;

    @Operation(summary = "API-EPC-01：业绩导出")
    @PostMapping(value = "/performance")
    public void exportPerformance(@RequestBody @Valid ExportPerformancRequest request, HttpServletResponse response) {
        log.info("API-EPC-01：业绩导出，请求参数:{}", JSONUtil.toJsonStr(request));
        try {
            //查询数据
            List<PrformanceInfoResponse> responseList = exportPerformanceServcie.export(request);
            //导出
            EasyExcelUtil.download(response, responseList, "业绩统计-" + DateUtils.format(new Date(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss), Boolean.TRUE);
        } catch (Exception e) {
            log.error("业绩导出异常", e);
            throw new BusinessException("业绩导出异常");
        }
    }
}
