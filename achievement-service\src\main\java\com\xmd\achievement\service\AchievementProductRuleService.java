package com.xmd.achievement.service;

import com.xmd.achievement.service.entity.request.AddProductRuleConfigRequest;
import com.xmd.achievement.service.entity.request.CheckProductRuleStatusRequest;
import com.xmd.achievement.service.entity.request.QueryRuleProductRequest;
import com.xmd.achievement.service.entity.request.PageQueryProductRuleConfigRequest;
import com.xmd.achievement.service.entity.response.QueryProductRuleConfigResponse;
import com.xmd.achievement.service.entity.response.QueryRuleProductResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/09:45
 * @since 1.0
 */
public interface AchievementProductRuleService {
    WebResult<Boolean> addProductRuleConfig(@Valid AddProductRuleConfigRequest request);

    Boolean checkProductRuleStatus(@Valid CheckProductRuleStatusRequest request);

    List<QueryProductRuleConfigResponse> queryProductRuleConfig();

    List<QueryRuleProductResponse> queryRuleProduct(@Valid QueryRuleProductRequest request);
}
