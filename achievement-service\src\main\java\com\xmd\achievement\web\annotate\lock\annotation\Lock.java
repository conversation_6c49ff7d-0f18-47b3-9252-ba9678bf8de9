package com.xmd.achievement.web.annotate.lock.annotation;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Order(Integer.MIN_VALUE)
public @interface Lock {

    /**
     * lock key
     */
    String value();

    long expireTime() default 50000L;

    long waitTime() default 100L;

}
