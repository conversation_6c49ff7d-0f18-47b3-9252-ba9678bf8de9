package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/09/16:12
 * @since 1.0
 */
@Data
public class QueryLeveRelationResponse implements Serializable {
    @Schema(description = "当前组织信息")
    private CurrentLeveRelationResponse currentLeveOrgInfo;

    @Schema(description = "上级组织信息")
    private UpLeveRelationResponse upLeveOrgInfo;

    @Schema(description = "下级组织信息")
    private List<LowerLeveRelationResponse> lowerLeveOrgInfos;
}
