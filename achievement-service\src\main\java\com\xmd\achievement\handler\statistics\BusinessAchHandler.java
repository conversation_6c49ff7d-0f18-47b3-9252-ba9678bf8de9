package com.xmd.achievement.handler.statistics;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.OrgPathInfoDTO;
import com.xmd.achievement.rpc.entity.dto.UserInfoDetailResp;
import com.xmd.achievement.service.IBusinessAchievementService;
import com.xmd.achievement.service.ICustomerSaasService;
import com.xmd.achievement.service.ISaasTabService;
import com.xmd.achievement.service.ISegmentService;
import com.xmd.achievement.util.enums.BusinessAchievementUpdateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商务业绩统计
 *
 * <AUTHOR>
 * @date: 2024/12/25 09:27
 */
@Slf4j
@Service
public class BusinessAchHandler {
    @Resource
    private BusinessHandler businessHandler;
    @Resource
    private NetCashReceiptHandler netCashReceiptHandler;
    @Resource
    private ActualCommissionHandler actualCommissionHandler;
    @Resource
    private NonRenewalOrdersHandler nonRenewalOrdersHandler;
    @Resource
    private NewWebsiteCountHandler newWebsiteCountHandler;
    @Resource
    private WebsiteNonRenewalCustomersHandler websiteNonRenewalCustomersHandler;
    @Resource
    private AgentCommissionAchievementHandler agentCommissionAchievementHandler;
    @Resource
    private NewOldCustomerCountHandler newOldCustomerCountHandler;
    @Resource
    private SiteNetCashReceiptHandler siteNetCashReceiptHandler;

    public List<StatisticsHandler> handlers = new ArrayList<>();
    @Resource
    private IBusinessAchievementService businessAchievementService;
    @Resource
    private ISegmentService segmentService;
    @Resource
    private InnerService innerService;
    @Resource
    private ICustomerSaasService customerSaasService;

    @Resource
    private ISaasTabService  saasTabService;
    @PostConstruct
    public void init() {
        handlers.add(businessHandler);
        handlers.add(netCashReceiptHandler);
        handlers.add(newOldCustomerCountHandler);
        handlers.add(actualCommissionHandler);
        handlers.add(nonRenewalOrdersHandler);
        handlers.add(newWebsiteCountHandler);
        handlers.add(websiteNonRenewalCustomersHandler);
        handlers.add(agentCommissionAchievementHandler);
        handlers.add(siteNetCashReceiptHandler);
    }

    @Transactional(rollbackFor = Exception.class)
    public void achStatistics(List<AchievementProductDetailModel> achList, Integer status, Integer updateType) {
        //取出第一条商品业绩
        AchievementProductDetailModel achievementProductDetailModel = achList.get(0);
        //获取商务月ID
        Long monthId = achievementProductDetailModel.getBusinessMonthId();
        //获取商务月
        String businessMonth = achievementProductDetailModel.getBusinessMonth();

        //商务ID 映射 商品信息
        Map<String, List<AchievementProductDetailModel>> businessIdAchListMap = achList.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getBusinessId));
        ArrayList<BusinessAchievementModel> businessModels = new ArrayList<>();

        //查询所有商务月商务统计信息
        List<BusinessAchievementModel> businessAchStatModelList = businessAchievementService.selectByBusinessMonthList(businessMonth);
        List<Long> unList = new ArrayList<>();
        for (BusinessAchievementModel businessAchievementModel : businessAchStatModelList) {
            String employeeId = businessAchievementModel.getEmployeeId();
            // 如果 businessIdAchListMap 中没有对应的键，则将该元素加入 unmatchedList
            if (!businessIdAchListMap.containsKey(employeeId)) {
                unList.add(businessAchievementModel.getId());
            }
        }
        //删除没有业绩的商务统计信息
        if(CollectionUtils.isNotEmpty(unList)){
            businessAchievementService.deleteByIdList(unList);
        }


        //循环商务ID 映射 商品信息
        for (Map.Entry<String, List<AchievementProductDetailModel>> entry : businessIdAchListMap.entrySet()) {
            String businessId = entry.getKey();

            List<AchievementProductDetailModel> aches = entry.getValue();
            //拼装计算因子
            StatisticsFactInfo factInfo = new StatisticsFactInfo().setBusinessId(businessId).setAchList(aches).setStatus(status);
            for (StatisticsHandler handler : handlers) {
                handler.statistics(factInfo);
            }
            BusinessAchievementModel businessAchStatModel = businessAchievementService.getByMonthIdAndEmployeeId(monthId, businessId);
            if (businessAchStatModel == null) {
                businessAchStatModel = new BusinessAchievementModel()
                        .setEmployeeId(businessId)
                        .setBusinessMonthId(monthId)
                        .setBusinessMonth(businessMonth)
                        .setNewSaasCustomerCount(customerSaasService.searchCount(businessId, monthId));
            }
            businessAchStatModel.setNewSaasCustomerCount(customerSaasService.searchCount(businessId, monthId));
            //商务信息
            UserInfoDetailResp business = innerService.getUserInfoDetail(businessId);
            if (ObjectUtil.isNotEmpty(business)) {
                OrgPathInfoDTO businessOrgInfo = business.getOrgPathInfoDTO();
                if (ObjectUtil.isEmpty(businessOrgInfo)) {
                    log.info("订单id：{}商务id：{}所属机构不存在", achievementProductDetailModel.getOrderId(), businessId);
                } else {
                    businessAchStatModel.setEmployeeName(business.getName());
                    businessAchStatModel.setCompanyId(businessOrgInfo.getCompanyId());
                    businessAchStatModel.setCompany(businessOrgInfo.getCompanyName());
                    businessAchStatModel.setDivisionId(businessOrgInfo.getCareerId());
                    businessAchStatModel.setDivision(businessOrgInfo.getCareerName());
                    businessAchStatModel.setDeptId(businessOrgInfo.getDeptId());
                    businessAchStatModel.setDepartment(businessOrgInfo.getDeptName());
                }
                businessAchStatModel.setPosition(business.getPosition());
                //职级类型
                PositionModel positionType = factInfo.getPositionType();
                if (positionType != null) {
                    businessAchStatModel.setPositionName(positionType.getPositionName())
                            .setConfirmed(positionType.getConfirmed())
                            .setPositionCode(positionType.getPositionCode());
                }
                //司龄
                SeniorityModel seniority = factInfo.getSeniority();
                if (seniority != null) {
                    businessAchStatModel.setSeniorityId(seniority.getSeniorityId());
                    businessAchStatModel.setSenioritySegment(seniority.getSeniorityName());
                }
            }

            // mq消息生成业绩 || 更新客户类型，只需要更改新老客户记录表，前面会根据这张表统计，这里可以不做修改
            if (BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType().equals(updateType)
                    || BusinessAchievementUpdateTypeEnum.CUSTOMER_TYPE.getUpdateType().equals(updateType)) {
                addBusinessNew(businessAchStatModel, factInfo, businessModels);
            } else if (BusinessAchievementUpdateTypeEnum.BUSINESS_MONTH.getUpdateType().equals(updateType)
                    || BusinessAchievementUpdateTypeEnum.DELETE.getUpdateType().equals(updateType)) {
                decreaseBusiness(businessAchStatModel, factInfo, businessModels);
            }
        }
        List<BusinessAchievementModel> finalBusinessModels = businessModels.stream().filter(v -> {
            if (ObjectUtil.isEmpty(v.getEmployeeName()) || ObjectUtil.isEmpty(v.getBusinessMonth())) {
                log.info("商务业绩数据异常{}", JSONUtil.toJsonStr(v));
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        businessAchievementService.saveOrUpdateBatch(finalBusinessModels);
    }

    /**
     * 商务业绩统计，只统计单个商务的业绩
     *
     * @param achList    业绩数据
     * @param status     状态
     * @param updateType 更新类型
     * <AUTHOR>
     * @since 1.0
     */
    @Transactional(rollbackFor = Exception.class)
    public void achStatisticsForEmployeeId(List<AchievementProductDetailModel> achList, Integer status, Integer updateType) {
        //取出第一条商品业绩
        AchievementProductDetailModel achievementProductDetailModel = achList.get(0);
        //获取商务月ID
        Long monthId = achievementProductDetailModel.getBusinessMonthId();
        //获取商务月
        String businessMonth = achievementProductDetailModel.getBusinessMonth();

        String businessId = achievementProductDetailModel.getBusinessId();
        //拼装计算因子
        StatisticsFactInfo factInfo = new StatisticsFactInfo().setBusinessId(businessId).setAchList(achList).setStatus(status);
        for (StatisticsHandler handler : handlers) {
            handler.statistics(factInfo);
        }
        BusinessAchievementModel businessAchStatModel = businessAchievementService.getByMonthIdAndEmployeeId(monthId, businessId);
        if (businessAchStatModel == null) {
            businessAchStatModel = new BusinessAchievementModel()
                    .setEmployeeId(businessId)
                    .setBusinessMonthId(monthId)
                    .setBusinessMonth(businessMonth)
                    .setNewSaasCustomerCount(customerSaasService.searchCount(businessId, monthId));
        }
        businessAchStatModel.setNewSaasCustomerCount(customerSaasService.searchCount(businessId, monthId));
        //商务信息
        UserInfoDetailResp business = innerService.getUserInfoDetail(businessId);
        if (ObjectUtil.isNotEmpty(business)) {
            OrgPathInfoDTO businessOrgInfo = business.getOrgPathInfoDTO();
            if (ObjectUtil.isEmpty(businessOrgInfo)) {
                log.info("订单id：{}商务id：{}所属机构不存在", achievementProductDetailModel.getOrderId(), businessId);
            } else {
                businessAchStatModel.setEmployeeName(business.getName());
                businessAchStatModel.setCompanyId(businessOrgInfo.getCompanyId());
                businessAchStatModel.setCompany(businessOrgInfo.getCompanyName());
                businessAchStatModel.setDivisionId(businessOrgInfo.getCareerId());
                businessAchStatModel.setDivision(businessOrgInfo.getCareerName());
                businessAchStatModel.setDeptId(businessOrgInfo.getDeptId());
                businessAchStatModel.setDepartment(businessOrgInfo.getDeptName());
            }
            businessAchStatModel.setPosition(business.getPosition());
            //职级类型
            PositionModel positionType = factInfo.getPositionType();
            if (positionType != null) {
                businessAchStatModel.setPositionName(positionType.getPositionName())
                        .setConfirmed(positionType.getConfirmed())
                        .setPositionCode(positionType.getPositionCode());
            }
            //司龄
            SeniorityModel seniority = factInfo.getSeniority();
            if (seniority != null) {
                businessAchStatModel.setSeniorityId(seniority.getSeniorityId());
                businessAchStatModel.setSenioritySegment(seniority.getSeniorityName());
            }
        }

        // mq消息生成业绩 || 更新客户类型，只需要更改新老客户记录表，前面会根据这张表统计，这里可以不做修改
        ArrayList<BusinessAchievementModel> businessModels = new ArrayList<>();
        if (BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType().equals(updateType)
                || BusinessAchievementUpdateTypeEnum.CUSTOMER_TYPE.getUpdateType().equals(updateType)) {
            addBusinessNew(businessAchStatModel, factInfo, businessModels);
        } else if (BusinessAchievementUpdateTypeEnum.BUSINESS_MONTH.getUpdateType().equals(updateType)
                || BusinessAchievementUpdateTypeEnum.DELETE.getUpdateType().equals(updateType)) {
            decreaseBusiness(businessAchStatModel, factInfo, businessModels);
        }

        List<BusinessAchievementModel> finalBusinessModels = businessModels.stream().filter(v -> {
            if (ObjectUtil.isEmpty(v.getEmployeeName()) || ObjectUtil.isEmpty(v.getBusinessMonth())) {
                log.info("商务业绩数据异常{}", JSONUtil.toJsonStr(v));
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        businessAchievementService.saveOrUpdateBatch(finalBusinessModels);
    }

    private void decreaseBusiness(BusinessAchievementModel businessAchStatModel, StatisticsFactInfo factInfo, ArrayList<BusinessAchievementModel> businessModels) {
        // 修改了商品业绩的商务月,需要把原商务月的该条业绩删掉
        //业绩分段设置
        BigDecimal totalNetCashReceipt = businessAchStatModel.getNetCashReceipt() == null ? factInfo.getNetCashReceipt().negate() : NumberUtil.add(businessAchStatModel.getNetCashReceipt(), factInfo.getNetCashReceipt().negate());
        AchievementSegmentModel segment = segmentService.getSegment(totalNetCashReceipt);
        if (null != segment) {
            businessAchStatModel.setAchievementSegment(segment.getSegmentName());
            businessAchStatModel.setAchievementSegmentId(segment.getSegmentId());
        }
        businessAchStatModel.setTenure(factInfo.getTenure())
                .setNetCashReceipt(businessAchStatModel.getNetCashReceipt() == null ? factInfo.getNetCashReceipt().negate() : NumberUtil.add(businessAchStatModel.getNetCashReceipt(), factInfo.getNetCashReceipt().negate()))
                .setActualCommission(businessAchStatModel.getActualCommission() == null ? factInfo.getActualCommission().negate() : NumberUtil.add(businessAchStatModel.getActualCommission(), factInfo.getActualCommission().negate()))
                .setNewCustomerCount(factInfo.getNewCustomerCount())
                .setOldCustomerCount(factInfo.getOldCustomerCount())
                .setNonRenewalOrders(businessAchStatModel.getNonRenewalOrders() == null ? Math.negateExact(factInfo.getNonRenewalOrders()) : businessAchStatModel.getNonRenewalOrders() + Math.negateExact(factInfo.getNonRenewalOrders()))
                .setNewWebsiteCount(businessAchStatModel.getNewWebsiteCount() == null ? Math.negateExact(factInfo.getNewWebsiteCount()) : businessAchStatModel.getNewWebsiteCount() + Math.negateExact(factInfo.getNewWebsiteCount()))
                .setWebsiteNonRenewalCustomers(businessAchStatModel.getWebsiteNonRenewalCustomers() == null ? Math.negateExact(factInfo.getWebsiteNonRenewalCustomers()) : businessAchStatModel.getWebsiteNonRenewalCustomers() + Math.negateExact(factInfo.getWebsiteNonRenewalCustomers()))
                .setAgentCommissionAchievement(businessAchStatModel.getAgentCommissionAchievement() == null ? factInfo.getAgentCommissionAchievement().negate() : NumberUtil.add(businessAchStatModel.getAgentCommissionAchievement(), factInfo.getAgentCommissionAchievement().negate()))
                .setSiteNetCashReceipt(businessAchStatModel.getSiteNetCashReceipt() == null ? factInfo.getSiteNetCashReceipt().negate() : NumberUtil.add(businessAchStatModel.getSiteNetCashReceipt(), factInfo.getSiteNetCashReceipt().negate()))
                .setSaasNetCash(businessAchStatModel.getSaasNetCash() == null ? factInfo.getSaasNetCash().negate() : NumberUtil.add(businessAchStatModel.getSaasNetCash(), factInfo.getSaasNetCash().negate())).setUpdateTime(new Date());
        businessModels.add(businessAchStatModel);
    }

    private void addBusiness(BusinessAchievementModel businessAchStatModel, StatisticsFactInfo factInfo, ArrayList<BusinessAchievementModel> businessModels) {
        //业绩分段设置
        BigDecimal totalNetCashReceipt = businessAchStatModel.getNetCashReceipt() == null ? factInfo.getNetCashReceipt() : NumberUtil.add(businessAchStatModel.getNetCashReceipt(), factInfo.getNetCashReceipt());
        AchievementSegmentModel segment = segmentService.getSegment(totalNetCashReceipt);
        if (null != segment) {
            businessAchStatModel.setAchievementSegment(segment.getSegmentName());
            businessAchStatModel.setAchievementSegmentId(segment.getSegmentId());
        }
        businessAchStatModel.setTenure(factInfo.getTenure())
                .setNetCashReceipt(businessAchStatModel.getNetCashReceipt() == null ? factInfo.getNetCashReceipt() : NumberUtil.add(businessAchStatModel.getNetCashReceipt(), factInfo.getNetCashReceipt()))
                .setActualCommission(businessAchStatModel.getActualCommission() == null ? factInfo.getActualCommission() : NumberUtil.add(businessAchStatModel.getActualCommission(), factInfo.getActualCommission()))
                .setNewCustomerCount(factInfo.getNewCustomerCount())
                .setOldCustomerCount(factInfo.getOldCustomerCount())
                .setNonRenewalOrders(businessAchStatModel.getNonRenewalOrders() == null ? factInfo.getNonRenewalOrders() : businessAchStatModel.getNonRenewalOrders() + factInfo.getNonRenewalOrders())
                .setNewWebsiteCount(businessAchStatModel.getNewWebsiteCount() == null ? factInfo.getNewWebsiteCount() : businessAchStatModel.getNewWebsiteCount() + factInfo.getNewWebsiteCount())
                .setWebsiteNonRenewalCustomers(businessAchStatModel.getWebsiteNonRenewalCustomers() == null ? factInfo.getWebsiteNonRenewalCustomers() : businessAchStatModel.getWebsiteNonRenewalCustomers() + factInfo.getWebsiteNonRenewalCustomers())
                .setAgentCommissionAchievement(businessAchStatModel.getAgentCommissionAchievement() == null ? factInfo.getAgentCommissionAchievement() : NumberUtil.add(businessAchStatModel.getAgentCommissionAchievement(), factInfo.getAgentCommissionAchievement()))
                .setSiteNetCashReceipt(businessAchStatModel.getSiteNetCashReceipt() == null ? factInfo.getSiteNetCashReceipt() : NumberUtil.add(businessAchStatModel.getSiteNetCashReceipt(), factInfo.getSiteNetCashReceipt()))
                .setSaasNetCash(businessAchStatModel.getSaasNetCash() == null ? factInfo.getSaasNetCash() : NumberUtil.add(businessAchStatModel.getSaasNetCash(), factInfo.getSaasNetCash()));
        businessModels.add(businessAchStatModel);
    }

    private void addBusinessNew(BusinessAchievementModel businessAchStatModel, StatisticsFactInfo factInfo, ArrayList<BusinessAchievementModel> businessModels) {
        //业绩分段设置
        BigDecimal totalNetCashReceipt = factInfo.getNetCashReceipt();
        AchievementSegmentModel segment = segmentService.getSegment(totalNetCashReceipt);
        if (null != segment) {
            businessAchStatModel.setAchievementSegment(segment.getSegmentName());
            businessAchStatModel.setAchievementSegmentId(segment.getSegmentId());
        }
        businessAchStatModel.setTenure(factInfo.getTenure())
                .setNetCashReceipt(factInfo.getNetCashReceipt())
                .setActualCommission( factInfo.getActualCommission())
                .setNewCustomerCount(factInfo.getNewCustomerCount())
                .setOldCustomerCount(factInfo.getOldCustomerCount())
                .setNonRenewalOrders(factInfo.getNonRenewalOrders())
                .setNewWebsiteCount(factInfo.getNewWebsiteCount())
                .setWebsiteNonRenewalCustomers(factInfo.getWebsiteNonRenewalCustomers())
                .setAgentCommissionAchievement(factInfo.getAgentCommissionAchievement())
                .setSaasNetCash(factInfo.getSaasNetCash())
                .setSiteNetCashReceipt(factInfo.getSiteNetCashReceipt()).setUpdateTime(new Date());
        businessModels.add(businessAchStatModel);
    }
}
