package com.xmd.achievement.handler.calculateCustomer;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ○非新老</br>
 * ■从第1笔订单付款时间计算，180天内所有业务类型为新开的订单累计实付金额＜3000元，则为非新老;</br>
 * ■与公司所有服务结束时间超过180天，再次与公司发生业务往来的企业、机构或个人，且满足从下一笔订单付款时间计算，90天内所有业务类型为新开的订单累计实付金额＜3000元，该客户为非新老</br>
 *
 * <AUTHOR>
 * @date: 2024/12/18 13:39
 */
@Service
public class NonExistingCustomer extends CustomerCalculateTemplate implements ICustomerCalculate {

    @Override
    public boolean calculateCustomerType(List<AchievementProductDetailModel> aches) {
        if (CollectionUtils.isEmpty(aches)) {
            return false;
        }
        return calculateTotalPaidAmountLast90Days4NoExisting(aches).compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) < 0
                || calculateTotalPaidAmountLast180Days4NoExisting(aches).compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) < 0;
    }

    BigDecimal calculateTotalPaidAmountLast90Days4NoExisting(List<AchievementProductDetailModel> aches) {
        Date minTime = getMinTime(aches);
        Date inner90DaysTime = calculateDate(minTime, NumberConstants.INTEGER_VALUE_90);

        return aches.stream()
                .filter(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType())
                        && (ach.getPaymentTime().after(minTime) || ach.getPaymentTime().equals(minTime))
                        && ach.getPaymentTime().before(inner90DaysTime))
                .map(AchievementProductDetailModel::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    BigDecimal calculateTotalPaidAmountLast180Days4NoExisting(List<AchievementProductDetailModel> aches) {
        Date minTime = getMinTime(aches);
        Date after180DaysTime = calculateDate(minTime, NumberConstants.INTEGER_VALUE_180);
        Date inner90DaysTime = calculateDate(after180DaysTime, NumberConstants.INTEGER_VALUE_90);
        return aches.stream()
                .filter(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType())
                        && (ach.getPaymentTime().after(after180DaysTime) || ach.getPaymentTime().equals(after180DaysTime))
                        && ach.getPaymentTime().before(inner90DaysTime))
                .map(AchievementProductDetailModel::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
