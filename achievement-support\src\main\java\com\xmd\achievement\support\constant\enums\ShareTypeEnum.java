package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 主分单人类型枚举
 * 0=副, 1=主
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Getter
public enum ShareTypeEnum {
    /**
     * 副
     */
    SUB(0, "副"),
    /**
     * 主
     */
    MAIN(1, "主");

    private final Integer code;
    private final String desc;

    ShareTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code code值
     * @return 枚举实例
     */
    public static ShareTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ShareTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}