package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;
import java.util.List;

@Data
public class ServeProductConfigResponse {
    private String serveProductConfigId;
    private String productId;
    private Integer deliveryType;
    private Integer deliveryValue;
    private Integer needProduction;
    private Integer openNodeType;
    private Integer delayOpenDays;
    private Integer softwareBillingNodeType;
    private String appId;
    private String newSopId;
    private Integer renewDays;
    private Integer deleteDays;
    private Integer acceptSource;
    private List<ServeProductConfigSpec> specList;
}
