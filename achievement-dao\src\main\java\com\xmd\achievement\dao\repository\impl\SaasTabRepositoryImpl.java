package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.entity.SaasTabModel;
import com.xmd.achievement.dao.mapper.SaasTabMapper;
import com.xmd.achievement.dao.repository.ISaasTabRepository;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

/**
 * saas标签表数据访问层实现类
 */
@Repository
public class SaasTabRepositoryImpl extends ServiceImpl<SaasTabMapper, SaasTabModel> implements ISaasTabRepository {
    @Resource
    private SaasTabMapper saasTabMapper;
} 