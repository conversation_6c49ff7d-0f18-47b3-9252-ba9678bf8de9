package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商务月查询开放接口 返回参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Schema(description = "商务月查询开放接口 返回参数")
public class OpenBusinessesMonthResponse implements Serializable {
    private static final long serialVersionUID = 1297889389606693931L;


    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 商务月id
     */
    @Schema(description = "商务月id")
    private Long monthId;

    /**
     * 商务月
     */
    @Schema(description = "商务月")
    private String month;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期")
    private Date startDate;
    /**
     * 月半日期
     */
    @Schema(description = "月半日期")
    private Date midDate;
    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    private Date endDate;

    @Schema(description = "是否冻结")
    private Integer isFreeze;
}
