package com.xmd.achievement.handler.productRuleConfig;

import cn.hutool.core.util.ObjectUtil;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductRuleConfigModel;
import com.xmd.achievement.service.entity.response.ProductChurnResponse;
import com.xmd.achievement.service.entity.response.RechargeInfoResponse;
import com.xmd.achievement.service.entity.response.ServiceInfoResponse;
import com.xmd.achievement.support.constant.SupportConstant;
import com.xmd.achievement.support.constant.enums.ProductChurnStatusEnum;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/11/15:12
 * @since 1.0
 */
@Component
@Slf4j
public class ProductRuleCs001ServiceImpl implements ProductRuleConfigHandel {
    @Override
    public void calculateProductChurnStatus(AchievementProductRuleConfigModel ruleConfig, AchievementProductDetailModel productDetailModel, RechargeInfoResponse rechargeInfo, Map<String, List<ServiceInfoResponse>> productServiceMap, List<ProductChurnResponse> churnResponseList, List<ProductChurnResponse> unChurnResponseList) {
        if (!ruleConfig.getRuleCode().equals(SupportConstant.CS0001)) {
            return;
        }
        List<ServiceInfoResponse> serviceInfoResponses = productServiceMap.get(productDetailModel.getProductId().toString());

        //配置的规则1的商品
        if (ObjectUtil.isEmpty(serviceInfoResponses)) {
            ProductChurnResponse response = new ProductChurnResponse();
            response.setProductId(productDetailModel.getProductId());
            response.setOrderId(productDetailModel.getOrderId());
            response.setChurnStatus(ProductChurnStatusEnum.NO_CHURN);
            unChurnResponseList.add(response);
            log.info("customerId:{} V4新老客户计算-规则1,productId:{},计算结果:无服务数据-产品未流失", productDetailModel.getCustomerId(), productDetailModel.getProductId());
            return;
        }
        for (ServiceInfoResponse serviceInfoResponse : serviceInfoResponses) {
            ProductChurnResponse response = new ProductChurnResponse();
            response.setProductId(productDetailModel.getProductId());
            response.setOrderId(productDetailModel.getOrderId());
            if (ObjectUtil.isEmpty(serviceInfoResponse.getEndTime())) {
                response.setChurnStatus(ProductChurnStatusEnum.NO_CHURN);
                unChurnResponseList.add(response);
                log.info("customerId:{} V4新老客户计算-规则1,productId:{},计算结果:有服务无时间-产品未流失", productDetailModel.getCustomerId(), productDetailModel.getProductId());
            } else {
                Date endTime = DateUtils.stringToDate(serviceInfoResponse.getEndTime(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss);
                boolean dateAfter = DateUtils.isDateAfter(endTime,new Date());
                if (dateAfter) {
                    response.setChurnStatus(ProductChurnStatusEnum.NO_CHURN);
                    unChurnResponseList.add(response);
                    log.info("customerId:{} V4新老客户计算-规则1,productId:{},计算结果:产品未流失", productDetailModel.getCustomerId(), productDetailModel.getProductId());
                } else {
                    response.setChurnStatus(ProductChurnStatusEnum.CHURN);
                    response.setChurnTime(endTime);
                    churnResponseList.add(response);
                    log.info("customerId:{} V4新老客户计算-规则1,productId:{},计算结果:产品流失", productDetailModel.getCustomerId(), productDetailModel.getProductId());
                }
            }
        }
    }
}
