package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.AchievementCalculateParamLogModel;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 业绩计算参数日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface IAchievementCalculateParamLogRepository extends IService <AchievementCalculateParamLogModel> {
    void saveOrUpdateFactInfo(String achievementParam, Long orderId, String remark);
}
