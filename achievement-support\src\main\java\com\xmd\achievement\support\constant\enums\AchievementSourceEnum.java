package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 任务状态
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 13:57
 **/
@Getter
public enum AchievementSourceEnum {
    /**
     * 跨境
     */
    KUAJINFG(1, "跨境"),
    /**
     * 中小
     */
    ZHONGXIAO(2, "中小");

    private final Integer code;
    private final String msg;

    AchievementSourceEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public static AchievementSourceEnum getByCode(Integer code) {
        for (AchievementSourceEnum value : AchievementSourceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null; // 或抛出异常，根据你的需求
    }
}