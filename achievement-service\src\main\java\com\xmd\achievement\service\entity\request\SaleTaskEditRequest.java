package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 销售任务提交参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
public class SaleTaskEditRequest implements
        Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "机构ID")
    @NotNull(message = "机构ID不能为空")
    private Long orgId;

    @Schema(description = "机构名称")
    @NotNull(message = "机构名称不能为空")
    private String orgName;

    @Schema(description = "商务月")
    private String businessMonth;

    @Schema(description = "市场分类")
    @NotNull(message = "市场分类不能为空")
    private String marketClassification;

    @Schema(description = "基本任务（元）")
    @NotNull(message = "基本任务不能为空")
    @Min(value = 0)
    private BigDecimal basicTask;

}