package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 职级表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("position")
public class PositionModel extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 职级ID
     */
    @TableField("position_id")
    private Long positionId;
    /**
     * 职级类型
     */
    @TableField("position_name")
    private String positionName;
    /**
     * 职级code
     */
    @TableField("position_code")
    private String positionCode;
    /**
     * 是否转正 0=未转正，1=已转正
     */
    @TableField("confirmed")
    private Integer confirmed;
}