package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/13:55
 * @since 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageQueryspecCombinationListRequest extends PageRequest implements Serializable {
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123")
    private Long combinationId;

    @Schema(description = "组合名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "组合名称")
    private String combinationName;

    @Schema(description = "状态 1=启用 2=禁用", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Integer status;
}
