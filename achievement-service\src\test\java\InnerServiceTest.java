import cn.hutool.json.JSONUtil;
import com.xmd.achievement.BspAchievementServiceApplication;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.service.entity.response.CustomerChurnResponse;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2024/11/19 09:36
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = BspAchievementServiceApplication.class)
public class InnerServiceTest {
    @Resource
    private InnerService innerService;

    /**
     *
     *     OrderSimpleInfoResponse getOrderSimpleInfo(String orderId);
     *
     *     QueryContractDetailResponse getContractDetail(String orderId);
     *
     *     UserInfoDetailResp getUserInfoDetail(String userId);
     *
     *     List<ProductListForAchievementResponse> getProductListForAchievement(List<Long> specIds, Integer specStatus);
     */
    public void prettyJson(Object obj) {
        log.warn(JSONUtil.toJsonPrettyStr(obj));
    }

    @Test
    public void getOrderSimpleInfo() {

        OrderSimpleInfoResponse orderSimpleInfo = innerService.getOrderSimpleInfo(1869215921834532864L);
        prettyJson(JSONUtil.toJsonStr(orderSimpleInfo));
    }

    @Test
    public void getContractDetail() {
        QueryContractDetailResponse contractDetail = innerService.getContractDetail(1864860404479590400L);
        prettyJson(JSONUtil.toJsonStr(contractDetail));
    }

    @Test
    public void getUserInfoDetail() {
        UserInfoDetailResp userInfoDetail = innerService.getUserInfoDetail("3333");
        prettyJson(JSONUtil.toJsonStr(userInfoDetail));
    }

    @Test
    public void queryCustomerInfo() {
        QueryCustomerResponse queryCustomerResponse = innerService.queryCustomerInfo("C1168B7B7581447AADF6B4552353DDE0");
        prettyJson(JSONUtil.toJsonStr(queryCustomerResponse));
    }

    @Test
    public void getProductListForAchievement() {

        List<Long> specIds = new ArrayList<>();
        specIds.add(1865935247828471808L);
        specIds.add(1865962499777695744L);
        List<ProductListForAchievementResponse> list = innerService.getProductListForAchievement(specIds, 1);
        log.info("根据规格id查询规格信息调用商品接口返回值:{}", JSONUtil.toJsonStr(list));
    }

    @Test
    public void queryOrgInfoById() {
        OrgInfoResponse orgInfoResponse = innerService.queryOrgInfoById(369L);
        log.info("调用EMP接口获取组织机构信息 result  = {}", JSONUtil.toJsonStr(orgInfoResponse));
    }

    @Test
    public void queryKjOrgTree() {
        List<OrgInfoNodeResponse> orgInfoNodeResponses = innerService.queryKjOrgTree();
        log.info("调用EMP接口获取组织机构信息 result  = {}", JSONUtil.toJsonStr(orgInfoNodeResponses));
    }


    @Test
    public void getGetCustomerInfoByCondition() {
        CustomerChurnResponse result = innerService.getGetCustomerInfoByCondition("1937737575116574787");
        log.info(JSONUtil.toJsonStr(result));
    }

    @Test
    public void queryAfterSalesOrderInfo() {
        String aftersaleOrderNo = "TZ2025062354910101298";
        AfterSalesOrderResp afterSalesOrderResp = innerService.queryAfterSalesOrderInfo(aftersaleOrderNo);
        log.info("获取售后订单信息信息 result  = {}", JSONUtil.toJsonStr(afterSalesOrderResp));
    }

    @Test
    public void queryAfterSalesOrderDetail() {
        String aftersaleOrderNo = "TZ2025062354910101298";
        AfterSalesOrderDetailResp afterSalesOrderDetailResp = innerService.queryAfterSalesOrderDetail(aftersaleOrderNo);
        log.info("获取售后订单信息信息 result  = {}", JSONUtil.toJsonStr(afterSalesOrderDetailResp));
    }
}
