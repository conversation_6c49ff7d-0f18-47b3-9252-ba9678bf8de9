package com.xmd.achievement.handler.achievement;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.dao.repository.*;
import com.xmd.achievement.dao.repository.impl.MqOrderPaymentInfoRepositoryImpl;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContextV4;
import com.xmd.achievement.handler.statistics.BusinessAchHandler;
import com.xmd.achievement.handler.wrapper.AchievementCategoryWrapper;
import com.xmd.achievement.handler.wrapper.AchievementProductWrapper;
import com.xmd.achievement.handler.wrapper.AchievementSpecWrapper;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.service.*;
import com.xmd.achievement.service.entity.dto.AchievementCategoryDetailDto;
import com.xmd.achievement.service.entity.dto.AchievementProductDetailDto;
import com.xmd.achievement.service.entity.dto.AchievementSpecDetailDto;
import com.xmd.achievement.service.entity.dto.RecalculateResult;
import com.xmd.achievement.service.entity.response.CheckInServeDataIsRightResponse;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.support.redis.RedisCache;
import com.xmd.achievement.util.constant.UtilConstant;
import com.xmd.achievement.util.enums.CalculateTypeEnum;
import com.xmd.achievement.util.enums.MainSplitPersonEnum;
import com.xmd.achievement.util.enums.StatusEnum;
import com.xmd.achievement.util.string.StringUtils;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import com.xmd.achievement.web.config.ProductAchievementConfig;
import com.xmd.achievement.web.config.SpecialProductConfig;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xmd.achievement.util.enums.MainSplitPersonEnum.ASSISTANT;


/**
 * 业绩处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AchievementHandler {
    @Resource
    private IAchievementProductDetailService achievementProductService;
    @Resource
    private IAchievementCategoryDetailService achievementCategoryService;
    @Resource
    private IAchievementSpecDetailService achievementSpecService;
    @Resource
    private InnerService innerService;
    @Resource
    private SpecCalculateFirstYearQuoteHandler specCalculateFirstYearQuote;
    @Resource
    private SpecCalculateRenewQuoteAmountHandler specCalculateRenewQuoteAmount;
    @Resource
    private SpecCalculateFirstYearIncomeAmountHandler specCalculateFirstYearIncomeAmount;
    @Resource
    private SpecCalculateRenewIncomeAmountHandler specCalculateRenewIncomeAmount;
    @Resource
    private SpecCalculateNetCashAmountHandler specCalculateNetCashAmount;
    @Resource
    private SpecCalculateSalesCommissionAmountHandler specCalculateSalesCommissionAmount;
    @Resource
    private SpecCalculateAgentActCommAchv specCalculateAgentActCommAchvAmount;
    @Resource
    private SpecCalculateAgentDefCommAchv specCalculateAgentDefCommAchvAmount;
    @Resource
    private SpecCalculateDeptBuBrachCommAchv specCalculateDeptBuBrachCommAchvAmount;
    @Resource
    private CalculateCustomerContextV4 calculateCustomerContextV4;
    @Resource
    private IBusinessMonthService businessMonthService;
    @Resource
    private BusinessAchHandler businessAchHandler;
    @Resource
    private INewOldCustomerRecordService newOldCustomerRecordService;
    @Resource
    private IAchievementService achievementService;
    @Resource
    private ThirdAchievementService thirdAchievementService;
    private final List<CalculateAmountHandler> calculateAmountHandlers = new ArrayList<>();
    @Autowired
    private AchievementProductWrapper achievementProductWrapper;
    @Autowired
    private AchievementCategoryWrapper achievementCategoryWrapper;
    @Autowired
    private AchievementSpecWrapper achievementSpecWrapper;
    @Autowired
    private IPolicyService policyService;
    @Autowired
    private SpecialProductConfig specialProductConfig;
    @Autowired
    private MqOrderPaymentInfoService mqOrderPaymentInfoService;

    @Resource
    private IBusinessMonthRepository businessMonthRepository;
    @Resource
    private IAchievementSpecDetailRepository achievementSpecDetailRepository;
    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;
    @Resource
    private ProductAchievementConfig productAchievementConfig;
    @Resource
    IAchievementCategoryDetailRepository achievementCategoryDetailRepository;
    @Autowired
    private IAchievementCalculateParamLogRepository achievementCalculateParamLogRepository;
    @Autowired
    private IMqOrderPaymentInfoRepository mqOrderPaymentInfoRepository;
    @Resource
    private ISaasTabService saasTabService;

    //中企订单来源
    public static final int ZHONG_QI_ORDER_SOURCE = 8;
    @Autowired
    private MqOrderPaymentInfoRepositoryImpl mqOrderPaymentInfoRepositoryImpl;
    @Resource
    private IAchievementBlacklistRepository achievementBlacklistRepository;
    @Autowired
    private RedisCache redisCache;

    @Resource
    IMqOrderPaymentInfoRepository mqOrderPaymentRepository;

    @Resource
    private IFreezeMonthErrorLogService freezeMonthErrorLogService;

    @Resource
    private ICustomerSaasService customerSaasService;

    @Autowired
    private IMqOrderRefundInfoRepository mqOrderRefundInfoRepository;

    @Autowired
    private AchievementRefundHandler achievementRefundHandler;

    @PostConstruct
    public void init() {
        //首年报价，规格维度
        calculateAmountHandlers.add(specCalculateFirstYearQuote);
        //续费报价，规格维度
        calculateAmountHandlers.add(specCalculateRenewQuoteAmount);
        //首年到账金额
        calculateAmountHandlers.add(specCalculateFirstYearIncomeAmount);
        //续费到账金额
        calculateAmountHandlers.add(specCalculateRenewIncomeAmount);
        //净现金
        calculateAmountHandlers.add(specCalculateNetCashAmount);
        //商代提成业绩
        calculateAmountHandlers.add(specCalculateSalesCommissionAmount);
        //商代实发业绩提成
        calculateAmountHandlers.add(specCalculateAgentActCommAchvAmount);
        //商代缓发业绩提成
        calculateAmountHandlers.add(specCalculateAgentDefCommAchvAmount);
        //部门提成业绩/事业部提成业绩/公司提成业绩=商代提成业绩
        calculateAmountHandlers.add(specCalculateDeptBuBrachCommAchvAmount);
    }

    @Transactional(rollbackFor = Exception.class)
    @Lock("'handelTask'+#orderId+#productId")
    public void processAchievement(MqOrderPaymentInfoModel model) {
        log.info("统计流水，计算业绩Start...orderId:{},model:{}", model.getOrderId(), JSONUtil.toJsonStr(model));

        try {
            BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(model.getCreateTime());
            if (AchievementSourceEnum.ZHONGXIAO.getCode().equals(model.getAchievementSource())) {
                log.info("统计流水，中小计算业绩Start...orderId:{}", model.getOrderId());
                //判断商务月是否冻结
                if (businessMonthService.isMonthFrozen(monthInfo)) {
                    freezeMonthErrorLogService.add(monthInfo.getMonthId(), model, SourceSystemEnum.THIRDACHIEVEMENT);
                    throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
                }
                handelThirdDataTask(model);
            } else {
                log.info("统计流水，跨境计算业绩Start...orderId:{}", model.getOrderId());
                //判断商务月是否冻结
                if (businessMonthService.isMonthFrozen(monthInfo)) {
                    freezeMonthErrorLogService.add(monthInfo.getMonthId(), model);
                    throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
                }
                handelCrossBorderTask(model);
            }
            //saas新客处理
            customerSaasService.handler(model);
            saveMqSuccess(model);
        } catch (Exception exception) {
            log.error(String.format("订单:%s保存业绩失败，失败原因", model.getOrderId()), exception);
            saveMqFailed(model, exception.getMessage());
            throw new BusinessException("订单" + model.getOrderId() + "保存业绩失败");
        }
    }

    /**
     * 转款订单处理
     * @return
     */
    private RecalculateResult processTransfer(OrderSimpleInfoResponse orderInfo, List<AchievementProductDetailModel> productDetails,
                                 List<AchievementCategoryDetailModel> categoryDetails, List<AchievementSpecDetailModel> specDetailModels) {
        RecalculateResult result = new RecalculateResult();
        List<AchievementProductDetailModel> productResult = Lists.newArrayList();
        List<AchievementCategoryDetailModel> categoryResult = Lists.newArrayList();
        List<AchievementSpecDetailModel> specResult = Lists.newArrayList();
        String orderNo = orderInfo.getOrderNo();
        OrderContactResponse contract = orderInfo.getOrderContactResponseList().stream().filter(c -> LinkManTypeEnum.SALE.getType().equals(c.getType())).findFirst().orElse(null);
        String currBusinessId = contract.getUserId();
        //String customerId = orderInfo.getCustomerId();

        // 因为可能有多笔订单退款到同一个钱包然后再下单，所以可能有一笔新订单关联多笔老订单，需要对每一笔老订单进行退款操作
        List<MqOrderRefundInfoModel> MqOrderRefundInfoModels = mqOrderRefundInfoRepository.selectByOrderNo(orderNo);
        // 1.判断是否是转款订单
        if (CollectionUtil.isEmpty(MqOrderRefundInfoModels)) {
            return null;
        }
        log.info("当前订单{}为转款订单", orderNo);

        //这些都是后面计算需要用到的值，在没有加入新对象前提前算出来
        // 最终退款金额
        BigDecimal refundAmount = BigDecimal.ZERO;
        // 当前订单实付金额
        BigDecimal currPayableAmount = orderInfo.getPayableAmount();
        // 判断新订单是否是主辅分单 （用来计算金额是否需要乘以2）
        boolean newFlag = productDetails.stream().allMatch(v -> v.getMainSplitPerson().equals(MainSplitPersonEnum.MAIN.getCode()));
        for (MqOrderRefundInfoModel mqOrderRefundInfoModel : MqOrderRefundInfoModels) {
            // 对每一笔原订单生成退款记录
            achievementRefundHandler.processRefundTask(mqOrderRefundInfoModel);

            if (!judgeInstallment(orderInfo)) {
                log.info("当前订单{}不是分期第一笔，处理下一笔订单", orderNo);
                continue;
            }
            String oldOrderNo = mqOrderRefundInfoModel.getOrderNo();
            String aftersaleOrderNo = mqOrderRefundInfoModel.getAftersaleOrderNo();
            // 2. 判断客保关系
            /**
             *  原订单 客户A -> 商代1
             *  新订单 客户A -> 商代2
             *  怎么判断客户客保关系从1变成2
             */
            OrderSimpleInfoResponse orderSimpleInfoByOrderNo = innerService.getOrderSimpleInfoByOrderNo(oldOrderNo);
            OrderContactResponse oldContract = orderSimpleInfoByOrderNo.getOrderContactResponseList().stream().filter(c -> LinkManTypeEnum.SALE.getType().equals(c.getType())).findFirst().orElse(null);
            String oldBusinessId = oldContract.getUserId();
            // 客保关系没变，直接返回不做处理
            if (oldBusinessId.equals(currBusinessId)) {
                continue;
            }
            // 调用获取售后订单详情服务，获取到总退款金额
            AfterSalesOrderDetailResp afterSalesOrderDetailResp = innerService.queryAfterSalesOrderDetail(aftersaleOrderNo);
            if (afterSalesOrderDetailResp == null) {
                log.info("转款原订单{}获取售后信息为空，请检查", oldOrderNo);
                continue;
                // todo 存入异常业绩列表 结束流程
            }
            // 查询原订单商代信息
            OrderSimpleInfoResponse oldOrderInfo = innerService.getOrderSimpleInfoByOrderNo(oldOrderNo);
            if (oldOrderInfo == null) {
                log.info("原订单{}查询订单服务返回信息为空", oldOrderNo);
                continue;
            }
            //由于客保关系发生变化  所以从订单获取原订单商务信息
            String oldBusinessName = oldContract.getName();
            // 获取原订单退款金额
            BigDecimal actualRefundAmount = afterSalesOrderDetailResp.getAfterSalesAmountResp().getActualRefundAmount();
            refundAmount = refundAmount.add(actualRefundAmount);
            BigDecimal paidAmount = afterSalesOrderDetailResp.getAfterSalesAmountResp().getPaidAmount();
            // 判断原订单是否是主辅分单
            List<AchievementProductDetailModel> models = achievementProductDetailRepository.selectByOrderNo(oldOrderNo);
            boolean flag = models.stream().allMatch(v -> v.getMainSplitPerson().equals(MainSplitPersonEnum.MAIN.getCode()));
            // 3.额外生成业绩流水 这个阶段只处理主单人
            List<AchievementProductDetailModel> oldProductDetails = com.xmd.achievement.web.util.ObjectUtil.deepCopy(productDetails.stream().filter(v -> v.getMainSplitPerson().equals(MainSplitPersonEnum.MAIN.getCode())).collect(Collectors.toList()));
            List<AchievementCategoryDetailModel> oldCategoryDetails = com.xmd.achievement.web.util.ObjectUtil.deepCopy(categoryDetails.stream().filter(v -> v.getMainSplitPerson().equals(MainSplitPersonEnum.MAIN.getCode())).collect(Collectors.toList()));
            List<AchievementSpecDetailModel> oldSpecDetailModels = com.xmd.achievement.web.util.ObjectUtil.deepCopy(specDetailModels.stream().filter(v -> v.getMainSplitPerson().equals(MainSplitPersonEnum.MAIN.getCode())).collect(Collectors.toList()));

            //这些都是后面计算需要用到的值，在没有加入新对象前提前算出来
            BigDecimal finalNewTotalNetCash = productDetails.stream()
                    .map(AchievementProductDetailModel::getNetCash)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).multiply(actualRefundAmount)
                    .divide(currPayableAmount, NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
            BigDecimal finalNewTotalCommission = productDetails.stream()
                    .map(AchievementProductDetailModel::getAgentCommissionAchievement)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).multiply(actualRefundAmount)
                    .divide(currPayableAmount, NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
            // 这个是后面用来计算提成差额的那一笔数据
            AchievementProductDetailModel newProduct = com.xmd.achievement.web.util.ObjectUtil.deepCopy(productDetails.get(0));

            if (!flag) {
                /**
                 * 原订单为主辅分单，新订单不是主辅分单，则计算结果需要乘以0.5
                 * 原订单为主辅分单，新订单也是主辅分单，则计算结果不需要进行额外计算
                 */
                oldProductDetails.stream().forEach(product -> {
                    product.setAchievementId(IdUtil.getSnowflakeNextId());
                    product.setBusinessId(oldBusinessId);
                    product.setBusinessRepresentative(oldBusinessName);
                    // 净现金
                    product.setNetCash(calculateOldCash(product.getNetCash(), actualRefundAmount, currPayableAmount, newFlag));
                    // 商代提成业绩
                    product.setAgentCommissionAchievement(calculateOldCash(product.getAgentCommissionAchievement(), actualRefundAmount, currPayableAmount, newFlag));
                    // 商代实发提成业绩
                    product.setAgentActualCommission(calculateOldCash(product.getAgentActualCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    // 商代缓发提成业绩
                    product.setAgentDeferredCommission(calculateOldCash(product.getAgentDeferredCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    // 部门提成业绩
                    product.setDeptCommission(calculateOldCash(product.getDeptCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    // 事业部提成业绩
                    product.setDivCommission(calculateOldCash(product.getDivCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    // 分司提成业绩
                    product.setBranchCommission(calculateOldCash(product.getBranchCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    oldCategoryDetails.stream().filter(i -> i.getProductId().equals(product.getProductId())).forEach(category -> {
                        category.setAchievementId(product.getAchievementId());
                        category.setAchievementCategoryId(IdUtil.getSnowflakeNextId());
                        Long categoryId = category.getCategoryId();
                        // 净现金
                        category.setNetCash(calculateOldCash(category.getNetCash(), actualRefundAmount, currPayableAmount, newFlag));
                        // 商代提成业绩
                        category.setAgentCommAchv(calculateOldCash(category.getAgentCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 商代实发提成业绩
                        category.setAgentActCommAchv(calculateOldCash(category.getAgentActCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 商代缓发提成业绩
                        category.setAgentDefCommAchv(calculateOldCash(category.getAgentDefCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 部门提成业绩
                        category.setDeptCommAchv(calculateOldCash(category.getDeptCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 事业部提成业绩
                        category.setBuCommAchv(calculateOldCash(category.getBuCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 分司提成业绩
                        category.setBranchCommAchv(calculateOldCash(category.getNetCash(), actualRefundAmount, currPayableAmount, newFlag));
                        oldSpecDetailModels.stream().filter(i -> categoryId.equals(i.getProductCategoryId())).forEach(spec -> {
                            spec.setAchievementCategoryId(category.getAchievementCategoryId());
                            spec.setAchievementSpecId(IdUtil.getSnowflakeNextId());
                            // 净现金
                            spec.setNetCash(calculateOldCash(spec.getNetCash(), actualRefundAmount, currPayableAmount, newFlag));
                            // 商代提成业绩
                            spec.setAgentCommAchv(calculateOldCash(spec.getAgentCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 商代实发提成业绩
                            spec.setAgentActCommAchv(calculateOldCash(spec.getAgentActCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 商代缓发提成业绩
                            spec.setAgentDefCommAchv(calculateOldCash(spec.getAgentDefCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 部门提成业绩
                            spec.setDeptCommAchv(calculateOldCash(spec.getDeptCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 事业部提成业绩
                            spec.setBuCommAchv(calculateOldCash(spec.getBuCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 分司提成业绩
                            spec.setBranchCommAchv(calculateOldCash(spec.getBranchCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        });
                    });
                });
                AchievementProductDetailModel model = models.stream().filter(v -> v.getMainSplitPerson().equals(MainSplitPersonEnum.ASSISTANT.getCode())).findFirst().get();
                // 复制一份作为辅单人的业绩
                List<AchievementProductDetailModel> oldAssistantProductDetails = com.xmd.achievement.web.util.ObjectUtil.deepCopy(oldProductDetails);
                List<AchievementCategoryDetailModel> oldAssistantCategoryDetails = com.xmd.achievement.web.util.ObjectUtil.deepCopy(oldCategoryDetails);
                List<AchievementSpecDetailModel> oldAssistantSpecDetailModels = com.xmd.achievement.web.util.ObjectUtil.deepCopy(oldSpecDetailModels);
                oldAssistantProductDetails.forEach(ach -> {
                    ach.setBusinessId(model.getBusinessId());
                    ach.setBusinessRepresentative(model.getBusinessRepresentative());
                    ach.setCompanyId(model.getCompanyId());
                    ach.setCompany(model.getCompany());
                    ach.setDivisionId(model.getDivisionId());
                    ach.setDivision(model.getDivision());
                    ach.setDeptId(model.getDeptId());
                    ach.setDepartment(model.getDepartment());
                    ach.setMainSplitPerson(ASSISTANT.getMainSplitPerson());
                });
                oldAssistantCategoryDetails.forEach(v -> v.setMainSplitPerson(ASSISTANT.getMainSplitPerson()));
                oldAssistantSpecDetailModels.forEach(v -> v.setMainSplitPerson(ASSISTANT.getMainSplitPerson()));
                // 将所有业绩流水添加到新集合
                // 将原订单的业绩和新订单的业绩汇总
                productResult.addAll(oldProductDetails);
                productResult.addAll(oldAssistantProductDetails);
                categoryResult.addAll(oldCategoryDetails);
                categoryResult.addAll(oldAssistantCategoryDetails);
                specResult.addAll(oldSpecDetailModels);
                specResult.addAll(oldAssistantSpecDetailModels);
            } else {
                /**
                 * 原订单不是主辅分单，新订单是主辅分单，则计算结果需要乘以2
                 * 原订单不是主辅分单，新订单也不是主辅分单，则计算结果不需要进行额外计算
                 */
                oldProductDetails.stream().forEach(product -> {
                    product.setAchievementId(IdUtil.getSnowflakeNextId());
                    product.setBusinessId(oldBusinessId);
                    product.setBusinessRepresentative(oldBusinessName);
                    // 净现金
                    product.setNetCash(calculateSpecialOldCash(product.getNetCash(), actualRefundAmount, currPayableAmount, newFlag));
                    // 商代提成业绩
                    product.setAgentCommissionAchievement(calculateSpecialOldCash(product.getAgentCommissionAchievement(), actualRefundAmount, currPayableAmount, newFlag));
                    // 商代实发提成业绩
                    product.setAgentActualCommission(calculateSpecialOldCash(product.getAgentActualCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    // 商代缓发提成业绩
                    product.setAgentDeferredCommission(calculateSpecialOldCash(product.getAgentDeferredCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    // 部门提成业绩
                    product.setDeptCommission(calculateSpecialOldCash(product.getDeptCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    // 事业部提成业绩
                    product.setDivCommission(calculateSpecialOldCash(product.getDivCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    // 分司提成业绩
                    product.setBranchCommission(calculateSpecialOldCash(product.getBranchCommission(), actualRefundAmount, currPayableAmount, newFlag));
                    oldCategoryDetails.stream().filter(i -> i.getProductId().equals(product.getProductId())).forEach(category -> {
                        category.setAchievementId(product.getAchievementId());
                        category.setAchievementCategoryId(IdUtil.getSnowflakeNextId());
                        Long categoryId = category.getCategoryId();
                        // 净现金
                        category.setNetCash(calculateSpecialOldCash(category.getNetCash(), actualRefundAmount, currPayableAmount, newFlag));
                        // 商代提成业绩
                        category.setAgentCommAchv(calculateSpecialOldCash(category.getAgentCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 商代实发提成业绩
                        category.setAgentActCommAchv(calculateSpecialOldCash(category.getAgentActCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 商代缓发提成业绩
                        category.setAgentDefCommAchv(calculateSpecialOldCash(category.getAgentDefCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 部门提成业绩
                        category.setDeptCommAchv(calculateSpecialOldCash(category.getDeptCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 事业部提成业绩
                        category.setBuCommAchv(calculateSpecialOldCash(category.getBuCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        // 分司提成业绩
                        category.setBranchCommAchv(calculateSpecialOldCash(category.getBranchCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        oldSpecDetailModels.stream().filter(i -> categoryId.equals(i.getProductCategoryId())).forEach(spec -> {
                            spec.setAchievementCategoryId(category.getAchievementCategoryId());
                            spec.setAchievementSpecId(IdUtil.getSnowflakeNextId());
                            // 净现金
                            spec.setNetCash(calculateSpecialOldCash(spec.getNetCash(), actualRefundAmount, currPayableAmount, newFlag));
                            // 商代提成业绩
                            spec.setAgentCommAchv(calculateSpecialOldCash(spec.getAgentCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 商代实发提成业绩
                            spec.setAgentActCommAchv(calculateSpecialOldCash(spec.getAgentActCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 商代缓发提成业绩
                            spec.setAgentDefCommAchv(calculateSpecialOldCash(spec.getAgentDefCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 部门提成业绩
                            spec.setDeptCommAchv(calculateSpecialOldCash(spec.getDeptCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 事业部提成业绩
                            spec.setBuCommAchv(calculateSpecialOldCash(spec.getBuCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                            // 分司提成业绩
                            spec.setBranchCommAchv(calculateSpecialOldCash(spec.getBranchCommAchv(), actualRefundAmount, currPayableAmount, newFlag));
                        });
                    });
                });
                // 将所有业绩流水添加到新集合
                productResult.addAll(oldProductDetails);
                categoryResult.addAll(oldCategoryDetails);
                specResult.addAll(oldSpecDetailModels);
            }

            // 处理商务信息
            //商务信息
            UserInfoDetailResp business = innerService.getUserInfoDetail(oldBusinessId);
            if (ObjectUtil.isNotEmpty(business)) {
                OrgPathInfoDTO businessOrgInfo = business.getOrgPathInfoDTO();
                if (ObjectUtil.isEmpty(businessOrgInfo)) {
                    log.info("订单编号：{}商务id：{}所属机构不存在", oldOrderNo, oldBusinessId);
                    oldProductDetails.forEach(ach -> {
                        ach.setCompanyId(null);
                        ach.setCompany(null);
                        ach.setDivisionId(null);
                        ach.setDivision(null);
                        ach.setDeptId(null);
                        ach.setDepartment(null);
                    });
                } else {
                    oldProductDetails.forEach(ach -> {
                        ach.setCompanyId(businessOrgInfo.getCompanyId());
                        ach.setCompany(businessOrgInfo.getCompanyName());
                        ach.setDivisionId(businessOrgInfo.getCareerId());
                        ach.setDivision(businessOrgInfo.getCareerName());
                        ach.setDeptId(businessOrgInfo.getDeptId());
                        ach.setDepartment(businessOrgInfo.getDeptName());
                    });
                }
            }
            // 对比当前订单老商代的净现金、商代提成和老订单老商代的净现金、商代提成
            // 不等于转款且不等于退款
            List<AchievementProductDetailModel> oldProducts = models.stream()
                    .filter(v -> !v.getSaleType().equals(OrderSaleTypeEnum.TRANSFER.getType()) && !v.getSaleType().equals(OrderSaleTypeEnum.REFUND.getType()))
                    .collect(Collectors.toList());
            BigDecimal oldTotalNetCash = oldProducts.stream()
                    .map(AchievementProductDetailModel::getNetCash)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .multiply(actualRefundAmount)
                    .divide(paidAmount, NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
            BigDecimal oldTotalCommission = oldProducts.stream()
                    .map(AchievementProductDetailModel::getAgentCommissionAchievement)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .multiply(actualRefundAmount)
                    .divide(paidAmount, NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
            if (finalNewTotalNetCash.compareTo(oldTotalNetCash) <= 0 && finalNewTotalCommission.compareTo(oldTotalCommission) <= 0) {
                // 新订单的净现金商代提成都不大于原订单的净现金商代提成，这种情况不需要额外生成业绩流水
                continue;
            }
            /**
             * 下面的代码的逻辑
             * 商代B在第一笔中的净现金是500，算出来的提成是400；在第二笔中的净现金是600，算出来的提成是300，净现金第二笔比第一笔多，提成比第一笔少。这种情况生成两条额外的流水，第一条净现金是-100，提成为0；第二条净现金为+100，提成为0
             */
            AchievementProductDetailModel oldProduct = com.xmd.achievement.web.util.ObjectUtil.deepCopy(oldProductDetails.get(0));
            oldProduct.setAchievementId(IdUtil.getSnowflakeNextId());
            setCashToZero(oldProduct);
            oldProduct.setLatestRemark(UtilConstant.REMARK_1 + oldOrderNo);
            // 复制一份作为原订单的辅单人
            AchievementProductDetailModel oldAssistantProduct = com.xmd.achievement.web.util.ObjectUtil.deepCopy(oldProduct);
            newProduct.setAchievementId(IdUtil.getSnowflakeNextId());
            setCashToZero(newProduct);
            newProduct.setLatestRemark(UtilConstant.REMARK_2 + oldOrderNo);
            oldAssistantProduct.setMainSplitPerson(ASSISTANT.getMainSplitPerson());
            // 复制一份作为新订单的辅单人
            AchievementProductDetailModel newAssistantProduct = com.xmd.achievement.web.util.ObjectUtil.deepCopy(newProduct);
            newAssistantProduct.setMainSplitPerson(ASSISTANT.getMainSplitPerson());
            // 原订单的金额是负数，新订单的金额是正数
            if (finalNewTotalNetCash.compareTo(oldTotalNetCash) > 0) {
                oldProduct.setNetCash(oldTotalNetCash.subtract(finalNewTotalNetCash));
                newProduct.setNetCash(finalNewTotalNetCash.subtract(oldTotalNetCash));
                if (!flag) {
                    oldProduct.setNetCash(oldProduct.getNetCash().multiply(BigDecimal.valueOf(0.5)));
                    oldAssistantProduct.setNetCash(oldProduct.getNetCash());
                }
                if (!newFlag) {
                    newProduct.setNetCash(newProduct.getNetCash().multiply(BigDecimal.valueOf(0.5)));
                    newAssistantProduct.setNetCash(newProduct.getNetCash());
                }
            }
            if (finalNewTotalCommission.compareTo(oldTotalCommission) > 0) {
                oldProduct.setAgentCommissionAchievement(oldTotalCommission.subtract(finalNewTotalCommission));
                newProduct.setAgentCommissionAchievement(finalNewTotalCommission.subtract(oldTotalCommission));
                if (!flag) {
                    oldProduct.setAgentCommissionAchievement(oldProduct.getAgentCommissionAchievement().multiply(BigDecimal.valueOf(0.5)));
                    oldAssistantProduct.setAgentCommissionAchievement(oldProduct.getAgentCommissionAchievement());
                }
                if (!newFlag) {
                    newProduct.setAgentCommissionAchievement(newProduct.getAgentCommissionAchievement().multiply(BigDecimal.valueOf(0.5)));
                    newAssistantProduct.setAgentCommissionAchievement(newProduct.getAgentCommissionAchievement());
                }
            }
            // 将所有业绩流水添加到新集合
            productResult.add(oldProduct);
            productResult.add(newProduct);
            if (!flag) {
                AchievementProductDetailModel model = models.stream().filter(v -> v.getMainSplitPerson().equals(MainSplitPersonEnum.ASSISTANT.getCode())).findFirst().get();
                oldAssistantProduct.setBusinessId(model.getBusinessId());
                oldAssistantProduct.setBusinessRepresentative(model.getBusinessRepresentative());
                oldAssistantProduct.setCompanyId(model.getCompanyId());
                oldAssistantProduct.setCompany(model.getCompany());
                oldAssistantProduct.setDivisionId(model.getDivisionId());
                oldAssistantProduct.setDivision(model.getDivision());
                oldAssistantProduct.setDeptId(model.getDeptId());
                oldAssistantProduct.setDepartment(model.getDepartment());
                productResult.add(oldAssistantProduct);
            }
            if (!newFlag) {
                AchievementProductDetailModel model = productDetails.stream().filter(v -> v.getMainSplitPerson().equals(MainSplitPersonEnum.ASSISTANT.getCode())
                        && v.getOrderNo().equals(orderInfo.getOrderNo())).findFirst().get();
                newAssistantProduct.setBusinessId(model.getBusinessId());
                newAssistantProduct.setBusinessRepresentative(model.getBusinessRepresentative());
                newAssistantProduct.setCompanyId(model.getCompanyId());
                newAssistantProduct.setCompany(model.getCompany());
                newAssistantProduct.setDivisionId(model.getDivisionId());
                newAssistantProduct.setDivision(model.getDivision());
                newAssistantProduct.setDeptId(model.getDeptId());
                newAssistantProduct.setDepartment(model.getDepartment());
                productResult.add(newAssistantProduct);
            }
        }
        //对新订单的金额做更改
        modifyNewOrderAmount(productDetails, categoryDetails, specDetailModels, refundAmount, currPayableAmount);
        // 合并数据到结果集
        productResult.addAll(productDetails);
        categoryResult.addAll(categoryDetails);
        specResult.addAll(specDetailModels);
        // 将原始变量指向最新结果
        result.setProductList(productResult);
        result.setCategoryList(categoryResult);
        result.setSpecList(specResult);
        return result;
    }

    private static void setCashToZero(AchievementProductDetailModel oldProduct) {
        // 净现金
        oldProduct.setNetCash(BigDecimal.ZERO);
        // 商代提成业绩
        oldProduct.setAgentCommissionAchievement(BigDecimal.ZERO);
        // 商代实发提成业绩
        oldProduct.setAgentActualCommission(BigDecimal.ZERO);
        // 商代缓发提成业绩
        oldProduct.setAgentDeferredCommission(BigDecimal.ZERO);
        // 部门提成业绩
        oldProduct.setDeptCommission(BigDecimal.ZERO);
        // 事业部提成业绩
        oldProduct.setDivCommission(BigDecimal.ZERO);
        // 分司提成业绩
        oldProduct.setBranchCommission(BigDecimal.ZERO);
    }

    private void modifyNewOrderAmount(List<AchievementProductDetailModel> productDetails, List<AchievementCategoryDetailModel> categoryDetails, List<AchievementSpecDetailModel> specDetailModels, BigDecimal actualRefundAmount, BigDecimal currPayableAmount) {
        /**
         *  下面这段代码主要是为了实现这个逻辑
         * 第二笔订单需要生成两笔一样的流水，按照金额的计算比例区分，第二笔600/1000，第一笔的金额也需要按照比例400/1000重新计算
         */
        productDetails.forEach(product -> {
            // 净现金
            product.setNetCash(calculateNewCash(product.getNetCash(), actualRefundAmount, currPayableAmount));
            // 商代提成业绩
            product.setAgentCommissionAchievement(calculateNewCash(product.getAgentCommissionAchievement(), actualRefundAmount, currPayableAmount));
            // 商代实发提成业绩
            product.setAgentActualCommission(calculateNewCash(product.getAgentActualCommission(), actualRefundAmount, currPayableAmount));
            // 商代缓发提成业绩
            product.setAgentDeferredCommission(calculateNewCash(product.getAgentDeferredCommission(), actualRefundAmount, currPayableAmount));
            // 部门提成业绩
            product.setDeptCommission(calculateNewCash(product.getDeptCommission(), actualRefundAmount, currPayableAmount));
            // 事业部提成业绩
            product.setDivCommission(calculateNewCash(product.getDivCommission(), actualRefundAmount, currPayableAmount));
            // 分司提成业绩
            product.setBranchCommission(calculateNewCash(product.getBranchCommission(), actualRefundAmount, currPayableAmount));
        });

        categoryDetails.forEach(category -> {
            // 净现金
            category.setNetCash(calculateNewCash(category.getNetCash(), actualRefundAmount, currPayableAmount));
            // 商代提成业绩
            category.setAgentCommAchv(calculateNewCash(category.getAgentCommAchv(), actualRefundAmount, currPayableAmount));
            // 商代实发提成业绩
            category.setAgentActCommAchv(calculateNewCash(category.getAgentActCommAchv(), actualRefundAmount, currPayableAmount));
            // 商代缓发提成业绩
            category.setAgentDefCommAchv(calculateNewCash(category.getAgentDefCommAchv(), actualRefundAmount, currPayableAmount));
            // 部门提成业绩
            category.setDeptCommAchv(calculateNewCash(category.getDeptCommAchv(), actualRefundAmount, currPayableAmount));
            // 事业部提成业绩
            category.setBuCommAchv(calculateNewCash(category.getBuCommAchv(), actualRefundAmount, currPayableAmount));
            // 分司提成业绩
            category.setBranchCommAchv(calculateNewCash(category.getBranchCommAchv(), actualRefundAmount, currPayableAmount));
        });
        specDetailModels.forEach(spec -> {
            // 净现金
            spec.setNetCash(calculateNewCash(spec.getNetCash(), actualRefundAmount, currPayableAmount));
            // 商代提成业绩
            spec.setAgentCommAchv(calculateNewCash(spec.getAgentCommAchv(), actualRefundAmount, currPayableAmount));
            // 商代实发提成业绩
            spec.setAgentActCommAchv(calculateNewCash(spec.getAgentActCommAchv(), actualRefundAmount, currPayableAmount));
            // 商代缓发提成业绩
            spec.setAgentDefCommAchv(calculateNewCash(spec.getAgentDefCommAchv(), actualRefundAmount, currPayableAmount));
            // 部门提成业绩
            spec.setDeptCommAchv(calculateNewCash(spec.getDeptCommAchv(), actualRefundAmount, currPayableAmount));
            // 事业部提成业绩
            spec.setBuCommAchv(calculateNewCash(spec.getBuCommAchv(), actualRefundAmount, currPayableAmount));
            // 分司提成业绩
            spec.setBranchCommAchv(calculateNewCash(spec.getBranchCommAchv(), actualRefundAmount, currPayableAmount));
        });
    }

    private boolean judgeInstallment(OrderSimpleInfoResponse orderInfo) {
        /**
         * 判断分期
         * 不是分期的直接过
         * 如果是分期的判断是否是分期的第一笔，只有第一笔需要走下面的逻辑
         */
        boolean flag = true;
        List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModels = mqOrderPaymentRepository.selectListByOrderId(orderInfo.getOrderId());
        for (MqOrderPaymentInfoModel mqOrderPaymentInfoModel : mqOrderPaymentInfoModels) {
            // 不是分期直接退出
            if (mqOrderPaymentInfoModel.getInstallmentStatus().equals(NumberConstants.INTEGER_VALUE_1)) {
                return flag;
            }
            // 有一笔不是分期第一笔，说明该订单就不是分期第一笔
            if (!mqOrderPaymentInfoModel.getInstallmentNum().equals(NumberConstants.INTEGER_VALUE_1)) {
                flag = false;
            }
        }
        return flag;
    }

    private BigDecimal calculateNewCash(BigDecimal bigDecimal, BigDecimal actualRefundAmount, BigDecimal paidAmount) {
        return bigDecimal.multiply(paidAmount.subtract(actualRefundAmount))
                .divide(paidAmount, NumberConstants.INTEGER_VALUE_10, RoundingMode.HALF_UP)
                .setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateOldCash(BigDecimal bigDecimal, BigDecimal actualRefundAmount, BigDecimal paidAmount, boolean newFlag) {
        return bigDecimal.multiply(actualRefundAmount)
                .divide(paidAmount, NumberConstants.INTEGER_VALUE_10, RoundingMode.HALF_UP)
                .multiply(newFlag ?  BigDecimal.valueOf(0.5) : BigDecimal.ONE)
                .setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateSpecialOldCash(BigDecimal bigDecimal, BigDecimal actualRefundAmount, BigDecimal paidAmount, boolean newFlag) {
        return bigDecimal.multiply(actualRefundAmount)
                .divide(paidAmount, NumberConstants.INTEGER_VALUE_10, RoundingMode.HALF_UP)
                .multiply(newFlag ?  BigDecimal.ONE : BigDecimal.valueOf(2))
                .setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
    }



    private void saveMqFailed(MqOrderPaymentInfoModel model, String message) {
        mqOrderPaymentInfoService.executeFailed(model, message);
    }

    private void saveMqSuccess(MqOrderPaymentInfoModel model) {
        mqOrderPaymentInfoService.executeSuccess(model);
    }

    private boolean checkTaskStatus(MqOrderPaymentInfoModel model) {
        //幂等-检测支付完成业绩是否生成
        if (CalculateTypeEnum.SERVEINPROGRESS.getCode().equals(model.getCalculateType())) {
            boolean flag = achievementService.selectAchievementInfo(model.getOrderId(), model.getOrderProductId());
            if (!flag) {
                //无需生成生成完成
                log.info("{}orderId:{}orderProductId:{}", WebCodeMessageEnum.DATA_NOT_EXIST.getMsg(), model.getOrderId(), model.getOrderProductId());
                return true;
            }
        }
        return mqOrderPaymentInfoService.checkTaskStatus(model);
    }

    private void handelThirdDataTask(MqOrderPaymentInfoModel model) {
        ThirdAchievementModel thirdAchievementModel = thirdAchievementService.getInfoByTaskId(model.getOrderId());
        achievementService.handelThirdAchievement(thirdAchievementModel, model);
    }

    private void handelCrossBorderTask(MqOrderPaymentInfoModel model) {
        //支付完成
        if (CalculateTypeEnum.PAYMENT.getCode().equals(model.getCalculateType())) {
            handlerPaidAchievement(model, false, null);
        }
        //幂等/校验任务状态是否执行完成
        if (checkTaskStatus(model)) {
            return;
        }
        //服务中
        if (CalculateTypeEnum.SERVEINPROGRESS.getCode().equals(model.getCalculateType())) {
            newHandlerServiceAchievement(model, false, null);
        }
    }

    /**
     * @param model           正常的入参
     * @param orderProductIds 根据订单明细编号重算业绩的入参
     */
    public void handlerPaidAchievement(MqOrderPaymentInfoModel model, boolean recalculate, List<String> orderProductIds) {
        List<AchievementProductDetailModel> newProductListAll = new ArrayList<>();
        List<AchievementCategoryDetailModel> newCategoryListAll = new ArrayList<>();
        List<AchievementSpecDetailModel> newSpecListAll = new ArrayList<>();
        //订单分期状态
        Integer installmentStatus = model.getInstallmentStatus();

        OrderSimpleInfoResponse orderInfo = innerService.getOrderSimpleInfo(model.getOrderId());
        if (null == orderInfo) {
            throw new RuntimeException(String.format("订单id:%s,获取订单信息失败", model.getOrderId()));
        }
        //校验订单黑名单 黑名单中存在订单编号且订单明细编号不存在直接不生成业绩
        List<AchievementBlacklistModel> achievementBlacklistModels = achievementBlacklistRepository.selectAll();
        if (!CollectionUtils.isEmpty(achievementBlacklistModels)) {
            List<String> orderOnList = achievementBlacklistModels.stream().filter(item -> org.apache.commons.lang3.StringUtils.isEmpty(item.getOrderInfoNo())).map(AchievementBlacklistModel::getOrderNo).collect(Collectors.toList());
            log.info("订单黑名单,订单编号:{}", JSONUtil.toJsonStr(orderOnList));
            boolean contains = orderOnList.contains(orderInfo.getOrderNo());
            if (contains) {
                //修改mq订单完成状态
                mqOrderPaymentRepository.updateByOrderId(orderInfo.getOrderId(), NumberConstants.INTEGER_VALUE_1);
                return;
            }
        }

        Integer orderSource = orderInfo.getOrderSource();
        //订单来源是中企跳过 不生成业绩
        if (ZHONG_QI_ORDER_SOURCE == orderSource) {
            return;
        }
        //商品ID 映射 商品信息
        List<Long> specIds = orderInfo.getSpecResponseList().stream().map(OrderSimpleProductSpecResponse::getProductSpecId).collect(Collectors.toList());
        List<ProductListForAchievementResponse> productListForAchievement = innerService.getProductListForAchievement(specIds, null);
        Map<Long, ProductListForAchievementResponse> productIdProductMap = productListForAchievement.stream().peek(ProductListForAchievementResponse::getProductId)
                .collect(Collectors.toMap(ProductListForAchievementResponse::getProductId, product -> product, (existing, replacement) -> existing));

        // 商品规格ID 映射 商品信息
        Map<Long, ProductListForAchievementResponse> sepcIdProductMap = productListForAchievement.stream().collect(Collectors.toMap(ProductListForAchievementResponse::getSpecId, Function.identity()));

        //订单规格ID 映射 规格信息
        Map<Long, OrderSimpleProductSpecItemResponse> orderProductSpecIdItemMap = orderInfo.getSpecItemResponseList().stream().collect(Collectors.toMap(OrderSimpleProductSpecItemResponse::getOrderProductSpecId, Function.identity()));

        //商品规格ID 映射  新老客户统计数量
        List<Long> productIds = orderInfo.getSpecResponseList().stream().map(OrderSimpleProductSpecResponse::getProductId).collect(Collectors.toList());
        Map<Long, Integer> specQuantityMap = innerService.getSpecStatistics(orderInfo.getCustomerId(), productIds).stream().collect(Collectors.toMap(SpecStatisticsResponse::getProductSpecId, SpecStatisticsResponse::getQuantity));

        List<InstallmentSpecResponse> list = new ArrayList<>();
        if (NumberConstants.INTEGER_VALUE_1.equals(model.getInstallmentNum())) {
            //分期订单不分期商品只处理首付单
            //找到不分期规格
            List<Long> installmentSpecList = orderInfo.getInstallmentSpecResponseList().stream().map(InstallmentSpecResponse::getOrderProductSpecId).collect(Collectors.toList());

            list.addAll(orderInfo.getInstallmentSpecResponseList());

            for (OrderSimpleProductSpecResponse orderSimpleProductSpecResponse : orderInfo.getSpecResponseList()) {
                boolean flag = installmentSpecList.contains(orderSimpleProductSpecResponse.getOrderProductSpecId());
                if (!flag) {
                    //未分期规格
                    InstallmentSpecResponse installmentSpecResponse = new InstallmentSpecResponse();
                    installmentSpecResponse.setOrderProductSpecId(orderSimpleProductSpecResponse.getOrderProductSpecId());
                    installmentSpecResponse.setInstallmentNum(model.getInstallmentNum());
                    installmentSpecResponse.setInstallmentPayableAmount(orderSimpleProductSpecResponse.getPaidAmount());
                    list.add(installmentSpecResponse);
                }
            }
        } else if (model.getInstallmentNum() > NumberConstants.INTEGER_VALUE_1) {
            //分期 并且不是 1期 只处理分期的规格
            if (CollectionUtils.isEmpty(orderInfo.getInstallmentSpecResponseList())) {
                return;
            }
            list.addAll(orderInfo.getInstallmentSpecResponseList());
        }

        //1.生成-商品规格业绩基础数据
        List<AchievementSpecDetailDto> specDtoList = orderInfo.getSpecResponseList().stream().map(specResponse -> {
            AchievementSpecDetailDto achievementSpecDetailDto = null;
            if (NumberConstants.INTEGER_VALUE_2.equals(installmentStatus)) {
                //分期
                //设置规格分期实付金额
                for (InstallmentSpecResponse installmentSpecResponse : list.stream().filter(f -> f.getInstallmentNum().equals(model.getInstallmentNum())).collect(Collectors.toList())) {
                    if (installmentSpecResponse.getOrderProductSpecId().equals(specResponse.getOrderProductSpecId())) {
                        specResponse.setPaidAmount(installmentSpecResponse.getInstallmentPayableAmount());
                        //过滤
                        List<OrderSimpleProductResponse> orderProductIdList = orderInfo.getProductResponseList().stream().filter(o -> o.getOrderProductId().equals(specResponse.getOrderProductId())).collect(Collectors.toList());
                        specResponse.setOrderProductId(orderProductIdList.get(0).getOrderProductCode());
                        boolean isGenerateAchievement = isGenerateAchievement(orderInfo, productIdProductMap, specResponse, specQuantityMap);
                        achievementSpecDetailDto = isGenerateAchievement ? AchievementSpecDetailDto.buildBaseDto(specResponse, sepcIdProductMap, orderProductSpecIdItemMap) : null;
                    }
                }
            } else {
                //不分期
                //过滤
                List<OrderSimpleProductResponse> orderProductIdList = orderInfo.getProductResponseList().stream().filter(o -> o.getOrderProductId().equals(specResponse.getOrderProductId())).collect(Collectors.toList());
                specResponse.setOrderProductId(orderProductIdList.get(0).getOrderProductCode());
                boolean isGenerateAchievement = isGenerateAchievement(orderInfo, productIdProductMap, specResponse, specQuantityMap);
                achievementSpecDetailDto = isGenerateAchievement ? AchievementSpecDetailDto.buildBaseDto(specResponse, sepcIdProductMap, orderProductSpecIdItemMap) : null;
            }

            return achievementSpecDetailDto;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(specDtoList)) {
            return;
        }
        AtomicReference<String> mainOrderEmployeeId = new AtomicReference<>();
        //根据订单明细id分组 每个订单商品对应的规格信息
        Map<String, List<AchievementSpecDetailDto>> byOrderProductIdMap = specDtoList.stream().collect(Collectors.groupingBy(AchievementSpecDetailDto::getOrderProductId));

        byOrderProductIdMap.entrySet().forEach(entry -> {
            List<AchievementProductDetailModel> newProductList = new ArrayList<>();
            List<AchievementCategoryDetailModel> newCategoryList = new ArrayList<>();
            List<AchievementSpecDetailModel> newSpecList = new ArrayList<>();
            //订单明细集合code
            String orderProductCode = entry.getKey();
            //订单明细集合下的规格集合
            List<AchievementSpecDetailDto> specDtoListGroup = entry.getValue();

            //商品分类ID 映射 规格List
            Map<Long, List<AchievementSpecDetailDto>> categoryIdSpecListMap = specDtoListGroup.stream().collect(Collectors.groupingBy(AchievementSpecDetailDto::getCategoryId));

            //2.生成-商品分类业绩基础数据
            List<AchievementCategoryDetailDto> categoryDtoList = categoryIdSpecListMap.values().stream().map(AchievementCategoryDetailDto::buildBaseDto).collect(Collectors.toList());

            // 商品ID 映射  商品分类List
            Map<Long, List<AchievementCategoryDetailDto>> productIdCategoryListMap = categoryDtoList.stream().collect(Collectors.groupingBy(AchievementCategoryDetailDto::getProductId));
            List<OrderSimpleProductResponse> orderSimpleProductList = new ArrayList<>();
            if (NumberConstants.INTEGER_VALUE_1.equals(installmentStatus)) {
                //不分期
                orderSimpleProductList = orderInfo.getProductResponseList().stream().filter(item -> item.getOrderProductCode().equals(orderProductCode)).collect(Collectors.toList());

            } else {
                //分期
                //分期期数
                Integer installmentNum = model.getInstallmentNum();
                if (CollectionUtils.isEmpty(orderInfo.getInstallmentProductResponseList())) {
                    return;
                }
                //当前分期分期商品集合
                List<InstallmentProductResponse> installmentProductResponseList = orderInfo.getInstallmentProductResponseList().stream().filter(e -> installmentNum.equals(e.getInstallmentNum())).collect(Collectors.toList());
                //分期商品code
                List<String> installmentProductCodeList = installmentProductResponseList.stream().filter(item -> item.getOrderProductCode().equals(orderProductCode)).map(InstallmentProductResponse::getOrderProductCode).collect(Collectors.toList());
                //订单中对应的分期商品数据  1期以后 只处理当前分期商品  1期有可能有点商品不分期  需要处理所有商品  不分期商品不在分期商品集合里面
                for (OrderSimpleProductResponse orderSimpleProductResponse : orderInfo.getProductResponseList()) {
                    boolean flag = installmentProductCodeList.contains(orderSimpleProductResponse.getOrderProductCode());
                    if (flag) {
                        orderSimpleProductList.add(orderSimpleProductResponse);
                    }
                }
                //分期 是1期 处理所有商品
                if (NumberConstants.INTEGER_VALUE_1.equals(model.getInstallmentNum())) {
                    orderSimpleProductList.clear();
                    orderSimpleProductList = orderInfo.getProductResponseList().stream().filter(item -> item.getOrderProductCode().equals(orderProductCode)).collect(Collectors.toList());
                }

            }
            if (CollectionUtils.isEmpty(orderSimpleProductList)) {
                return;
            }
            //3.生成-商品业绩基础信息
            List<AchievementProductDetailDto> productDtoList = orderSimpleProductList.stream().map(product -> AchievementProductDetailDto.buildBaseDtoList(orderInfo, product, productIdCategoryListMap.get(product.getProductId()), productIdProductMap.get(product.getProductId()), innerService, achievementProductDetailRepository, model.getInstallmentNum())).flatMap(List::stream).collect(Collectors.toList());

            if (NumberConstants.INTEGER_VALUE_2.equals(installmentStatus)) {
                //订单商品编号 商品详情
                Map<String, OrderSimpleProductResponse> productIdByEntityMap = orderInfo.getProductResponseList().stream().collect(Collectors.toMap(OrderSimpleProductResponse::getOrderProductCode, Function.identity(), (existing, replacement) -> existing));
                //设置标准价
                for (AchievementProductDetailDto achievementProductDetailDto : productDtoList) {
                    OrderSimpleProductResponse orderSimpleProductResponse = productIdByEntityMap.get(achievementProductDetailDto.getOrderProductId());
                    if (null == orderSimpleProductResponse) {
                        log.error("订单商品信息不存在，订单id:{},订单商品编号:{}", orderInfo.getOrderId(), achievementProductDetailDto.getOrderProductId());
                        continue;
                    }
                    achievementProductDetailDto.setPayableAmount(orderSimpleProductResponse.getPayableAmount());
                    achievementProductDetailDto.setStandardPrice(orderSimpleProductResponse.getPayableAmount());
                }
            }

            //商品业绩ID 商品分类ID 商品分类规格ID 进行组装
            for (AchievementProductDetailDto productDto : productDtoList) {
                Long achievementId = productDto.getAchievementId();
                AchievementProductDetailModel targetAchProductModel = new AchievementProductDetailModel();
                BeanUtils.copyProperties(productDto, targetAchProductModel);
                targetAchProductModel.setCreateTime(model.getCreateTime());
                targetAchProductModel.setStatisticsTime(model.getCreateTime());
                targetAchProductModel.setPayType(installmentStatus + "");
                final SaasEnum saasEnum;
                if (saasTabService.checkIsSaasKuaJing(targetAchProductModel.getProductId())) {
                    saasEnum = SaasEnum.YES;
                } else {
                    saasEnum = SaasEnum.NO;
                }
                targetAchProductModel.setIsSaas(saasEnum.getCode());
                newProductList.add(targetAchProductModel);
                productIdCategoryListMap.get(productDto.getProductId()).forEach(category -> {
                    category.setAchievementId(achievementId);
                    AchievementCategoryDetailModel achCategoryModel = new AchievementCategoryDetailModel();
                    BeanUtils.copyProperties(category, achCategoryModel);
                    achCategoryModel.setProductId(productDto.getProductId());
                    achCategoryModel.setOrderId(productDto.getOrderId());
                    achCategoryModel.setStatus(productDto.getStatus());
                    achCategoryModel.setMainSplitPerson(productDto.getMainSplitPerson());
                    achCategoryModel.setAchievementCategoryId(IdUtil.getSnowflakeNextId());
                    newCategoryList.add(achCategoryModel);
                    Long achievementCategoryId = achCategoryModel.getAchievementCategoryId();
                    Long categoryId = category.getCategoryId();
                    categoryIdSpecListMap.get(categoryId).forEach(spec -> {
                        spec.setAchievementCategoryId(achievementCategoryId);
                        AchievementSpecDetailModel targetSpecModel = new AchievementSpecDetailModel();
                        BeanUtils.copyProperties(spec, targetSpecModel);
                        targetSpecModel.setProductId(productDto.getProductId());
                        targetSpecModel.setOrderId(productDto.getOrderId());
                        targetSpecModel.setProductCategoryId(categoryId);
                        targetSpecModel.setStatus(productDto.getStatus());
                        targetSpecModel.setMainSplitPerson(productDto.getMainSplitPerson());
                        targetSpecModel.setAchievementSpecId(IdUtil.getSnowflakeNextId());
                        targetSpecModel.setIsSaas(saasEnum.getCode());
                        targetSpecModel.setCategoryId(achCategoryModel.getCategoryId());
                        newSpecList.add(targetSpecModel);
                    });
                });
            }
            log.info("订单:{}成功生成业绩流水及统计,productId:{},currentStatus:{}", model.getOrderId(), model.getProductId(), model.getCalculateType());
            calculateAchievement(newProductList, newCategoryList, newSpecList, model, orderInfo, mainOrderEmployeeId);
            newProductListAll.addAll(newProductList);
            newCategoryListAll.addAll(newCategoryList);
            newSpecListAll.addAll(newSpecList);
        });

        // 根据订单明细编号重算的逻辑，正常不会走
        if (recalculate) {
            RecalculateResult result = recalculateByOrderProductId(newProductListAll, newCategoryListAll, newSpecListAll, orderProductIds);
            newProductListAll.clear();
            newCategoryListAll.clear();
            newSpecListAll.clear();
            newProductListAll.addAll(result.getProductList());
            newCategoryListAll.addAll(result.getCategoryList());
            newSpecListAll.addAll(result.getSpecList());
        }
        //订单明细黑名单过滤
        filterBlacklist(newProductListAll, newCategoryListAll, newSpecListAll, achievementBlacklistModels);
        // 退款转款部分退款的逻辑
        RecalculateResult result = processTransfer(orderInfo, newProductListAll, newCategoryListAll, newSpecListAll);
        if (ObjectUtil.isNotEmpty(result)) {
            //计算完成后批量入库
            achievementProductService.saveOrUpdateBatch(result.getProductList());
            achievementCategoryService.saveOrUpdateBatch(result.getCategoryList());
            achievementSpecService.saveOrUpdateBatch(result.getSpecList());
        } else {
            //计算完成后批量入库
            achievementProductService.saveOrUpdateBatch(newProductListAll);
            achievementCategoryService.saveOrUpdateBatch(newCategoryListAll);
            achievementSpecService.saveOrUpdateBatch(newSpecListAll);
        }


        Integer status = model.getCalculateType();
        //businessAchHandler.achStatistics(newProductListAll, status, BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
    }

    /**
     * 订单明细黑名单过滤
     *
     * @param newProductListAll          业绩商品明细
     * @param newCategoryListAll         业绩规格分类
     * @param newSpecListAll             业绩规格明细表
     * @param achievementBlacklistModels 业绩黑名单
     */
    private void filterBlacklist(List<AchievementProductDetailModel> newProductListAll, List<AchievementCategoryDetailModel> newCategoryListAll, List<AchievementSpecDetailModel> newSpecListAll, List<AchievementBlacklistModel> achievementBlacklistModels) {
        if (CollectionUtils.isEmpty(achievementBlacklistModels)) {
            return;
        }
        //黑名单订单详情编号
        List<String> blackOrderInfoNoList = achievementBlacklistModels.stream().map(AchievementBlacklistModel::getOrderInfoNo).filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        log.info("订单编号:{}黑名单订单详情编号:{}", achievementBlacklistModels.get(0).getOrderNo(), blackOrderInfoNoList);
        if (CollectionUtils.isEmpty(blackOrderInfoNoList) || CollectionUtils.isEmpty(newProductListAll)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(newProductListAll)) {
            newProductListAll.removeIf(model -> blackOrderInfoNoList.contains(model.getOrderProductId()));
        }
        if (CollectionUtils.isEmpty(newProductListAll)) {
            newCategoryListAll = null;
            newSpecListAll = null;
            return;
        }
        List<Long> achievementIds = newProductListAll.stream().map(AchievementProductDetailModel::getAchievementId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newCategoryListAll)) {
            newCategoryListAll.removeIf(model -> !achievementIds.contains(model.getAchievementId()));
        }
        if (CollectionUtils.isEmpty(newCategoryListAll)) {
            newSpecListAll = null;
            return;
        }
        List<Long> achievementCategoryIds = newCategoryListAll.stream().map(AchievementCategoryDetailModel::getAchievementCategoryId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newSpecListAll)) {
            newSpecListAll.removeIf(model -> !achievementCategoryIds.contains(model.getAchievementCategoryId()));
        }
    }


    private RecalculateResult recalculateByOrderProductId(List<AchievementProductDetailModel> productList, List<AchievementCategoryDetailModel> categoryList, List<AchievementSpecDetailModel> specList, List<String> orderProductIds) {
        List<AchievementProductDetailModel> filteredProductList = productList.stream()
                .filter(v -> orderProductIds.contains(v.getOrderProductId()))
                .collect(Collectors.toList());

        List<Long> achievementIds = filteredProductList.stream()
                .map(AchievementProductDetailModel::getAchievementId)
                .collect(Collectors.toList());

        List<AchievementCategoryDetailModel> filteredCategoryList = categoryList.stream()
                .filter(v -> achievementIds.contains(v.getAchievementId()))
                .collect(Collectors.toList());

        List<AchievementSpecDetailModel> filteredSpecList = specList.stream()
                .filter(v -> orderProductIds.contains(v.getOrderProductId()))
                .collect(Collectors.toList());

        return new RecalculateResult(filteredProductList, filteredCategoryList, filteredSpecList);
    }


    public void newHandlerServiceAchievement(MqOrderPaymentInfoModel mqPaymentInfoModel, boolean recalculate, List<String> orderProductId) {
        log.info("taskId:{},计算服务中业绩Start....", mqPaymentInfoModel.getTaskId());

        //获取规格上可以生成业绩的订单（标志为生产完成规格单才可以生成业绩）
        List<AchievementSpecDetailModel> specDetailModelList = achievementSpecService.queryValidSpcList(mqPaymentInfoModel.getOrderId(), mqPaymentInfoModel.getProductId(), mqPaymentInfoModel.getInstallmentNum(), mqPaymentInfoModel.getOrderProductId());
        if (ObjectUtil.isEmpty(specDetailModelList)) {
            return;
        }
        //规格 分类 商品
        List<AchievementSpecDetailModel> newSpecDetailModelList = new ArrayList<>();
        List<AchievementCategoryDetailModel> newCategoryDetailModelList = new ArrayList<>();
        List<AchievementProductDetailModel> newProductDetailModelList = new ArrayList<>();

        //主单人数据
        List<AchievementSpecDetailModel> mastModels = specDetailModelList.stream()
                .filter(e -> e.getMainSplitPerson().equals(NumberConstants.INTEGER_VALUE_1))
                .collect(Collectors.toList());
        //分单人数据
        List<AchievementSpecDetailModel> subModels = specDetailModelList.stream()
                .filter(e -> e.getMainSplitPerson().equals(NumberConstants.INTEGER_VALUE_2))
                .collect(Collectors.toList());

        //主单人 规格 分类 商品 关联ID 计算
        if (ObjectUtil.isNotEmpty(mastModels)) {
            List<AchievementSpecDetailModel> newMastSpecDetailModelList = achievementSpecWrapper.newBuildSpecDetailModels(NumberConstants.INTEGER_VALUE_1, mastModels, mqPaymentInfoModel);
            List<AchievementCategoryDetailModel> newMastCategoryDetailModelList = achievementCategoryWrapper.newBuildCategoryDetailModels(NumberConstants.INTEGER_VALUE_1, newMastSpecDetailModelList, mqPaymentInfoModel);
            if (CollectionUtils.isEmpty(newMastCategoryDetailModelList)) {
                return;
            }
            List<AchievementProductDetailModel> newMastProductDetailModelList = achievementProductWrapper.newBuildProductDetailModels(NumberConstants.INTEGER_VALUE_1, newMastCategoryDetailModelList, mqPaymentInfoModel);
            if (CollectionUtils.isEmpty(newMastProductDetailModelList)) {
                return;
            }
            achievementProductWrapper.relationId(newMastSpecDetailModelList, newMastCategoryDetailModelList, newMastProductDetailModelList, mqPaymentInfoModel);
            newSpecDetailModelList.addAll(newMastSpecDetailModelList);
            newCategoryDetailModelList.addAll(newMastCategoryDetailModelList);
            newProductDetailModelList.addAll(newMastProductDetailModelList);
            log.info("taskId:{},计算服务中业绩，完成主单人业绩计算", mqPaymentInfoModel.getTaskId());
        }

        //分单人 规格 分类 商品 关联ID 计算
        if (ObjectUtil.isNotEmpty(subModels)) {
            List<AchievementSpecDetailModel> newSubSpecDetailModelList = achievementSpecWrapper.newBuildSpecDetailModels(NumberConstants.INTEGER_VALUE_2, mastModels, mqPaymentInfoModel);
            List<AchievementCategoryDetailModel> newSubCategoryDetailModelList = achievementCategoryWrapper.newBuildCategoryDetailModels(NumberConstants.INTEGER_VALUE_2, newSubSpecDetailModelList, mqPaymentInfoModel);
            if (CollectionUtils.isEmpty(newSubCategoryDetailModelList)) {
                return;
            }
            List<AchievementProductDetailModel> newSubProductDetailModelList = achievementProductWrapper.newBuildProductDetailModels(NumberConstants.INTEGER_VALUE_2, newSubCategoryDetailModelList, mqPaymentInfoModel);
            if (CollectionUtils.isEmpty(newSubProductDetailModelList)) {
                return;
            }
            achievementProductWrapper.relationId(newSubSpecDetailModelList, newSubCategoryDetailModelList, newSubProductDetailModelList, mqPaymentInfoModel);
            newSpecDetailModelList.addAll(newSubSpecDetailModelList);
            newCategoryDetailModelList.addAll(newSubCategoryDetailModelList);
            newProductDetailModelList.addAll(newSubProductDetailModelList);
            log.info("taskId:{},计算服务中业绩，完成分单人业绩计算", mqPaymentInfoModel.getTaskId());
        }

        log.info("taskId:{},计算服务中业绩，完成主分单人业绩计算", mqPaymentInfoModel.getTaskId());
        // 根据订单明细编号重算逻辑，正常不会走
        if (recalculate) {
            RecalculateResult result = recalculateByOrderProductId(newProductDetailModelList, newCategoryDetailModelList, newSpecDetailModelList, orderProductId);
            newProductDetailModelList.clear();
            newCategoryDetailModelList.clear();
            newSpecDetailModelList.clear();
            newProductDetailModelList.addAll(result.getProductList());
            newCategoryDetailModelList.addAll(result.getCategoryList());
            newSpecDetailModelList.addAll(result.getSpecList());
        }
        //保存
        achievementProductService.saveProductDetailModels(newProductDetailModelList, mqPaymentInfoModel);
        achievementCategoryService.saveBatch(newCategoryDetailModelList);
        achievementSpecService.saveBatch(newSpecDetailModelList);

        //商务人员根据商品业绩统计
        //businessAchHandler.achStatistics(newProductDetailModelList, ProcessType.SERVE_IN_PROGRESS.getCode(),BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
        newProductDetailModelList.clear();
        newCategoryDetailModelList.clear();
        newSpecDetailModelList.clear();
        log.info("taskId:{},计算服务中业绩，保存完成", mqPaymentInfoModel.getTaskId());
    }


    private void handlerServiceAchievement(MqOrderPaymentInfoModel model) {
        log.info("taskId:{},开始生成业绩流水及统计", model.getTaskId());
        //获取规格上可以生成业绩的订单（标志为生产完成规格单才可以生成业绩）
        List<AchievementSpecDetailModel> specDetailModelList = achievementSpecService.queryValidSpcList(model.getOrderId(), model.getProductId(), model.getInstallmentNum(), model.getOrderProductId());
        if (ObjectUtil.isEmpty(specDetailModelList)) {
            return;
        }

        //基础定义
        List<AchievementSpecDetailModel> specDetailModels = new ArrayList<>();
        List<AchievementCategoryDetailModel> categoryDetailModels = new ArrayList<>();
        List<AchievementProductDetailModel> productDetailModels = new ArrayList<>();

        //组装分类信息-主副单人 映射 规格集合 循环specDetailModelMap
        Map<Integer, List<AchievementSpecDetailModel>> mainSplitPersonMap = specDetailModelList.stream().collect(Collectors.groupingBy(AchievementSpecDetailModel::getMainSplitPerson));

        for (Map.Entry<Integer, List<AchievementSpecDetailModel>> mainSplitPersonEntry : mainSplitPersonMap.entrySet()) {
            //组装分类信息-分类ID 映射 规格集合 循环specDetailModelMap
            Map<Long, List<AchievementSpecDetailModel>> specDetailModelMap = mainSplitPersonEntry.getValue().stream().collect(Collectors.groupingBy(AchievementSpecDetailModel::getAchievementCategoryId));
            for (Map.Entry<Long, List<AchievementSpecDetailModel>> entry : specDetailModelMap.entrySet()) {
                //补充achievementCategor  yId
                Long achievementCategoryId = IdUtil.getSnowflakeNextId();
                entry.getValue().forEach(spec -> {
                    spec.setAchievementCategoryId(achievementCategoryId);
                    spec.setAchievementSpecId(IdUtil.getSnowflakeNextId());
                    spec.setStatus(CurrentStatusEnum.SERVICE.getStatus());
                    PolicySpecDetailModel policyDetail = policyService.getPolicyDetailBySpecId(spec.getSpecId());
                    //服务交付完成，商务实发业绩提成=商代提成业绩x（1-实发业绩提成比例);
                    BigDecimal commissionRatio = policyDetail.getCommissionRatio().divide(new BigDecimal(100), RoundingMode.HALF_UP);
                    spec.setAgentActCommAchv(spec.getAgentCommAchv().multiply((new BigDecimal(1).subtract(commissionRatio))));
                    spec.setAgentDefCommAchv(BigDecimal.ZERO);
                    spec.setMainSplitPerson(mainSplitPersonEntry.getKey());
                    spec.setCreateTime(new Date());
                    spec.setUpdateTime(new Date());
                    spec.setId(null);

                    //设置为0
                    spec.setBillingPrice(BigDecimal.ZERO);
                    spec.setRenewalPrice(BigDecimal.ZERO);
                    spec.setStandardPrice(BigDecimal.ZERO);
                    spec.setPayableAmount(BigDecimal.ZERO);
                    spec.setPaidAmount(BigDecimal.ZERO);
                    spec.setFirstYearQuote(BigDecimal.ZERO);
                    spec.setFirstYearIncome(BigDecimal.ZERO);
                    spec.setRenewalQuote(BigDecimal.ZERO);
                    spec.setRenewalIncome(BigDecimal.ZERO);
                    spec.setNetCash(BigDecimal.ZERO);
                    spec.setAgentCommAchv(BigDecimal.ZERO);
                    spec.setDeptCommAchv(BigDecimal.ZERO);
                    spec.setBuCommAchv(BigDecimal.ZERO);
                    spec.setBranchCommAchv(BigDecimal.ZERO);
                    specDetailModels.add(spec);
                });

                //分类
                AchievementCategoryDetailModel categoryDetailModel = achievementCategoryWrapper.buildServiceCategoryDetailModel(mainSplitPersonEntry.getKey(), achievementCategoryId, entry.getValue(), model.getOrderId(), entry.getKey());
                categoryDetailModel.setId(null);
                categoryDetailModels.add(categoryDetailModel);

                //组装商品信息-商品ID 映射 分类集合 循环specDetailModelMap
                Map<Long, List<AchievementCategoryDetailModel>> categoryDetailModelMap = categoryDetailModels.stream().collect(Collectors.groupingBy(AchievementCategoryDetailModel::getProductId));
                for (Map.Entry<Long, List<AchievementCategoryDetailModel>> categoryEntry : categoryDetailModelMap.entrySet()) {
                    //补充achievementId
                    Long achievementId = IdUtil.getSnowflakeNextId();
                    categoryEntry.getValue().forEach(e -> e.setAchievementId(achievementId));
                    //商品
                    AchievementProductDetailModel productDetailModel = achievementProductWrapper.buildServiceProductDetailModel(mainSplitPersonEntry.getKey(), achievementId, categoryEntry.getValue(), model.getOrderId(), categoryEntry.getKey(), model.getServeNo());
                    productDetailModel.setId(null);
                    productDetailModels.add(productDetailModel);
                }
            }
            //保存
            achievementProductService.saveProductDetailModels(productDetailModels, model);
            achievementCategoryService.saveBatch(categoryDetailModels);
            achievementSpecService.saveBatch(specDetailModels);

            //商务人员根据商品业绩统计
            //businessAchHandler.achStatistics(productDetailModels, ProcessType.SERVE_IN_PROGRESS.getCode(), BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
            productDetailModels.clear();
            categoryDetailModels.clear();
            specDetailModels.clear();
        }

    }

    public void calculateAchievement(List<AchievementProductDetailModel> achList, List<AchievementCategoryDetailModel> categoryList, List<AchievementSpecDetailModel> specList, MqOrderPaymentInfoModel model, OrderSimpleInfoResponse orderInfo, AtomicReference<String> mainOrderEmployeeId) {
        Integer status = model.getCalculateType();
        Date createTime = model.getCreateTime();
        //商品ID集合
        List<Long> achProductIds = achList.stream().map(AchievementProductDetailModel::getProductId).collect(Collectors.toList());

        //商品规格id集合
        List<Long> sepcIdList = specList.stream().map(AchievementSpecDetailModel::getSpecId).collect(Collectors.toList());

        //商品信息
        List<ProductListForAchievementResponse> productListForAchievement = innerService.getProductListForAchievement(sepcIdList, null);

        //商品规格id 映射 商品id
        Map<Long, Long> spedIdProductIdMap = productListForAchievement.stream().collect(Collectors.toMap(ProductListForAchievementResponse::getSpecId, ProductListForAchievementResponse::getProductId));

        //  拼装业绩计算前期数据
        //  所有的规格ID
        //  订单类型：1=普通订单，2=折扣订单
        //  订单销售渠道：1=新开，2=续费，3=升级，4=另购、是否分单）
        AchievementProductDetailModel achModel = achList.get(0);
        //客户id
        String customerId = achModel.getCustomerId();

        CalculateFactInfo factInfo = new CalculateFactInfo();
        factInfo.setSpecList(specList);
        factInfo.setOrderType(achModel.getOrderType());
        factInfo.setOrderSaleType(achModel.getSaleType());
        factInfo.setProductDiscountRulesList(innerService.getProductDiscountRulesListForAchievement(achProductIds));
        factInfo.setCurrentStatus(status);
        factInfo.setOrderInfo(orderInfo);
        factInfo.setSplitOrder(achList.stream().anyMatch(ach -> MainSubEnum.SUB.getType().equals(ach.getMainSplitPerson())));
        // 0提成线的计算逻辑
        BigDecimal zeroStandardPrice = new BigDecimal(NumberConstants.INTEGER_VALUE_0);
        for (AchievementProductDetailModel product : achList) {
            //普通订单
            if (OrderTypeEnum.NORMAL.getType().equals(factInfo.getOrderType())) {
                factInfo.setZeroNetCash(false);
            } else if (OrderTypeEnum.DISCOUNT.getType().equals(factInfo.getOrderType())) {
                if (ObjectUtil.isNotEmpty(product.getStandardPrice()) && zeroStandardPrice.compareTo(product.getStandardPrice()) == NumberConstants.INTEGER_VALUE_0) {
                    // 标准价 = 0，一般是赠送商品，不参与0提成线计算
                    factInfo.setZeroNetCash(false);
                } else {
                    //获取当前折扣率
                    BigDecimal discountRate = product.getDiscountRate();
                    //currentDiscountRate乘以10，保留小数位
                    discountRate = discountRate.multiply(new BigDecimal(NumberConstants.INTEGER_VALUE_10)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                    //获取折扣规则
                    ProductDiscountRulesListForAchievementResponse discountRule = factInfo.getProductDiscountRulesList().stream().filter(dr -> dr.getProductId().equals(product.getProductId())).findFirst().orElse(null);
                    //获取0提成线
                    BigDecimal zeroCommissionLine = null == discountRule ? BigDecimal.ZERO : discountRule.getZeroCommissionLine();
                    if (discountRate.compareTo(zeroCommissionLine) < 0) {
                        factInfo.setZeroNetCash(true);
                        break;
                    } else {
                        factInfo.setZeroNetCash(false);
                    }
                }
            }
        }
        // 特殊商品特殊政策规格计算逻辑
        List<Long> specialSpecIds = Lists.newArrayList();
        for (AchievementSpecDetailModel spec : specList) {
            // 特殊商品且为新开
            if (!OrderSaleTypeEnum.OPEN.getType().equals(factInfo.getOrderSaleType())) {
                break;
            }
            if (specialProductConfig.getItems().stream().anyMatch(p -> p.getId().equals(spec.getProductId()))
                    && specialProductConfig.getSpecs().stream().anyMatch(p -> p.getId().equals(spec.getSpecId()))) {
                specialSpecIds.add(spec.getSpecId());
            }
        }
        factInfo.setSpecialSpecIds(specialSpecIds);
        //计算每一个规格的业绩
        List<AchievementSpecDetailModel> specDetailModelList = specList.stream().peek(spec -> {
            factInfo.setSpec(spec);
            factInfo.setProduct(achList.stream().filter(p -> Objects.equals(p.getProductId(), spedIdProductIdMap.get(spec.getSpecId()))).findFirst().orElseThrow(() -> new RuntimeException(String.format("从二方接口中未找到商品id,规格id:%s", spec.getSpecId()))));
            for (CalculateAmountHandler handler : calculateAmountHandlers) {
                handler.calculate(factInfo);
            }
        }).collect(Collectors.toList());
        // 添加日志，方便排查问题
        addAchievementCalculateLog(factInfo);
        for (AchievementSpecDetailModel achievementSpecDetailModel : specList) {
            achievementSpecDetailModel.setInstallmentNum(model.getInstallmentNum());
        }
        //计算每一个规格分类业绩
        Map<Long, List<AchievementSpecDetailModel>> categoryIdSpecList = specDetailModelList.stream().collect(Collectors.groupingBy(AchievementSpecDetailModel::getAchievementCategoryId));
        categoryList.forEach(cate -> {
            List<AchievementSpecDetailModel> specs = categoryIdSpecList.get(cate.getAchievementCategoryId());
            cate.setFirstYearQuote(specs.stream().map(AchievementSpecDetailModel::getFirstYearQuote).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setFirstYearIncome(specs.stream().map(AchievementSpecDetailModel::getFirstYearIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setRenewalQuote(specs.stream().map(AchievementSpecDetailModel::getRenewalQuote).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setRenewalIncome(specs.stream().map(AchievementSpecDetailModel::getRenewalIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setNetCash(specs.stream().map(AchievementSpecDetailModel::getNetCash).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setAgentCommAchv(specs.stream().map(AchievementSpecDetailModel::getAgentCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setAgentActCommAchv(specs.stream().map(AchievementSpecDetailModel::getAgentActCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setAgentDefCommAchv(specs.stream().map(AchievementSpecDetailModel::getAgentDefCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setDeptCommAchv(specs.stream().map(AchievementSpecDetailModel::getDeptCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setBuCommAchv(specs.stream().map(AchievementSpecDetailModel::getBuCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setBranchCommAchv(specs.stream().map(AchievementSpecDetailModel::getBranchCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            cate.setInstallmentNum(model.getInstallmentNum());
        });

        //计算每一个商品的业绩
        Map<Long, List<AchievementCategoryDetailModel>> cateAchIdList = categoryList.stream().collect(Collectors.groupingBy(AchievementCategoryDetailModel::getAchievementId));

        //合同编号
        Long orderId = achList.get(0).getOrderId();
        QueryContractDetailResponse contractDetail = innerService.getContractDetail(orderId);
        if (contractDetail == null) {
            throw new RuntimeException(String.format("订单id:%s ,contractDetail未找到合同信息", orderId));
        }
        ContractDto contractDto = contractDetail.getContractDto();
        if (contractDto == null) {
            throw new RuntimeException(String.format("订单id:%s ,contractDetail未找到合同信息", orderId));
        }
        String contractCode = contractDto.getContractCode();

        //客户类型 1:新客户 2:老客户 3:非新老客户
        CustomerType customerTypeEnum = calculateCustomerContextV4.calculateCustomerV4(model.getInstallmentStatus(), model.getInstallmentNum(), customerId, achList);
        //CustomerType customerTypeEnum = calculateCustomerContext.calculateCustomer(customerId, achList);
        if (null == customerTypeEnum) {
            throw new RuntimeException(String.format("订单:%s计算新老客户出错", orderId));
        }
        Integer customerType = customerTypeEnum.getType();
        //客户信息
        QueryCustomerResponse customer = innerService.queryCustomerInfo(customerId);

        //商务月 商务月和产品确认按照创建时间获取 20250313(改)
        //AtomicReference<String> mainOrderEmployeeId = new AtomicReference<>();
        BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(achList.get(0).getStatisticsTime());

        achList.forEach(ach -> {
            List<AchievementCategoryDetailModel> categories = cateAchIdList.get(ach.getAchievementId());
            ach.setFirstYearQuote(categories.stream().map(AchievementCategoryDetailModel::getFirstYearQuote).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setFirstYearRevenue(categories.stream().map(AchievementCategoryDetailModel::getFirstYearIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setRenewalQuote(categories.stream().map(AchievementCategoryDetailModel::getRenewalQuote).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setRenewalRevenue(categories.stream().map(AchievementCategoryDetailModel::getRenewalIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setNetCash(categories.stream().map(AchievementCategoryDetailModel::getNetCash).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setAgentCommissionAchievement(categories.stream().map(AchievementCategoryDetailModel::getAgentCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setAgentActualCommission(categories.stream().map(AchievementCategoryDetailModel::getAgentActCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setAgentDeferredCommission(categories.stream().map(AchievementCategoryDetailModel::getAgentDefCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setDeptCommission(categories.stream().map(AchievementCategoryDetailModel::getDeptCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setDivCommission(categories.stream().map(AchievementCategoryDetailModel::getBuCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setBranchCommission(categories.stream().map(AchievementCategoryDetailModel::getBranchCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
            ach.setContractNo(contractCode);
            ach.setInstallmentNum(model.getInstallmentNum());
            // 只有支付完成才记录客户类型，服务完成沿用支付完成时的客户类型
            if (CurrentStatusEnum.PAID.getStatus().equals(status)) {
                ach.setCustomerType(customerType);
            }
            if (customer != null) {
                ach.setProvinceCode(customer.getProvinceCode());
                ach.setProvinceName(customer.getProvinceName());
                ach.setCityCode(customer.getCityCode());
                ach.setCityName(customer.getCityName());
                ach.setDistrictCode(customer.getDistrictCode());
                ach.setDistrictName(customer.getDistrictName());
                ach.setCustomerRegion(String.format("%s%s%s", customer.getProvinceName(), customer.getCityName(), customer.getDistrictName()));
            } /*else {
                throw new RuntimeException(String.format("调用二方接口未找到客户信息,客户id:%s", achModel.getCustomerId()));
            }*/

            //商务信息
            UserInfoDetailResp business = innerService.getUserInfoDetail(ach.getBusinessId());
            if (ObjectUtil.isNotEmpty(business)) {

                OrgPathInfoDTO businessOrgInfo = business.getOrgPathInfoDTO();
                if (ObjectUtil.isEmpty(businessOrgInfo)) {
                    log.info("订单id：{}商务id：{}所属机构不存在", orderId, ach.getBusinessId());
                } else {
                    ach.setCompanyId(businessOrgInfo.getCompanyId());
                    ach.setCompany(businessOrgInfo.getCompanyName());
                    ach.setDivisionId(businessOrgInfo.getCareerId());
                    ach.setDivision(businessOrgInfo.getCareerName());
                    ach.setDeptId(businessOrgInfo.getDeptId());
                    ach.setDepartment(businessOrgInfo.getDeptName());
                }
            }


            if (monthInfo != null) {
                ach.setBusinessMonth(monthInfo.getMonth());
                ach.setBusinessMonthId(monthInfo.getMonthId());
            } else {
                throw new RuntimeException(String.format("根据流水创建时间未找到商务月,创建时间:%s ,订单id:%s", DateUtil.format(achModel.getStatisticsTime(), DatePattern.NORM_DATETIME_PATTERN), orderId));
            }
            // 业绩生成时间取mq落库时间
            ach.setCreateTime(createTime);
            ach.setStatisticsTime(createTime);

            //找到主单人员工id
            if (mainOrderEmployeeId.get() == null && MainSubEnum.MAIN.getType().equals(ach.getMainSplitPerson())) {
                mainOrderEmployeeId.set(ach.getBusinessId());
            }

        });

        //广告通50%核算
        accountingHalf(achList, categoryList, specList, orderInfo, customerId, factInfo);
        //广告通另购置0
        anotherBuyZero(achList, categoryList, specList, orderInfo);

        //计算完成后批量入库
//        achievementProductService.saveOrUpdateBatch(achList);
//        achievementCategoryService.saveOrUpdateBatch(categoryList);
//        achievementSpecService.saveOrUpdateBatch(specList);
        if (!CustomerType.NON_EXISTENT.getType().equals(customerType) && AchStatus.VALID.getType().equals(achModel.getStatus())) {
            newOldCustomerRecordService.save(CustomerType.NEW.getType().equals(customerType) ?
                    new NewOldCustomerRecordModel().setBusinessMonthId(monthInfo.getMonthId()).setEmployeeId(mainOrderEmployeeId.get()).setNewCustomerId(customerId) :
                    new NewOldCustomerRecordModel().setBusinessMonthId(monthInfo.getMonthId()).setEmployeeId(mainOrderEmployeeId.get()).setOldCustomerId(customerId));
        }
        //广告通后补50%
        calculateRepairAchievementAll(achList, orderInfo, customerId, factInfo, model);

//        businessAchHandler.achStatistics(achList, status, BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
    }

    private void addAchievementCalculateLog(CalculateFactInfo factInfo) {
        try {
            Long orderId = factInfo.getOrderInfo().getOrderId();
            String factInfoJson = new ObjectMapper().writeValueAsString(factInfo);
            achievementCalculateParamLogRepository.saveOrUpdateFactInfo(factInfoJson, orderId, "正常执行");
        } catch (Exception e) {
            log.error("业绩计算记录日志异常：{}", StringUtils.getStackTraceAsString(e));
        }

    }

    private void accountingHalf(List<AchievementProductDetailModel> achList, List<AchievementCategoryDetailModel> categoryList, List<AchievementSpecDetailModel> specList, OrderSimpleInfoResponse orderInfo, String customerId, CalculateFactInfo factInfo) {

        if (!OrderSaleTypeEnum.OPEN.getType().equals(orderInfo.getOrderSaleType()) && !OrderSaleTypeEnum.UPGRADE.getType().equals(orderInfo.getOrderSaleType())) {
            return;
        }
        //按50%核算的规格id
        List<String> specIdList = calculateRepairAchievement(achList, orderInfo, customerId, factInfo);
        if (CollectionUtils.isEmpty(specIdList)) {
            return;
        }
        List<Long> achievementCategoryIdList = new ArrayList<>();
        //需要生成的业绩规格id是否属于配置的规格id（50%核算规格id） 如果属于 净现金*0.5
        for (AchievementSpecDetailModel achievementSpecDetailModel : specList) {
            boolean flag = specIdList.contains(achievementSpecDetailModel.getSpecId() + "");
            if (flag) {
                achievementSpecDetailModel.setNetCash(achievementSpecDetailModel.getNetCash().multiply(BigDecimal.valueOf(NumberConstants.DOUBLE_VALUE_0_5)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP));
                achievementCategoryIdList.add(achievementSpecDetailModel.getAchievementCategoryId());
            }
        }
        List<Long> achievementIdList = new ArrayList<>();
        //汇总规格分类业绩
        for (AchievementCategoryDetailModel achievementCategoryDetailModel : categoryList) {
            Map<Long, List<AchievementSpecDetailModel>> byAchievementCategoryIdMap = specList.stream().collect(Collectors.groupingBy(AchievementSpecDetailModel::getAchievementCategoryId));

            List<AchievementSpecDetailModel> achievementSpecDetailModelList = byAchievementCategoryIdMap.get(achievementCategoryDetailModel.getAchievementCategoryId());

            BigDecimal totalNetCash = achievementSpecDetailModelList.stream().map(AchievementSpecDetailModel::getNetCash)
                    .filter(netCash -> netCash != null).reduce(BigDecimal.ZERO, BigDecimal::add);

            achievementCategoryDetailModel.setNetCash(totalNetCash);

            boolean flag = achievementCategoryIdList.contains(achievementCategoryDetailModel.getAchievementCategoryId());
            if (flag) {
                achievementIdList.add(achievementCategoryDetailModel.getAchievementId());
            }
        }
        //汇总商品业绩
        for (AchievementProductDetailModel achievementProductDetailModel : achList) {
            Map<Long, List<AchievementCategoryDetailModel>> byAchievementIdMap = categoryList.stream().collect(Collectors.groupingBy(AchievementCategoryDetailModel::getAchievementId));
            List<AchievementCategoryDetailModel> achievementCategoryDetailModelList = byAchievementIdMap.get(achievementProductDetailModel.getAchievementId());

            BigDecimal totalNetCash = achievementCategoryDetailModelList.stream().map(AchievementCategoryDetailModel::getNetCash)
                    .filter(netCash -> netCash != null).reduce(BigDecimal.ZERO, BigDecimal::add);
            achievementProductDetailModel.setNetCash(totalNetCash);

            boolean flag = achievementIdList.contains(achievementProductDetailModel.getAchievementId());
            if (flag) {
                //设置业绩核算50%标识
                achievementProductDetailModel.setCalculateAll(NumberConstants.INTEGER_VALUE_1);
            }
        }
    }

    private void anotherBuyZero(List<AchievementProductDetailModel> achList, List<AchievementCategoryDetailModel> categoryList, List<AchievementSpecDetailModel> specList, OrderSimpleInfoResponse orderInfo) {
        //广告通商品id
        List<String> advertisementProductList = Arrays.asList(productAchievementConfig.getAdvertisement().split(","));
        //续费规格
        List<String> renewSpecIdList = Arrays.asList(productAchievementConfig.getSpecId().split(","));

        Integer orderSaleType = orderInfo.getOrderSaleType();
        if (!OrderSaleTypeEnum.ANOTHER_BUY.getType().equals(orderSaleType)) {
            return;
        }
        List<Long> renewProductSpecIds = new ArrayList<>();
        //遍历订单商品规格
        for (OrderSimpleProductSpecItemResponse orderSimpleProductSpecItemResponse : orderInfo.getSpecItemResponseList()) {

            boolean flag = advertisementProductList.contains(orderSimpleProductSpecItemResponse.getProductId() + "");
            if (!flag) {
                continue;
            }
            //订单规格id
            Long productSpecId = orderSimpleProductSpecItemResponse.getProductSpecId();
            //判断订单规格id是否是续费规格
            boolean specFlag = renewSpecIdList.contains(productSpecId + "");
            if (!specFlag) {
                continue;
            }
            renewProductSpecIds.add(productSpecId);
        }

        if (CollectionUtils.isNotEmpty(renewProductSpecIds)) {
            for (AchievementSpecDetailModel achievementSpecDetailModel : specList) {
                boolean flag = renewProductSpecIds.contains(achievementSpecDetailModel.getSpecId());
                if (flag) {
                    achievementSpecDetailModel.setNetCash(BigDecimal.ZERO);
                }
            }

            for (AchievementCategoryDetailModel achievementCategoryDetailModel : categoryList) {
                Map<Long, List<AchievementSpecDetailModel>> byAchievementCategoryIdMap = specList.stream().collect(Collectors.groupingBy(AchievementSpecDetailModel::getAchievementCategoryId));

                List<AchievementSpecDetailModel> achievementSpecDetailModelList = byAchievementCategoryIdMap.get(achievementCategoryDetailModel.getAchievementCategoryId());

                BigDecimal totalNetCash = achievementSpecDetailModelList.stream().map(AchievementSpecDetailModel::getNetCash)
                        .filter(netCash -> netCash != null).reduce(BigDecimal.ZERO, BigDecimal::add);

                achievementCategoryDetailModel.setNetCash(totalNetCash);
            }

            for (AchievementProductDetailModel achievementProductDetailModel : achList) {
                Map<Long, List<AchievementCategoryDetailModel>> byAchievementIdMap = categoryList.stream().collect(Collectors.groupingBy(AchievementCategoryDetailModel::getAchievementId));
                List<AchievementCategoryDetailModel> achievementCategoryDetailModelList = byAchievementIdMap.get(achievementProductDetailModel.getAchievementId());

                BigDecimal totalNetCash = achievementCategoryDetailModelList.stream().map(AchievementCategoryDetailModel::getNetCash)
                        .filter(netCash -> netCash != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                achievementProductDetailModel.setNetCash(totalNetCash);
            }
        }
    }

    /**
     * 广告通后补50
     *
     * @param achList
     * @param orderInfo
     * @param customerId
     * @param factInfo
     * @param model
     */
    private void calculateRepairAchievementAll(List<AchievementProductDetailModel> achList, OrderSimpleInfoResponse orderInfo, String customerId, CalculateFactInfo factInfo, MqOrderPaymentInfoModel model) {
        if (!OrderSaleTypeEnum.OPEN.getType().equals(orderInfo.getOrderSaleType()) && !OrderSaleTypeEnum.UPGRADE.getType().equals(orderInfo.getOrderSaleType())) {
            return;
        }
        //根据商品id分组
        Map<Long, List<AchievementProductDetailModel>> byProductIdMap = achList.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getProductId));
        //当前订单网站商品
        List<AchievementProductDetailModel> websiteList = byProductIdMap.get(Long.valueOf(productAchievementConfig.getWebsite()));
        if (CollectionUtils.isEmpty(websiteList)) {
            return;
        }
        //判断是否存在不分期或存在首款的网站并且实付金额大于0
        websiteList = websiteList.stream().filter(ach -> ach.getInstallmentNum() <= 1 && ach.getPaidAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(websiteList)){
            return;
        }
        //获取3个商务月内的开始结束时间
        Pair<Date, Date> threeBusinessMonthDate = getBusinessMonthDate(false, orderInfo.getCreateTime());
        Date startDate = threeBusinessMonthDate.getKey();
        Date endDate = threeBusinessMonthDate.getValue();

        Integer saleOpenType = OrderSaleTypeEnum.OPEN.getType();
        Integer saleUpgradeType = OrderSaleTypeEnum.UPGRADE.getType();
        List<Integer> saleTypeList = new ArrayList<>();
        saleTypeList.add(saleOpenType);

        //存在网站
        //查询3个月内广告通业绩流水
        List<AchievementProductDetailModel> achievementProductDetailModelList = achievementProductDetailRepository.selectAchievementProductByBetweenDate(startDate, endDate, saleTypeList, customerId, productAchievementConfig.getAdvertisement(), null, null, null);
        if (CollectionUtils.isEmpty(achievementProductDetailModelList)) {
            return;
        }
        //广告通没有按100%核算的商品业绩集合 后补生成50%业绩
        List<AchievementProductDetailModel> notCalculateAllProductDetailList = achievementProductDetailModelList.stream().filter(ach -> ach.getCalculateAll().equals(NumberConstants.INTEGER_VALUE_1)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notCalculateAllProductDetailList)) {
            return;
        }
        //业绩流水id
        List<Long> notCalculateAllAchievementIdList = notCalculateAllProductDetailList.stream().map(AchievementProductDetailModel::getAchievementId).collect(Collectors.toList());

        List<AchievementProductDetailModel> repairProductListAll = new ArrayList<>();
        List<AchievementCategoryDetailModel> repairCategoryListAll = new ArrayList<>();
        List<AchievementSpecDetailModel> repairSpecListAll = new ArrayList<>();

        for (Long notCalculateAllAchievementId : notCalculateAllAchievementIdList) {

            List<AchievementProductDetailModel> repairProductList = new ArrayList<>();
            List<AchievementCategoryDetailModel> repairCategoryList = new ArrayList<>();
            List<AchievementSpecDetailModel> repairSpecList = new ArrayList<>();

            List<Long> notCalculateAllAchievementIdParma = new ArrayList<>();
            notCalculateAllAchievementIdParma.add(notCalculateAllAchievementId);
            //通过业绩流水id查询业绩规格分类明细
            List<AchievementCategoryDetailModel> achievementCategoryDetailModelList = achievementCategoryDetailRepository.selectAchievementCategoryByAchievementIds(notCalculateAllAchievementIdParma);
            if (CollectionUtils.isEmpty(achievementCategoryDetailModelList)) {
                continue;
            }

            //根据业绩规格分类id查询业绩规格数据
            List<Long> achievementCategoryIdList = achievementCategoryDetailModelList.stream().map(AchievementCategoryDetailModel::getAchievementCategoryId).collect(Collectors.toList());
            List<AchievementSpecDetailModel> achievementSpecDetailModelList = achievementSpecDetailRepository.selectAchievementSpecListByCategoryIds(achievementCategoryIdList);
            if (CollectionUtils.isEmpty(achievementSpecDetailModelList)) {
                continue;
            }

            //续费规格
            List<String> specIdList = Arrays.asList(productAchievementConfig.getSpecId().split(","));
            for (AchievementSpecDetailModel achievementSpecDetailModel : achievementSpecDetailModelList) {
                boolean flag = specIdList.contains(achievementSpecDetailModel.getSpecId() + "");
                if (!flag) {
                    continue;
                }
                List<AchievementSpecDetailModel> existRefundList = achievementSpecDetailRepository.selectByOrderIdAndSpecId(achievementSpecDetailModel.getOrderId(),achievementSpecDetailModel.getSpecId());

                AchievementSpecDetailModel repairSpec = new AchievementSpecDetailModel();
                BeanUtils.copyProperties(achievementSpecDetailModel, repairSpec);
                repairSpec.setGroupById(achievementSpecDetailModel.getAchievementCategoryId());
                if(!CollectionUtils.isEmpty(existRefundList)){
                    //存在退款 走到这里 第一笔发了百分之50或者第一笔发了100 拿退款的净现金填充就好
                    repairSpec.setNetCash(existRefundList.get(0).getNetCash().multiply(new BigDecimal("-1")));
                }else{
                    //不存在退款 走到这里 只能是第一笔发了50%
                }

                repairSpec.setId(null);
                repairSpec.setAchievementCategoryId(null);
                repairSpec.setAchievementSpecId(IdUtil.getSnowflakeNextId());

                repairSpec.setStandardPrice(BigDecimal.ZERO);
                repairSpec.setPayableAmount(BigDecimal.ZERO);
                repairSpec.setPaidAmount(BigDecimal.ZERO);
                repairSpec.setFirstYearQuote(BigDecimal.ZERO);
                repairSpec.setFirstYearIncome(BigDecimal.ZERO);
                repairSpec.setRenewalQuote(BigDecimal.ZERO);
                repairSpec.setRenewalIncome(BigDecimal.ZERO);
                repairSpec.setAgentCommAchv(BigDecimal.ZERO);
                repairSpec.setAgentActCommAchv(BigDecimal.ZERO);
                repairSpec.setAgentDefCommAchv(BigDecimal.ZERO);
                repairSpec.setDeptCommAchv(BigDecimal.ZERO);
                repairSpec.setBuCommAchv(BigDecimal.ZERO);
                repairSpec.setBranchCommAchv(BigDecimal.ZERO);
                repairSpec.setCreateTime(websiteList.get(0).getStatisticsTime());
                repairSpec.setUpdateTime(new Date());
                repairSpecList.add(repairSpec);
            }
            if (CollectionUtils.isEmpty(repairSpecList)) {
                continue;
            }

            Map<Long, List<AchievementSpecDetailModel>> byAchievementCategoryIdMap = repairSpecList.stream().collect(Collectors.groupingBy(AchievementSpecDetailModel::getGroupById));

            for (AchievementCategoryDetailModel achievementCategoryDetailModel : achievementCategoryDetailModelList) {
                List<AchievementSpecDetailModel> achievementSpecDetailModels = byAchievementCategoryIdMap.get(achievementCategoryDetailModel.getAchievementCategoryId());
                if (CollectionUtils.isEmpty(achievementSpecDetailModels)) {
                    continue;
                }
                BigDecimal totalNetCash = achievementSpecDetailModels.stream().map(AchievementSpecDetailModel::getNetCash)
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

                AchievementCategoryDetailModel repairCategory = new AchievementCategoryDetailModel();
                BeanUtils.copyProperties(achievementCategoryDetailModel, repairCategory);
                repairCategory.setGroupById(achievementCategoryDetailModel.getAchievementId());
                repairCategory.setId(null);
                repairCategory.setAchievementId(null);
                repairCategory.setAchievementCategoryId(IdUtil.getSnowflakeNextId());

                repairCategory.setStandardPrice(BigDecimal.ZERO);
                repairCategory.setPayableAmount(BigDecimal.ZERO);
                repairCategory.setPaidAmount(BigDecimal.ZERO);
                repairCategory.setFirstYearQuote(BigDecimal.ZERO);
                repairCategory.setFirstYearIncome(BigDecimal.ZERO);
                repairCategory.setRenewalQuote(BigDecimal.ZERO);
                repairCategory.setRenewalIncome(BigDecimal.ZERO);
                repairCategory.setAgentCommAchv(BigDecimal.ZERO);
                repairCategory.setAgentActCommAchv(BigDecimal.ZERO);
                repairCategory.setAgentDefCommAchv(BigDecimal.ZERO);
                repairCategory.setDeptCommAchv(BigDecimal.ZERO);
                repairCategory.setBuCommAchv(BigDecimal.ZERO);
                repairCategory.setBranchCommAchv(BigDecimal.ZERO);
                repairCategory.setNetCash(totalNetCash);
                repairCategory.setCreateTime(websiteList.get(0).getStatisticsTime());
                repairCategory.setUpdateTime(new Date());
                repairCategoryList.add(repairCategory);
            }
            List<Long> refundIdList = new ArrayList<>();
            Map<Long, List<AchievementCategoryDetailModel>> byAchievementIdMap = repairCategoryList.stream().collect(Collectors.groupingBy(AchievementCategoryDetailModel::getGroupById));
            for (AchievementProductDetailModel achievementProductDetailModel : notCalculateAllProductDetailList) {
                List<AchievementCategoryDetailModel> achievementCategoryDetailModels = byAchievementIdMap.get(achievementProductDetailModel.getAchievementId());
                if (CollectionUtils.isEmpty(achievementCategoryDetailModels)) {
                    continue;
                }
                BigDecimal totalNetCash = achievementCategoryDetailModels.stream().map(AchievementCategoryDetailModel::getNetCash)
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

                AchievementProductDetailModel repairProduct = new AchievementProductDetailModel();
                BeanUtils.copyProperties(achievementProductDetailModel, repairProduct);
                repairProduct.setId(null);
                repairProduct.setAchievementId(IdUtil.getSnowflakeNextId());

                //通过客户id获取商务id
                ProtectByCustomer protectByCustomerId = innerService.getProtectByCustomerId(achievementProductDetailModel.getCustomerId());

                //通过商务id获取商务信息
                if (null == protectByCustomerId) {
                    log.info("calculateRepairAchievementAll 客保关系为空:{}", achievementProductDetailModel.getCustomerId());
                } else {
                    //商务id
                    String salerId = protectByCustomerId.getSalerId();
                    //商务信息
                    UserInfoDetailResp userInfoDetail = innerService.getUserInfoDetail(salerId);
                    repairProduct.setBusinessId(salerId);
                    repairProduct.setBusinessRepresentative(userInfoDetail.getName());
                }

                repairProduct.setStandardPrice(BigDecimal.ZERO);
                repairProduct.setPayableAmount(BigDecimal.ZERO);
                repairProduct.setPaidAmount(BigDecimal.ZERO);
                repairProduct.setFirstYearQuote(BigDecimal.ZERO);
                repairProduct.setFirstYearRevenue(BigDecimal.ZERO);
                repairProduct.setRenewalQuote(BigDecimal.ZERO);
                repairProduct.setRenewalRevenue(BigDecimal.ZERO);
                repairProduct.setAgentCommissionAchievement(BigDecimal.ZERO);
                repairProduct.setAgentActualCommission(BigDecimal.ZERO);
                repairProduct.setAgentDeferredCommission(BigDecimal.ZERO);
                repairProduct.setDeptCommission(BigDecimal.ZERO);
                repairProduct.setDivCommission(BigDecimal.ZERO);
                repairProduct.setBranchCommission(BigDecimal.ZERO);
                repairProduct.setNetCash(totalNetCash);
                //补完业绩后设置以按100%核算
                repairProduct.setCalculateAll(NumberConstants.INTEGER_VALUE_0);
                repairProduct.setCreateTime(websiteList.get(0).getStatisticsTime());
                repairProduct.setUpdateTime(new Date());
                repairProduct.setStatisticsTime(websiteList.get(0).getStatisticsTime());

                BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(repairProduct.getStatisticsTime());
                if (monthInfo != null) {
                    repairProduct.setBusinessMonth(monthInfo.getMonth());
                    repairProduct.setBusinessMonthId(monthInfo.getMonthId());
                } else {
                    throw new RuntimeException(String.format("根据流水创建时间未找到商务月,创建时间:%s ,订单id:%s", DateUtil.format(repairProduct.getStatisticsTime(), DatePattern.NORM_DATETIME_PATTERN), repairProduct.getOrderId()));
                }
                //查询该订单明细是否存在退转款
                String orderProductId = achievementProductDetailModel.getOrderProductId();
                List<String> orderProductIdList = new ArrayList<>();
                orderProductIdList.add(orderProductId);
                List<AchievementProductDetailModel> existList = achievementProductDetailRepository.selectByOrderIdAndOrderProductId(achievementProductDetailModel.getOrderId(), orderProductIdList);
                //支付完成才会有退款
                List<AchievementProductDetailModel> refundList = existList.stream().filter(exist -> StatusEnum.REFUND.getCode().equals(exist.getStatus())).collect(Collectors.toList());
                repairProductList.add(repairProduct);
                //退款后补广告通
                if(!CollectionUtils.isEmpty(refundList)){
                    //存在退款 找到后补的广告通
                    List<AchievementProductDetailModel> repairList = existList.stream().filter(exist -> StatusEnum.VALID.getCode().equals(exist.getStatus())).collect(Collectors.toList());

                    Map<Integer, List<AchievementProductDetailModel>> byCyclCountMap = repairList.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getPayCycleCount));
                    for (Map.Entry<Integer, List<AchievementProductDetailModel>> cyclCountMap : byCyclCountMap.entrySet()) {

                        List<AchievementProductDetailModel> achRefundList = cyclCountMap.getValue();
                        if(CollectionUtils.isEmpty(achRefundList)){
                            continue;
                        }
                        //主单人退款
                        List<AchievementProductDetailModel> mainRefundList = achRefundList.stream().filter(ach -> ach.getMainSplitPerson().equals(MainSplitPersonEnum.MAIN.getCode())).collect(Collectors.toList());
                        if(!CollectionUtils.isEmpty(mainRefundList)){
                            List<Long> mainRefundIdList = mainRefundList.stream().map(AchievementProductDetailModel::getId).collect(Collectors.toList());
                            refundIdList.addAll(mainRefundIdList);
                        }

                        //辅单人退款
                        List<AchievementProductDetailModel> assistantRefundList = achRefundList.stream().filter(ach -> ach.getMainSplitPerson().equals(MainSplitPersonEnum.ASSISTANT.getCode())).collect(Collectors.toList());
                        if(!CollectionUtils.isEmpty(assistantRefundList)){
                            List<Long> assistantRefundIdList = assistantRefundList.stream().map(AchievementProductDetailModel::getId).collect(Collectors.toList());
                            refundIdList.addAll(assistantRefundIdList);
                        }

                    }
                }
            }
            //奖后补的广告通后补状态置为0
            if(!CollectionUtils.isEmpty(refundIdList)){
                achievementProductDetailRepository.updateCalculateByIdList(refundIdList);
            }
            if(!CollectionUtils.isEmpty(repairProductList)){
                //匹配业绩流水id、业绩规格分类id
                achievementProductWrapper.relationId(repairSpecList, repairCategoryList, repairProductList, model);

                repairProductListAll.addAll(repairProductList);
                repairCategoryListAll.addAll(repairCategoryList);
                repairSpecListAll.addAll(repairSpecList);
            }

        }
        //批量入库
        achievementProductService.saveOrUpdateBatch(repairProductListAll);
        achievementCategoryService.saveOrUpdateBatch(repairCategoryListAll);
        achievementSpecService.saveOrUpdateBatch(repairSpecListAll);

        List<Long> orderIdList = repairProductListAll.stream().map(AchievementProductDetailModel::getOrderId).collect(Collectors.toList());
        //修改业绩核算状态已按100%核算
        achievementProductDetailRepository.updateByOrderIdAndProductId(orderIdList, null);

    }

    private List<String> calculateRepairAchievement(List<AchievementProductDetailModel> achList, OrderSimpleInfoResponse orderInfo, String customerId, CalculateFactInfo factInfo) {
        //根据商品id分组
        Map<Long, List<AchievementProductDetailModel>> byProductIdMap = achList.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getProductId));

        //广告通商品id
        List<String> advertisementProductList = Arrays.asList(productAchievementConfig.getAdvertisement().split(","));
        //当前订单广告通商品
        List<AchievementProductDetailModel> advertisementProducts = new ArrayList<>();
        for (String productId : advertisementProductList) {
            List<AchievementProductDetailModel> advertisementList = byProductIdMap.get(Long.valueOf(productId));
            if (CollectionUtils.isEmpty(advertisementList)) {
                continue;
            }
            advertisementProducts.addAll(advertisementList);
        }
        //没有广告通 直接返回
        if (CollectionUtils.isEmpty(advertisementProducts)) {
            return null;
        }

        //获取3个商务月内的开始结束时间
        Pair<Date, Date> threeBusinessMonthDate = getBusinessMonthDate(false, orderInfo.getCreateTime());
        Date startDate = threeBusinessMonthDate.getKey();
        Date endDate = threeBusinessMonthDate.getValue();
        //新开类型
        Integer saleOpenType = OrderSaleTypeEnum.OPEN.getType();
        Integer saleUpgradeType = OrderSaleTypeEnum.UPGRADE.getType();
        List<Integer> saleTypeOpenList = new ArrayList<>();
        saleTypeOpenList.add(saleOpenType);
        List<Integer> saleTypeUpgradeList = new ArrayList<>();
        saleTypeUpgradeList.add(saleUpgradeType);

        //存在广告通
        //当前订单是否有新开网站 并且是首款 并且实付金额大于0
        List<String> webSiteProductList = Arrays.asList( productAchievementConfig.getWebsite().split(","));

        List<AchievementProductDetailModel> openSiteList = achList.stream().filter(ach -> webSiteProductList.contains(String.valueOf(ach.getProductId())) && ach.getSaleType().equals(saleOpenType) &&  ach.getInstallmentNum() <= 1 && ach.getPaidAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

        //查询客户3个月内新开有效首款网站业绩流水商品
        List<AchievementProductDetailModel> achievementProductDetailModelList = achievementProductDetailRepository.selectAchievementProductByBetweenDate(startDate, endDate, saleTypeOpenList, customerId,productAchievementConfig.getWebsite(),productAchievementConfig.getWebsiteName(),1,orderInfo.getOrderId());
        achievementProductDetailModelList = achievementProductDetailModelList.stream().filter(ach -> ach.getPaidAmount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        boolean refundFlag = false;
        //查询当前订单是否存在退款
        if(CollectionUtils.isNotEmpty(achievementProductDetailModelList)){
            List<String> orderProductIdList = achievementProductDetailModelList.stream().map(AchievementProductDetailModel::getOrderProductId).distinct().collect(Collectors.toList());
            List<AchievementProductDetailModel> refundAchList =  achievementProductDetailRepository.selectAchRefundListByOrderProductId(orderProductIdList);

            if(CollectionUtils.isNotEmpty(refundAchList)){
                List<String> refundOrderProductIdList = refundAchList.stream().map(AchievementProductDetailModel::getOrderProductId).distinct().collect(Collectors.toList());
                refundFlag = orderProductIdList.containsAll(refundOrderProductIdList) && orderProductIdList.size() == refundOrderProductIdList.size();
                if(refundFlag){
                    //全匹配上 说明全部退款 没有saas 直接按100%核算
                }
            }
        }else{
            refundFlag = true;
        }

        if(CollectionUtils.isEmpty(openSiteList) && refundFlag){
            //3个月内不存在新开网站 并且当前订单没有新开网站
            //获取当前商务月开始结束时间
            Pair<Date, Date> businessMonthDate = getBusinessMonthDate(true,orderInfo.getCreateTime());
            Date currentStartDate = businessMonthDate.getKey();
            Date currentEndDate = businessMonthDate.getValue();
            //当前订单是否存在升级网站
            List<AchievementProductDetailModel> upgradeSiteList = achList.stream().filter(ach -> webSiteProductList.contains(String.valueOf(ach.getProductId())) && ach.getSaleType().equals(saleUpgradeType) &&  ach.getInstallmentNum() <= 1).collect(Collectors.toList());
            //当前商务月是否存在升级首款网站商品
            List<AchievementProductDetailModel> upgradeSiteMonthList = achievementProductDetailRepository.selectAchievementProductByBetweenDate(currentStartDate, currentEndDate, saleTypeUpgradeList, customerId,productAchievementConfig.getWebsite(),productAchievementConfig.getWebsiteName(),1,orderInfo.getOrderId());
            upgradeSiteMonthList = upgradeSiteMonthList.stream().filter(ach -> ach.getPaidAmount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());

            boolean upgradeRefundFlag = false;
            //查询当前订单是否存在退款
            if(CollectionUtils.isNotEmpty(upgradeSiteMonthList)){
                List<String> orderProductIdList = upgradeSiteMonthList.stream().map(AchievementProductDetailModel::getOrderProductId).distinct().collect(Collectors.toList());
                List<AchievementProductDetailModel> refundAchList =  achievementProductDetailRepository.selectAchRefundListByOrderProductId(orderProductIdList);

                if(CollectionUtils.isNotEmpty(refundAchList)){
                    List<String> refundOrderProductIdList = refundAchList.stream().map(AchievementProductDetailModel::getOrderProductId).distinct().collect(Collectors.toList());
                    //ture 全匹配上 说明全部退款 没有saas 直接按100%核算
                    upgradeRefundFlag = orderProductIdList.containsAll(refundOrderProductIdList) && orderProductIdList.size() == refundOrderProductIdList.size();;

                }
            }else{
                //没有升级网站  upgradeSiteMonthList 按50%核算
                upgradeRefundFlag = true;
            }

            if(CollectionUtils.isEmpty(upgradeSiteList) && upgradeRefundFlag){
                //当前商务月和当前订单不存在升级网站  广告通业绩按50%计算

                //当前订单存在的广告通商品id集合
                List<Long> dvertisementProductIdList = advertisementProducts.stream().map(AchievementProductDetailModel::getProductId).collect(Collectors.toList());

                Map<Long, List<OrderSimpleProductSpecItemResponse>> orderSpecMap = orderInfo.getSpecItemResponseList().stream().collect(Collectors.groupingBy(OrderSimpleProductSpecItemResponse::getProductId));
                //广告通商品id对应的规格集合
                Map<Long, List<Long>> productBySpecIdMap = new HashMap<>();
                for (Long productId : dvertisementProductIdList) {
                    List<OrderSimpleProductSpecItemResponse> orderSimpleProductSpecItemResponses = orderSpecMap.get(productId);
                    if(CollectionUtils.isNotEmpty(orderSimpleProductSpecItemResponses)){
                        List<Long> orderSpecIdList = orderSimpleProductSpecItemResponses.stream().map(OrderSimpleProductSpecItemResponse::getProductSpecId).collect(Collectors.toList());
                        productBySpecIdMap.put(productId,orderSpecIdList);
                    }
                }
                factInfo.setProductBySpecIdMap(productBySpecIdMap);

                List<String> calculateSpecIdList = new ArrayList<>();
                //续费规格
                List<String> specIdList = Arrays.asList(productAchievementConfig.getSpecId().split(","));

                //商品订单规格集合
                List<Long> orderSpecIdList = orderInfo.getSpecItemResponseList().stream().map(OrderSimpleProductSpecItemResponse::getProductSpecId).collect(Collectors.toList());
                //配置续费规格
                for (String specId : specIdList) {
                    boolean flag = orderSpecIdList.contains(Long.valueOf(specId));
                    if (flag) {
                        calculateSpecIdList.add(specId);
                    }
                }
                return calculateSpecIdList;
            }
        }
        return null;
    }

    /**
     * 获取商务月开始结束时间
     *
     * @param flag
     * @return
     */
    private Pair<Date, Date> getBusinessMonthDate(Boolean flag, Date createTime) {
        BusinessMonthModel businessMonthModel = businessMonthRepository.selectBusinessMonthByDate(createTime);
        //当前商务月月
        String currentMonthStr = businessMonthModel.getMonth();
        //查询前3个商务月
        List<BusinessMonthModel> threeMonthList = businessMonthRepository.selectBusinessMonthByThreeMonth(currentMonthStr);

        if (flag) {
            return new Pair<>(threeMonthList.get(0).getStartDate(), threeMonthList.get(0).getEndDate());
        }

        Date startDate = threeMonthList.get(threeMonthList.size() - 1).getStartDate();
        Date endDate = threeMonthList.get(0).getEndDate();
        return new Pair<>(startDate, endDate);
    }


    private boolean isGenerateAchievement(OrderSimpleInfoResponse orderInfo, Map<Long, ProductListForAchievementResponse> productSpecMap, OrderSimpleProductSpecResponse specResponse, Map<Long, Integer> specQuantityMap) {
        // 获取订单来源
        Integer orderSource = orderInfo.getOrderSource();
        // 如果订单来源为代理订单或销售平台订单，则返回true
        if (OrderSourceEnum.PROXY_ORDER.getType().equals(orderSource) || OrderSourceEnum.SALER_PLATFORM.getType().equals(orderSource)) {
            return true;
        } else if (OrderSaleTypeEnum.ANOTHER_BUY.getType().equals(orderInfo.getOrderSaleType()) && (OrderSourceEnum.OFF_LINE_ORDER.getType().equals(orderSource) || OrderSourceEnum.SOP_PLATFORM.getType().equals(orderSource))) {
            // 商品为广告类商品时 ;记录售卖类型为另购;且商品规格的规格服务项次数=1;，则生成该规格的业绩流水；
            // 获取商品规格信息
            Long productId = specResponse.getProductId();
            ProductListForAchievementResponse productListForAchievementResponse = productSpecMap.get(productId);
            // 如果商品规格信息不为空，且商品为广告类商品，则获取商品规格ID
            if (productListForAchievementResponse != null && productListForAchievementResponse.getAdFlag() == 1) {
                Long productSpecId = specResponse.getProductSpecId();
                // 如果商品规格的规格服务项次数为1，则返回true
                if (specQuantityMap.isEmpty()) {
                    log.error("订单:{} ,商品规格Id:{}广告类商品的服务项次数为空,不能进行记录业绩流水", orderInfo.getOrderId(), productSpecId);
                }
                return !specQuantityMap.isEmpty() && NumberConstants.INTEGER_VALUE_2.equals(specQuantityMap.get(productSpecId));
            }
        }
        return false;
    }


    public CheckInServeDataIsRightResponse checkInServeData(MqOrderPaymentInfoModel model) {
        if (ObjectUtil.isEmpty(model)) {
            return null;
        }
        return startCheckInServeData(model);
    }


    private CheckInServeDataIsRightResponse startCheckInServeData(MqOrderPaymentInfoModel mqPaymentInfoModel) {
        CheckInServeDataIsRightResponse response = new CheckInServeDataIsRightResponse();
        log.info("taskId:{},StartCheckInServeData计算服务中业绩Start....", mqPaymentInfoModel.getTaskId());
        //获取规格上可以生成业绩的订单（标志为生产完成规格单才可以生成业绩）
        List<AchievementSpecDetailModel> specDetailModelList = achievementSpecService.queryValidSpcList(mqPaymentInfoModel.getOrderId(), mqPaymentInfoModel.getProductId(), mqPaymentInfoModel.getInstallmentNum(), mqPaymentInfoModel.getOrderProductId());
        if (ObjectUtil.isEmpty(specDetailModelList)) {
            return null;
        }
        //规格 分类 商品
        List<AchievementSpecDetailModel> newSpecDetailModelList = new ArrayList<>();
        List<AchievementCategoryDetailModel> newCategoryDetailModelList = new ArrayList<>();
        List<AchievementProductDetailModel> newProductDetailModelList = new ArrayList<>();

        //主单人数据
        List<AchievementSpecDetailModel> mastModels = specDetailModelList.stream()
                .filter(e -> e.getMainSplitPerson().equals(NumberConstants.INTEGER_VALUE_1))
                .collect(Collectors.toList());
        //分单人数据
        List<AchievementSpecDetailModel> subModels = specDetailModelList.stream()
                .filter(e -> e.getMainSplitPerson().equals(NumberConstants.INTEGER_VALUE_2))
                .collect(Collectors.toList());

        //主单人 规格 分类 商品 关联ID 计算
        if (ObjectUtil.isNotEmpty(mastModels)) {
            List<AchievementSpecDetailModel> newMastSpecDetailModelList = achievementSpecWrapper.newBuildSpecDetailModels(NumberConstants.INTEGER_VALUE_1, mastModels, mqPaymentInfoModel);
            List<AchievementCategoryDetailModel> newMastCategoryDetailModelList = achievementCategoryWrapper.newBuildCategoryDetailModels(NumberConstants.INTEGER_VALUE_1, newMastSpecDetailModelList, mqPaymentInfoModel);
            List<AchievementProductDetailModel> newMastProductDetailModelList = achievementProductWrapper.newBuildProductDetailModels(NumberConstants.INTEGER_VALUE_1, newMastCategoryDetailModelList, mqPaymentInfoModel);
            achievementProductWrapper.relationId(newMastSpecDetailModelList, newMastCategoryDetailModelList, newMastProductDetailModelList, mqPaymentInfoModel);
            newSpecDetailModelList.addAll(newMastSpecDetailModelList);
            newCategoryDetailModelList.addAll(newMastCategoryDetailModelList);
            newProductDetailModelList.addAll(newMastProductDetailModelList);
        }

        //分单人 规格 分类 商品 关联ID 计算
        if (ObjectUtil.isNotEmpty(subModels)) {
            List<AchievementSpecDetailModel> newSubSpecDetailModelList = achievementSpecWrapper.newBuildSpecDetailModels(NumberConstants.INTEGER_VALUE_2, mastModels, mqPaymentInfoModel);
            List<AchievementCategoryDetailModel> newSubCategoryDetailModelList = achievementCategoryWrapper.newBuildCategoryDetailModels(NumberConstants.INTEGER_VALUE_2, newSubSpecDetailModelList, mqPaymentInfoModel);
            List<AchievementProductDetailModel> newSubProductDetailModelList = achievementProductWrapper.newBuildProductDetailModels(NumberConstants.INTEGER_VALUE_2, newSubCategoryDetailModelList, mqPaymentInfoModel);
            achievementProductWrapper.relationId(newSubSpecDetailModelList, newSubCategoryDetailModelList, newSubProductDetailModelList, mqPaymentInfoModel);
            newSpecDetailModelList.addAll(newSubSpecDetailModelList);
            newCategoryDetailModelList.addAll(newSubCategoryDetailModelList);
            newProductDetailModelList.addAll(newSubProductDetailModelList);
        }

        response.setNewSpecDetailModelList(newSpecDetailModelList);
        response.setNewCategoryDetailModelList(newCategoryDetailModelList);
        response.setNewProductDetailModelList(newProductDetailModelList);
        log.info("taskId:{},StartCheckInServeData计算服务中业绩end...，newSpecDetailModelList：{},newCategoryDetailModelList:{},newProductDetailModelList:{}", mqPaymentInfoModel.getTaskId(), JSONUtil.toJsonStr(newSpecDetailModelList), JSONUtil.toJsonStr(newCategoryDetailModelList), JSONUtil.toJsonStr(newProductDetailModelList));
        return response;
    }


    public void syncAdvertisingPassHistory(AchievementProductDetailModel productDetailModel, Integer saleType) {

        String customerId = productDetailModel.getCustomerId();

        List<Integer> saleTypeList = new ArrayList<>();
        Integer saleOpenType = OrderSaleTypeEnum.OPEN.getType();
        saleTypeList.add(saleOpenType);
        //存在网站
        List<AchievementProductDetailModel> achievementProductDetailModelList = new ArrayList<>();
        if (OrderSaleTypeEnum.OPEN.getType().equals(saleType)) {
            //获取3个商务月内的开始结束时间
            Pair<Date, Date> threeBusinessMonthDate = getBusinessMonthDate(false, productDetailModel.getCreateTime());
            Date startDate = threeBusinessMonthDate.getKey();
            Date endDate = threeBusinessMonthDate.getValue();
            //查询3个月内广告通业绩流水
            achievementProductDetailModelList = achievementProductDetailRepository.selectAchievementProductByBetweenDate(startDate, endDate, saleTypeList, customerId, productAchievementConfig.getAdvertisement(), productAchievementConfig.getWebsiteName(), null, null);
        }
        if (OrderSaleTypeEnum.UPGRADE.getType().equals(saleType)) {
            //获取1个商务月内的开始结束时间
            Pair<Date, Date> threeBusinessMonthDate = getBusinessMonthDate(true, productDetailModel.getCreateTime());
            Date startDate = threeBusinessMonthDate.getKey();
            Date endDate = threeBusinessMonthDate.getValue();
            //查询1个月内广告通业绩流水
            achievementProductDetailModelList = achievementProductDetailRepository.selectAchievementProductByBetweenDate(startDate, endDate, saleTypeList, customerId, productAchievementConfig.getAdvertisement(), productAchievementConfig.getWebsiteName(), null, null);
        }

        if (CollectionUtils.isEmpty(achievementProductDetailModelList)) {
            return;
        }
        //广告通没有按100%核算的商品业绩集合 后补生成50%业绩
        List<AchievementProductDetailModel> notCalculateAllProductDetailList = achievementProductDetailModelList.stream().filter(ach -> ach.getCalculateAll().equals(NumberConstants.INTEGER_VALUE_1)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notCalculateAllProductDetailList)) {
            return;
        }
        //业绩流水id
        List<Long> notCalculateAllAchievementIdList = notCalculateAllProductDetailList.stream().map(AchievementProductDetailModel::getAchievementId).collect(Collectors.toList());

        List<AchievementProductDetailModel> repairProductListAll = new ArrayList<>();
        List<AchievementCategoryDetailModel> repairCategoryListAll = new ArrayList<>();
        List<AchievementSpecDetailModel> repairSpecListAll = new ArrayList<>();

        for (Long notCalculateAllAchievementId : notCalculateAllAchievementIdList) {

            List<AchievementProductDetailModel> repairProductList = new ArrayList<>();
            List<AchievementCategoryDetailModel> repairCategoryList = new ArrayList<>();
            List<AchievementSpecDetailModel> repairSpecList = new ArrayList<>();

            List<Long> notCalculateAllAchievementIdParma = new ArrayList<>();
            notCalculateAllAchievementIdParma.add(notCalculateAllAchievementId);
            //通过业绩流水id查询业绩规格分类明细
            List<AchievementCategoryDetailModel> achievementCategoryDetailModelList = achievementCategoryDetailRepository.selectAchievementCategoryByAchievementIds(notCalculateAllAchievementIdParma);
            if (CollectionUtils.isEmpty(achievementCategoryDetailModelList)) {
                continue;
            }

            //根据业绩规格分类id查询业绩规格数据
            List<Long> achievementCategoryIdList = achievementCategoryDetailModelList.stream().map(AchievementCategoryDetailModel::getAchievementCategoryId).collect(Collectors.toList());
            List<AchievementSpecDetailModel> achievementSpecDetailModelList = achievementSpecDetailRepository.selectAchievementSpecListByCategoryIds(achievementCategoryIdList);
            if (CollectionUtils.isEmpty(achievementSpecDetailModelList)) {
                continue;
            }

            //续费规格
            List<String> specIdList = Arrays.asList(productAchievementConfig.getSpecId().split(","));
            for (AchievementSpecDetailModel achievementSpecDetailModel : achievementSpecDetailModelList) {
                boolean flag = specIdList.contains(achievementSpecDetailModel.getSpecId() + "");
                if (!flag) {
                    continue;
                }
                List<AchievementSpecDetailModel> existRefundList = achievementSpecDetailRepository.selectByOrderIdAndSpecId(achievementSpecDetailModel.getOrderId(),achievementSpecDetailModel.getSpecId());

                AchievementSpecDetailModel repairSpec = new AchievementSpecDetailModel();
                BeanUtils.copyProperties(achievementSpecDetailModel, repairSpec);
                repairSpec.setGroupById(achievementSpecDetailModel.getAchievementCategoryId());

                if(!CollectionUtils.isEmpty(existRefundList)){
                    //存在退款 走到这里 第一笔发了百分之50或者第一笔发了100 拿退款的净现金填充就好
                    repairSpec.setNetCash(existRefundList.get(0).getNetCash().multiply(new BigDecimal("-1")));
                }else{
                    //不存在退款 走到这里 只能是第一笔发了50%
                }

                repairSpec.setId(null);
                repairSpec.setAchievementCategoryId(null);
                repairSpec.setAchievementSpecId(IdUtil.getSnowflakeNextId());

                repairSpec.setStandardPrice(BigDecimal.ZERO);
                repairSpec.setPayableAmount(BigDecimal.ZERO);
                repairSpec.setPaidAmount(BigDecimal.ZERO);
                repairSpec.setFirstYearQuote(BigDecimal.ZERO);
                repairSpec.setFirstYearIncome(BigDecimal.ZERO);
                repairSpec.setRenewalQuote(BigDecimal.ZERO);
                repairSpec.setRenewalIncome(BigDecimal.ZERO);
                repairSpec.setAgentCommAchv(BigDecimal.ZERO);
                repairSpec.setAgentActCommAchv(BigDecimal.ZERO);
                repairSpec.setAgentDefCommAchv(BigDecimal.ZERO);
                repairSpec.setDeptCommAchv(BigDecimal.ZERO);
                repairSpec.setBuCommAchv(BigDecimal.ZERO);
                repairSpec.setBranchCommAchv(BigDecimal.ZERO);
                repairSpec.setCreateTime(productDetailModel.getStatisticsTime());
                repairSpec.setUpdateTime(new Date());
                repairSpecList.add(repairSpec);
            }
            if (CollectionUtils.isEmpty(repairSpecList)) {
                continue;
            }

            Map<Long, List<AchievementSpecDetailModel>> byAchievementCategoryIdMap = repairSpecList.stream().collect(Collectors.groupingBy(AchievementSpecDetailModel::getGroupById));

            for (AchievementCategoryDetailModel achievementCategoryDetailModel : achievementCategoryDetailModelList) {
                List<AchievementSpecDetailModel> achievementSpecDetailModels = byAchievementCategoryIdMap.get(achievementCategoryDetailModel.getAchievementCategoryId());
                if (CollectionUtils.isEmpty(achievementSpecDetailModels)) {
                    continue;
                }
                BigDecimal totalNetCash = achievementSpecDetailModels.stream().map(AchievementSpecDetailModel::getNetCash)
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

                AchievementCategoryDetailModel repairCategory = new AchievementCategoryDetailModel();
                BeanUtils.copyProperties(achievementCategoryDetailModel, repairCategory);
                repairCategory.setGroupById(achievementCategoryDetailModel.getAchievementId());
                repairCategory.setId(null);
                repairCategory.setAchievementId(null);
                repairCategory.setAchievementCategoryId(IdUtil.getSnowflakeNextId());

                repairCategory.setStandardPrice(BigDecimal.ZERO);
                repairCategory.setPayableAmount(BigDecimal.ZERO);
                repairCategory.setPaidAmount(BigDecimal.ZERO);
                repairCategory.setFirstYearQuote(BigDecimal.ZERO);
                repairCategory.setFirstYearIncome(BigDecimal.ZERO);
                repairCategory.setRenewalQuote(BigDecimal.ZERO);
                repairCategory.setRenewalIncome(BigDecimal.ZERO);
                repairCategory.setAgentCommAchv(BigDecimal.ZERO);
                repairCategory.setAgentActCommAchv(BigDecimal.ZERO);
                repairCategory.setAgentDefCommAchv(BigDecimal.ZERO);
                repairCategory.setDeptCommAchv(BigDecimal.ZERO);
                repairCategory.setBuCommAchv(BigDecimal.ZERO);
                repairCategory.setBranchCommAchv(BigDecimal.ZERO);
                repairCategory.setNetCash(totalNetCash);
                repairCategory.setCreateTime(productDetailModel.getStatisticsTime());
                repairCategory.setUpdateTime(new Date());
                repairCategoryList.add(repairCategory);
            }
            List<Long> refundIdList = new ArrayList<>();
            Map<Long, List<AchievementCategoryDetailModel>> byAchievementIdMap = repairCategoryList.stream().collect(Collectors.groupingBy(AchievementCategoryDetailModel::getGroupById));
            for (AchievementProductDetailModel achievementProductDetailModel : notCalculateAllProductDetailList) {
                List<AchievementCategoryDetailModel> achievementCategoryDetailModels = byAchievementIdMap.get(achievementProductDetailModel.getAchievementId());
                if (CollectionUtils.isEmpty(achievementCategoryDetailModels)) {
                    continue;
                }
                BigDecimal totalNetCash = achievementCategoryDetailModels.stream().map(AchievementCategoryDetailModel::getNetCash)
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

                AchievementProductDetailModel repairProduct = new AchievementProductDetailModel();
                BeanUtils.copyProperties(achievementProductDetailModel, repairProduct);
                repairProduct.setId(null);
                repairProduct.setAchievementId(IdUtil.getSnowflakeNextId());

                repairProduct.setStandardPrice(BigDecimal.ZERO);
                repairProduct.setPayableAmount(BigDecimal.ZERO);
                repairProduct.setPaidAmount(BigDecimal.ZERO);
                repairProduct.setFirstYearQuote(BigDecimal.ZERO);
                repairProduct.setFirstYearRevenue(BigDecimal.ZERO);
                repairProduct.setRenewalQuote(BigDecimal.ZERO);
                repairProduct.setRenewalRevenue(BigDecimal.ZERO);
                repairProduct.setAgentCommissionAchievement(BigDecimal.ZERO);
                repairProduct.setAgentActualCommission(BigDecimal.ZERO);
                repairProduct.setAgentDeferredCommission(BigDecimal.ZERO);
                repairProduct.setDeptCommission(BigDecimal.ZERO);
                repairProduct.setDivCommission(BigDecimal.ZERO);
                repairProduct.setBranchCommission(BigDecimal.ZERO);
                repairProduct.setNetCash(totalNetCash);
                //补完业绩后设置以按100%核算
                repairProduct.setCalculateAll(NumberConstants.INTEGER_VALUE_0);
                repairProduct.setCreateTime(productDetailModel.getStatisticsTime());
                repairProduct.setUpdateTime(new Date());
                repairProduct.setStatisticsTime(productDetailModel.getStatisticsTime());

                BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(repairProduct.getStatisticsTime());
                if (monthInfo != null) {
                    repairProduct.setBusinessMonth(monthInfo.getMonth());
                    repairProduct.setBusinessMonthId(monthInfo.getMonthId());
                } else {
                    throw new RuntimeException(String.format("根据流水创建时间未找到商务月,创建时间:%s ,订单id:%s", DateUtil.format(repairProduct.getStatisticsTime(), DatePattern.NORM_DATETIME_PATTERN), repairProduct.getOrderId()));
                }
            //查询该订单明细是否存在退转款
            String orderProductId = achievementProductDetailModel.getOrderProductId();
            List<String> orderProductIdList = new ArrayList<>();
            orderProductIdList.add(orderProductId);
            List<AchievementProductDetailModel> existList = achievementProductDetailRepository.selectByOrderIdAndOrderProductId(achievementProductDetailModel.getOrderId(), orderProductIdList);
            //支付完成才会有退款
            List<AchievementProductDetailModel> refundList = existList.stream().filter(exist -> StatusEnum.REFUND.getCode().equals(exist.getStatus())).collect(Collectors.toList());
            repairProductList.add(repairProduct);
            //退款后补广告通
            if(!CollectionUtils.isEmpty(refundList)){
                //存在退款 找到后补的广告通
                List<AchievementProductDetailModel> repairList = existList.stream().filter(exist -> StatusEnum.VALID.getCode().equals(exist.getStatus())).collect(Collectors.toList());

                Map<Integer, List<AchievementProductDetailModel>> byCyclCountMap = repairList.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getPayCycleCount));
                for (Map.Entry<Integer, List<AchievementProductDetailModel>> cyclCountMap : byCyclCountMap.entrySet()) {

                    List<AchievementProductDetailModel> achRefundList = cyclCountMap.getValue();
                    if(CollectionUtils.isEmpty(achRefundList)){
                        continue;
                    }
                    //主单人退款
                    List<AchievementProductDetailModel> mainRefundList = achRefundList.stream().filter(ach -> ach.getMainSplitPerson().equals(MainSplitPersonEnum.MAIN.getCode())).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(mainRefundList)){
                        List<Long> mainRefundIdList = mainRefundList.stream().map(AchievementProductDetailModel::getId).collect(Collectors.toList());
                        refundIdList.addAll(mainRefundIdList);
                    }

                    //辅单人退款
                    List<AchievementProductDetailModel> assistantRefundList = achRefundList.stream().filter(ach -> ach.getMainSplitPerson().equals(MainSplitPersonEnum.ASSISTANT.getCode())).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(assistantRefundList)){
                        List<Long> assistantRefundIdList = assistantRefundList.stream().map(AchievementProductDetailModel::getId).collect(Collectors.toList());
                        refundIdList.addAll(assistantRefundIdList);
                    }

                }
            }
        }
        //奖后补的广告通后补状态置为0
        if(!CollectionUtils.isEmpty(refundIdList)){
            achievementProductDetailRepository.updateCalculateByIdList(refundIdList);
        }
        if(!CollectionUtils.isEmpty(repairProductList)){
            MqOrderPaymentInfoModel model = new MqOrderPaymentInfoModel();
            model.setTaskId(repairProductList.get(0).getOrderId());
            //匹配业绩流水id、业绩规格分类id
            achievementProductWrapper.relationId(repairSpecList, repairCategoryList, repairProductList, model);

            repairProductListAll.addAll(repairProductList);
            repairCategoryListAll.addAll(repairCategoryList);
            repairSpecListAll.addAll(repairSpecList);
        }

    }
        //批量入库
        achievementProductService.saveOrUpdateBatch(repairProductListAll);
        achievementCategoryService.saveOrUpdateBatch(repairCategoryListAll);
        achievementSpecService.saveOrUpdateBatch(repairSpecListAll);

        List<Long> orderIdList = repairProductListAll.stream().map(AchievementProductDetailModel::getOrderId).collect(Collectors.toList());
        //修改业绩核算状态已按100%核算
        achievementProductDetailRepository.updateByOrderIdAndProductId(orderIdList, null);
    }
}
