package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ProtectByCustomer implements Serializable {

    private String customerId;        // 客户ID
    private String customerName;      // 客户名称
    private int customerType;         // 客户类型
    private String salerId;           // 销售人员ID
    private String deptId;            // 部门ID
    private String subId;             // 子公司ID
    private String areaId;            // 区域ID
    private String provinceCode;       // 省份代码
    private String provinceName;       // 省份名称
    private String cityCode;           // 城市代码
    private String cityName;           // 城市名称
    private String districtCode;       // 区域代码
    private String districtName;       // 区域名称
    private String tagLostCust;        // 丢失客户标记
    private String tagLostTime;        // 丢失时间

}
