package com.xmd.achievement.util.enums;

import lombok.Getter;

public enum DateTimeFormatStyleEnum {

    //时间类型
    yyyy_MM_dd_HH_mm_ss("yyyy-MM-dd HH:mm:ss"),
    yyyyMMddHHmmss("yyyyMMddHHmmss"),
    yyyyMMdd("yyyyMMdd"),
    yyyy_MM("yyyy-MM"),
    yyyyMMddHHmmssSSS("yyyyMMddHHmmssSSS"),
    yyyyMMddTHHmmssXXX("yyyy-MM-dd'T'HH:mm:ssXXX"),
    yyMMddTHHmmssXXX("yyMMddHHmmss"),
    yyMMddTHHmmssSSS("yyMMddHHmmssSSS"),
    standard("EEE MMM dd HH:mm:ss zzz yyyy"),
    yyyyMMddTHHmmssSSSXXX("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

    @Getter
    private final String code;

    DateTimeFormatStyleEnum(String code) {
        this.code = code;
    }
}

