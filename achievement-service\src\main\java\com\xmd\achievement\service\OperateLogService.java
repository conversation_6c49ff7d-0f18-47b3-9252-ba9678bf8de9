package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.OperateLogModel;
import com.xmd.achievement.support.constant.enums.OperateTypeEnum;
import com.xmd.achievement.support.constant.enums.SourceSystemEnum;

import java.util.List;

/**
 * 操作日志服务接口
 */
public interface OperateLogService {
    /**
     * 保存操作日志（使用枚举类型，自动获取className，不指定来源系统）
     * @param beforeObj 操作前对象
     * @param afterObj 操作后对象
     * @param operationType 操作类型枚举
     * @return 保存结果
     */
    boolean save(Object beforeObj, Object afterObj, OperateTypeEnum operationType);

    boolean save(Object beforeObj, Object afterObj, OperateTypeEnum operationType,String remark);

    boolean save(Object beforeObj, Object afterObj, OperateTypeEnum operationType, SourceSystemEnum sourceSystem,String remark);

    boolean save(Class<?> clazz, String beforeJson, String afterJson, OperateTypeEnum operationType, SourceSystemEnum sourceSystem, String remark);
    
    /**
     * 批量保存操作日志
     * @param operateLogs 操作日志列表
     * @return 保存结果
     */
    boolean saveBatch(List<OperateLogModel> operateLogs);

    OperateLogModel createOperateLog(Object beforeObj, Object afterObj, OperateTypeEnum operationType, SourceSystemEnum sourceSystem,String remark);

    OperateLogModel createOperateLog(Object beforeObj, Object afterObj, OperateTypeEnum operationType);

    OperateLogModel createOperateLog(Class<?> clazz, String beforeJson, String afterJson, OperateTypeEnum operationType, SourceSystemEnum sourceSystem, String remark);
} 