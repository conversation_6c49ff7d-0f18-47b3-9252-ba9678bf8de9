package com.xmd.achievement.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xmd.achievement.dao.dto.SearchCustomerSaasDto;
import com.xmd.achievement.dao.entity.CustomerSaasModel;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ICustomerSaasRepository extends IService<CustomerSaasModel> {
    /**
     * 获取指定客户的最大流失时间churnDate
     * @param customerId 客户ID
     * @return 最大流失时间
     */
    Date getMaxChurnDateByCustomerId(String customerId);

    /**
     * 获取指定客户在指定日期之前的最大流失时间churnDate
     * @param customerId 客户ID
     * @param date 截止日期（order_create_time < date）
     * @return 最大流失时间
     */
    Date getMaxChurnDateByCustomerId(String customerId, Date date,Long orderId);


    int countOrderBetween(String customerId, Date start, Date end, Integer orderSource);

    List<SearchCustomerSaasDto> searchCustomerSaasCount(List<String> businessIds, String startDate, String endDate);
}
