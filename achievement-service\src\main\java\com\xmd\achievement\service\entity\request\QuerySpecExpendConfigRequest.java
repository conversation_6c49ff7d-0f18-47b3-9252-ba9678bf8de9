package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 规格分类查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/21/20:32
 * @since 1.0
 */
@Data
public class QuerySpecExpendConfigRequest implements Serializable {
    private static final long serialVersionUID = -3912864366457596156L;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "商品ID不能为空")
    private String productId;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "分类")
    private String specCategoryName;

    @Schema(description = "规格名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "规格")
    private String specName;
}
