package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailSelectModel;
import com.xmd.achievement.dao.mapper.AchievementSpecDetailSelectMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IAchievementSpecDetailSelectRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 业绩规格明细表(查询) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@Slf4j
public class AchievementSpecDetailSelectRepositoryImpl extends ServiceImpl<AchievementSpecDetailSelectMapper,AchievementSpecDetailSelectModel> implements IAchievementSpecDetailSelectRepository {

@Resource
private AchievementSpecDetailSelectMapper achievementSpecDetailSelectMapper;

    @Override
    public List<AchievementSpecDetailSelectModel> selectAchievementSpecListByCategoryIds(List<Long> achievementCategoryIdList) {
        LambdaQueryWrapper<AchievementSpecDetailSelectModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AchievementSpecDetailSelectModel::getAchievementCategoryId, achievementCategoryIdList);
        queryWrapper.eq(AchievementSpecDetailSelectModel::getDeleteFlag, 0);
        return achievementSpecDetailSelectMapper.selectList(queryWrapper);
    }

    @Override
    public void insertOrUpdate(List<AchievementSpecDetailSelectModel> models) {
        if (ObjectUtils.isNotEmpty(models)) {
            achievementSpecDetailSelectMapper.batchInsertOrUpdate(models);
        }
    }

}