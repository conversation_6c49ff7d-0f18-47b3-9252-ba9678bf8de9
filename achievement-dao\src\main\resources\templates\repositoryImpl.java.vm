package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${superServiceImplClassPackage};
import ${package.Service}.${table.serviceName};
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * $!{table.comment} 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Service
@Slf4j
public class ${table.serviceImplName} extends ServiceImpl<${table.mapperName},${entity}> implements ${table.serviceName} {
#set($param = $table.mapperName)
#set($aa = $param.substring(0,1).toLowerCase()+ ${param.substring(1)})

@Resource
private ${table.mapperName} $aa;

}