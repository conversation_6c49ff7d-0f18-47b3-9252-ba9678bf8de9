package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.OrganizationDailyReportModel;
import com.xmd.achievement.dao.entity.OrganizationMonthlyReportModel;
import com.xmd.achievement.dao.entity.ReportDownloadLogModel;
import com.xmd.achievement.dao.repository.IOrganizationDailyReportRepository;
import com.xmd.achievement.dao.repository.IOrganizationMonthlyReportRepository;
import com.xmd.achievement.dao.repository.IReportDownloadLogRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.service.IOrganizationReportService;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.entity.request.DailyReportQueryReq;
import com.xmd.achievement.service.entity.request.MonthlyReportQueryReq;
import com.xmd.achievement.service.entity.response.DailyReportResp;
import com.xmd.achievement.service.entity.response.MonthlyReportResp;
import com.xmd.achievement.support.constant.enums.DataPermitEnum;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.MarketCategoryTypeEnum;
import com.xmd.achievement.support.constant.enums.OrganizationTypeEnum;
import com.xmd.achievement.web.util.EasyExcelUtil;
import com.xmd.achievement.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xmd.achievement.util.constant.UtilConstant.REQUEST_SOURCE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrganizationReportServiceImpl implements IOrganizationReportService {

    @Autowired
    private IOrganizationDailyReportRepository organizationDailyReportRepository;

    @Autowired
    private IOrganizationMonthlyReportRepository organizationMonthlyReportRepository;

    @Autowired
    private IReportDownloadLogRepository reportDownloadLogRepository;

    @Resource
    private InnerService innerService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private IBusinessMonthService businessMonthService;


    private boolean permitCheck(UserLoginInfoDTO userInfo){
        OrgPathInfoDTO org = userInfo.getOrgPathInfoDTO();
        UserDataPermissionsDTO permit = userInfo.getUserDataPermissionsDTO();
        if (org==null||permit==null){
            return false;
        }
        DataPermitEnum permitEnum = DataPermitEnum.getDataPermitEnumByCode(permit.getDataPermissionsType());
        if (permitEnum==null) {return false;}
        switch (permitEnum){
            case ALL:return true;
            case COMPANY:
                return Objects.nonNull(org.getCompanyId());
            case DEPT:
                return Objects.nonNull(org.getDeptId());
            default:return false;
        }
    }


    @Override
    public Page<DailyReportResp> queryDailyReport(DailyReportQueryReq req) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = userInfo.getUserDataPermissionsDTO();
        if (DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return new Page<>();
        }
        if (!permitCheck(userInfo)){
            return new Page<>();
        }
        Integer type = req.getType();
        List<Long> organizationIds = req.getOrganizationIds();
        IPage<OrganizationDailyReportModel> page = organizationDailyReportRepository.lambdaQuery()
                .eq(Objects.nonNull(req.getType()),OrganizationDailyReportModel::getOrganizationType, req.getType())
                .in(CollUtil.isNotEmpty(organizationIds), OrganizationDailyReportModel::getOrganizationId, organizationIds)
                .eq(Objects.nonNull(req.getSystemId()), OrganizationDailyReportModel::getSystemId, req.getSystemId())
                .eq(Objects.nonNull(req.getMarketCategoryId()), OrganizationDailyReportModel::getMarketCategoryId, req.getMarketCategoryId())
                .between(Objects.nonNull(req.getStartDate()) && Objects.nonNull(req.getEndDate()), OrganizationDailyReportModel::getCurrentDayDate, req.getStartDate(), req.getEndDate())
                .eq(OrganizationDailyReportModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(OrganizationDailyReportModel::getBusinessMonth)
                .orderByAsc(OrganizationDailyReportModel::getOrganizationId)
                .page(req.toPage());
        List<OrganizationDailyReportModel> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new Page<>();
        }
        Map<Long, OrgInfoResp> orgInfoMap = new HashMap<>();
        //如果是事业部或者部门还要查上一级机构
        if (Objects.equals(OrganizationTypeEnum.DEPT.getCode(), type) || Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
            List<Long> orgIds = records.stream().map(OrganizationDailyReportModel::getOrganizationId).distinct().collect(Collectors.toList());
            List<OrgInfoResp> orgInfoResps = innerService.queryListOrg(orgIds);
            orgInfoMap = orgInfoResps.stream().collect(Collectors.toMap(OrgInfoResp::getOrgId, Function.identity()));
        }
        Map<Long, OrgInfoResp> finalOrgInfoMap = orgInfoMap;
        List<DailyReportResp> resultList = records.stream().map(model -> {
            DailyReportResp resp = BeanUtil.copyProperties(model, DailyReportResp.class);
            resp.setMarketCategory(MarketCategoryTypeEnum.getNameByType(Convert.toInt(resp.getMarketCategoryId())));
            resp.setReportDay(DateUtil.formatDate(model.getCurrentDayDate()));
            if (Objects.equals(OrganizationTypeEnum.DEPT.getCode(), type) || Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
                resp.setCompanyId(finalOrgInfoMap.getOrDefault(model.getOrganizationId(), new OrgInfoResp()).getParentId());
                resp.setCompanyName(finalOrgInfoMap.getOrDefault(model.getOrganizationId(), new OrgInfoResp()).getParentName());
            }
            return resp;
        }).collect(Collectors.toList());

        Page<DailyReportResp> respPage = BeanUtil.copyProperties(page, Page.class, "records");
        respPage.setRecords(resultList);
        return respPage;
    }

    @Override
    public Page<MonthlyReportResp> queryMonthlyReport(MonthlyReportQueryReq req) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = userInfo.getUserDataPermissionsDTO();
        if (DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return new Page<>();
        }
        if (!permitCheck(userInfo)){
            return new Page<>();
        }
        Integer type = req.getType();
        List<Long> organizationIds = req.getOrganizationIds();
        IPage<OrganizationMonthlyReportModel> page = organizationMonthlyReportRepository.lambdaQuery()
                .eq(Objects.nonNull(req.getType()),OrganizationMonthlyReportModel::getOrganizationType, req.getType())
                .in(CollUtil.isNotEmpty(organizationIds), OrganizationMonthlyReportModel::getOrganizationId, organizationIds)
                .eq(Objects.nonNull(req.getSystemId()), OrganizationMonthlyReportModel::getSystemId, req.getSystemId())
                .eq(Objects.nonNull(req.getMarketCategoryId()), OrganizationMonthlyReportModel::getMarketCategoryId, req.getMarketCategoryId())
                .between(StringUtils.isNotEmpty(req.getBusinessMonthStart()) && StringUtils.isNotEmpty(req.getBusinessMonthEnd()), OrganizationMonthlyReportModel::getBusinessMonth, req.getBusinessMonthStart(), req.getBusinessMonthEnd())
                .eq(OrganizationMonthlyReportModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(OrganizationMonthlyReportModel::getBusinessMonth)
                .orderByAsc(OrganizationMonthlyReportModel::getOrganizationId)
                .page(req.toPage());
        List<OrganizationMonthlyReportModel> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new Page<>();
        }
        Map<Long, OrgInfoResp> orgInfoMap = new HashMap<>();
        //如果是事业部或者部门还要查上一级机构
        if (Objects.equals(OrganizationTypeEnum.DEPT.getCode(), type) || Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
            List<Long> orgIds = records.stream().map(OrganizationMonthlyReportModel::getOrganizationId).distinct().collect(Collectors.toList());
            List<OrgInfoResp> orgInfoResps = innerService.queryListOrg(orgIds);
            orgInfoMap = orgInfoResps.stream().collect(Collectors.toMap(OrgInfoResp::getOrgId, Function.identity()));
        }
        Map<Long, OrgInfoResp> finalOrgInfoMap = orgInfoMap;
        List<MonthlyReportResp> resultList = records.stream().map(model -> {
            MonthlyReportResp resp = BeanUtil.copyProperties(model, MonthlyReportResp.class);
            resp.setAppointmentDate(DateUtil.formatDateTime(model.getAppointmentDate()));
            resp.setMarketCategory(MarketCategoryTypeEnum.getNameByType(Convert.toInt(resp.getMarketCategoryId())));
            if (Objects.equals(OrganizationTypeEnum.DEPT.getCode(), type) || Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
                resp.setCompanyId(finalOrgInfoMap.getOrDefault(model.getOrganizationId(), new OrgInfoResp()).getParentId());
                resp.setCompanyName(finalOrgInfoMap.getOrDefault(model.getOrganizationId(), new OrgInfoResp()).getParentName());
            }
            return resp;
        }).collect(Collectors.toList());

        Page<MonthlyReportResp> respPage = BeanUtil.copyProperties(page, Page.class, "records");
        respPage.setRecords(resultList);
        return respPage;
    }

    @Override
    public void exportDailyReport(DailyReportQueryReq req, HttpServletResponse response) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        if (!permitCheck(userInfo)){
            return;
        }
        //查询全部记录
        req.clearPageParam();
        Page<DailyReportResp> respPage = this.queryDailyReport(req);
        List<DailyReportResp> records = respPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }

        Integer type = req.getType();
        try {
            Set<String> excludeColumns = new HashSet<>();
            if (!Objects.equals(OrganizationTypeEnum.DEPT.getCode(), type) && !Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
                excludeColumns.add("companyId");
                excludeColumns.add("companyName");
            }
            EasyExcelUtil.dynamicExcludeExport(response, records, "机构日报", Boolean.TRUE, excludeColumns);
            saveReportDownloadLog(JSON.toJSONString(req));
        } catch (IOException e) {
            log.error("导出机构日报失败", e);
        }
    }

    @Override
    public void exportMonthlyReport(MonthlyReportQueryReq req, HttpServletResponse response) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        if (!permitCheck(userInfo)){
            return;
        }
        //查询全部记录
        req.clearPageParam();
        Page<MonthlyReportResp> respPage = this.queryMonthlyReport(req);
        List<MonthlyReportResp> records = respPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }

        Integer type = req.getType();
        try {
            Set<String> excludeColumns = new HashSet<>();
            if (!Objects.equals(OrganizationTypeEnum.DEPT.getCode(), type) && !Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
                excludeColumns.add("companyId");
                excludeColumns.add("companyName");
            }
            EasyExcelUtil.dynamicExcludeExport(response, records, "机构月报", Boolean.TRUE, excludeColumns);
            saveReportDownloadLog(JSON.toJSONString(req));
        } catch (IOException e) {
            log.error("导出机构月报失败", e);
        }
    }

    @Override
    public List<OrgInfoNodeResponse> getOrganizationRoleList() {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = userInfo.getUserDataPermissionsDTO();
        if (DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return Collections.emptyList();
        }
        List<OrgInfoNodeResponse> orgInfoNodeResps = innerService.queryKjOrgTree();
        if (CollUtil.isEmpty(orgInfoNodeResps)) {
            return Collections.emptyList();
        }

        if (DataPermitEnum.DEPT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return findThirdLevel(orgInfoNodeResps, userInfo.getOrgPathInfoDTO().getDeptId());
        } else if (DataPermitEnum.CAREER.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return findThirdLevel(orgInfoNodeResps, userInfo.getOrgPathInfoDTO().getCareerId());
        } else if (DataPermitEnum.COMPANY.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())
                || DataPermitEnum.SELF_AND_COMPANY.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return findSecondLevel(orgInfoNodeResps, userInfo.getOrgPathInfoDTO().getCompanyId());
        }

        //如果是全部则返回所有商务机构

        // 遍历第一层 (层级1)
        orgInfoNodeResps.forEach(level1 -> {
            // 过滤第二层不是商务机构的组织
            level1.getChildOrgInfoNodeRespList().removeIf(level2 -> level2.getCommerceFlag() == 0);
            // 过滤第三层不是商务机构的组织
            level1.getChildOrgInfoNodeRespList().forEach(level2 ->
                level2.getChildOrgInfoNodeRespList().removeIf(level3 -> level3.getCommerceFlag() == 0)
            );
        });

        return orgInfoNodeResps;
    }

    @Override
    public List<OrgFunctionResp> getOrgFunctionList() {
        return innerService.queryOrgFunctionList(REQUEST_SOURCE);
    }


    public List<OrgInfoNodeResponse> findSecondLevel(List<OrgInfoNodeResponse> orgInfoNodeRespList, Long orgId) {
        List<OrgInfoNodeResponse> result = new ArrayList<>();
        if (CollUtil.isEmpty(orgInfoNodeRespList) || orgId == null) {
            return result;
        }
        // 遍历第一层 (层级1)
        for (OrgInfoNodeResponse level1 : orgInfoNodeRespList) {
            // 遍历第二层 (层级2)
            for (OrgInfoNodeResponse level2 : level1.getChildOrgInfoNodeRespList()) {
                if (orgId.equals(level2.getOrgId())) {
                    result.add(level2);
                }
            }
        }

        // 过滤第三层不是商务机构的组织
        if (CollUtil.isNotEmpty(result)) {
            OrgInfoNodeResponse orgInfoNodeResponse = result.get(0);
            orgInfoNodeResponse.getChildOrgInfoNodeRespList().removeIf(level3 -> level3.getCommerceFlag() == 0);
        }
        return result;
    }

    public List<OrgInfoNodeResponse> findThirdLevel(List<OrgInfoNodeResponse> orgInfoNodeRespList, Long orgId) {
        List<OrgInfoNodeResponse> result = new ArrayList<>();
        if (CollUtil.isEmpty(orgInfoNodeRespList) || orgId == null) {
            return result;
        }
        // 遍历第一层 (层级1)
        for (OrgInfoNodeResponse level1 : orgInfoNodeRespList) {
            // 遍历第二层 (层级2)
            for (OrgInfoNodeResponse level2 : level1.getChildOrgInfoNodeRespList()) {
                // 遍历第三层 (层级3)
                for (OrgInfoNodeResponse level3 : level2.getChildOrgInfoNodeRespList()) {
                    if (orgId.equals(level3.getOrgId())) {
                        result.add(level3);
                    }
                }
            }
        }
        return result;
    }

    public void saveReportDownloadLog(String req) {
        ReportDownloadLogModel entity = new ReportDownloadLogModel();
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        entity.setOperatorId(userInfo.getUserId());
        entity.setOperatorName(userInfo.getName());
        entity.setOperatorTime(new Date());
        entity.setOperatorParameter(req);
        entity.setCreateTime(new Date());
        reportDownloadLogRepository.save(entity);
    }

    /**
     * 批量查询机构和月份的月报
     * @param orgIds 机构ID列表
     * @param months 月份字符串列表
     * @return 机构月报实体集合
     */
    @Override
    public List<OrganizationMonthlyReportModel> searchMonthReport(List<Long> orgIds, List<String> months) {
        if (CollUtil.isEmpty(orgIds) || CollUtil.isEmpty(months)) {
            return Collections.emptyList();
        }
        return organizationMonthlyReportRepository.lambdaQuery()
                .in(OrganizationMonthlyReportModel::getOrganizationId, orgIds)
                .in(OrganizationMonthlyReportModel::getBusinessMonth, months)
                .eq(OrganizationMonthlyReportModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
    }

}
