package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.OrganizationDailyReportModel;
import com.xmd.achievement.dao.entity.OrganizationMonthlyReportModel;
import com.xmd.achievement.dao.entity.ReportDownloadLogModel;
import com.xmd.achievement.dao.repository.IOrganizationDailyReportRepository;
import com.xmd.achievement.dao.repository.IOrganizationMonthlyReportRepository;
import com.xmd.achievement.dao.repository.IReportDownloadLogRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.service.IExcelImportExceptionService;
import com.xmd.achievement.service.IOrganizationReportService;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.entity.dto.ImportExcelExceptionDTO;
import com.xmd.achievement.service.entity.request.DailyReportQueryReq;
import com.xmd.achievement.service.entity.request.MonthlyReportQueryReq;
import com.xmd.achievement.service.entity.response.DailyReportResp;
import com.xmd.achievement.service.entity.response.MonthlyReportImportResultResponse;
import com.xmd.achievement.service.entity.response.MonthlyReportResp;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.web.util.EasyExcelUtil;
import com.xmd.achievement.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static com.xmd.achievement.support.constant.enums.OrganizationTypeEnum.BRANCH_COMPANY;
import static com.xmd.achievement.support.constant.enums.OrganizationTypeEnum.DEPT;
import static com.xmd.achievement.util.constant.UtilConstant.REQUEST_SOURCE;
import static com.xmd.achievement.util.date.DateUtils.*;
import static com.xmd.achievement.web.util.ExcelUtil.*;
import static com.xmd.achievement.web.util.ExcelUtil.getStringCellValue;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrganizationReportServiceImpl implements IOrganizationReportService {

    @Autowired
    private IOrganizationDailyReportRepository organizationDailyReportRepository;

    @Autowired
    private IOrganizationMonthlyReportRepository organizationMonthlyReportRepository;

    @Autowired
    private IReportDownloadLogRepository reportDownloadLogRepository;

    @Resource
    private InnerService innerService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private IBusinessMonthService businessMonthService;
    @Resource
    private IExcelImportExceptionService excelImportExceptionService;


    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final String ORG_COMPANY = "分司";

    private static final String ORG_DEPT = "部门";

    private boolean permitCheck(UserLoginInfoDTO userInfo){
        OrgPathInfoDTO org = userInfo.getOrgPathInfoDTO();
        UserDataPermissionsDTO permit = userInfo.getUserDataPermissionsDTO();
        if (org==null||permit==null){
            return false;
        }
        DataPermitEnum permitEnum = DataPermitEnum.getDataPermitEnumByCode(permit.getDataPermissionsType());
        if (permitEnum==null) {return false;}
        switch (permitEnum){
            case ALL:return true;
            case COMPANY:
                return Objects.nonNull(org.getCompanyId());
            case DEPT:
                return Objects.nonNull(org.getDeptId());
            default:return false;
        }
    }


    @Override
    public Page<DailyReportResp> queryDailyReport(DailyReportQueryReq req) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = userInfo.getUserDataPermissionsDTO();
        if (DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return new Page<>();
        }
        if (!permitCheck(userInfo)){
            return new Page<>();
        }
        Integer type = req.getType();
        List<Long> organizationIds = req.getOrganizationIds();
        IPage<OrganizationDailyReportModel> page = organizationDailyReportRepository.lambdaQuery()
                .eq(Objects.nonNull(req.getType()),OrganizationDailyReportModel::getOrganizationType, req.getType())
                .in(CollUtil.isNotEmpty(organizationIds), OrganizationDailyReportModel::getOrganizationId, organizationIds)
                .eq(Objects.nonNull(req.getSystemId()), OrganizationDailyReportModel::getSystemId, req.getSystemId())
                .eq(Objects.nonNull(req.getMarketCategoryId()), OrganizationDailyReportModel::getMarketCategoryId, req.getMarketCategoryId())
                .between(Objects.nonNull(req.getStartDate()) && Objects.nonNull(req.getEndDate()), OrganizationDailyReportModel::getCurrentDayDate, req.getStartDate(), req.getEndDate())
                .eq(OrganizationDailyReportModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(OrganizationDailyReportModel::getBusinessMonth)
                .orderByAsc(OrganizationDailyReportModel::getOrganizationId)
                .page(req.toPage());
        List<OrganizationDailyReportModel> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new Page<>();
        }
        Map<Long, OrgInfoResp> orgInfoMap = new HashMap<>();
        //如果是事业部或者部门还要查上一级机构
        if (Objects.equals(DEPT.getCode(), type) || Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
            List<Long> orgIds = records.stream().map(OrganizationDailyReportModel::getOrganizationId).distinct().collect(Collectors.toList());
            List<OrgInfoResp> orgInfoResps = innerService.queryListOrg(orgIds);
            orgInfoMap = orgInfoResps.stream().collect(Collectors.toMap(OrgInfoResp::getOrgId, Function.identity()));
        }
        Map<Long, OrgInfoResp> finalOrgInfoMap = orgInfoMap;
        List<DailyReportResp> resultList = records.stream().map(model -> {
            DailyReportResp resp = BeanUtil.copyProperties(model, DailyReportResp.class);
            resp.setMarketCategory(MarketCategoryTypeEnum.getNameByType(Convert.toInt(resp.getMarketCategoryId())));
            resp.setReportDay(DateUtil.formatDate(model.getCurrentDayDate()));
            if (Objects.equals(DEPT.getCode(), type) || Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
                resp.setCompanyId(finalOrgInfoMap.getOrDefault(model.getOrganizationId(), new OrgInfoResp()).getParentId());
                resp.setCompanyName(finalOrgInfoMap.getOrDefault(model.getOrganizationId(), new OrgInfoResp()).getParentName());
            }
            return resp;
        }).collect(Collectors.toList());

        Page<DailyReportResp> respPage = BeanUtil.copyProperties(page, Page.class, "records");
        respPage.setRecords(resultList);
        return respPage;
    }

    @Override
    public Page<MonthlyReportResp> queryMonthlyReport(MonthlyReportQueryReq req) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = userInfo.getUserDataPermissionsDTO();
        if (DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return new Page<>();
        }
        if (!permitCheck(userInfo)){
            return new Page<>();
        }
        Integer type = req.getType();
        List<Long> organizationIds = req.getOrganizationIds();
        IPage<OrganizationMonthlyReportModel> page = organizationMonthlyReportRepository.lambdaQuery()
                .eq(Objects.nonNull(req.getType()),OrganizationMonthlyReportModel::getOrganizationType, req.getType())
                .in(CollUtil.isNotEmpty(organizationIds), OrganizationMonthlyReportModel::getOrganizationId, organizationIds)
                .eq(Objects.nonNull(req.getSystemId()), OrganizationMonthlyReportModel::getSystemId, req.getSystemId())
                .eq(Objects.nonNull(req.getMarketCategoryId()), OrganizationMonthlyReportModel::getMarketCategoryId, req.getMarketCategoryId())
                .between(StringUtils.isNotEmpty(req.getBusinessMonthStart()) && StringUtils.isNotEmpty(req.getBusinessMonthEnd()), OrganizationMonthlyReportModel::getBusinessMonth, req.getBusinessMonthStart(), req.getBusinessMonthEnd())
                .eq(OrganizationMonthlyReportModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(OrganizationMonthlyReportModel::getBusinessMonth)
                .orderByAsc(OrganizationMonthlyReportModel::getOrganizationId)
                .page(req.toPage());
        List<OrganizationMonthlyReportModel> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new Page<>();
        }
        Map<Long, OrgInfoResp> orgInfoMap = new HashMap<>();
        //如果是事业部或者部门还要查上一级机构
        if (Objects.equals(DEPT.getCode(), type) || Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
            List<Long> orgIds = records.stream().map(OrganizationMonthlyReportModel::getOrganizationId).distinct().collect(Collectors.toList());
            List<OrgInfoResp> orgInfoResps = innerService.queryListOrg(orgIds);
            orgInfoMap = orgInfoResps.stream().collect(Collectors.toMap(OrgInfoResp::getOrgId, Function.identity()));
        }
        Map<Long, OrgInfoResp> finalOrgInfoMap = orgInfoMap;
        List<MonthlyReportResp> resultList = records.stream().map(model -> {
            MonthlyReportResp resp = BeanUtil.copyProperties(model, MonthlyReportResp.class);
            resp.setAppointmentDate(DateUtil.formatDateTime(model.getAppointmentDate()));
            resp.setMarketCategory(MarketCategoryTypeEnum.getNameByType(Convert.toInt(resp.getMarketCategoryId())));
            if (Objects.equals(DEPT.getCode(), type) || Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
                resp.setCompanyId(finalOrgInfoMap.getOrDefault(model.getOrganizationId(), new OrgInfoResp()).getParentId());
                resp.setCompanyName(finalOrgInfoMap.getOrDefault(model.getOrganizationId(), new OrgInfoResp()).getParentName());
            }
            return resp;
        }).collect(Collectors.toList());

        Page<MonthlyReportResp> respPage = BeanUtil.copyProperties(page, Page.class, "records");
        respPage.setRecords(resultList);
        return respPage;
    }

    @Override
    public void exportDailyReport(DailyReportQueryReq req, HttpServletResponse response) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        if (!permitCheck(userInfo)){
            return;
        }
        //查询全部记录
        req.clearPageParam();
        Page<DailyReportResp> respPage = this.queryDailyReport(req);
        List<DailyReportResp> records = respPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }

        Integer type = req.getType();
        try {
            Set<String> excludeColumns = new HashSet<>();
            if (!Objects.equals(DEPT.getCode(), type) && !Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
                excludeColumns.add("companyId");
                excludeColumns.add("companyName");
            }
            EasyExcelUtil.dynamicExcludeExport(response, records, "机构日报", Boolean.TRUE, excludeColumns);
            saveReportDownloadLog(JSON.toJSONString(req));
        } catch (IOException e) {
            log.error("导出机构日报失败", e);
        }
    }

    @Override
    public void exportMonthlyReport(MonthlyReportQueryReq req, HttpServletResponse response) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        if (!permitCheck(userInfo)){
            return;
        }
        //查询全部记录
        req.clearPageParam();
        Page<MonthlyReportResp> respPage = this.queryMonthlyReport(req);
        List<MonthlyReportResp> records = respPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }

        Integer type = req.getType();
        try {
            Set<String> excludeColumns = new HashSet<>();
            if (!Objects.equals(DEPT.getCode(), type) && !Objects.equals(OrganizationTypeEnum.DIVISION.getCode(), type)) {
                excludeColumns.add("companyId");
                excludeColumns.add("companyName");
            }
            EasyExcelUtil.dynamicExcludeExport(response, records, "机构月报", Boolean.TRUE, excludeColumns);
            saveReportDownloadLog(JSON.toJSONString(req));
        } catch (IOException e) {
            log.error("导出机构月报失败", e);
        }
    }

    @Override
    public List<OrgInfoNodeResponse> getOrganizationRoleList() {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = userInfo.getUserDataPermissionsDTO();
        if (DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return Collections.emptyList();
        }
        List<OrgInfoNodeResponse> orgInfoNodeResps = innerService.queryKjOrgTree();
        if (CollUtil.isEmpty(orgInfoNodeResps)) {
            return Collections.emptyList();
        }

        if (DataPermitEnum.DEPT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return findThirdLevel(orgInfoNodeResps, userInfo.getOrgPathInfoDTO().getDeptId());
        } else if (DataPermitEnum.CAREER.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return findThirdLevel(orgInfoNodeResps, userInfo.getOrgPathInfoDTO().getCareerId());
        } else if (DataPermitEnum.COMPANY.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())
                || DataPermitEnum.SELF_AND_COMPANY.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return findSecondLevel(orgInfoNodeResps, userInfo.getOrgPathInfoDTO().getCompanyId());
        }

        //如果是全部则返回所有商务机构

        // 遍历第一层 (层级1)
        orgInfoNodeResps.forEach(level1 -> {
            // 过滤第二层不是商务机构的组织
            level1.getChildOrgInfoNodeRespList().removeIf(level2 -> level2.getCommerceFlag() == 0);
            // 过滤第三层不是商务机构的组织
            level1.getChildOrgInfoNodeRespList().forEach(level2 ->
                level2.getChildOrgInfoNodeRespList().removeIf(level3 -> level3.getCommerceFlag() == 0)
            );
        });

        return orgInfoNodeResps;
    }

    @Override
    public List<OrgFunctionResp> getOrgFunctionList() {
        return innerService.queryOrgFunctionList(REQUEST_SOURCE);
    }


    public List<OrgInfoNodeResponse> findSecondLevel(List<OrgInfoNodeResponse> orgInfoNodeRespList, Long orgId) {
        List<OrgInfoNodeResponse> result = new ArrayList<>();
        if (CollUtil.isEmpty(orgInfoNodeRespList) || orgId == null) {
            return result;
        }
        // 遍历第一层 (层级1)
        for (OrgInfoNodeResponse level1 : orgInfoNodeRespList) {
            // 遍历第二层 (层级2)
            for (OrgInfoNodeResponse level2 : level1.getChildOrgInfoNodeRespList()) {
                if (orgId.equals(level2.getOrgId())) {
                    result.add(level2);
                }
            }
        }

        // 过滤第三层不是商务机构的组织
        if (CollUtil.isNotEmpty(result)) {
            OrgInfoNodeResponse orgInfoNodeResponse = result.get(0);
            orgInfoNodeResponse.getChildOrgInfoNodeRespList().removeIf(level3 -> level3.getCommerceFlag() == 0);
        }
        return result;
    }

    public List<OrgInfoNodeResponse> findThirdLevel(List<OrgInfoNodeResponse> orgInfoNodeRespList, Long orgId) {
        List<OrgInfoNodeResponse> result = new ArrayList<>();
        if (CollUtil.isEmpty(orgInfoNodeRespList) || orgId == null) {
            return result;
        }
        // 遍历第一层 (层级1)
        for (OrgInfoNodeResponse level1 : orgInfoNodeRespList) {
            // 遍历第二层 (层级2)
            for (OrgInfoNodeResponse level2 : level1.getChildOrgInfoNodeRespList()) {
                // 遍历第三层 (层级3)
                for (OrgInfoNodeResponse level3 : level2.getChildOrgInfoNodeRespList()) {
                    if (orgId.equals(level3.getOrgId())) {
                        result.add(level3);
                    }
                }
            }
        }
        return result;
    }

    public void saveReportDownloadLog(String req) {
        ReportDownloadLogModel entity = new ReportDownloadLogModel();
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        entity.setOperatorId(userInfo.getUserId());
        entity.setOperatorName(userInfo.getName());
        entity.setOperatorTime(new Date());
        entity.setOperatorParameter(req);
        entity.setCreateTime(new Date());
        reportDownloadLogRepository.save(entity);
    }

    /**
     * 批量查询机构和月份的月报
     * @param orgIds 机构ID列表
     * @param months 月份字符串列表
     * @return 机构月报实体集合
     */
    @Override
    public List<OrganizationMonthlyReportModel> searchMonthReport(List<Long> orgIds, List<String> months) {
        if (CollUtil.isEmpty(orgIds) || CollUtil.isEmpty(months)) {
            return Collections.emptyList();
        }
        return organizationMonthlyReportRepository.lambdaQuery()
                .in(OrganizationMonthlyReportModel::getOrganizationId, orgIds)
                .in(OrganizationMonthlyReportModel::getBusinessMonth, months)
                .eq(OrganizationMonthlyReportModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
    }

    @Transactional
    @Override
    public MonthlyReportImportResultResponse excelImport(MultipartFile file) {
        MonthlyReportImportResultResponse response = new MonthlyReportImportResultResponse();
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        int totalCount = 0;
        int successCount = 0;
        List<ImportExcelExceptionDTO> excelExceptionDTOS = new ArrayList<>();
        List<OrganizationMonthlyReportModel> result = Lists.newArrayList();
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 读取第一个 sheet
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();
        // 跳过标题行
        rowIterator.next();
        int rowNum = 0;
        while (rowIterator.hasNext()) {
            rowNum++;
            totalCount++;
            Row row = rowIterator.next();
            if (isRowEmpty(row)) {
                continue;
            }
            String businessMonth = getStringCellValue(row.getCell(0));
            Integer orgId = getIntegerCellValue(row.getCell(1));
            Integer leaderId = getIntegerCellValue(row.getCell(2));
            String date = getStringCellValue(row.getCell(3));
            BigDecimal monthSignAmount = getBigDecimalFromCell(row.getCell(4));
            BigDecimal monthNetCash = getBigDecimalFromCell(row.getCell(5));
            BigDecimal halfMonthNetCash = getBigDecimalFromCell(row.getCell(6));
            String orgType = getStringCellValue(row.getCell(7));
            List<String> errors = validate(businessMonth, orgId, leaderId, date, monthSignAmount, monthNetCash, halfMonthNetCash, orgType);
            // 机构id商务月存在数据不能导入
            OrganizationMonthlyReportModel organizationMonthlyReportModel = organizationMonthlyReportRepository.selectByMonthAndOrgId(businessMonth, Long.valueOf(orgId));
            if (ObjectUtil.isNotEmpty(organizationMonthlyReportModel)) {
                errors.add("机构id商务月存在数据不能导入");
            }
            if (!errors.isEmpty()) {
                // 记录错误信息，并且执行下一条
                excelExceptionDTOS.add(new ImportExcelExceptionDTO("当前行数：" + rowNum, errors.toString()));
                continue;
            }
            Long businessMonthId = businessMonthService.getMonthInfoByBusinessMonth(businessMonth);
            if (ObjectUtil.isEmpty(businessMonthId)) {
                // 记录错误信息，并且执行下一条
                errors.add(businessMonth + "商务月查询异常");
                excelExceptionDTOS.add(new ImportExcelExceptionDTO("当前行数：" + rowNum, errors.toString()));
                continue;
            }
            OrganizationMonthlyReportModel model = new OrganizationMonthlyReportModel();
            model.setBusinessMonth(businessMonth);
            model.setBusinessMonthId(businessMonthId);
            model.setCurrentDayMonth(businessMonth);
            model.setOrganizationId(Long.valueOf(orgId));
            if (ORG_COMPANY.equals(orgType)) {
                model.setOrganizationType(BRANCH_COMPANY.getCode());
            } else {
                model.setOrganizationType(DEPT.getCode());
            }
            if (ObjectUtil.isNotEmpty(leaderId)) {
                model.setOrganizationLeaderId(Long.valueOf(leaderId));
            }
            if (!isBlank(date)) {
                model.setAppointmentDate(Date.from(LocalDate.parse(date.trim(), DATE_FORMAT).atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }
            model.setMonthlySigningAmount(monthSignAmount);
            model.setMonthlyNetCash(monthNetCash);
            model.setMonthlyHalfNetCash(halfMonthNetCash);
            String orgName = getStringCellValue(row.getCell(8));
            if (ObjectUtil.isNotEmpty(orgName)) {
                // 填了orgName，取填的值
                model.setOrganizationName(orgName);
            } else {
                // 没填需要查询最新的
                String preMonth = getPreviousOrTargetMonth(new Date());
                OrgBusinessResponse orgBusiness = innerService.getOrgBusiness(Long.valueOf(orgId), preMonth, REQUEST_SOURCE);
                if (ObjectUtil.isNotEmpty(orgBusiness)) {
                    model.setOrganizationName(orgBusiness.getShortName());
                }
            }
            //下面这些字段不做校验
            model.setOrganizationLeaderName(getStringCellValue(row.getCell(9)));
            model.setSystemCategory(getStringCellValue(row.getCell(10)));
            model.setMarketCategory(getStringCellValue(row.getCell(11)));
            model.setExaminationDeptCount(getBigDecimalFromCell(row.getCell(12)));
            model.setDeptCount(getBigDecimalFromCell(row.getCell(13)));
            // 商务代表数量
            model.setBusinessRepresentativeCount(getLongCellValue(row.getCell(14)));
            // SaaS产品净现金
            model.setSaasNetCash(getBigDecimalFromCell(row.getCell(15)));
            model.setBasicTask(getBigDecimalFromCell(row.getCell(16)));
            model.setTaskCompletionRate(getBigDecimalFromCell(row.getCell(17)));
            model.setTotalSignPersonCount(getLongCellValue(row.getCell(18)));
            model.setSignRate(getBigDecimalFromCell(row.getCell(19)));
            model.setFormalBusinessRepresentativeCount(getLongCellValue(row.getCell(20)));
            model.setNetCashPerEmployee(getBigDecimalFromCell(row.getCell(21)));
            // 非续费客户数
            model.setNonRenewalCustomerCount(getLongCellValue(row.getCell(22)));
            // 月网站净现金
            model.setMonthlyWebsiteNetCash(getBigDecimalFromCell(row.getCell(23)));
            // 月新客户数
            model.setMonthlyNewCustomerCount(getLongCellValue(row.getCell(24)));
            // 月网站新客户数
            model.setMonthlyWebsiteNewCustomerCount(getLongCellValue(row.getCell(25)));
            // 部门提成业绩
            model.setDeptCommission(getBigDecimalFromCell(row.getCell(26)));
            // 分公司提成业绩
            model.setBranchCommission(getBigDecimalFromCell(row.getCell(27)));
            // 业绩段
            model.setAchievementSegment(getStringCellValue(row.getCell(28)));
            // 月老客户数
            model.setMonthlyOldCustomerCount(getLongCellValue(row.getCell(29)));
            model.setCreateUserId(userInfo.getUserId());
            model.setCreateUserName(userInfo.getName());
            model.setUpdateUserId(userInfo.getUserId());
            model.setUpdateUserName(userInfo.getName());
            result.add(model);
            successCount++;
        }
        try {
            workbook.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        organizationMonthlyReportRepository.saveBatch(result);
        int failCount = totalCount - successCount;
        //记录错误日志
        excelImportExceptionService.saveExcelException(excelExceptionDTOS, ExcelImportExceptionEnum.MONTH_REPORT);
        response.setTotalCount(totalCount);
        response.setSuccessCount(successCount);
        response.setFailedCount(failCount);
        response.setCode(ExcelImportExceptionEnum.MONTH_REPORT.getCode());
        return response;
    }

    public List<String> validate(String businessMonth, Integer orgId, Integer leaderId, String date, BigDecimal monthSignAmount, BigDecimal monthNetCash, BigDecimal halfMonthNetCash, String orgType) {
        List<String> errors = new ArrayList<>();

        // 商务月 必填
        if (businessMonth == null) {
            errors.add("商务月不能为空");
        }

        // 机构ID 必填且为正整数
        if (orgId == null || orgId <= 0) {
            errors.add("机构ID必须为大于0的整数");
        }

        // 机构领导ID 非必填，但若填写必须为正整数
        if (leaderId != null && leaderId <= 0) {
            errors.add("机构领导ID若填写，必须为大于0的整数");
        }

        // 4. 任职本机构日期：非必填，格式 yyyy-MM-dd
        if (!isBlank(date)) {
            try {
                LocalDate.parse(date.trim(), DATE_FORMAT);
            } catch (DateTimeParseException e) {
                errors.add("任职本机构日期格式错误，应为 yyyy-MM-dd，例如：2025-05-05");
            }
        }

        // 月签单金额 非必填，但若填写必须 > 0
        if (monthSignAmount != null && monthSignAmount.compareTo(BigDecimal.ZERO) <= 0) {
            errors.add("月签单金额必须大于0");
        }

        // 月净现金 必填且 > 0
        if (monthNetCash == null || monthNetCash.compareTo(BigDecimal.ZERO) <= 0) {
            errors.add("月净现金必须大于0");
        }

        // 月半净现金 非必填，但若填写必须 > 0
        if (halfMonthNetCash != null && halfMonthNetCash.compareTo(BigDecimal.ZERO) <= 0) {
            errors.add("月半净现金必须大于0");
        }
        // 商务月 必填
        if (orgType == null) {
            errors.add("机构类型不能为空");
        }
        return errors;
    }

}
