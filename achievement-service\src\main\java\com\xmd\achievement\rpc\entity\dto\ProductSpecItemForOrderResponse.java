package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ProductSpecItemForOrderResponse {

    @Schema(description = "规格计费项ID")
    private Long specBillingItemId;

    @Schema(description = "规格ID")
    private Long specId;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "计费项名称")
    private String billingItemName;

    @Schema(description = "计费项类型")
    private Integer billingItemType;

    @Schema(description = "计费单位")
    private String billingUnit;

    @Schema(description = "最大购买数量")
    private Integer maxPurchase;

    @Schema(description = "最小购买数量")
    private Integer minPurchase;

    @Schema(description = "步长")
    private Integer stepSize;

    @Schema(description = "另购最大购买数量")
    private Integer additionalMaxPurchase;

    @Schema(description = "另购最小购买数量")
    private Integer additionalMinPurchase;

    @Schema(description = "另购步长")
    private Integer additionalStepSize;

    @Schema(description = "计费项规则")
    private Integer billingItemRule;
}
