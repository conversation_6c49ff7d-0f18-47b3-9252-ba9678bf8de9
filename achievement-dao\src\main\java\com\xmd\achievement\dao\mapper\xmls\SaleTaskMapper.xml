<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.SaleTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.SaleTaskModel">
        <id column="id" property="id" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="org_id" property="orgId" />
        <result column="org_name" property="orgName" />
        <result column="business_month" property="businessMonth" />
        <result column="market_classification" property="marketClassification" />
        <result column="basic_task" property="basicTask" />
        <result column="branch_office_total" property="branchOfficeTotal" />
        <result column="department_total" property="departmentTotal" />
        <result column="business_unit_total" property="businessUnitTotal" />
        <result column="task_status" property="taskStatus" />
        <result column="parent_id" property="parentId" />
        <result column="task_id" property="taskId" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, delete_flag, org_id, org_name, business_month, market_classification, basic_task, branch_office_total, department_total, business_unit_total, task_status, parent_id, task_id, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

</mapper>
