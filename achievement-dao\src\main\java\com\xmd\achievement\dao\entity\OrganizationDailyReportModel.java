package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 机构日报
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@TableName("organization_daily_report")
public class OrganizationDailyReportModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 商务月id
     */
    @TableField("business_month_id")
    private Long businessMonthId;
    /**
     * 商务月
     */
    @TableField("business_month")
    private String businessMonth;
    /**
     * 报表生成时间
     */
    @TableField("report_create_time")
    private Date reportCreateTime;
    /**
     * 当天日期 yyyy-MM-dd
     */
    @TableField("current_day_date")
    private Date currentDayDate;
    /**
     * 机构ID
     */
    @TableField("organization_id")
    private Long organizationId;
    /**
     * 机构名称
     */
    @TableField("organization_name")
    private String organizationName;
    /**
     * 体系
     */
    @TableField("system_category")
    private String systemCategory;
    /**
     * 市场类别
     */
    @TableField("market_category")
    private String marketCategory;
    /**
     * 日签单金额（元）
     */
    @TableField("daily_signing_amount")
    private BigDecimal dailySigningAmount;
    /**
     * 日净现金（元）
     */
    @TableField("daily_net_cash")
    private BigDecimal dailyNetCash;
    /**
     * 月总签单金额（元）
     */
    @TableField("monthly_signing_amount")
    private BigDecimal monthlySigningAmount;
    /**
     * 月净现金（元）
     */
    @TableField("monthly_net_cash")
    private BigDecimal monthlyNetCash;
    /**
     * 基本任务（元）
     */
    @TableField("basic_task")
    private BigDecimal basicTask;
    /**
     * 任务完成率
     */
    @TableField("task_completion_rate")
    private BigDecimal taskCompletionRate;
    /**
     * 网站净现金（元）
     */
    @TableField("website_net_cash")
    private BigDecimal websiteNetCash;
    /**
     * 日新客户数
     */
    @TableField("daily_new_customer_count")
    private Long dailyNewCustomerCount;
    /**
     * 月新客户数
     */
    @TableField("monthly_new_customer_count")
    private Long monthlyNewCustomerCount;
    /**
     * 日网站新开客户数
     */
    @TableField("daily_website_new_customer_count")
    private Long dailyWebsiteNewCustomerCount;
    /**
     * 月网站新开客户数
     */
    @TableField("monthly_website_new_customer_count")
    private Long monthlyWebsiteNewCustomerCount;
    /**
     * 日新增网站数
     */
    @TableField("daily_new_website_count")
    private Long dailyNewWebsiteCount;
    /**
     * 月新增网站数
     */
    @TableField("monthly_new_website_count")
    private Long monthlyNewWebsiteCount;
    /**
     * 体系id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 市场类别id
     */
    @TableField("market_category_id")
    private Long marketCategoryId;
    /**
     *
     * 机构类型1总部；2区域；3分公司；4事业部；5商务组
     */
    @TableField("organization_type")
    private Integer organizationType;
    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;
    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新人id
     */
    @TableField("update_user_id")
    private String updateUserId;
    /**
     * 更新人名称
     */
    @TableField("update_user_name")
    private String updateUserName;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 删除标记: 0: 未删除, 1: 删除
     */
    @TableField("delete_flag")
    private Integer deleteFlag;

    @TableField("saas_net_cash")
    private BigDecimal saasNetCash;
}