package com.xmd.achievement.web.controller;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.*;
import com.xmd.achievement.service.entity.dto.ProductedDataAddDto;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.CsvUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/14/13:39
 * @since 1.0
 */
@Tag(name = "MC-中小数据信息处理")
@Slf4j
@RestController
@RequestMapping("data")
public class MqDataController {

    @Resource
    private MqOrderPaymentInfoService marketOrderPaymentInfoService;

/*    @Resource
    private MqServeInProgressInfoService mqServeInProgressInfoService;*/

    @Resource
    private MqServeFinishTimeInfoService mqServeFinishTimeInfoService;

    @Resource
    private IAchievementProductDetailService achievementProductDetailService;

    @Resource
    private IPolicyService policyService;

    @Deprecated
    @Operation(summary = "支付完成信息录入")
    @PostMapping("infoSaveMqOrderPaymentInfo")
    public WebResult<Boolean> infoSaveMqOrderPaymentInfo(@RequestBody @Valid InfoSaveMqOrderPaymentInfoRequest request) {
        log.info("MC-01-信息录入，请求参数:{}", JSONUtil.toJsonStr(request));
        return WebResult.success(marketOrderPaymentInfoService.infoSaveMqOrderPaymentInfo(request));
    }


  /*  @Deprecated
    @Operation(summary = "MC-02-服务进行中数据信息录入")
    @PostMapping("infoSaveMqServeInprogressInfo")
    public WebResult<Boolean> infoSaveMqServeInprogressInfo(@RequestBody @Valid InfoSaveMqServeInprogressInfo request) {
        log.info("MC-02-服务进行中数据信息录入，请求参数:{}", JSONUtil.toJsonStr(request));
        return WebResult.success(mqServeInProgressInfoService.infoSaveMqServeInprogressInfo(request));
    }*/

    @Deprecated
    @Operation(summary = "服务时间通知数据信息录入")
    @PostMapping("infoSaveMqServeFinishTimeInfo")
    public WebResult<Boolean> infoSaveMqServeFinishTimeInfo(@RequestBody @Valid InfoSaveMqServeFinishTimeInfoRequest request) {
        log.info("MC-03-服务时间通知数据信息录入，请求参数:{}", JSONUtil.toJsonStr(request));
        return WebResult.success(mqServeFinishTimeInfoService.infoSaveMqServeFinishTimeInfo(request));
    }

    @Deprecated
    @Operation(summary = "修复支付完成数据")
    @PostMapping("repairMqOrderPaymentInfo")
    public WebResult<Boolean> repairMqOrderPaymentInfo() {
        log.info("MC-04-修复支付完成数据");
        return WebResult.success(mqServeFinishTimeInfoService.repairMqOrderPaymentInfo());
    }

    @Deprecated
    @Operation(summary = "修复服务时间数据")
    @PostMapping("repairMqServeFinishTimeInfo")
    public WebResult<Boolean> repairMqServeFinishTimeInfo() {
        log.info("MC-05-修复服务时间数据");
        return WebResult.success(mqServeFinishTimeInfoService.repairMqServeFinishTimeInfo());
    }

    @Deprecated
    @Operation(summary = "修复服务中数据")
    @PostMapping("repairMqServeInprogressInfo")
    public WebResult<Boolean> repairMqServeInprogressInfo() {
        log.info("MC-06-修复服务中数据");
        return WebResult.success(mqServeFinishTimeInfoService.repairMqServeInprogressInfo());
    }


    @Deprecated
    @Operation(summary = "清洗支付表中的支付时间")
    @GetMapping("addPayTime")
    public WebResult<Boolean> addPayTime() {
        log.info("MC-08-清洗支付表中的支付时间");
        return WebResult.success(marketOrderPaymentInfoService.addPayTime());
    }

    @Deprecated
    @Operation(summary = "计费节点为生产完成数据清洗")
    @PostMapping("productedDataAdd")
    public WebResult<Integer> importProductedDataInsert(@RequestParam("file") MultipartFile file) {
        try {
            // 解析为 Java 对象列表
            //List<ProductedDataAddDto> productedDataAddDtos = CsvUtils.parseCsvToBeans(file, ProductedDataAddDto.class);
            List<ProductedDataAddDto> productedDataAddDtos = parseExcel(file);
            log.info("MC-07-计费节点为生产完成数据清洗,request:{}", JSONUtil.toJsonStr(productedDataAddDtos));
            Boolean aBoolean = achievementProductDetailService.productedDataInsert(productedDataAddDtos);
            return WebResult.success(productedDataAddDtos.size());
        } catch (Exception e) {
            log.error("解析Excel表格异常", e);
            return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
        }
    }

    private List<ProductedDataAddDto> parseExcel(MultipartFile file) throws Exception {
        List<ProductedDataAddDto> list = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        // 读取第一个 sheet
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();
        // 跳过标题行
        rowIterator.next();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            ProductedDataAddDto dto = new ProductedDataAddDto();
            dto.setOrderNo(getStringCellValue(row.getCell(0)));
            dto.setProductCode(getStringCellValue(row.getCell(1)));
            list.add(dto);
        }
        workbook.close();
        return list;
    }


    /** 处理 Excel 单元格数据 */
    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue().trim();
    }


}
