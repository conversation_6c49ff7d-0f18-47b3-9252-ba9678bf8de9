package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 分期状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/11:29
 * @since 1.0
 */
@Getter
public enum InstallmentEnum {
    /**
     * 不可分期
     */
    NOT_INSTALLMENT(1, "不可分期"),
    /**
     * 可分期
     */
    INSTALLMENT(2, "可分期"),
    /**
     * 分期申请中
     */
    INSTALLMENT_APPLYING(3, "分期申请中"),
    /**
     * 分期成功
     */
    INSTALLMENT_SUCCESS(4, "分期成功"),
    /**
     * 分期申请失败
     */
    INSTALLMENT_FAIL(5, "分期申请失败");

    private final Integer code;
    private final String msg;

    InstallmentEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
