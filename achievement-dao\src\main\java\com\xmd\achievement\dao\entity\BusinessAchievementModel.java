package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 商务业绩表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Accessors(chain = true)
@TableName("business_achievement")
public class BusinessAchievementModel extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 商务月id
     */
    @TableField("business_month_id")
    private Long businessMonthId;
    /**
     * 商务月
     */
    @TableField("business_month")
    private String businessMonth;
    /**
     * 员工ID
     */
    @TableField("employee_id")
    private String employeeId;
    /**
     * 员工姓名
     */
    @TableField("employee_name")
    private String employeeName;
    /**
     * 职级类型
     */
    @TableField("position_name")
    private String positionName;
    /**
     * 职级code
     */
    @TableField("position_code")
    private String positionCode;
    /**
     * 职级
     */
    @TableField("position")
    private String position;
    /**
     * 是否转正 1=是 2=否
     */
    @TableField("confirmed")
    private Integer confirmed;
    /**
     * 在岗时长
     */
    @TableField("tenure")
    private Integer tenure;
    /**
     * 司龄id
     */
    @TableField("seniority_id")
    private Long seniorityId;
    /**
     * 司龄细分
     */
    @TableField("seniority_segment")
    private String senioritySegment;
    /**
     * 公司id
     */
    @TableField("company_id")
    private Long companyId;
    /**
     * 区域
     */
    @TableField("region")
    private String region;
    /**
     * 区域ID
     */
    @TableField("region_id")
    private Long regionId;
    /**
     * 分公司
     */
    @TableField("company")
    private String company;
    /**
     * 事业部ID
     */
    @TableField("division_id")
    private Long divisionId;
    /**
     * 事业部
     */
    @TableField("division")
    private String division;
    /**
     * 部门
     */
    @TableField("department")
    private String department;
    /**
     * 部门ID
     */
    @TableField("dept_id")
    private Long deptId;
    /**
     * 净现金到账
     */
    @TableField("net_cash_receipt")
    private BigDecimal netCashReceipt;
    /**
     * 业绩段
     */
    @TableField("achievement_segment")
    private String achievementSegment;
    /**
     * 业绩段id
     */
    @TableField("achievement_segment_id")
    private Long achievementSegmentId;
    /**
     * 实发提成业绩
     */
    @TableField("actual_commission")
    private BigDecimal actualCommission;
    /**
     * 新客户数
     */
    @TableField("new_customer_count")
    private Integer newCustomerCount;
    /**
     * 新客户数
     */
    @TableField("old_customer_count")
    private Integer oldCustomerCount;
    /**
     * 非续费单
     */
    @TableField("non_renewal_orders")
    private Integer nonRenewalOrders;
    /**
     * 新开网站个数
     */
    @TableField("new_website_count")
    private Integer newWebsiteCount;
    /**
     * 网站非续费客户数
     */
    @TableField("website_non_renewal_customers")
    private Integer websiteNonRenewalCustomers;
    /**
     * 商代提成业绩
     */
    @TableField("agent_commission_achievement")
    private BigDecimal agentCommissionAchievement;
    /**
     * 网站净现金到账
     */
    @TableField("site_net_cash_receipt")
    private BigDecimal siteNetCashReceipt;

    /**
     * 净现金到账
     */
    @TableField("saas_net_cash")
    private BigDecimal saasNetCash;


    @TableField("new_saas_customer_count")
    private Integer newSaasCustomerCount;
}