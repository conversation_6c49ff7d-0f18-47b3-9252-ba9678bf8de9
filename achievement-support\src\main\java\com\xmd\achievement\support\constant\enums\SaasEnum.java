package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum SaasEnum {
     
    YES(0, "是"),
    
    NO(1, "否");

    private final Integer code;
    private final String msg;

    SaasEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static SaasEnum getByCode(Integer code) {
        return Arrays.stream(SaasEnum.values())
                .filter(e -> Objects.equals(code, e.getCode()))
                .findFirst()
                .orElse(null); // 未找到时返回 null
    }
}
