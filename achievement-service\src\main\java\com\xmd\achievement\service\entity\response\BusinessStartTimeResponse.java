package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/25 09:23
 * @version: 1.0.0
 * @return {@link }
 */
@Data
public class BusinessStartTimeResponse implements Serializable {
    private static final long serialVersionUID = 5316499599186386195L;

    @Schema(description = "开始时间")
    private Date startTime;
}
