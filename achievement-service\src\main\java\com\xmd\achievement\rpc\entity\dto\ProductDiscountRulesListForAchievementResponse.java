package com.xmd.achievement.rpc.entity.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 提供给业绩的折扣规则响应
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ProductDiscountRulesListForAchievementResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 关联的产品标识符
     */
    @Schema(description = "关联的产品标识符")
    private Long productId;

    /**
     * 分公司范围内适用的折扣
     */
    @Schema(description = "分公司范围内适用的折扣")
    private BigDecimal branchDiscount;

    /**
     * 经理可给予的折扣
     */
    @Schema(description = "经理可给予的折扣")
    private BigDecimal managerDiscount;

    /**
     * 商务层面的折扣
     */
    @Schema(description = "商务层面的折扣")
    private BigDecimal businessDiscount;

    /**
     * 提成减半的销售金额标准
     */
    @Schema(description = "提成减半的销售金额标准")
    private BigDecimal halfCommissionLine;

    /**
     * 无提成的销售金额标准
     */
    @Schema(description = "无提成的销售金额标准")
    private BigDecimal zeroCommissionLine;

    /**
     * 该折扣规则当前是否有效:0否1是
     */
    @Schema(description = "该折扣规则当前是否有效:0否1是 ")
    private Integer validFlag;
}