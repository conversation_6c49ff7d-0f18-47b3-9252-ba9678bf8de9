package com.xmd.achievement.support.message;

import com.xmd.achievement.util.constant.UtilConstant;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

/**
 * http配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 16:28
 **/
@Configuration
public class HttpConfig {

    /**
     * 设置restTemplate连接属性
     *
     * @param templateBuilder 模板建造者
     * @return RestTemplate
     * <AUTHOR>
     * @date 2023/4/6 16:28
     **/
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder templateBuilder) {
        //超时时间配置
        templateBuilder.setConnectTimeout(Duration.of(UtilConstant.RestFul.CONNECT_TIME_OUT, ChronoUnit.MILLIS)).setReadTimeout(Duration.of(UtilConstant.RestFul.READ_TIME_OUT, ChronoUnit.MILLIS));
        return templateBuilder.build();
    }
}
