package ${package.Entity};

    #foreach($pkg in ${table.importPackages})
    import ${pkg};
    #end
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * $!{table.comment}
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@TableName("${table.name}")
public class ${entity} implements

        Serializable {
            private static final long serialVersionUID = 1L;
            ## ----------  BEGIN 字段循环遍历  ----------
            #foreach($field in ${table.fields})
                #if(${field.keyFlag})
                    #set($keyPropertyName=${field.propertyName})
                #end
                #if("$!field.comment" != "")
                    /**
                     * ${field.comment}
                     */
                #end
                #if(${field.keyFlag})
                    ## 主键
                    #if(${field.keyIdentityFlag})
                    @TableId(value = "${field.annotationColumnName}", type = IdType.AUTO)
                    #elseif(!$null.isNull(${idType}) && "$!idType" != "")
                    @TableId(value = "${field.annotationColumnName}", type = IdType.${idType})
                    #elseif(${field.convert})
                    @TableId("${field.annotationColumnName}")
                    #end
                    ## 普通字段
                #elseif(${field.fill})
                    ## -----   存在字段填充设置   -----
                    #if(${field.convert})
                    @TableField(value = "${field.annotationColumnName}", fill = FieldFill.${field.fill})
                    #else
                    @TableField(fill = FieldFill.${field.fill})
                    #end
                #elseif(${field.convert})
                @TableField("${field.annotationColumnName}")
                #end
            private ${field.propertyType} ${field.propertyName};
            #end
        }