package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/09:55
 * @since 1.0
 */
@Data
@Schema(description = "查询规则下商品信息")
public class QueryRuleProductResponse implements Serializable {
    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品Code")
    private String productCode;

    @Schema(description = "商品名称")
    private String productName;
}
