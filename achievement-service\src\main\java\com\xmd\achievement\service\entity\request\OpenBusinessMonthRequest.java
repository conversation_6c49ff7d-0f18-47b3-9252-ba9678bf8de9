package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 商务月查询开放接口 入参
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Schema(description = "商务月查询开放接口 入参")
public class OpenBusinessMonthRequest implements Serializable {

    private static final long serialVersionUID = -3205838436677661187L;

    @Schema(description = "当前商务月 yyyy-MM")
    private String currentDate;
}
