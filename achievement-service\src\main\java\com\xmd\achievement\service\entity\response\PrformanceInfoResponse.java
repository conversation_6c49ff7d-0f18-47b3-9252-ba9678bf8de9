package com.xmd.achievement.service.entity.response;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/10/17:28
 * @since 1.0
 */
@Data
public class PrformanceInfoResponse implements Serializable {
        @ExcelProperty("商务月")
        private String businessMonth;

        @ExcelProperty("结算单ID")
        private String settlementId;

        @ExcelProperty("客户名称")
        private String customerName;

//        @ExcelProperty("晋降级新客户")
//        private String customerLevel;

        @ExcelProperty("客户所在地区")
        private String customerRegion;

//        @ExcelProperty("文本序号")
//        private String textNumber;

//        @ExcelProperty("签单日期")
//        private String signDate;

        @ExcelProperty("主副分享人")
        private String sharer;

        @ExcelProperty("分司")
        private String branch;

        @ExcelProperty("商务部门")
        private String businessDepartment;

        @ExcelProperty("商务名称")
        private String businessName;

//        @ExcelProperty("产品类型")
//        private String productType;

        @ExcelProperty("商品分类")
        private String productCategory;

        @ExcelProperty("商品名称")
        private String productName;

        @ExcelProperty("规格分类")
        private String specificationCategory;

        @ExcelProperty("规格")
        private String specification;

        @ExcelProperty("状态")
        private String status;

        @ExcelProperty("业务类型")
        private String businessType;

        @ExcelProperty("到账时间")
        private String paymentTime;

        @ExcelProperty("实际支付金额")
        private String actualPaymentAmount;

        @ExcelProperty("业绩")
        private String performance;

        @ExcelProperty("职级体系")
        private String rankSystem;

        @ExcelProperty("岗位名称")
        private String positionName;

        @ExcelProperty("累计工龄")
        private Integer totalWorkYears;
}
