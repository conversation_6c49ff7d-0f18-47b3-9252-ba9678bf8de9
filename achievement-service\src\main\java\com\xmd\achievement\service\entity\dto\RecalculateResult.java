package com.xmd.achievement.service.entity.dto;

import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 支付完成根据订单明细编号重算
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecalculateResult {

    /**
     * 商品集合
     */
    private List<AchievementProductDetailModel> productList;
    /**
     * 规格分类集合
     */
    private List<AchievementCategoryDetailModel> categoryList;
    /**
     * 规格集合
     */
    private List<AchievementSpecDetailModel> specList;
}
