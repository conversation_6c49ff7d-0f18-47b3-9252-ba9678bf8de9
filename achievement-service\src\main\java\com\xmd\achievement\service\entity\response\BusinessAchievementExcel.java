package com.xmd.achievement.service.entity.response;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.NumberFormat;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/25 15:44
 * @version: 1.0.0
 * @return {@link }
 */
@Data
public class BusinessAchievementExcel {

    @ExcelProperty("商务月")
    private String businessMonth;

    @ExcelProperty("员工ID")
    private String employeeId;

    @ExcelProperty("员工姓名")
    private String employeeName;

    @ExcelProperty("职级类型")
    private String positionName;

    @ExcelProperty("职级")
    private String position;

    @ExcelProperty("是否转正")
    private String confirmed;

    @ExcelProperty("在岗时长")
    private Integer tenure;

    @ExcelProperty("司龄分段")
    private String senioritySegment;

    @ExcelProperty("分公司")
    private String company;

    @ExcelProperty("分公司ID")
    private Long companyId;

    @ExcelProperty("事业部名称")
    private String division;

    @ExcelProperty("事业部ID")
    private Long divisionId;

    @ExcelProperty("商务部")
    private String department;

    @ExcelProperty("商务部ID")
    private Long deptId;

    @ExcelProperty("净现金到账")
    @NumberFormat("0.000")
    private BigDecimal netCashReceipt;

    @ExcelProperty("业绩段")
    private String achievementSegment;

    @ExcelProperty("商代提成")
    @NumberFormat("0.000")
    private BigDecimal agentCommissionAchievement;

    @ExcelProperty("实发提成业绩")
    @NumberFormat("0.000")
    private BigDecimal actualCommission;

    @ExcelProperty("SAAS产品新客户数")
    private Integer newSaasCustomerCount;

    @ExcelProperty("新客户数")
    private Integer newCustomerCount;

    @ExcelProperty("老客户数")
    private Integer oldCustomerCount;

    @ExcelProperty("非续费单")
    private Integer nonRenewalOrders;

    @ExcelProperty("新开网站个数")
    private Integer newWebsiteCount;

    @ExcelProperty("网站和商城非续费客户数")
    private Integer websiteNonRenewalCustomers;


    @ExcelProperty("网站净现金到账")
    @NumberFormat("0.000")
    private BigDecimal siteNetCashReceipt;

    @ExcelProperty("saas净现金")
    @NumberFormat("0.000")
    private BigDecimal saasNetCash;
}
