package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * saas标签新增请求参数
 */
@Data
public class SaasTabAddRequest {

    @NotNull(message = "关联ID不能为空")
    @Schema(description = "关联ID", example = "1")
    private Long associationId;

    @NotNull(message = "关联名称不能为空")
    @Schema(description = "关联名称", example = "示例名称")
    private String associationName;

    @NotNull(message = "是否SAAS产品不能为空")
    @Schema(description = "是否SAAS产品：0-非SAAS产品，1-SAAS产品", example = "1")
    private Integer isSaas;

    @NotNull(message = "SAAS类型不能为空")
    @Schema(description = "SAAS类型：0=中企,1=跨境", example = "0")
    private Integer saasType;
} 