package com.xmd.achievement.service.entity.dto;

import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductSpecItemResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductSpecResponse;
import com.xmd.achievement.rpc.entity.dto.ProductListForAchievementResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>
 * 业绩规格明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@Accessors(chain = true)
public class AchievementSpecDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 业绩规格id
     */
    @Schema(description = "业绩规格id")
    private Long achievementSpecId;

    /**
     * 业绩规格分类id
     */
    @Schema(description = "业绩规格分类id")
    private Long achievementCategoryId;

    /**
     * 规格ID
     */
    @Schema(description = "规格ID")
    private Long specId;

    /**
     * 规格名称
     */
    @Schema(description = "规格名称")
    private String specName;
    /**
     * 规格项类型 1=数量，2=时长，3=自定义
     */
    @Schema(description = "规格名称")
    private Integer itemType;

    /**
     * 服务项编码
     */
    @Schema(description = "服务项编码")
    private String serveItemNo;

    /**
     * 标准价
     */
    @Schema(description = "标准价")
    private BigDecimal standardPrice;

    /**
     * 应付金额
     */
    @Schema(description = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额")
    private BigDecimal paidAmount;

    /**
     * 首年报价
     */
    @Schema(description = "首年报价")
    private BigDecimal firstYearQuote;

    /**
     * 首年到账金额
     */
    @Schema(description = "首年到账金额")
    private BigDecimal firstYearIncome;

    /**
     * 续费报价
     */
    @Schema(description = "续费报价")
    private BigDecimal renewalQuote;

    /**
     * 续费到账金额
     */
    @Schema(description = "续费到账金额")
    private BigDecimal renewalIncome;

    /**
     * 净现金
     */
    @Schema(description = "净现金")
    private BigDecimal netCash;

    /**
     * 商代提成业绩
     */
    @Schema(description = "商代提成业绩")
    private BigDecimal agentCommAchv;

    /**
     * 商代实发提成业绩
     */
    @Schema(description = "商代实发提成业绩")
    private BigDecimal agentActCommAchv;

    /**
     * 商代缓发提成业绩
     */
    @Schema(description = "商代缓发提成业绩")
    private BigDecimal agentDefCommAchv;

    /**
     * 部门提成业绩
     */
    @Schema(description = "部门提成业绩")
    private BigDecimal deptCommAchv;
    /**
     * 订单规格类型：1=普通，2=赠品
     */
    private Integer orderSpecType;
    /**
     * 事业部提成业绩
     */
    @Schema(description = "事业部提成业绩")
    private BigDecimal buCommAchv;

    /**
     * 分公司提成业绩
     */
    @Schema(description = "分公司提成业绩")
    private BigDecimal branchCommAchv;
    /**
     * 商品id （冗余字段）
     */
    private Long productId;
    /**
     * 商品名称 （冗余字段）
     */
    private String productName;
    /**
     * 规格分类id （冗余字段）
     */
    private Long categoryId;
    /**
     * 规格分类名称 （冗余字段）
     */
    private String categoryName;
    /**
     * 计费单位 冗余字段）
     */
    private String itemUnit;
    /**
     * 计费个数 冗余字段）
     */
    private Integer itemNum;
    /**
     * 计费价格(新开价格) 冗余字段）
     */
    private BigDecimal billingPrice;
    /**
     * 续费价格 冗余字段）
     */
    private BigDecimal renewalPrice;

    /**
     * 订单商品明细id
     */
    private String orderProductId;

    public static AchievementSpecDetailDto buildBaseDto(OrderSimpleProductSpecResponse specResponse, Map<Long, ProductListForAchievementResponse> sepcIdProductMap, Map<Long, OrderSimpleProductSpecItemResponse> orderProductSpecIdItemMap) {
        AchievementSpecDetailDto dto = new AchievementSpecDetailDto()
                .setSpecId(specResponse.getProductSpecId())
                .setSpecName(specResponse.getSpecName())
                .setStandardPrice(specResponse.getSpecTotalAmount())
                .setPayableAmount(specResponse.getPayableAmount())
                .setPaidAmount(specResponse.getPaidAmount())
                .setProductId(specResponse.getProductId())
                .setOrderSpecType(specResponse.getOrderSpecType())
                .setOrderProductId(specResponse.getOrderProductId());

        OrderSimpleProductSpecItemResponse specItem = orderProductSpecIdItemMap.get(specResponse.getOrderProductSpecId());
        if (null != specItem) {
            dto.setItemType(specItem.getItemType());
            dto.setItemUnit(specItem.getItemUnit());
            dto.setItemNum(specItem.getItemNum());
        }
        ProductListForAchievementResponse productResponse = sepcIdProductMap.get(specResponse.getProductSpecId());
        if (null != productResponse) {
            dto.setCategoryId(productResponse.getSpecCategoryId());
            dto.setCategoryName(productResponse.getSpecCategoryName());
            dto.setProductName(productResponse.getProductName());
            dto.setBillingPrice(productResponse.getBillingPrice());
            dto.setRenewalPrice(productResponse.getRenewalPrice());
        }
        return dto;
    }
}
