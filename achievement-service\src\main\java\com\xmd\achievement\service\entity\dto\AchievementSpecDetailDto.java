package com.xmd.achievement.service.entity.dto;

import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductSpecItemResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductSpecResponse;
import com.xmd.achievement.rpc.entity.dto.ProductListForAchievementResponse;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import com.xmd.achievement.rpc.entity.dto.StandardBillingPriceResponse;
import com.xmd.achievement.support.constant.enums.PricingTypeEnum;

import com.xmd.achievement.util.enums.SaleTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>
 * 业绩规格明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Data
@Accessors(chain = true)
public class AchievementSpecDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 业绩规格id
     */
    @Schema(description = "业绩规格id")
    private Long achievementSpecId;

    /**
     * 业绩规格分类id
     */
    @Schema(description = "业绩规格分类id")
    private Long achievementCategoryId;

    /**
     * 规格ID
     */
    @Schema(description = "规格ID")
    private Long specId;

    /**
     * 规格名称
     */
    @Schema(description = "规格名称")
    private String specName;
    /**
     * 规格项类型 1=数量，2=时长，3=自定义
     */
    @Schema(description = "规格名称")
    private Integer itemType;

    /**
     * 服务项编码
     */
    @Schema(description = "服务项编码")
    private String serveItemNo;

    /**
     * 标准价
     */
    @Schema(description = "标准价")
    private BigDecimal standardPrice;

    /**
     * 应付金额
     */
    @Schema(description = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额")
    private BigDecimal paidAmount;

    /**
     * 首年报价
     */
    @Schema(description = "首年报价")
    private BigDecimal firstYearQuote;

    /**
     * 首年到账金额
     */
    @Schema(description = "首年到账金额")
    private BigDecimal firstYearIncome;

    /**
     * 续费报价
     */
    @Schema(description = "续费报价")
    private BigDecimal renewalQuote;

    /**
     * 续费到账金额
     */
    @Schema(description = "续费到账金额")
    private BigDecimal renewalIncome;

    /**
     * 净现金
     */
    @Schema(description = "净现金")
    private BigDecimal netCash;

    /**
     * 商代提成业绩
     */
    @Schema(description = "商代提成业绩")
    private BigDecimal agentCommAchv;

    /**
     * 商代实发提成业绩
     */
    @Schema(description = "商代实发提成业绩")
    private BigDecimal agentActCommAchv;

    /**
     * 商代缓发提成业绩
     */
    @Schema(description = "商代缓发提成业绩")
    private BigDecimal agentDefCommAchv;

    /**
     * 部门提成业绩
     */
    @Schema(description = "部门提成业绩")
    private BigDecimal deptCommAchv;
    /**
     * 订单规格类型：1=普通，2=赠品
     */
    private Integer orderSpecType;
    /**
     * 业务类型 1=新开，2=续费，3=升级，4=另购，5=转款，6=退款，7=高价赎回
     */
    private Integer saleType;
    /**
     * 事业部提成业绩
     */
    @Schema(description = "事业部提成业绩")
    private BigDecimal buCommAchv;

    /**
     * 分公司提成业绩
     */
    @Schema(description = "分公司提成业绩")
    private BigDecimal branchCommAchv;
    /**
     * 商品id （冗余字段）
     */
    private Long productId;
    /**
     * 商品名称 （冗余字段）
     */
    private String productName;
    /**
     * 规格分类id （冗余字段）
     */
    private Long categoryId;
    /**
     * 规格分类名称 （冗余字段）
     */
    private String categoryName;
    /**
     * 计费单位 冗余字段）
     */
    private String itemUnit;
    /**
     * 计费个数 冗余字段）
     */
    private Integer itemNum;
    /**
     * 计费价格(新开价格) 冗余字段）
     */
    private BigDecimal billingPrice;
    /**
     * 续费价格 冗余字段）
     */
    private BigDecimal renewalPrice;

    /**
     * 订单商品明细id
     */
    private String orderProductId;

    public static AchievementSpecDetailDto buildBaseDto(OrderSimpleProductSpecResponse specResponse, Map<Long, ProductListForAchievementResponse> sepcIdProductMap, Map<Long, OrderSimpleProductSpecItemResponse> orderProductSpecIdItemMap,
                                                        Map<Long, Integer> specQuantityMap, OrderSimpleInfoResponse orderInfo, Map<Long, OrderSimpleProductSpecItemResponse> productSpecIdItemMap,Integer orderSaleType) {
        AchievementSpecDetailDto dto = new AchievementSpecDetailDto();
        Integer quantity = specQuantityMap.get(specResponse.getProductSpecId());
        dto.setSpecId(specResponse.getProductSpecId())
                .setSpecName(specResponse.getSpecName())
                .setStandardPrice(specResponse.getSpecTotalAmount())
                .setPayableAmount(specResponse.getPayableAmount())
                .setPaidAmount(specResponse.getPaidAmount())
                .setProductId(specResponse.getProductId())
                .setOrderSpecType(specResponse.getOrderSpecType())
                .setOrderProductId(specResponse.getOrderProductId())
                .setSaleType(orderInfo.getOrderSaleType());

        OrderSimpleProductSpecItemResponse specItem = orderProductSpecIdItemMap
                .get(specResponse.getOrderProductSpecId());
        if(!SaleTypeEnum.NEW_OPEN.getType().equals(orderSaleType)){
            specItem = productSpecIdItemMap
                    .get(specResponse.getProductSpecId());
        }
        if (null != specItem) {
            dto.setItemType(specItem.getItemType());
            dto.setItemUnit(specItem.getItemUnit());
            dto.setItemNum(specItem.getItemNum());
        }else{
            log.info("服务查询计费项数据为空 productSpecId：{}",specResponse.getProductSpecId());
            dto.setItemType(0);
            dto.setItemUnit("");
            dto.setItemNum(0);
        }
        ProductListForAchievementResponse productResponse = sepcIdProductMap.get(specResponse.getProductSpecId());
        if (null != productResponse) {
            dto.setCategoryId(productResponse.getSpecCategoryId());
            dto.setCategoryName(productResponse.getSpecCategoryName());
            dto.setProductName(productResponse.getProductName());
            setPricingInfo(dto, productResponse, specResponse);
        }
        // 2025-8-5 售后v1.1需求新加逻辑 订单类型为另购，但是购买次数为1，说明是新逻辑,这种需要走新开
        if (OrderSaleTypeEnum.ANOTHER_BUY.getType().equals(orderInfo.getOrderSaleType()) && NumberConstants.INTEGER_VALUE_1.equals(quantity)) {
            dto.setSaleType(OrderSaleTypeEnum.OPEN.getType());
        }
        return dto;
    }

    /**
     * 根据定价类型设置计费价格和续费价格
     *
     * @param dto DTO对象
     * @param productResponse 商品响应信息
     * @param orderSpecResponse 订单规格响应信息
     */
    private static void setPricingInfo(AchievementSpecDetailDto dto,
                                     ProductListForAchievementResponse productResponse,
                                     OrderSimpleProductSpecResponse orderSpecResponse) {
        if (PricingTypeEnum.isStandard(productResponse.getPricingType())) {
            // 标准定价：从标准计费价格中获取
            setStandardPricing(dto, productResponse.getStandardBillingPrice());
        } else if (PricingTypeEnum.isTiered(productResponse.getPricingType())) {
            // 阶梯定价：使用订单规格的单价
            setTieredPricing(dto, orderSpecResponse);
        }
    }

    /**
     * 设置标准定价
     */
    private static void setStandardPricing(AchievementSpecDetailDto dto, StandardBillingPriceResponse standardBillingPrice) {
        if (standardBillingPrice != null) {
            dto.setBillingPrice(standardBillingPrice.getBillingPrice());
            dto.setRenewalPrice(standardBillingPrice.getRenewalPrice());
        }
    }

    /**
     * 设置阶梯定价
     */
    private static void setTieredPricing(AchievementSpecDetailDto dto, OrderSimpleProductSpecResponse orderSpecResponse) {
        if (orderSpecResponse != null && orderSpecResponse.getUnitPrice() != null) {
            dto.setBillingPrice(orderSpecResponse.getUnitPrice());
            dto.setRenewalPrice(orderSpecResponse.getUnitPrice());
        }
    }
}
