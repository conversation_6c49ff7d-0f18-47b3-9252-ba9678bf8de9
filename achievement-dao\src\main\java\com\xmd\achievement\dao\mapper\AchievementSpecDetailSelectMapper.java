package com.xmd.achievement.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xmd.achievement.dao.entity.AchievementSpecDetailSelectModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 业绩规格明细表(查询) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface AchievementSpecDetailSelectMapper extends BaseMapper<AchievementSpecDetailSelectModel> {
    void batchInsertOrUpdate(@Param("list") List<AchievementSpecDetailSelectModel> list);
}
