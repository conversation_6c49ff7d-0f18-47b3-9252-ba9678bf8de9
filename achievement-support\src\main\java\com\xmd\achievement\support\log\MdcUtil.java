package com.xmd.achievement.support.log;

import com.xmd.achievement.util.constant.UtilConstant;
import org.slf4j.MDC;

/**
 * Mdc工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 20:24
 */
public class MdcUtil {

    /**
     * 初始化mdc数据
     *
     * <AUTHOR>
     * @date 2023/4/6 20:20
     **/
    public static void initMdc() {
        UtilConstant.Mdc.MDC_LIST.forEach((key, value) -> MDC.put(key, value.get()));
    }

    /**
     * 清除mdc数据
     *
     * <AUTHOR>
     * @date 2023/4/6 20:20
     **/
    public static void clearMdc() {
        MDC.clear();
    }
}