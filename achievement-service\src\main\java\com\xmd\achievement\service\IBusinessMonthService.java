package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.BusinessAchievementModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.service.entity.dto.UpdateBusinessMonthDto;
import com.xmd.achievement.service.entity.request.BusinessMonthSaveRequest;
import com.xmd.achievement.service.entity.request.MonthPageRequest;
import com.xmd.achievement.service.entity.request.OpenBusinessMonthRequest;
import com.xmd.achievement.service.entity.response.BusinessMonthInfoResponse;
import com.xmd.achievement.service.entity.response.BusinessMonthListResponse;
import com.xmd.achievement.service.entity.response.BusinessStartTimeResponse;
import com.xmd.achievement.service.entity.response.OpenBusinessesMonthResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;

import java.util.Date;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/19 16:17
 * @version: 1.0.0
 * @return {@link }
 */
public interface IBusinessMonthService {
    WebResult<Boolean> saveMonth(BusinessMonthSaveRequest request);

    WebResult<Boolean> updateMonth(UpdateBusinessMonthDto updateBusinessMonthDto);

    PageResponse<BusinessMonthListResponse> monthList(MonthPageRequest monthPageRequest);

    BusinessMonthInfoResponse getMonth(Long monthId);

    BusinessMonthModel getMonthInfo(Date paidTime);

    BusinessStartTimeResponse getMonthStartDate();

    Long getMonthInfoByBusinessMonth(String businessMonth);

    void saveOrUpdate(BusinessAchievementModel model);

    String getCurrentBusinessesMonth();

    OpenBusinessesMonthResponse openBusinessesMonth(OpenBusinessMonthRequest request);

    /**
     * 获取上一个工作月
     *
     * @return {@link String }
     * <AUTHOR>
     * @since 1.0
     */
    String queryLastBusinessMonth();

    /**
     * 冻结商务月
     */
    Boolean freezeMonth(Long id);

    boolean isMonthFrozen(Long id);
    
    /**
     * 判断商务月是否已冻结（通过月份字符串）
     */
    boolean isMonthFrozen(String month);

    boolean isMonthFrozen(Date paidTime);

    boolean isMonthFrozen(BusinessMonthModel model);
}
