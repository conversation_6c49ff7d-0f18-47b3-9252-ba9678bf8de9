package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/11:29
 * @since 1.0
 */
@Getter
public enum TaskStatusEnum {
    /**
     * 未完成
     */
    NO(1, "未完成"),

    /**
     * 已完成
     */
    YES(2, "已完成");

    private final Integer code;
    private final String msg;

    TaskStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
