package com.xmd.achievement.support.constant.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 联系人信息类型
 * */
@Getter
public enum LinkManTypeEnum {

    CUSTOMER(1, "客户联系人"), SALE(2, "卖方联系人"), SUB_SALE(3, "卖方分单人"), OPTIMIZATION(4, "优化师");

    private final Integer type;

    private final String visitorCode;

    LinkManTypeEnum(Integer type, String visitorCode) {
        this.type = type;
        this.visitorCode = visitorCode;
    }

    public static LinkManTypeEnum getOrderSaleTypeByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        LinkManTypeEnum[] values = LinkManTypeEnum.values();
        for (int i = 0; i < values.length; i++) {
            LinkManTypeEnum value = values[i];
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static LinkManTypeEnum getOrderSaleTypeByVisitorCode(String visitorCode) {
        if (StringUtils.isEmpty(visitorCode)) {
            return null;
        }
        LinkManTypeEnum[] values = LinkManTypeEnum.values();
        for (int i = 0; i < values.length; i++) {
            LinkManTypeEnum value = values[i];
            if (StringUtils.equals(value.getVisitorCode(), visitorCode)) {
                return value;
            }
        }
        return null;
    }
}
