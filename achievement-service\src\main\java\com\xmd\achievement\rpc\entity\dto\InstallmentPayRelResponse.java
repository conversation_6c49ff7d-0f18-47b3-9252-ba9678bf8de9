package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分期支付关联响应
 * 
 * <AUTHOR> Generated
 * @date 2025-01-30
 */
@Data
@Schema(description = "分期支付关联响应")
public class InstallmentPayRelResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分期支付关联id
     */
    @Schema(description = "分期支付关联id")
    private Long installmentPayRelId;

    /**
     * 分期详情id
     */
    @Schema(description = "分期详情id")
    private Long installmentDetailId;

    /**
     * 订单支付详情id
     */
    @Schema(description = "订单支付详情id")
    private String orderPayDetailId;

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 交易id
     */
    @Schema(description = "交易id")
    private Long transactionId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}
