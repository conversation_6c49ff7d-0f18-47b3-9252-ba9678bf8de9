<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.AchievementPolicyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.AchievementPolicyModel">
        <id column="id" property="id" />
        <result column="policy_id" property="policyId" />
        <result column="signing_method" property="signingMethod" />
        <result column="revenue_node" property="revenueNode" />
        <result column="achievement_ratio" property="achievementRatio" />
        <result column="commission_ratio" property="commissionRatio" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, policy_id, signing_method, revenue_node, achievement_ratio, commission_ratio, product_id, product_name, delete_flag, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

</mapper>
