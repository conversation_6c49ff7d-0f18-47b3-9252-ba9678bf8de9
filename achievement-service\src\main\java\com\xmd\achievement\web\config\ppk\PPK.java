package com.xmd.achievement.web.config.ppk;

import java.lang.annotation.*;


/**
 * ppk验证
 *
 * <AUTHOR>
 * @date: 2024/3/14 2:05 下午
 * @version: 1.0.0
 * @return:
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PPK {

    /**
     * 解析后的数据类型
     *
     * @return
     */
    public Class clazz() default Object.class;

    /**
     * 解密请求的key
     *
     * @return
     */
    public PpkManager.Ppk decryptRequestKey();

}
