package com.xmd.achievement.web.controller;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.IOrderService;
import com.xmd.achievement.service.entity.request.ManualOrderRequest;
import com.xmd.achievement.service.entity.response.ManualOrderExcelResponse;
import com.xmd.achievement.service.entity.response.ManualOrderResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xxl.job.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */

@Tag(name = "订单数据处理")
@Slf4j
@RestController
@RequestMapping("order")
public class OrderController {

    @Resource
    private IOrderService orderService;

    @Operation(summary = "")
    @PostMapping("manualOrder")
    public WebResult<List<ManualOrderResponse>> manualOrder(@RequestBody @Valid ManualOrderRequest request) {
        return WebResult.success(orderService.manualOrder(request));
    }

    @Operation(summary = "")
    @PostMapping("importManualOrderExcel")
    public WebResult<List<ManualOrderExcelResponse>> importManualOrderExcel(@RequestParam("file") MultipartFile file) {
        return WebResult.success(orderService.manualOrderExcel(file));
    }
}
