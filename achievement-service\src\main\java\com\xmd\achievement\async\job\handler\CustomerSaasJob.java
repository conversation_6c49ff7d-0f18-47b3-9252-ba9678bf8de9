package com.xmd.achievement.async.job.handler;


import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.service.ICustomerSaasService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class CustomerSaasJob {

    @Resource
    private ICustomerSaasService customerSaasService;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.CUSTOMER_SAAS_JOB)
    public ReturnT<String> jobHandler(String param) {
        execute();
        return ReturnT.SUCCESS;
    }

    private void execute() {
        log.info("执行CustomerSaasJob任务Start...");
        try{
            customerSaasService.customerSaasJob();
        }catch(Exception e){
            log.error("CustomerSaasJob任务失败,失败原因：", e);
        }    
        log.info("执行CustomerSaasJob任务End...");    
    }
}
