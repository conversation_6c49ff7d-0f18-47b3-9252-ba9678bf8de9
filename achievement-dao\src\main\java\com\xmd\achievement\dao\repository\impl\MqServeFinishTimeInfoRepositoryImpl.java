package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.MqServeFinishTimeInfoModel;
import com.xmd.achievement.dao.mapper.MqServeFinishTimeInfoMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IMqServeFinishTimeInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
@Slf4j
public class MqServeFinishTimeInfoRepositoryImpl extends ServiceImpl<MqServeFinishTimeInfoMapper, MqServeFinishTimeInfoModel> implements IMqServeFinishTimeInfoRepository {

    @Resource
    private MqServeFinishTimeInfoMapper mqServeFinishTimeInfoMapper;

    @Override
    public List<MqServeFinishTimeInfoModel> selectByOrderIdAndProductId(Long orderId, List<Long> productId) {
        LambdaQueryWrapper<MqServeFinishTimeInfoModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqServeFinishTimeInfoModel::getOrderId, orderId);
        queryWrapper.in(MqServeFinishTimeInfoModel::getProductId, productId);
        queryWrapper.eq(MqServeFinishTimeInfoModel::getDeleteFlag, 0);
        return mqServeFinishTimeInfoMapper.selectList(queryWrapper);
    }
}