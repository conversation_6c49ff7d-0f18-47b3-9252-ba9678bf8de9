package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销售任务开放接口 返回参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Schema(description = "销售任务开放接口 返回参数")
public class OpenSaleTaskResponse implements Serializable {
    private static final long serialVersionUID = 4141174835548034353L;

    @Schema(description = "机构ID")
    private Long orgId;

    @Schema(description = "机构名称")
    private String orgName;

    @Schema(description = "商务月")
    private String businessMonth;

    @Schema(description = "基本任务（元）")
    private BigDecimal basicTask;

    @Schema(description = "分公司任务汇总（元）")
    private BigDecimal branchOfficeTotal;

    @Schema(description = "部门任务汇总（元）")
    private BigDecimal departmentTotal;

    @Schema(description = "事业部任务汇总（元）")
    private BigDecimal businessUnitTotal;
}
