package com.xmd.achievement.service.entity.dto;

import lombok.Data;

/**
 * 退转款消息体
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class ReceiveOrderRefundDto {

    private Long messageId;

    private String afterSalesOrderNo;

    /**
     * 售后订单类型，1退款 2转款
     */
    private Integer afterSalesOrderType;

    private String operateType;

    /**
     * 售后状态：1金额核算中 2金额待确认 3待分司/总部审核 4合同待上传 5合同待审核 6待退款 7待转款 8售后完成 9售后取消
     */
    private Integer afterSalesStatus;

}
