package com.xmd.achievement.service.entity.response;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/10/17:28
 * @since 1.0
 */
@Data
public class ExportKuajingAchievementResponse implements Serializable {
    @ExcelProperty("序号(必填,同条业绩序号一致)")
    private String id;

    @ExcelProperty("客户ID")
    private String custId;

    @ExcelProperty("客户名称")
    private String custName;

    @ExcelProperty("客户类型(1:老客 2:新客户 3:非新老客)")
    private Integer custType;

    @ExcelProperty("文本序号")
    private String txtCode;

    @ExcelProperty("主副：0副 1主")
    private Integer shareType;

    @ExcelProperty("商务ID")
    private String salerId;

    @ExcelProperty("产品类型")
    private Integer productType;

    @ExcelProperty("业务类型")
    private Integer businessType;

    @ExcelProperty("标准价")
    private BigDecimal standardPrice;

    @ExcelProperty("签单金额")
    private BigDecimal singingAmount;

    @ExcelProperty("签单时间")
    private String singingDate;

    @ExcelProperty("实际到账金额")
    private BigDecimal actualAccount;

    @ExcelProperty("到账日期")
    private String toAccountDate;

    @ExcelProperty("结算单状态(0:有效 1:失效 2:已退 3:已完成)")
    private Integer status;

    @ExcelProperty("到账类型(1:首付款 ********:尾款 11:历史数据)")
    private Integer toAccountType = 1;

    @ExcelProperty("商代提成业绩（saleHiredMoney）")
    private BigDecimal saleHiredMoney;

    @ExcelProperty("缓发商代提成业绩（delaySaleHiredMoney）")
    private BigDecimal delaySaleHiredMoney;

    @ExcelProperty("实发商代提成业绩（relaySaleHiredMoney）")
    private BigDecimal relaySaleHiredMoney;

    @ExcelProperty("部门提成业绩（managerHiredMoney）")
    private BigDecimal managerHiredMoney;

    @ExcelProperty("分司提成业绩（subManagerHiredMoney）")
    private BigDecimal subManagerHiredMoney;

    @ExcelProperty("业绩是否核算个人(1:是 0:否)")
    private Integer isSalerYj;

    @ExcelProperty("业绩是否核算部门(1:是 0:否)")
    private Integer isDeptYj;

    @ExcelProperty("业绩是否核算分司(1:是 0:否)")
    private Integer isOrgYj;

    @ExcelProperty("业绩是否核算区域(1:是 0:否)")
    private Integer isAreaYj;

    @ExcelProperty("备注")
    private String remark = "跨境批量导入";

    @ExcelProperty("定单条目ID(非必填)")
    private String orderId;

    @ExcelProperty("商品ID")
    private String productId;

    @ExcelProperty("商品名称")
    private String productName;

    @ExcelProperty("商品净现金")
    private BigDecimal productPrice;

    @ExcelProperty("计收节点：1-生产完成 2-支付完成")
    private String nodeInfo;

    @ExcelProperty("订单商品明细")
    private String orderProductId;

    @ExcelProperty("商品一级分类")
    private String levelOneCategoryName;

    @ExcelProperty("商品二级分类")
    private String levelTwoCategoryName;

    @ExcelProperty("商品三级分类")
    private String levelThreeCategoryName;
}
