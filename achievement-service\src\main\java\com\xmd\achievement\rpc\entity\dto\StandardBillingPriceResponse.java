package com.xmd.achievement.rpc.entity.dto;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class StandardBillingPriceResponse {

    @Schema(description = "计费价格")
    private BigDecimal billingPrice;

    @Schema(description = "续费价格")
    private BigDecimal renewalPrice;

    @Schema(description = "另购价格")
    private BigDecimal additionalPrice;
}
