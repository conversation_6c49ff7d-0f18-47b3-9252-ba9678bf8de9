package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 政策规格明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class PolicySpecDetailResponse implements Serializable {
    private static final long serialVersionUID = 1039900543509743363L;

    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Long id;

    /**
     * 业绩政策ID
     */
    @Schema(description = "业绩政策ID")
    private Long policySpecDetailId;

    /**
     * 业绩政策ID
     */
    @Schema(description = "业绩政策ID")
    private Long policyId;

    /**
     * 业绩计收节点 1=支付完成，2=生产完成
     */
    @Schema(description = "业绩计收节点 1=支付完成，2=生产完成")
    private Integer revenueNode;

    /**
     * 业绩核算比例
     */
    @Schema(description = "业绩核算比例")
    private BigDecimal achievementRatio;

    /**
     * 实发业绩提成比例
     */
    @Schema(description = "实发业绩提成比例")
    private BigDecimal commissionRatio;

    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
    private Long productId;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String productName;

    /**
     * 规格分类ID
     */
    @Schema(description = "规格分类ID")
    private Long specCategoryId;

    /**
     * 规格分类
     */
    @Schema(description = "规格分类")
    private String specCategoryName;

    /**
     * 规格ID
     */
    @Schema(description = "规格ID")
    private Long specId;

    /**
     * 规格名称
     */
    @Schema(description = "规格名称")
    private String specName;

    /**
     * 商代政策性成本新开
     */
    @Schema(description = "商代政策性成本新开")
    private BigDecimal policyCostOpen;

    /**
     * 商代政策性成本续费
     */
    @Schema(description = "商代政策性成本续费")
    private BigDecimal policyCostRenew;

    /**
     * 商代政策性成本另购
     */
    @Schema(description = "商代政策性成本另购")
    private BigDecimal policyCostAdd;

    /**
     * 商代政策性成本升级
     */
    @Schema(description = "商代政策性成本升级")
    private BigDecimal policyCostUpgrade;
}
