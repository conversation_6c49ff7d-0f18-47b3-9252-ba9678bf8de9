<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bsp-achievement</artifactId>
    <packaging>pom</packaging>
    <groupId>com.xmd.achievement</groupId>
    <name>bsp-achievement</name>
    <version>1.0.0-SNAPSHOT</version>
    <description>bsp-achievement</description>

    <modules>
        <module>achievement-dao</module>
        <module>achievement-cache</module>
        <module>achievement-util</module>
        <module>achievement-service</module>
        <module>achievement-support</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.6.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!-- springboot版本 -->
        <spring-boot.version>2.3.6.RELEASE</spring-boot.version>
        <spring.version>5.2.11.RELEASE</spring.version>
        <!-- redis-->
        <spring.redis.version>2.3.6.RELEASE</spring.redis.version>
        <!-- redisson-->
        <redisson.version>3.17.7</redisson.version>
        <!-- 升级tomcat版本为修复原版本漏洞-->
        <tomcat.version>9.0.55</tomcat.version>
        <!-- fastJson版本 -->
        <fastjson.version>1.2.83</fastjson.version>
        <!-- mysql版本 -->
        <mysql.version>8.0.29</mysql.version>
        <!-- alibaba.druid版本 -->
        <druid.version>1.2.11</druid.version>
        <!-- service版本 -->
        <service.version>1.0.0-SNAPSHOT</service.version>
        <!-- rocketMq springboot-start版本 -->
        <rocketmq.spring.boot.version>2.0.3</rocketmq.spring.boot.version>
        <!-- cat版本 -->
        <cat.version>3.0.0</cat.version>
        <!-- xxljob版本 -->
        <xxljob.version>1.0.11-RELEASE</xxljob.version>
        <!--httpmime 版本 -->
        <okhttp3.version>4.9.3</okhttp3.version>
        <!-- mybatis版本 -->
        <mybatis.version>3.5.2</mybatis.version>
        <!-- transmittable-thread-local版本-->
        <transmittable.thread.local.version>2.12.0</transmittable.thread.local.version>

        <!-- slf4j版本 -->
        <slf4j.version>1.7.32</slf4j.version>
        <!-- logback版本 -->
        <logback.version>1.2.9</logback.version>
        <!-- hutool 版本-->
        <hutool.version>5.8.11</hutool.version>
        <!-- commons-text版本 -->
        <commons-text.version>1.1</commons-text.version>
        <!-- commons-collections版本 -->
        <commons-collections.version>3.2.1</commons-collections.version>
        <!-- Lombok版本-->
        <lombok.version>1.18.24</lombok.version>
        <!-- Logback版本-->
        <logback.version>1.2.9</logback.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xmd.achievement</groupId>
                <artifactId>achievement-cache</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmd.achievement</groupId>
                <artifactId>achievement-dao</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmd.achievement</groupId>
                <artifactId>achievement-service</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmd.achievement</groupId>
                <artifactId>achievement-support</artifactId>
                <version>${service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xmd.achievement</groupId>
                <artifactId>achievement-util</artifactId>
                <version>${service.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.junit.vintage</groupId>
                        <artifactId>junit-vintage-engine</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <optional>true</optional>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.redis.version}</version>
            </dependency>


            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>2.3</version>
            </dependency>


            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.dianping.cat</groupId>
                <artifactId>cat-client</artifactId>
                <version>${cat.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <optional>true</optional>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable.thread.local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.ce</groupId>
                <artifactId>spring-boot-starter-xxljob</artifactId>
                <version>${xxljob.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>2.0.5</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>private-repos-releases</id>
            <url>https://test-omo.aiyouyi.cn/nexus/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>private-repos-snapshots</id>
            <url>https://test-omo.aiyouyi.cn/nexus/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>private-repos</id>
            <name>private repos</name>
            <url>https://test-omo.aiyouyi.cn/nexus/repository/maven-public/</url>
        </repository>
    </repositories>
    <build>
    </build>

</project>