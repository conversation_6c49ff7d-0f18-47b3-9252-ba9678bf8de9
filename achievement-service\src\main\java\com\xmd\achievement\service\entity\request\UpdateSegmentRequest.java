package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25/12:36
 * @since 1.0
 */
@Data
public class UpdateSegmentRequest implements Serializable {
    @Schema(description = "业绩分段id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业绩分段id不能为空")
    private Long segmentId;

    @Schema(description = "业绩分段名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "业绩分段名称")
    @NotBlank(message = "业绩分段名称不能为空")
    private String segmentName;

    @Schema(description = "最小值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "最小值不能为空")
    private Integer minValue;

    @Schema(description = "最大值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "最大值不能为空")
    private Integer maxValue;
}
