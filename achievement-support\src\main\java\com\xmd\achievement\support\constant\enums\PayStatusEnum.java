package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Getter
public enum PayStatusEnum {
    WAIT_PAY(1, "待付款"),
    /**
     * 分期
     */
    PAYING(2, "付款中"),

    FINISH_PAY(3,"已付款"),
    NON_PAY(4,"未付款"),
    PAY_FAIL(5,"付款失败"),
    PARTIAL_REFUND(6,"部分退款"),
    REFUNDED(7,"已退款"),
    PARTIAL_PAY(8, "部分支付");

    private final Integer code;
    private final String msg;

    PayStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
