package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25/12:35
 * @since 1.0
 */
@Data
public class QuerySegmentListResponse implements Serializable {
    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "业绩分段id")
    private Long segmentId;

    @Schema(description = "业绩分段名称")
    private String segmentName;

    @Schema(description = "最小值")
    private Integer minValue;

    @Schema(description = "最大值")
    private Integer maxValue;
}
