package com.xmd.achievement.handler.achievement;

import cn.hutool.core.util.ObjectUtil;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.dao.repository.*;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.OrgBusinessResponse;
import com.xmd.achievement.rpc.entity.dto.OrgFunctionResp;
import com.xmd.achievement.rpc.entity.dto.OrgInfoNodeResponse;
import com.xmd.achievement.rpc.entity.dto.OrgInfoResponse;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.ISegmentService;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.OrganizationTypeEnum;
import com.xmd.achievement.support.constant.enums.SaasEnum;
import com.xmd.achievement.util.date.DateUtils;
import com.xmd.achievement.util.enums.CustomerTypeEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.xmd.achievement.util.constant.UtilConstant.REQUEST_SOURCE;
import static com.xmd.achievement.util.date.DateUtils.*;

/**
 * 机构日报月报数据处理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class ReportHandler {

    @Autowired
    private IBusinessMonthService businessMonthService;
    @Autowired
    private IAchievementProductDetailRepository achievementProductDetailRepository;
    @Autowired
    private IOrganizationDailyReportRepository organizationDailyReportRepository;
    @Autowired
    private IOrganizationMonthlyReportRepository organizationMonthlyReportRepository;
    @Resource
    private IBusinessMonthRepository businessMonthRepository;
    @Autowired
    private ISegmentService segmentService;
    @Autowired
    private InnerService innerService;
    @Autowired
    private ISaleTaskRepository saleTaskRepository;

    private static final String ORGANIZATION_NAME = "中企";
    private static final String ORGANIZATION_NAME_PREFIX = "中企/";

    @Transactional(rollbackFor = Exception.class)
    @Lock("'processMonthlyReport_' + #startTime.getTime()")
    public void processMonthlyReport(Date startTime) {
        log.info("统计业绩，生成月报开始");
        List<OrganizationMonthlyReportModel> deptResult = Lists.newArrayList();
        List<OrganizationMonthlyReportModel> companyResult = Lists.newArrayList();
        List<OrganizationMonthlyReportModel> regionResult = Lists.newArrayList();
        BusinessMonthModel monthInfo;
        if (ObjectUtil.isEmpty(startTime)) {
            startTime = new Date();
            monthInfo = businessMonthService.getMonthInfo(new Date());
        } else {
            monthInfo = businessMonthService.getMonthInfo(startTime);
        }
        if (ObjectUtil.isEmpty(monthInfo)) {
            log.info("统计业绩，生成月报失败：当前日期对应商务月未生成");
            return;
        }
        // 查询当前商务月的业绩
        List<AchievementProductDetailModel> monthModels = achievementProductDetailRepository.listByBusinessMonthId(monthInfo.getMonthId());
        // 按照 deptId 分组（只处理 deptId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> monthGroupedByDeptId = monthModels.stream()
                .filter(product -> product.getDeptId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getDeptId));

        // 按照 divisionId 分组（只处理 divisionId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> monthGroupedByDivisionId = monthModels.stream()
                .filter(product -> product.getDivisionId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getDivisionId));
        // 处理部门
        generatingMonthlyEntity(monthGroupedByDeptId, monthInfo, deptResult, OrganizationTypeEnum.DEPT.getCode(), startTime);
        // 处理事业部
        generatingMonthlyEntity(monthGroupedByDivisionId, monthInfo,deptResult, OrganizationTypeEnum.DIVISION.getCode(), startTime);

        // 按照 CompanyId 分组（只处理 CompanyId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> monthGroupedByCompanyId = monthModels.stream()
                .filter(product -> product.getCompanyId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getCompanyId));
        generatingCompanyOrRegionMonthlyEntity(monthGroupedByCompanyId, monthInfo, companyResult, OrganizationTypeEnum.BRANCH_COMPANY.getCode(), startTime);

        Map<Long, List<AchievementProductDetailModel>> monthGroupedByRegionId = monthModels.stream()
                .filter(product -> product.getRegionId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getRegionId));
        generatingCompanyOrRegionMonthlyEntity(monthGroupedByRegionId, monthInfo, regionResult, OrganizationTypeEnum.REGION.getCode(), startTime);

        //String preMonth = getPreMonth(startTime);
        //删除月报
        organizationMonthlyReportRepository.deleteByMonth(monthInfo.getMonth());
        //新增
        organizationMonthlyReportRepository.batchInsertOrUpdate(deptResult);
        organizationMonthlyReportRepository.batchInsertOrUpdate(companyResult);
        organizationMonthlyReportRepository.batchInsertOrUpdate(regionResult);
        log.info("统计业绩，生成月报完成");
    }



    @Transactional(rollbackFor = Exception.class)
    @Lock("'processDailyReport_' + #currentDate")
    public void processDailyReport(String currentDate) {
        log.info("统计业绩，生成日报开始");
        List<OrganizationDailyReportModel> deptResult = Lists.newArrayList();
        List<OrganizationDailyReportModel> companyResult = Lists.newArrayList();
        List<OrganizationDailyReportModel> regionResult = Lists.newArrayList();
        // 获取最新的商务月
        BusinessMonthModel currentBusinessMonth = businessMonthRepository.selectCurrentBusinessMonth();
        BusinessMonthModel monthInfo;
        if (ObjectUtil.isEmpty(currentDate)) {
            // 没传参数走正常逻辑
            monthInfo = businessMonthService.getMonthInfo(new Date());
            if (ObjectUtil.isEmpty(monthInfo)) {
                log.info("统计业绩，生成日报失败：当前日期对应商务月未生成");
                return;
            }
            Date startOfDay = getStartOfDay();
            Date endOfDay = getEndOfDay();
            // 查询当日的业绩
            List<AchievementProductDetailModel> models = achievementProductDetailRepository.listByStatisticsTime(startOfDay, endOfDay);

            Date businessMonthStart = monthInfo.getStartDate();

            // 商务月的起始日期 - 今天的截止日期
            List<AchievementProductDetailModel> monthModels = achievementProductDetailRepository.listByStatisticsTime(businessMonthStart, endOfDay);
            process(models, monthInfo, deptResult, startOfDay, companyResult, regionResult, monthModels);

        } else {
            Date endOfDay = null;
            // 传参数需要拆解日期
            if (currentBusinessMonth.getMonth().equals(currentDate)) {
                //如果传的是当前商务月
                monthInfo = currentBusinessMonth;
                endOfDay = getEndOfDay();
            } else {
                //如果传的是别的商务月
                monthInfo = businessMonthRepository.selectBusinessMonthByCurrentDate(currentDate);
                endOfDay = monthInfo.getEndDate();
            }
            Date startOfDay = monthInfo.getStartDate();
            // 查询当日的业绩
            List<AchievementProductDetailModel> models = achievementProductDetailRepository.listByStatisticsTime(startOfDay, endOfDay);
            // 商务月的起始日期 - 今天的截止日期
            List<AchievementProductDetailModel> monthModels = achievementProductDetailRepository.listByStatisticsTime(startOfDay, endOfDay);
            List<Date[]> dayTimeRanges = getDayTimeRanges(startOfDay, endOfDay);
            for (Date[] range : dayTimeRanges) {
                Date start = range[0];
                Date end = range[1];
                List<AchievementProductDetailModel> dailyModels = models.stream()
                        .filter(v -> !v.getStatisticsTime().before(start) && !v.getStatisticsTime().after(end))
                        .collect(Collectors.toList());

                process(dailyModels, monthInfo, deptResult, start, companyResult, regionResult, monthModels);
            }
        }

        organizationDailyReportRepository.batchInsertOrUpdate(deptResult);
        log.info("统计业绩，生成日报完成");
    }

    private void process(List<AchievementProductDetailModel> dailyModels, BusinessMonthModel monthInfo, List<OrganizationDailyReportModel> deptResult,
                         Date startOfDay, List<OrganizationDailyReportModel> companyResult,
                         List<OrganizationDailyReportModel> regionResult, List<AchievementProductDetailModel> monthModels) {
        // 按照 deptId 分组（只处理 deptId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> monthGroupedByDeptId = monthModels.stream()
                .filter(product -> product.getDeptId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getDeptId));

        // 按照 divisionId 分组（只处理 divisionId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> monthGroupedByDivisionId = monthModels.stream()
                .filter(product -> product.getDivisionId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getDivisionId));

        // 按照 deptId 分组（只处理 deptId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> monthGroupedByCompanyId = monthModels.stream()
                .filter(product -> product.getCompanyId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getCompanyId));

        // 按照 deptId 分组（只处理 deptId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> monthGroupedByRegionId = monthModels.stream()
                .filter(product -> product.getRegionId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getRegionId));

        // 对 业绩进行分组，按照部门id或者事业部id
        // 按照 deptId 分组（只处理 deptId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> groupedByDeptId = dailyModels.stream()
                .filter(product -> product.getDeptId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getDeptId));

        // 按照 divisionId 分组（只处理 divisionId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> groupedByDivisionId = dailyModels.stream()
                .filter(product -> product.getDivisionId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getDivisionId));
        // 按照 companyId 分组（只处理 companyId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> groupedByCompanyId = dailyModels.stream()
                .filter(product -> product.getCompanyId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getCompanyId));

        // 按照 regionId 分组（只处理 regionId 不为空的对象）
        Map<Long, List<AchievementProductDetailModel>> groupedByRegionId = dailyModels.stream()
                .filter(product -> product.getRegionId() != null)
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getRegionId));
        // 处理部门(商务组)
        generatingDailyEntity(groupedByDeptId, monthInfo, monthGroupedByDeptId, deptResult, OrganizationTypeEnum.DEPT.getCode(), startOfDay);
        // 处理事业部
        generatingDailyEntity(groupedByDivisionId, monthInfo, monthGroupedByDivisionId, deptResult, OrganizationTypeEnum.DIVISION.getCode(), startOfDay);

        // 处理公司
        generatingDailyEntity(groupedByCompanyId, monthInfo, monthGroupedByCompanyId, deptResult, OrganizationTypeEnum.BRANCH_COMPANY.getCode(), startOfDay);

        // 处理区域日报
        generatingDailyEntity(groupedByRegionId, monthInfo, monthGroupedByRegionId, deptResult, OrganizationTypeEnum.REGION.getCode(), startOfDay);

    }

    private void generatingCompanyOrRegionMonthlyEntity(Map<Long, List<AchievementProductDetailModel>> reportGroupedByOrgId, BusinessMonthModel monthInfo, List<OrganizationMonthlyReportModel> result, Integer type, Date startTime) {
        for (Map.Entry<Long, List<AchievementProductDetailModel>> reports : reportGroupedByOrgId.entrySet()) {
            Long orgId = reports.getKey();
            List<AchievementProductDetailModel> reportModels = reports.getValue();
            if (ObjectUtil.isEmpty(reportModels)) {
                continue;
            }
            OrganizationMonthlyReportModel monthlyReportModel = new OrganizationMonthlyReportModel();
            monthlyReportModel.setBusinessMonthId(monthInfo.getMonthId());
            monthlyReportModel.setBusinessMonth(monthInfo.getMonth());
            monthlyReportModel.setOrganizationId(orgId);
            monthlyReportModel.setCurrentDayMonth(DateUtils.getCurrentMonth(startTime));
            monthlyReportModel.setOrganizationType(type);

            //String preMonth = getPreMonth(startTime);
            String preMonth = getPreviousOrTargetMonth(startTime);
            OrgBusinessResponse orgBusiness = innerService.getOrgBusiness(orgId, preMonth, REQUEST_SOURCE);
            if (!ObjectUtil.isEmpty(orgBusiness)) {
                // 机构领导ID 机构领导名称 任职本机构日期
                monthlyReportModel.setOrganizationLeaderId(orgBusiness.getEid());
                monthlyReportModel.setOrganizationLeaderName(orgBusiness.getLeader());
                monthlyReportModel.setAppointmentDate(orgBusiness.getLeaderDate());
                // 考核部门数 部门数 商代数量
                monthlyReportModel.setExaminationDeptCount(orgBusiness.getTcSubOrgCounts());
                monthlyReportModel.setDeptCount(new BigDecimal(orgBusiness.getSubOrgCounts()));
                monthlyReportModel.setBusinessRepresentativeCount(Long.valueOf(orgBusiness.getSalesCounts()));
                OrgFunctionResp orgFunction = innerService.getOrgFunctionById(String.valueOf(orgBusiness.getOrgFunction()), REQUEST_SOURCE);
                // 体系
                if (ObjectUtil.isNotEmpty(orgFunction)) {
                    monthlyReportModel.setSystemCategory(orgFunction.getName());
                    monthlyReportModel.setSystemId(orgFunction.getOrgFunctionId());
                }
                // 市场类别
                monthlyReportModel.setMarketCategoryId(ObjectUtil.isEmpty(orgBusiness.getMarketCategoryId()) ? null : Long.valueOf(orgBusiness.getMarketCategoryId()));
            }

            OrgInfoResponse orgInfo = innerService.queryOrgInfoById(orgId);
            if (ObjectUtil.isEmpty(orgInfo)) {
                log.warn("调用EMP接口获取组织机构信息为空，组织id：{}", orgId);
            } else {
                // 机构名称
                monthlyReportModel.setOrganizationName(ObjectUtil.isEmpty(orgInfo.getShortName()) ? ""
                        : orgInfo.getShortName().substring(orgInfo.getShortName().indexOf(ORGANIZATION_NAME_PREFIX) + NumberConstants.INTEGER_VALUE_3));
            }
            // 月签单金额
            BigDecimal signAmount = reportModels.stream()
                    .map(AchievementProductDetailModel::getPayableAmount)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setMonthlySigningAmount(signAmount);
            // 累加所有 净现金 月净现金
            BigDecimal totalNetCash = reportModels.stream()
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setMonthlyNetCash(totalNetCash);

            // 累加所有 月saas净现金
            BigDecimal totalSaasNetCash = reportModels.stream()
                    .filter(data->SaasEnum.YES.getCode().equals(data.getIsSaas()))
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setSaasNetCash(totalSaasNetCash);
            // 月半净现金
            // 月半净现金
            Date midDate = monthInfo.getMidDate();
            BigDecimal totalMidNetCash = reportModels.stream()
                    .filter(v -> v.getStatisticsTime().compareTo(midDate) <= NumberConstants.INTEGER_VALUE_0)
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setMonthlyHalfNetCash(totalMidNetCash);
            // 基本任务
            //基本任务  任务完成率
            SaleTaskModel saleTaskModel = saleTaskRepository.selectSaleTaskByOrgId(orgId, monthInfo.getMonth());
            if (ObjectUtil.isNotEmpty(saleTaskModel)) {
                BigDecimal basicTask = saleTaskModel.getBasicTask();
                if (BigDecimal.ZERO.compareTo(basicTask) != NumberConstants.INTEGER_VALUE_0) {
                    monthlyReportModel.setBasicTask(basicTask);
                    monthlyReportModel.setTaskCompletionRate(totalNetCash.divide(basicTask, NumberConstants.INTEGER_VALUE_5, RoundingMode.HALF_UP));
                }
            }
//            BigDecimal basicTask = reportModels.stream()
//                    .map(OrganizationMonthlyReportModel::getBasicTask)
//                    .filter(ObjectUtil::isNotEmpty)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            monthlyReportModel.setBasicTask(basicTask);
//            // 任务完成率
//            if (BigDecimal.ZERO.compareTo(basicTask) != NumberConstants.INTEGER_VALUE_0) {
//                monthlyReportModel.setTaskCompletionRate(totalNetCash.divide(basicTask, NumberConstants.INTEGER_VALUE_5, BigDecimal.ROUND_HALF_UP));
//            }
            // 总出单人数 todo 是否需要考虑订单状态为已完成的
            Long orderSignCount = reportModels.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType()))
                    .map(AchievementProductDetailModel::getBusinessId)
                    .distinct()
                    .count();
            monthlyReportModel.setTotalSignPersonCount(orderSignCount);
            // 出单率
            Long businessRepresentativeCount = monthlyReportModel.getBusinessRepresentativeCount();
            if (null != businessRepresentativeCount && !Objects.equals(businessRepresentativeCount, NumberConstants.LONG_VALUE_0)) {
                // 将 Long 转换为 BigDecimal
                BigDecimal bigDecimalDividend = new BigDecimal(orderSignCount);
                BigDecimal bigDecimalDivisor = new BigDecimal(businessRepresentativeCount);
                // 执行除法运算，保留 5 位小数并四舍五入
                monthlyReportModel.setSignRate(bigDecimalDividend.divide(bigDecimalDivisor, NumberConstants.INTEGER_VALUE_5, RoundingMode.HALF_UP));
            }
            if(null != orgBusiness){
                // 正式骨干数量
                monthlyReportModel.setFormalBusinessRepresentativeCount(Long.valueOf(orgBusiness.getGgCounts()));
                // 在岗人均净现金
                Long businessCount = monthlyReportModel.getBusinessRepresentativeCount();
                if (!Objects.equals(businessCount, NumberConstants.LONG_VALUE_0)) {
                    BigDecimal bigDecimalDividend = new BigDecimal(businessCount);
                    // 执行除法运算，保留 2 位小数并四舍五入
                    monthlyReportModel.setNetCashPerEmployee(totalNetCash.divide(bigDecimalDividend, NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP));
                }
            }

            // 非续费客户数
            Long noRenewCount = reportModels.stream()
                    .filter(product -> product.getPaymentTime().compareTo(monthInfo.getStartDate()) >= NumberConstants.INTEGER_VALUE_0
                            && product.getPaymentTime().compareTo(monthInfo.getEndDate()) <= NumberConstants.INTEGER_VALUE_0
                            && !ObjectUtil.equals(SaleTypeEnum.RENEW.getType(), product.getSaleType()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            monthlyReportModel.setNonRenewalCustomerCount(noRenewCount);
            // 月新客户数
            Long monthNewCustomerCount = reportModels.stream()
                    .filter(product -> ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getStatus()) && ObjectUtil.equals(CustomerTypeEnum.NEW.getCustomerType(), product.getCustomerType()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            monthlyReportModel.setMonthlyNewCustomerCount(monthNewCustomerCount);
            // 月网站新客户数
            Long uniqueNewWebCustomerCount = reportModels.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType()) && ObjectUtil.equals(CustomerTypeEnum.NEW.getCustomerType(), product.getCustomerType())
                            && ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getSiteFlag()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            monthlyReportModel.setMonthlyWebsiteNewCustomerCount(uniqueNewWebCustomerCount);
            // 月新网站数
            Long newWebCount = reportModels.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType())
                            && ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getSiteFlag()))
                    .count();
            monthlyReportModel.setMonthlyNewWebsiteCount(newWebCount);
            // 部门提成业绩
            BigDecimal deptCommission = reportModels.stream()
                    .map(AchievementProductDetailModel::getDeptCommission)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setDeptCommission(deptCommission);
            // 分公司提成业绩
            BigDecimal companyCommission = reportModels.stream()
                    .map(AchievementProductDetailModel::getBranchCommission)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setBranchCommission(companyCommission);
            // 月 网站净现金
            BigDecimal totalWebNetCash = reportModels.stream()
                    .filter(v -> ObjectUtil.equal(NumberConstants.INTEGER_VALUE_1, v.getSiteFlag()))
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setMonthlyWebsiteNetCash(totalWebNetCash);
            // 业绩段
            AchievementSegmentModel segment = segmentService.getSegment(totalNetCash);
            if (null != segment) {
                monthlyReportModel.setAchievementSegment(segment.getSegmentName());
                monthlyReportModel.setAchievementSegmentId(segment.getSegmentId());
            }
            // 月老客户数
            Long monthOldCustomerCount = reportModels.stream()
                    .filter(product -> ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getStatus()) && ObjectUtil.equals(CustomerTypeEnum.OLD.getCustomerType(), product.getCustomerType()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            monthlyReportModel.setMonthlyOldCustomerCount(monthOldCustomerCount);
            result.add(monthlyReportModel);
        }
    }

    private  void generatingMonthlyEntity(Map<Long, List<AchievementProductDetailModel>> groupedById, BusinessMonthModel monthInfo, List<OrganizationMonthlyReportModel> result, int type, Date startTime) {
        groupedById.forEach((id, achievementProductDetailList) -> {
            OrganizationMonthlyReportModel monthlyReportModel = new OrganizationMonthlyReportModel();
            monthlyReportModel.setBusinessMonthId(monthInfo.getMonthId());
            monthlyReportModel.setBusinessMonth(monthInfo.getMonth());
            monthlyReportModel.setOrganizationId(id);
            monthlyReportModel.setCurrentDayMonth(DateUtils.getCurrentMonth(startTime));
            monthlyReportModel.setOrganizationType(type);

            //String preMonth = getPreMonth(startTime);
            String preMonth = getPreviousOrTargetMonth(startTime);

            OrgBusinessResponse orgBusiness = innerService.getOrgBusiness(id, preMonth, REQUEST_SOURCE);
            if (!ObjectUtil.isEmpty(orgBusiness)) {
                log.info("当前机构{}查询机构信息为空", id);

                // 机构领导ID 机构领导名称 任职本机构日期
                monthlyReportModel.setOrganizationLeaderId(orgBusiness.getEid());
                monthlyReportModel.setOrganizationLeaderName(orgBusiness.getLeader());
                monthlyReportModel.setAppointmentDate(orgBusiness.getLeaderDate());
                // 考核部门数 部门数 商代数量
                monthlyReportModel.setExaminationDeptCount(orgBusiness.getTcSubOrgCounts());
                monthlyReportModel.setDeptCount(new BigDecimal(orgBusiness.getSubOrgCounts()));
                monthlyReportModel.setBusinessRepresentativeCount(Long.valueOf(orgBusiness.getSalesCounts()));
                OrgFunctionResp orgFunction = innerService.getOrgFunctionById(String.valueOf(orgBusiness.getOrgFunction()), REQUEST_SOURCE);
                // 体系
                if (ObjectUtil.isNotEmpty(orgFunction)) {
                    monthlyReportModel.setSystemCategory(orgFunction.getName());
                    monthlyReportModel.setSystemId(orgFunction.getOrgFunctionId());
                }
                // 市场类别
                monthlyReportModel.setMarketCategoryId(ObjectUtil.isEmpty(orgBusiness.getMarketCategoryId()) ? null : Long.valueOf(orgBusiness.getMarketCategoryId()));
            }

            OrgInfoResponse orgInfo = innerService.queryOrgInfoById(id);
            if (ObjectUtil.isEmpty(orgInfo)) {
                log.warn("调用EMP接口获取组织机构信息为空，组织id：{}", id);
            } else {
                // 机构名称
                monthlyReportModel.setOrganizationName(ObjectUtil.isEmpty(orgInfo.getShortName()) ? ""
                        : orgInfo.getShortName().substring(orgInfo.getShortName().indexOf(ORGANIZATION_NAME_PREFIX) + NumberConstants.INTEGER_VALUE_3));
            }
//            // 根据主辅分单人过滤
//            List<AchievementProductDetailModel> uniqueAchievement = new ArrayList<>(achievementProductDetailList.stream()
//                    .collect(Collectors.toMap(
//                            AchievementProductDetailModel::getOrderId,
//                            product -> product,
//                            (existing, replacement) -> Objects.equals(existing.getMainSplitPerson(), MainSplitPersonEnum.MAIN.getCode()) ? existing : Objects.equals(replacement.getMainSplitPerson(), MainSplitPersonEnum.MAIN.getCode()) ? replacement : existing
//                    ))
//                    .values());
            // 月签单金额
            BigDecimal signAmount = achievementProductDetailList.stream()
                    .map(AchievementProductDetailModel::getPayableAmount)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setMonthlySigningAmount(signAmount);
            // 月净现金( 累加所有 净现金 )
            BigDecimal totalNetCash = achievementProductDetailList.stream()
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setMonthlyNetCash(totalNetCash);

            // 月saas净现金( 累加所有 净现金 )
            BigDecimal totalSaasNetCash = achievementProductDetailList.stream()
                    .filter(data->SaasEnum.YES.getCode().equals(data.getIsSaas()))
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setSaasNetCash(totalSaasNetCash);
            // 月半净现金
            Date midDate = monthInfo.getMidDate();
            BigDecimal totalMidNetCash = achievementProductDetailList.stream()
                    .filter(v -> v.getStatisticsTime().compareTo(midDate) <= NumberConstants.INTEGER_VALUE_0)
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setMonthlyHalfNetCash(totalMidNetCash);
            //基本任务  任务完成率
            SaleTaskModel saleTaskModel = saleTaskRepository.selectSaleTaskByOrgId(id, monthInfo.getMonth());
            if (ObjectUtil.isNotEmpty(saleTaskModel)) {
                BigDecimal basicTask = saleTaskModel.getBasicTask();
                if (BigDecimal.ZERO.compareTo(basicTask) != NumberConstants.INTEGER_VALUE_0) {
                    monthlyReportModel.setBasicTask(basicTask);
                    monthlyReportModel.setTaskCompletionRate(totalNetCash.divide(basicTask, NumberConstants.INTEGER_VALUE_5, RoundingMode.HALF_UP));
                }
            }
            // 总出单人数 todo 是否需要考虑订单状态为已完成的
            Long orderSignCount = achievementProductDetailList.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType()))
                    .map(AchievementProductDetailModel::getBusinessId)
                    .distinct()
                    .count();
            monthlyReportModel.setTotalSignPersonCount(orderSignCount);
            // 出单率
            Long businessRepresentativeCount = monthlyReportModel.getBusinessRepresentativeCount();
            if (null != businessRepresentativeCount && !Objects.equals(businessRepresentativeCount, NumberConstants.LONG_VALUE_0)) {
                // 将 Long 转换为 BigDecimal
                BigDecimal bigDecimalDividend = new BigDecimal(orderSignCount);
                BigDecimal bigDecimalDivisor = new BigDecimal(businessRepresentativeCount);
                // 执行除法运算，保留 5 位小数并四舍五入
                monthlyReportModel.setSignRate(bigDecimalDividend.divide(bigDecimalDivisor, NumberConstants.INTEGER_VALUE_5, RoundingMode.HALF_UP));
            }
            if(null != orgBusiness ){
                // 正式骨干数
                monthlyReportModel.setFormalBusinessRepresentativeCount(Long.valueOf(orgBusiness.getGgCounts()));
                // 在岗人均净现金
                Long businessCount = monthlyReportModel.getBusinessRepresentativeCount();
                if (!Objects.equals(businessCount, NumberConstants.LONG_VALUE_0)) {
                    BigDecimal bigDecimalDividend = new BigDecimal(businessCount);
                    // 执行除法运算，保留 2 位小数并四舍五入
                    monthlyReportModel.setNetCashPerEmployee(totalNetCash.divide(bigDecimalDividend, NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP));
                }
            }

            // 非续费客户数
            Long noRenewCount = achievementProductDetailList.stream()
                    .filter(product -> product.getPaymentTime().compareTo(monthInfo.getStartDate()) >= NumberConstants.INTEGER_VALUE_0
                            && product.getPaymentTime().compareTo(monthInfo.getEndDate()) <= NumberConstants.INTEGER_VALUE_0
                            && !ObjectUtil.equals(SaleTypeEnum.RENEW.getType(), product.getSaleType()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            monthlyReportModel.setNonRenewalCustomerCount(noRenewCount);
            // 月新客户数
            Long monthNewCustomerCount = achievementProductDetailList.stream()
                    .filter(product -> ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getStatus()) && ObjectUtil.equals(CustomerTypeEnum.NEW.getCustomerType(), product.getCustomerType()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            monthlyReportModel.setMonthlyNewCustomerCount(monthNewCustomerCount);
            // 月网站新客户数
            Long uniqueNewWebCustomerCount = achievementProductDetailList.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType()) && ObjectUtil.equals(CustomerTypeEnum.NEW.getCustomerType(), product.getCustomerType())
                            && ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getSiteFlag()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            monthlyReportModel.setMonthlyWebsiteNewCustomerCount(uniqueNewWebCustomerCount);
            // 月新网站数
            Long newWebCount = achievementProductDetailList.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType())
                            && ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getSiteFlag()))
                    .count();
            monthlyReportModel.setMonthlyNewWebsiteCount(newWebCount);
            // 部门提成业绩
            BigDecimal deptCommission = achievementProductDetailList.stream()
                    .map(AchievementProductDetailModel::getDeptCommission)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setDeptCommission(deptCommission);
            // 分公司提成业绩
            BigDecimal companyCommission = achievementProductDetailList.stream()
                    .map(AchievementProductDetailModel::getBranchCommission)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setBranchCommission(companyCommission);
            // 月 网站净现金
            BigDecimal totalWebNetCash = achievementProductDetailList.stream()
                    .filter(v -> ObjectUtil.equal(NumberConstants.INTEGER_VALUE_1, v.getSiteFlag()))
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyReportModel.setMonthlyWebsiteNetCash(totalWebNetCash);
            // 业绩段
            AchievementSegmentModel segment = segmentService.getSegment(totalNetCash);
            if (null != segment) {
                monthlyReportModel.setAchievementSegment(segment.getSegmentName());
                monthlyReportModel.setAchievementSegmentId(segment.getSegmentId());
            }
            // 月老客户数
            Long monthOldCustomerCount = achievementProductDetailList.stream()
                    .filter(product -> ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getStatus()) && ObjectUtil.equals(CustomerTypeEnum.OLD.getCustomerType(), product.getCustomerType()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            monthlyReportModel.setMonthlyOldCustomerCount(monthOldCustomerCount);
            result.add(monthlyReportModel);
        });
    }

    private  String getPreMonth(Date startTime) {
        LocalDateTime localDateTime = com.xmd.achievement.web.util.DateUtils.convertToLocalDateTime(startTime);
        int month = localDateTime.getMonthValue();
        int currentMonth = com.xmd.achievement.web.util.DateUtils.convertToLocalDateTime(new Date()).getMonthValue();
        String preMonth =  month == currentMonth ?  getPreviousMonth(startTime) : getCurrentMonth(startTime);
        return preMonth;
    }

    private void generatingDailyEntity(Map<Long, List<AchievementProductDetailModel>> groupedById, BusinessMonthModel monthInfo, Map<Long
            , List<AchievementProductDetailModel>> monthGroupedById, List<OrganizationDailyReportModel> result, int type, Date startOfDay) {
        groupedById.forEach((id, achievementProductDetailList) -> {
            OrganizationDailyReportModel dailyReportModel = new OrganizationDailyReportModel();
            dailyReportModel.setBusinessMonthId(monthInfo.getMonthId());
            dailyReportModel.setBusinessMonth(monthInfo.getMonth());
            dailyReportModel.setOrganizationId(id);
            dailyReportModel.setOrganizationType(type);
            dailyReportModel.setCurrentDayDate(startOfDay);

            String preMonth = getPreviousOrTargetMonth(startOfDay);
            OrgBusinessResponse orgBusiness = innerService.getOrgBusiness(id, preMonth, REQUEST_SOURCE);
            if (ObjectUtil.isEmpty(orgBusiness)) {
                log.info("当前机构{}查询机构信息为空", id);
            } else {
                // 市场类别
                dailyReportModel.setMarketCategoryId(ObjectUtil.isEmpty(orgBusiness.getMarketCategoryId()) ? null : Long.valueOf(orgBusiness.getMarketCategoryId()));
                // 机构名称
                dailyReportModel.setOrganizationName(ObjectUtil.isEmpty(orgBusiness.getShortName()) ? ""
                        : orgBusiness.getShortName().substring(orgBusiness.getShortName().indexOf(ORGANIZATION_NAME_PREFIX) + NumberConstants.INTEGER_VALUE_3));
                OrgFunctionResp orgFunction = innerService.getOrgFunctionById(String.valueOf(orgBusiness.getOrgFunction()), REQUEST_SOURCE);
                // 体系
                if (ObjectUtil.isNotEmpty(orgFunction)) {
                    dailyReportModel.setSystemCategory(orgFunction.getName());
                    dailyReportModel.setSystemId(orgFunction.getOrgFunctionId());
                }
            }

            List<AchievementProductDetailModel> monthAchievementList = monthGroupedById.get(id);
            // 签单金额 = 应付金额
            BigDecimal signAmount = achievementProductDetailList.stream()
                    .map(AchievementProductDetailModel::getPayableAmount)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dailyReportModel.setDailySigningAmount(signAmount);
            // 日净现金 (累加所有 净现金)
            BigDecimal totalNetCash = achievementProductDetailList.stream()
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dailyReportModel.setDailyNetCash(totalNetCash);
            //saas日淨現金
            BigDecimal totalSaasNetCash = achievementProductDetailList.stream()
                    .filter(data -> SaasEnum.YES.getCode().equals(data.getIsSaas()))
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dailyReportModel.setSaasNetCash(totalSaasNetCash);
            // 月净现金
            BigDecimal totalMonthNetCash = monthAchievementList.stream()
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dailyReportModel.setMonthlyNetCash(totalMonthNetCash);
            // 月签单金额
            BigDecimal monthLySignAmount = monthAchievementList.stream()
                    .map(AchievementProductDetailModel::getPayableAmount)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dailyReportModel.setMonthlySigningAmount(monthLySignAmount);
            // 基本任务 任务完成率
            SaleTaskModel saleTaskModel = saleTaskRepository.selectSaleTaskByOrgId(id, monthInfo.getMonth());
            if (ObjectUtil.isNotEmpty(saleTaskModel)) {
                BigDecimal basicTask = saleTaskModel.getBasicTask();
                if (BigDecimal.ZERO.compareTo(basicTask) != NumberConstants.INTEGER_VALUE_0) {
                    dailyReportModel.setBasicTask(basicTask);
                    dailyReportModel.setTaskCompletionRate(totalMonthNetCash.divide(basicTask, NumberConstants.INTEGER_VALUE_5, RoundingMode.HALF_UP));
                }
            }
            // 网站净现金
            BigDecimal totalWebNetCash = achievementProductDetailList.stream()
                    .filter(v -> ObjectUtil.equal(NumberConstants.INTEGER_VALUE_1, v.getSiteFlag()))
                    .map(AchievementProductDetailModel::getNetCash)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dailyReportModel.setWebsiteNetCash(totalWebNetCash);
            // 日新客户数
            Long dailyUniqueNewCustomerCount = achievementProductDetailList.stream()
                    .filter(product -> ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getStatus()) && ObjectUtil.equals(CustomerTypeEnum.NEW.getCustomerType(), product.getCustomerType()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            dailyReportModel.setDailyNewCustomerCount(dailyUniqueNewCustomerCount);
            // 月新客户数
            Long uniqueNewCustomerCount = monthAchievementList.stream()
                    .filter(product -> ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getStatus()) && ObjectUtil.equals(CustomerTypeEnum.NEW.getCustomerType(), product.getCustomerType()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            dailyReportModel.setMonthlyNewCustomerCount(uniqueNewCustomerCount);
            // 日网站 新客户数
            Long dailyUniqueNewWebCustomerCount = achievementProductDetailList.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType()) && ObjectUtil.equals(CustomerTypeEnum.NEW.getCustomerType(), product.getCustomerType())
                            && ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getSiteFlag()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            dailyReportModel.setDailyWebsiteNewCustomerCount(dailyUniqueNewWebCustomerCount);
            // 月 网站 新客户数
            Long uniqueNewWebCustomerCount = monthAchievementList.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType()) && ObjectUtil.equals(CustomerTypeEnum.NEW.getCustomerType(), product.getCustomerType())
                            && ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getSiteFlag()))
                    .map(AchievementProductDetailModel::getCustomerId)
                    .distinct()
                    .count();
            dailyReportModel.setMonthlyWebsiteNewCustomerCount(uniqueNewWebCustomerCount);
            // 日新增网站数
            Long dailyNewWebCount = achievementProductDetailList.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType())
                            && ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getSiteFlag()))
                    .count();
            dailyReportModel.setDailyNewWebsiteCount(dailyNewWebCount);
            // 月新增网站数
            Long newWebCount = monthAchievementList.stream()
                    .filter(product -> ObjectUtil.equals(SaleTypeEnum.NEW_OPEN.getType(), product.getSaleType())
                            && ObjectUtil.equals(NumberConstants.INTEGER_VALUE_1, product.getSiteFlag()))
                    .count();
            dailyReportModel.setMonthlyNewWebsiteCount(newWebCount);
            result.add(dailyReportModel);
        });
    }

    /**
     * 根据 companyGroup 或者 regionGroup 对 Report 进行分组
     */
    private static Map<Long, List<OrganizationDailyReportModel>> groupReportsByCompanyOrRegion(Map<Long, List<Long>> group, List<OrganizationDailyReportModel> reports) {
        Map<Long, List<OrganizationDailyReportModel>> reportGroupedByCompany = new HashMap<>();

        for (Map.Entry<Long, List<Long>> entry : group.entrySet()) {
            // group 的键
            Long companyKey = entry.getKey();
            // group 的值（orgId 列表）
            List<Long> subOrgIds = entry.getValue();

            // 筛选出属于当前分支的 Report 对象
            List<OrganizationDailyReportModel> filteredReports = reports.stream()
                    .filter(report -> subOrgIds.contains(report.getOrganizationId()))
                    .collect(Collectors.toList());

            // 放入结果 Map
            reportGroupedByCompany.put(companyKey, filteredReports);
        }

        return reportGroupedByCompany;
    }

    /**
     * 根据 companyGroup 或者 regionGroup 对 Report 进行分组
     */
    private static Map<Long, List<OrganizationMonthlyReportModel>> groupReportsByCompanyOrRegionMonthly(Map<Long, List<Long>> group, List<OrganizationMonthlyReportModel> reports) {
        Map<Long, List<OrganizationMonthlyReportModel>> reportGroupedByCompany = new HashMap<>();

        for (Map.Entry<Long, List<Long>> entry : group.entrySet()) {
            // group 的键
            Long companyKey = entry.getKey();
            // group 的值（orgId 列表）
            List<Long> subOrgIds = entry.getValue();

            // 筛选出属于当前分支的 Report 对象
            List<OrganizationMonthlyReportModel> filteredReports = reports.stream()
                    .filter(report -> subOrgIds.contains(report.getOrganizationId()))
                    .collect(Collectors.toList());

            // 放入结果 Map
            reportGroupedByCompany.put(companyKey, filteredReports);
        }

        return reportGroupedByCompany;
    }

    /**
     * 生成 regionGroup
     */
    private static Map<Long, List<Long>> generateRegionGroup(OrgInfoNodeResponse root) {
        Map<Long, List<Long>> regionGroup = new HashMap<>();
        collectRegionGroup(root, regionGroup);
        return regionGroup;
    }

    private static void collectRegionGroup(OrgInfoNodeResponse node, Map<Long, List<Long>> regionGroup) {
        // 如果是 type=2 的节点
        if (OrganizationTypeEnum.REGION.getCode().equals(node.getType())) {
            List<Long> typeCompanyOrgIds = getAllTypeCompanyOrgIds(node);
            regionGroup.put(node.getOrgId(), typeCompanyOrgIds);
        }
        if (node.getChildOrgInfoNodeRespList() != null) {
            for (OrgInfoNodeResponse child : node.getChildOrgInfoNodeRespList()) {
                collectRegionGroup(child, regionGroup);
            }
        }
    }

    /**
     * 获取某个节点下所有 type=3 的 orgId
     */
    private static List<Long> getAllTypeCompanyOrgIds(OrgInfoNodeResponse node) {
        List<Long> result = new ArrayList<>();
        collectTypeCompanyOrgIds(node, result);
        return result;
    }

    private static void collectTypeCompanyOrgIds(OrgInfoNodeResponse node, List<Long> result) {
        if (node.getType() == 3) {
            result.add(node.getOrgId());
        }
        if (node.getChildOrgInfoNodeRespList() != null) {
            for (OrgInfoNodeResponse child : node.getChildOrgInfoNodeRespList()) {
                collectTypeCompanyOrgIds(child, result);
            }
        }
    }

    /**
     * 生成 companyGroup
     */
    private static Map<Long, List<Long>> generateCompanyGroup(OrgInfoNodeResponse root) {
        Map<Long, List<Long>> companyGroup = new HashMap<>();
        collectCompanyGroup(root, companyGroup);
        return companyGroup;
    }

    private static void collectCompanyGroup(OrgInfoNodeResponse node, Map<Long, List<Long>> companyGroup) {
        // 如果是 type=3 的节点
        if (OrganizationTypeEnum.BRANCH_COMPANY.getCode().equals(node.getType())) {
            List<Long> subOrgIds = getAllSubOrgIds(node);
            companyGroup.put(node.getOrgId(), subOrgIds);
        }
        if (node.getChildOrgInfoNodeRespList() != null) {
            for (OrgInfoNodeResponse child : node.getChildOrgInfoNodeRespList()) {
                collectCompanyGroup(child, companyGroup);
            }
        }
    }

    /**
     * 获取某个节点及其子节点的所有 orgId
     */
    private static List<Long> getAllSubOrgIds(OrgInfoNodeResponse node) {
        List<Long> result = new ArrayList<>();
        collectSubOrgIds(node, result);
        return result;
    }

    private static void collectSubOrgIds(OrgInfoNodeResponse node, List<Long> result) {
        result.add(node.getOrgId());
        if (node.getChildOrgInfoNodeRespList() != null) {
            for (OrgInfoNodeResponse child : node.getChildOrgInfoNodeRespList()) {
                collectSubOrgIds(child, result);
            }
        }
    }

}
