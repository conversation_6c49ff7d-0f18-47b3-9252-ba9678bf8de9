package com.xmd.achievement.support.constant.enums;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/17/15:08
 * @since 1.0
 */
public enum OrderRuleTypeEnum {
    /**
     * 升序
     */
    ASC(1, "ASC"),
    /**
     * 降序
     */
    DESC(2, "DESC");

    private final Integer code;
    private final String msg;

    OrderRuleTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }
}
