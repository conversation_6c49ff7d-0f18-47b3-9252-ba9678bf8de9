package com.xmd.achievement.service.entity.response;

import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/23/07:48
 * @since 1.0
 */
@Data
public class CheckInServeDataIsRightResponse implements Serializable {
    List<AchievementSpecDetailModel> newSpecDetailModelList;
    List<AchievementCategoryDetailModel> newCategoryDetailModelList;
    List<AchievementProductDetailModel> newProductDetailModelList;

}
