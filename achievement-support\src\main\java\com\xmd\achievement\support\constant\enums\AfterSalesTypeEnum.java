package com.xmd.achievement.support.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 售后类型枚举
 */
@Getter
@AllArgsConstructor
public enum AfterSalesTypeEnum {

    REFUND(1, "退款", OrderSaleTypeEnum.REFUND.getType()),
    TRANSFER(2, "转款", OrderSaleTypeEnum.TRANSFER.getType());

    private final Integer code;
    private final String desc;
    /**
     * 业绩存储的业务类型
     */
    private final Integer orderSalesType;

    /**
     * 根据类型码获取枚举值
     *
     * @param code 类型码
     * @return 对应的枚举项
     * @throws IllegalArgumentException 如果找不到对应的类型码
     */
    public static AfterSalesTypeEnum fromCode(int code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode() == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未知的售后类型码: " + code));
    }

    public static AfterSalesTypeEnum fromOrderSalesType(int orderSalesType) {
        return Arrays.stream(values())
                .filter(e -> e.getOrderSalesType() == orderSalesType)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未知的售后类型码: " + orderSalesType));
    }

    public static Integer getOrderSalesTypeByCode(int code) {
        return fromCode(code).getOrderSalesType();
    }
}

