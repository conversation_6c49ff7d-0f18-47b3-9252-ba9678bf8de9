package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商务月表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class BusinessMonthInfoResponse implements Serializable {
    private static final long serialVersionUID = -8428618915473495735L;
    /**
     * 自增主键
     */

    private Long id;
    /**
     * 商务月
     */
    @Schema(description = "商务月")
    private String month;
    /**
     * 开始日期
     */
    @Schema(description = "商务月开始")
    private Date startDate;

    /**
     * 商务月id
     */
    @Schema(description = "商务月id")
    private Long monthId;

    /**
     * 月半日期
     */
    @Schema(description = "月半日期")
    private Date midDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    private Date endDate;

    /**
     * 是否冻结 0-未冻结 1-已冻结
     */
    @Schema(description = "是否冻结 0-未冻结 1-已冻结")
    private Integer isFreeze;

}