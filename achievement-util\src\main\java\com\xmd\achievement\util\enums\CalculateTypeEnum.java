package com.xmd.achievement.util.enums;

import lombok.Getter;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/18 10:58
 * @version: 1.0.0
 * @return {@link }
 */
@Getter
public enum CalculateTypeEnum {
    /**
     * 1 支付完成
     */
    PAYMENT(1, "支付完成"),
    /**
     * 2 服务中
     */
    SERVEINPROGRESS(2, "服务中");

    private final Integer customerType;
    private final String message;

    CalculateTypeEnum(Integer customerType, String message) {
        this.customerType = customerType;
        this.message = message;
    }

    public Integer getCode() {
        return customerType;
    }

    public String getMessage() {
        return message;
    }

    public static CalculateTypeEnum fromCode(int code) {
        for (CalculateTypeEnum value : CalculateTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("CustomerTypeEnum code: " + code);
    }

}