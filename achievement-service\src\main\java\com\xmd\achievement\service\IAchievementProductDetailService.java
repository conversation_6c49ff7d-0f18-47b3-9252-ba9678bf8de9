package com.xmd.achievement.service;

import com.xmd.achievement.dao.dto.SearchAchievementDetailsDto;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.service.entity.dto.ProductedDataAddDto;
import com.xmd.achievement.service.entity.request.ThirdExcelPerformanceRequest;
import com.xmd.achievement.support.constant.enums.OrderRuleTypeEnum;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * <功能描述> 业绩产品明细接口
 *
 * <AUTHOR>
 * @date: 2024/12/18 09:59
 */
public interface IAchievementProductDetailService {
    /**
     * <功能描述> 保存业绩产品明细
     *
     * @param ach 业绩产品明细实体类
     * <AUTHOR>
     * @date: 2024/12/19 10:00
     * @version: 1.0.0
     */
    void save(AchievementProductDetailModel ach);

    /**
     * 批量保存业绩产品明细
     *
     * @param models 业绩产品明细实体类
     */
    void saveBatch(List<AchievementProductDetailModel> models);

    /**
     * 批量更新业绩产品明细
     *
     * @param models 业绩产品明细实体类
     * <AUTHOR>
     * @date: 2024/12/24 14:41
     * @version: 1.0.0
     */
    void saveOrUpdateBatch(List<AchievementProductDetailModel> models);

    /**
     * 根据订单id查询业绩产品明细
     *
     * @param orderId 订单id
     * <AUTHOR>
     * @date: 2024/12/23 17:32
     * @version: 1.0.0
     */
    List<AchievementProductDetailModel> listByOrderId(Long orderId);

    /**
     * 根据客户id查询所有业绩
     * 描述
     *
     * @param customerId 客户id
     * @return {@link }
     * <AUTHOR>
     * @date: 2024/12/24 16:06
     * @version: 1.0.0
     */
    List<AchievementProductDetailModel> listByCustomerId(String customerId);

    /**
     * 根据支付时间查询业绩产品明细
     *
     * @param startTime 生成开始时间
     * @param endTime   生成结束时间
     * @return {@link }
     * <AUTHOR>
     * @date: 2024/12/25 09:36
     * @version: 1.0.0
     */
    List<AchievementProductDetailModel> listByCreateTime(Date startTime, Date endTime);

    /**
     * 根据订单id和产品id查询
     * 描述
     *
     * @param orderId   订单id
     * @param productId 商品id
     * @return {@link }
     * <AUTHOR>
     * @date: 2024/12/26 16:03
     * @version: 1.0.0
     */
    List<AchievementProductDetailModel> getByOrderIdAndProductId(Long orderId, Long productId,Integer mainSplitPerson,Integer installmentNum,String orderProductId);

    /**
     * 查询区间订单
     *
     * @param customerId 客户ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param asc        升降
     * @return List<AchievementProductDetailModel>
     */
    List<AchievementProductDetailModel> selectListByPayTime(String customerId, Date startTime, Date endTime, OrderRuleTypeEnum asc);

    void importUpdateZhongXiaoTime(List<ThirdExcelPerformanceRequest> requests) throws ParseException;

    Boolean productedDataInsert(List<ProductedDataAddDto> reques);

    List<AchievementProductDetailModel> getNewCustomerInfo(String customerId,Date achievementCreateTime);

    AchievementProductDetailModel firstAchievement(String customerId,Date achievementCreateTime);

    List<AchievementProductDetailModel>  historyTotalMoney(String customerId, Date firstCreateTime,Date currentCreateTime,OrderRuleTypeEnum orderRuleTypeEnum);

    List<AchievementProductDetailModel>  queryCustomerAllProductInfo(String customerId,Date achievementCreateTime);

    void saveProductDetailModels(List<AchievementProductDetailModel> productDetailModels, MqOrderPaymentInfoModel mqPaymentInfoModel);

    void businessOrgInfoRepair();

    /**
     * 根据商务月、业务ID集合、公司ID查询业绩产品明细
     * @param businessMonths 商务月集合
     * @param businessIds 业务ID集合
     * @param companyId 公司ID（可选）
     * @return 业绩产品明细列表
     */
    List<SearchAchievementDetailsDto> searchAchievementDetails(List<String> businessMonths, List<String> businessIds, Long companyId);
}
