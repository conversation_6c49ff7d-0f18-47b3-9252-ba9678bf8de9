package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("mq_order_payment_info")
public class MqOrderPaymentInfoModel extends BaseModel {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * task任务类型：ADD-新增 UPDATE-修改 DELETE-删除
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 计算类型：1-支付完成 2-服务中
     */
    @TableField("calculate_type")
    private Integer calculateType;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 任务状态：1-未完成 2-已完成
     */
    @TableField("task_status")
    private String taskStatus;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 服务编号
     */
    @TableField("serve_no")
    private String serveNo;

    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;
    /**
     * 失败次数
     */
    @TableField("fail_count")
    private Integer failCount;
    /**
     * 业绩来源：1-跨境 2-中小
     */
    @TableField("achievement_source")
    private Integer achievementSource;
    /**
     * 三方业绩流水ID
     */
    @TableField("third_achievement_id")
    private String thirdAchievementId;

    /**
     * 付款时间
     */
    @TableField("payment_time")
    private Date paymentTime;
    /**
     * 客户ID
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 三方数据来源：EXCEL-表格 ONLINE实时
     */
    @TableField("third_source")
    private String thirdSource;

    /**
     * 分期状态 1 不分期 2 分期
     */
    @TableField("installment_status")
    private Integer installmentStatus;

    /**
     * 分期期数
     */
    @TableField("installment_num")
    private Integer installmentNum;

    /**
     * 生产完成订单商品id
     */
    @TableField("order_product_id ")
    private String orderProductId;
    /**
     * 是否展示 0 是，1 否
     */
    @TableField("displayed")
    private Integer displayed;

}