package com.xmd.achievement.util.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 促销活动限定售卖销售类型枚举
 *
 * <AUTHOR>
 * @date: 2024/11/08 10:45
 */
@Getter
public enum SaleTypeEnum {
    /**
     * 1 新开
     */
    NEW_OPEN(1,"新开"),
    /**
     * 2 续费
     */
    RENEW(2,"续费"),
    /**
     * 3 升级
     */
    UPGRADE(3,"升级"),
    /**
     * 4 另购
     */
    ADDITIONAL_PURCHASE(4,"另购"),
    //中小产品类型
    CAPACITY_EXPANSION_ZT(7, "升级(ZT升级ZT)"),
    HIGH_PRICE_REDEMPTION(5, "高价赎回"),
    ZADD_UPGRADE(8, "Zadd升级"),
    ZTSZT_UPGRADE(3115, "Z+升级ZTSZT"),
    ZTSZM_UPGRADE(3116, "Z+升级ZTSZM"),
    TRANSFER_IN(6, "转入"),
    NZTSZT_UPGRADE(3117, "NZ+升级ZTSZT"),
    NZTSZM_UPGRADE(3118, "NZ+升级ZTSZM"),
    NZTSZM_UPGRADE_DA(169, "大把推续费升级"),
    DSP_UPGRADE(12, "DSP升级"),
    REFUND(18, "退款"),
    PORTAL_EXTENSION_UPGRADE(188, "门户扩展升级"),
    EIGHTY_EIGHT_UPGRADE(88, "升级"),
    TRANSFER(20, "转款"),
    DOMAIN_TRANSFER(3310, "域名转入"),
    CAPACITY_EXPANSION(50, "扩容");



    private final Integer type;
    private final String message;

    SaleTypeEnum(Integer type, String message) {
        this.type = type;
        this.message = message;
    }

    public static List<Integer> getTypeList() {
        return Arrays.stream(SaleTypeEnum.values()).map(SaleTypeEnum::getType).collect(Collectors.toList());
    }


    public Integer getCode() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public static SaleTypeEnum fromCode(int code) {
        for (SaleTypeEnum value : SaleTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("SaleTypeEnum code: " + code);
    }
}
