package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 业绩规格明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("achievement_spec_detail")
public class AchievementSpecDetailModel extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业绩规格id
     */
    @TableField("achievement_spec_id")
    private Long achievementSpecId;
    /**
     * 业绩规格分类id
     */
    @TableField("achievement_category_id")
    private Long achievementCategoryId;
    /**
     * 规格ID
     */
    @TableField("spec_id")
    private Long specId;
    /**
     * 规格名称
     */
    @TableField("spec_name")
    private String specName;
    /**
     * 规格项类型 1=数量，2=时长，3=自定义
     */
    @TableField("item_type")
    private Integer itemType;
    /**
     * 服务项编码
     */
    @TableField("serve_item_no")
    private String serveItemNo;
    /**
     * 计费单位
     */
    @TableField("item_unit")
    private String itemUnit;
    /**
     * 计费个数
     */
    @TableField("item_num")
    private Integer itemNum;
    /**
     * 计费价格(新开价格)
     */
    @TableField("billing_price")
    private BigDecimal billingPrice;
    /**
     * 续费价格
     */
    @TableField("renewal_price")
    private BigDecimal renewalPrice;
    /**
     * 标准价
     */
    @TableField("standard_price")
    private BigDecimal standardPrice;
    /**
     * 应付金额
     */
    @TableField("payable_amount")
    private BigDecimal payableAmount;
    /**
     * 实付金额
     */
    @TableField("paid_amount")
    private BigDecimal paidAmount;
    /**
     * 订单规格类型：1=普通，2=赠品
     */
    @TableField("order_spec_type")
    private Integer orderSpecType;
    /**
     * 首年报价
     */
    @TableField("first_year_quote")
    private BigDecimal firstYearQuote;
    /**
     * 首年到账金额
     */
    @TableField("first_year_income")
    private BigDecimal firstYearIncome;
    /**
     * 续费报价
     */
    @TableField("renewal_quote")
    private BigDecimal renewalQuote;
    /**
     * 续费到账金额
     */
    @TableField("renewal_income")
    private BigDecimal renewalIncome;
    /**
     * 净现金
     */
    @TableField("net_cash")
    private BigDecimal netCash;
    /**
     * 商代提成业绩
     */
    @TableField("agent_comm_achv")
    private BigDecimal agentCommAchv;
    /**
     * 商代实发提成业绩
     */
    @TableField("agent_act_comm_achv")
    private BigDecimal agentActCommAchv;
    /**
     * 商代缓发提成业绩
     */
    @TableField("agent_def_comm_achv")
    private BigDecimal agentDefCommAchv;
    /**
     * 部门提成业绩
     */
    @TableField("dept_comm_achv")
    private BigDecimal deptCommAchv;
    /**
     * 事业部提成业绩
     */
    @TableField("bu_comm_achv")
    private BigDecimal buCommAchv;
    /**
     * 分公司提成业绩
     */
    @TableField("branch_comm_achv")
    private BigDecimal branchCommAchv;
    /**
     * 业绩计收节点 1=支付完成，2=服务完成
     */
    @TableField("revenue_node")
    private Integer revenueNode;
    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品分类ID
     */
    @TableField("product_category_id")
    private Long productCategoryId;

    /**
     * 业务类型 1=有效，2=已完成
     */
    @TableField("status")
    private Integer status;

    /**
     * 主分单人 1=主，2=辅
     */
    @TableField("main_split_person")
    private Integer mainSplitPerson;

    /**
     * 数据改动标识  1 - 正常数据、2 - 人工新增、3 - 人工修改
     */
    @TableField("data_change_type")
    private Integer dataChangeType;

    /**
     * 分期期数
     */
    @TableField("installment_num")
    private Integer installmentNum;

    /**
     * 订单商品明细id
     */
    @TableField("order_product_id")
    private String orderProductId;

    @TableField(exist = false)
    private Long groupById;

    @TableField("is_saas")
    private Integer isSaas;

    /**
     * 规格分类id
     */
    @TableField(exist = false)
    private Long categoryId;
}