package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date: 2024/12/25 14:32
 */
@Getter
public enum AchStatus {
    /**
     * 3 有效
     */
    VALID(1, "有效"),
    /**
     * 3 已完成
     */
    FINISHED(2, "已完成"),
    /**
     * 3 无效
     */
    NOT_VALID(3, "无效"),
    /**
     * 4 已退
     */
    REFUND(4, "已退")
    ;

    private final Integer type;
    private final String desc;

    AchStatus(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescriptionByType(int type) {
        for (AchStatus flag : AchStatus.values()) {
            if (flag.getType().equals(type)) {
                return flag.getDesc();
            }
        }
        throw new IllegalArgumentException("No enum constant with type " + type);
    }

}
