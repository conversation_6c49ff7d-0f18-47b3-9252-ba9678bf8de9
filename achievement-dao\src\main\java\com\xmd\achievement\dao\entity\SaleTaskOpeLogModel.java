package com.xmd.achievement.dao.entity;

    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableField;
    import com.baomidou.mybatisplus.annotation.TableId;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.extension.activerecord.Model;
    import java.math.BigDecimal;
    import java.util.Date;
import lombok.Data;
    import lombok.EqualsAndHashCode;

    import java.io.Serializable;

/**
 * <p>
 * 销售任务修改记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sale_task_ope_log")
public class SaleTaskOpeLogModel extends BaseModel implements
        Serializable {
            private static final long serialVersionUID = 1L;

    // 日志操作类型
    public static final int LOG_OPE_TYPE_ADD = 1;
    public static final int LOG_OPE_TYPE_UPDATE = 2;

    /**
    * 自增主键
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("task_id")
    private Long taskId;
    /**
     * 基本任务修改前值
     */
    @TableField("previous_basic_task")
    private BigDecimal previousBasicTask;
    /**
     * 基本任务修改后值
     */
    @TableField("modified_basic_task")
    private BigDecimal modifiedBasicTask;
    /**
     * 操作类型：1新增；2编辑
     */
    @TableField("ope_type")
    private Integer opeType;

}