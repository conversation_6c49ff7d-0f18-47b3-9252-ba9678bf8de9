package com.xmd.achievement.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xmd.achievement.cache.constant.CacheConstant;
import com.xmd.achievement.service.IExcelImportExceptionService;
import com.xmd.achievement.service.entity.dto.ImportExcelExceptionDTO;
import com.xmd.achievement.support.constant.enums.ExcelImportExceptionEnum;
import com.xmd.achievement.support.redis.RedisCache;
import com.xmd.achievement.web.util.EasyExcelUtil;
import com.xmd.achievement.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Service
@Slf4j
public class ExcelImportExceptionServiceImpl implements IExcelImportExceptionService {

    @Resource
    private RedisCache redisCache;

    @Override
    public void saveExcelException(List<ImportExcelExceptionDTO> excelExceptionDTOList, ExcelImportExceptionEnum typeEnum) {
        if (typeEnum == null) {
            return;
        }
        //本次导入的记录为空, 移除历史错误信息并返回
        String userId = UserContext.getCurrentUserInfo().getUserId();
        String redisKey = typeEnum.getCode() + ":" + userId;
        if (CollectionUtils.isEmpty(excelExceptionDTOList)) {
            redisCache.del(typeEnum.getCode() + ":" + userId);
            return;
        }

        redisCache.set(redisKey, JSON.toJSONString(excelExceptionDTOList), CacheConstant.CacheExpire.TEN_MINUTES);
    }

    @Override
    //@ReportDownload(reportName = "导出错误日志")
    public void exportExcel(String typeCode, HttpServletResponse response) throws IOException {
        String userId = UserContext.getCurrentUserInfo().getUserId();
        ExcelImportExceptionEnum typeEnum = Arrays.stream(ExcelImportExceptionEnum.values())
                .filter(excelImportExceptionEnum -> excelImportExceptionEnum.getCode().equals(typeCode)).findFirst().orElse(null);
        if (typeEnum == null) {
            log.info("下载错误文件传入类型错误;{}", typeCode);
            return;
        }

        String redisKey = typeEnum.getCode() + ":" + userId;
        String cacheData = redisCache.get(redisKey);
        if (StringUtils.isBlank(cacheData)) {
            return;
        }
        List<ImportExcelExceptionDTO> excelExceptionDTOList = JSONArray.parseArray(cacheData, ImportExcelExceptionDTO.class);
        EasyExcelUtil.download(response, excelExceptionDTOList, typeEnum.getDesc() + "导出错误日志", Boolean.TRUE);
    }
}

