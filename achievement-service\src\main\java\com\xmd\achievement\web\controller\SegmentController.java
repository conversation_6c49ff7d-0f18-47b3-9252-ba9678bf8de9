package com.xmd.achievement.web.controller;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.ISegmentService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PageQuerySegmentResponse;
import com.xmd.achievement.service.entity.response.QuerySegmentDetailResponse;
import com.xmd.achievement.service.entity.response.QuerySegmentListResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 业绩分段
 *
 * <AUTHOR>
 */
@Tag(name = "SC-业绩分段接口")
@Slf4j
@RestController
@RequestMapping("/segment")
public class SegmentController {
    @Resource
    ISegmentService segmentService;

    @Operation(summary = "SC-01-业绩分段保存")
    @PostMapping("saveSegment")
    public WebResult<Boolean> saveSegment(@RequestBody @Valid SaveSegmentRequest request) {
        log.info("SC-01-业绩分段保存,请求参数:{}", JSONUtil.toJsonStr(request));
        return segmentService.saveSegment(request);
    }

    @Operation(summary = "SC-02-分页查询业绩分段")
    @PostMapping("pageQuerySegment")
    public WebResult<PageResponse<PageQuerySegmentResponse>> pageQuerySegment(@RequestBody @Valid PageRequest request) {
        log.info("SC-02-分页查询业绩分段,请求参数:{}", JSONUtil.toJsonStr(request));
        return segmentService.pageQuerySegment(request);
    }

    @Operation(summary = "SC-03-查询业绩分段详情")
    @PostMapping("querySegmentDetail")
    public WebResult<QuerySegmentDetailResponse> querySegmentDetail(@RequestBody @Valid QuerySegmentDetailResquest request) {
        log.info("SC-03-查询业绩分段详情,请求参数:{}", JSONUtil.toJsonStr(request));
        return segmentService.querySegmentDetail(request);
    }

    @Operation(summary = "SC-04-查询业绩分段列表")
    @GetMapping("querySegmentList")
    public WebResult<List<QuerySegmentListResponse>> querySegmentList() {
        log.info("SC-04-查询业绩分段列表");
        return segmentService.querySegmentList();
    }

    @Operation(summary = "SC-05-修改业绩分段")
    @PostMapping("updateSegment")
    public WebResult<Boolean> updateSegment(@RequestBody @Valid UpdateSegmentRequest request) {
        log.info("SC-05-修改业绩分段,请求参数:{}", JSONUtil.toJsonStr(request));
        return segmentService.updateSegment(request);
    }
}
