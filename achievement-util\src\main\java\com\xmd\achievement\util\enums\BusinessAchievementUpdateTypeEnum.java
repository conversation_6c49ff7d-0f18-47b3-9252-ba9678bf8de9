package com.xmd.achievement.util.enums;

import lombok.Getter;

@Getter
public enum BusinessAchievementUpdateTypeEnum {

    NORMAL(1, "正常数据"),
    BUSINESS_MONTH(2, "商务月修改"),
    CUSTOMER_TYPE(3, "客户类型修改"),
    DELETE(4, "删除数据");

    private final Integer updateType;
    private final String description;

    BusinessAchievementUpdateTypeEnum(Integer updateType, String description) {
        this.updateType = updateType;
        this.description = description;
    }
}
