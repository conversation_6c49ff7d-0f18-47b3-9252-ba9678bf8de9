package com.xmd.achievement.util.encrypt;


import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 加解密工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/19 上午10:47
 **/
public class EncryptAndDecryptUtil {
    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(EncryptAndDecryptUtil.class);

    /**
     * MD5 加密
     *
     * @param value 待加密字符串
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午10:47
     * @version 1.0.0
     **/
    public static String md5Encrypt(String value) {
        return StringUtils.isBlank(value) ? null : Md5Util.encrypt(value, Md5Util.MD5_KEY);
    }

    /**
     * SHA加密
     *
     * @param value 待加密字符串
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午10:49
     * @version 1.0.0
     **/
    public static String shaEncrypt(String value) {
        return StringUtils.isBlank(value) ? null : Md5Util.encrypt(value, Md5Util.SHA_KEY);
    }

    /**
     * BASE64 加密
     *
     * @param value 待加密字符串
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午10:50
     * @version 1.0.0
     **/
    public static String base64Encrypt(String value) {
        return StringUtils.isBlank(value) ? null : Base64Util.encrypt(value.getBytes());
    }

    /**
     * DES加密
     *
     * @param value 待加密字符串
     * @param key   加密key
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午10:53
     * @version 1.0.0
     **/
    public static String desEncrypt(String value, String key) {
        key = key == null ? DesUtil.KEY : key;
        return StringUtils.isBlank(value) ? null : DesUtil.encrypt(value, key);
    }

    /**
     * escape加密
     *
     * @param value 待加密字符串
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午11:04
     * @version 1.0.0
     **/
    public static String escapeEncrypt(String value) {
        return StringUtils.isBlank(value) ? null : EscapeUtil.escape(value);
    }


    /**
     * BASE64 解密
     *
     * @param value 待解密字符串
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午10:51
     * @version 1.0.0
     **/
    public static String base64Decrypt(String value) {
        try {
            return StringUtils.isBlank(value) ? null : new String(Base64Util.decrypt(value));
        } catch (Exception e) {
            LOGGER.error("inputParams:{} and errorMessage:{}", value, e.getMessage(), e);
            throw new RuntimeException("BASE64 解密失败");
        }
    }

    /**
     * DES解密
     *
     * @param value 待解密字符串
     * @param key   解密key
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午10:55
     * @version 1.0.0
     **/
    public static String desDecrypt(String value, String key) {
        key = key == null ? DesUtil.KEY : key;
        return StringUtils.isBlank(value) ? null : DesUtil.decrypt(value, key);
    }


    /**
     * escape解密
     *
     * @param value 待解密字符串
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/19 上午11:04
     * @version 1.0.0
     **/
    public static String escapeDecrypt(String value) {
        return StringUtils.isBlank(value) ? null : EscapeUtil.unescape(value);
    }
}
