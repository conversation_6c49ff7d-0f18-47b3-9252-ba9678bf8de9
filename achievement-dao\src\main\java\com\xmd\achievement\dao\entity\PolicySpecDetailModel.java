package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 政策规格明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("policy_spec_detail")
public class PolicySpecDetailModel extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业绩政策ID
     */
    @TableField("policy_spec_detail_id")
    private Long policySpecDetailId;
    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;
    /**
     * 规格分类ID
     */
    @TableField("spec_category_id")
    private Long specCategoryId;
    /**
     * 规格分类
     */
    @TableField("spec_category_name")
    private String specCategoryName;
    /**
     * 规格ID
     */
    @TableField("spec_id")
    private Long specId;
    /**
     * 规格名称
     */
    @TableField("spec_name")
    private String specName;
    /**
     * 商代政策性成本新开
     */
    @TableField("policy_cost_open")
    private BigDecimal policyCostOpen;
    /**
     * 商代政策性成本续费
     */
    @TableField("policy_cost_renew")
    private BigDecimal policyCostRenew;
    /**
     * 商代政策性成本另购
     */
    @TableField("policy_cost_add")
    private BigDecimal policyCostAdd;
    /**
     * 商代政策性成本升级
     */
    @TableField("policy_cost_upgrade")
    private BigDecimal policyCostUpgrade;

    /**
     * 业绩计收节点 1=支付完成，2=生产完成
     */
    @TableField("revenue_node")
    private Integer revenueNode;
    /**
     * 业绩核算比例
     */
    @TableField("achievement_ratio")
    private BigDecimal achievementRatio;
    /**
     * 实发业绩提成比例
     */
    @TableField("commission_ratio")
    private BigDecimal commissionRatio;
}