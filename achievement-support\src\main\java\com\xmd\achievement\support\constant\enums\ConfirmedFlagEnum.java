package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 是否是转正 2=否 1=是
 * <AUTHOR>
 * @date: 2024/12/25 16:46
 */
@Getter
public enum ConfirmedFlagEnum {
    NO(0, "否"), YES(1, "是");

    private final Integer type;
    private final String desc;

    ConfirmedFlagEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
    public static String getDescriptionByType(int type) {
        for (ConfirmedFlagEnum flag : ConfirmedFlagEnum.values()) {
            if (flag.getType().equals(type)) {
                return flag.getDesc();
            }
        }
        throw new IllegalArgumentException("No enum constant with type " + type);
    }
}
