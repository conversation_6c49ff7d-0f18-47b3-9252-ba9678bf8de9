package com.xmd.achievement.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.handler.achievement.AchievementHandler;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContextV4;
import com.xmd.achievement.service.*;
import com.xmd.achievement.service.entity.request.CalculateCustomTypeRequest;
import com.xmd.achievement.service.entity.request.CheckInServeDataIsRightRequest;
import com.xmd.achievement.service.entity.request.RebuildBusinessAchStatInfoRequest;
import com.xmd.achievement.service.entity.request.UpdatePaycompleteTestRequest;
import com.xmd.achievement.service.entity.response.CheckInServeDataIsRightResponse;
import com.xmd.achievement.support.constant.enums.CustomerType;
import com.xmd.achievement.support.constant.enums.PayTypeEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/23/01:33
 * @since 1.0
 */
@Tag(name = "DR-清洗")
@Slf4j
@RestController
@RequestMapping("/repair")
public class DataRepairController {
    @Resource
    MqOrderPaymentInfoService mqOrderPaymentInfoService;
    @Resource
    AchievementHandler achievementHandler;
    @Resource
    CalculateCustomerContextV4 calculateCustomerContextV4;
    @Resource
    private IBusinessAchievementService businessAchievementService;
    @Resource
    IAchievementProductDetailRepository productDetailRepository;

    @Operation(summary = "DR-01 检查服务中的数据成业绩否是正确")
    @PostMapping("checkInServeDataFor323")
    public WebResult<CheckInServeDataIsRightResponse> checkInServeDataFor323(@RequestBody @Valid CheckInServeDataIsRightRequest request) {
        MqOrderPaymentInfoModel model = mqOrderPaymentInfoService.queryExcutTaskByTaskId(request.getTaskId());
        return WebResult.success(achievementHandler.checkInServeData(model));
    }

    @Operation(summary = "DR-02-修改支付完成状态")
    @PostMapping("updatePayCompleteFor323")
    public WebResult<Boolean> updatePayCompleteFor323(@RequestBody @Valid UpdatePaycompleteTestRequest request) {
        mqOrderPaymentInfoService.updatePayCompleteTest(request);
        return WebResult.success(true);
    }

    @Operation(summary = "DR-03-支付完成xxljob")
    @GetMapping("payCompleteExecuteFor323")
    public WebResult<Boolean> payCompleteExecuteFor323() {
        List<MqOrderPaymentInfoModel> models = mqOrderPaymentInfoService.queryExcutTask();
        for (MqOrderPaymentInfoModel model : models) {
            try {
                achievementHandler.processAchievement(model);
            } catch (Exception e) {
                log.error("MqOrderPaymentInfoModel任务失败，TaskId:{},失败原因：", model.getTaskId(), e);
            }
        }
        log.info("执行MqOrderPaymentInfoModel任务End...");

        return WebResult.success(true);
    }

    @Operation(summary = "DR-04-重新计算商务人员业绩按照商务月")
    @PostMapping("rebuildBusinessAchStatInfoFor323")
    public WebResult<Boolean> rebuildBusinessAchStatInfoFor323(@RequestBody @Valid RebuildBusinessAchStatInfoRequest request) {
        businessAchievementService.rebuildBusinessAchStatInfo(request);
        return WebResult.success(true);
    }

    //5.新老客户重新计算
    @Operation(summary = "DATA-05-V4版本重新计算新老客户并(入库使用)")
    @GetMapping("repairCalculateCustomerTypeFor323")
    public WebResult<Boolean> repairCalculateCustomerType() {
        log.info("DATA-05-V4版本重新计算新老客户并(入库使用)");
        mqOrderPaymentInfoService.repairCalculateCustomerType();
        return WebResult.success(true);
    }

    @Operation(summary = "DR-06-V4版本返回新老客户计算结果（验证使用）")
    @PostMapping("calculateCustomTypeFor323")
    public WebResult<String> calculateCustomTypeFor323(@RequestBody @Valid CalculateCustomTypeRequest request) {
        log.info("DR-06-V4版本返回新老客户计算结果（验证使用）:请求参数{}", request);
        List<AchievementProductDetailModel> productDetailModels = productDetailRepository.list(
                new LambdaQueryWrapper<AchievementProductDetailModel>()
                        .eq(AchievementProductDetailModel::getOrderNo, request.getOrderNo()));
        CustomerType customerType = calculateCustomerContextV4.calculateCustomerV4(1, 2, request.getCustomerId(), productDetailModels);
        return WebResult.success(customerType.getDescription());
    }

    @Operation(summary = "DR-07-V4版本修复mq支付表的计算类型")
    @GetMapping("repairThirdDataCreateTime")
    public WebResult<Boolean> repairThirdDataCreateTime() {
        log.info("DR-07-V4版本修复mq支付表中中企数据的创建时间");
        return WebResult.success(mqOrderPaymentInfoService.repairThirdDataCreateTime());
    }
}
