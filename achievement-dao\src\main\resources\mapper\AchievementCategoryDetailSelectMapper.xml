<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.AchievementCategoryDetailSelectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.AchievementCategoryDetailSelectModel">
        <id column="id" property="id" />
        <result column="achievement_category_id" property="achievementCategoryId" />
        <result column="achievement_id" property="achievementId" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="standard_price" property="standardPrice" />
        <result column="payable_amount" property="payableAmount" />
        <result column="paid_amount" property="paidAmount" />
        <result column="first_year_quote" property="firstYearQuote" />
        <result column="first_year_income" property="firstYearIncome" />
        <result column="renewal_quote" property="renewalQuote" />
        <result column="renewal_income" property="renewalIncome" />
        <result column="net_cash" property="netCash" />
        <result column="agent_comm_achv" property="agentCommAchv" />
        <result column="agent_act_comm_achv" property="agentActCommAchv" />
        <result column="agent_def_comm_achv" property="agentDefCommAchv" />
        <result column="dept_comm_achv" property="deptCommAchv" />
        <result column="bu_comm_achv" property="buCommAchv" />
        <result column="branch_comm_achv" property="branchCommAchv" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="order_id" property="orderId" />
        <result column="product_id" property="productId" />
        <result column="status" property="status" />
        <result column="main_split_person" property="mainSplitPerson" />
        <result column="data_change_type" property="dataChangeType" />
        <result column="installment_num" property="installmentNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, achievement_category_id, achievement_id, category_id, category_name, standard_price, payable_amount, paid_amount, first_year_quote, first_year_income, renewal_quote, renewal_income, net_cash, agent_comm_achv, agent_act_comm_achv, agent_def_comm_achv, dept_comm_achv, bu_comm_achv, branch_comm_achv, delete_flag, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name, order_id, product_id, `status`, main_split_person, data_change_type, installment_num
    </sql>

    <insert id="batchInsertOrUpdate">
        INSERT INTO achievement_category_detail_select (
        id,
        achievement_category_id,
        achievement_id,
        category_id,
        category_name,
        standard_price,
        payable_amount,
        paid_amount,
        first_year_quote,
        first_year_income,
        renewal_quote,
        renewal_income,
        net_cash,
        agent_comm_achv,
        agent_act_comm_achv,
        agent_def_comm_achv,
        dept_comm_achv,
        bu_comm_achv,
        branch_comm_achv,
        delete_flag,
        create_time,
        create_user_id,
        create_user_name,
        update_time,
        update_user_id,
        update_user_name,
        order_id,
        product_id,
        status,
        main_split_person,
        data_change_type,
        installment_num
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.achievementCategoryId},
            #{item.achievementId},
            #{item.categoryId},
            #{item.categoryName},
            #{item.standardPrice},
            #{item.payableAmount},
            #{item.paidAmount},
            #{item.firstYearQuote},
            #{item.firstYearIncome},
            #{item.renewalQuote},
            #{item.renewalIncome},
            #{item.netCash},
            #{item.agentCommAchv},
            #{item.agentActCommAchv},
            #{item.agentDefCommAchv},
            #{item.deptCommAchv},
            #{item.buCommAchv},
            #{item.branchCommAchv},
            #{item.deleteFlag},
            #{item.createTime},
            #{item.createUserId},
            #{item.createUserName},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.updateUserName},
            #{item.orderId},
            #{item.productId},
            #{item.status},
            #{item.mainSplitPerson},
            #{item.dataChangeType},
            #{item.installmentNum}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        achievement_category_id = VALUES(achievement_category_id),
        achievement_id = VALUES(achievement_id),
        category_id = VALUES(category_id),
        category_name = VALUES(category_name),
        standard_price = VALUES(standard_price),
        payable_amount = VALUES(payable_amount),
        paid_amount = VALUES(paid_amount),
        first_year_quote = VALUES(first_year_quote),
        first_year_income = VALUES(first_year_income),
        renewal_quote = VALUES(renewal_quote),
        renewal_income = VALUES(renewal_income),
        net_cash = VALUES(net_cash),
        agent_comm_achv = VALUES(agent_comm_achv),
        agent_act_comm_achv = VALUES(agent_act_comm_achv),
        agent_def_comm_achv = VALUES(agent_def_comm_achv),
        dept_comm_achv = VALUES(dept_comm_achv),
        bu_comm_achv = VALUES(bu_comm_achv),
        branch_comm_achv = VALUES(branch_comm_achv),
        delete_flag = VALUES(delete_flag),
        create_time = VALUES(create_time),
        create_user_id = VALUES(create_user_id),
        create_user_name = VALUES(create_user_name),
        update_time = VALUES(update_time),
        update_user_id = VALUES(update_user_id),
        update_user_name = VALUES(update_user_name),
        order_id = VALUES(order_id),
        product_id = VALUES(product_id),
        status = VALUES(status),
        main_split_person = VALUES(main_split_person),
        data_change_type = VALUES(data_change_type),
        installment_num = VALUES(installment_num)
    </insert>

</mapper>
