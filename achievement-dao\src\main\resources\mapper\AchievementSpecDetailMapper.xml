<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.AchievementSpecDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.AchievementSpecDetailModel">
        <id column="id" property="id"/>
        <result column="achievement_spec_id" property="achievementSpecId"/>
        <result column="achievement_category_id" property="achievementCategoryId"/>
        <result column="spec_id" property="specId"/>
        <result column="spec_name" property="specName"/>
        <result column="item_type" property="itemType"/>
        <result column="order_spec_type" property="orderSpecType"/>
        <result column="serve_item_no" property="serveItemNo"/>
        <result column="standard_price" property="standardPrice"/>
        <result column="payable_amount" property="payableAmount"/>
        <result column="paid_amount" property="paidAmount"/>
        <result column="first_year_quote" property="firstYearQuote"/>
        <result column="first_year_income" property="firstYearIncome"/>
        <result column="renewal_quote" property="renewalQuote"/>
        <result column="renewal_income" property="renewalIncome"/>
        <result column="net_cash" property="netCash"/>
        <result column="agent_comm_achv" property="agentCommAchv"/>
        <result column="agent_act_comm_achv" property="agentActCommAchv"/>
        <result column="agent_def_comm_achv" property="agentDefCommAchv"/>
        <result column="dept_comm_achv" property="deptCommAchv"/>
        <result column="bu_comm_achv" property="buCommAchv"/>
        <result column="branch_comm_achv" property="branchCommAchv"/>
        <result column="item_unit" property="itemUnit"/>
        <result column="item_num" property="itemNum"/>
        <result column="billing_price" property="billingPrice"/>
        <result column="renewal_price" property="renewalPrice"/>
        <result column="installment_num" property="installmentNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, achievement_spec_id, achievement_category_id,item_unit, item_num,order_spec_type,billing_price,renewal_price,spec_id, spec_name, item_type, serve_item_no, standard_price, payable_amount, paid_amount, first_year_quote, first_year_income, renewal_quote, renewal_income, net_cash, agent_comm_achv, agent_act_comm_achv, agent_def_comm_achv, dept_comm_achv, bu_comm_achv, branch_comm_achv,installment_num
    </sql>

    <!-- 注意，这里用了achievement_category_detail的Id作为achievementCategoryId返回了 -->
    <select id="listByAchievementId" resultMap="BaseResultMap">
        select t1.id, t1.achievement_spec_id, t2.id as achievement_category_id, t1.spec_id, t1.spec_name, t1.order_product_id,
               t1.net_cash, t1.agent_comm_achv, t1.agent_act_comm_achv, t1.agent_def_comm_achv, t1.dept_comm_achv,
               t1.bu_comm_achv, t1.branch_comm_achv
        from achievement_spec_detail t1
        inner join achievement_category_detail t2 on t1.achievement_category_id = t2.achievement_category_id
        where t2.achievement_id = #{achievementId}
        and t1.delete_flag = '0'
        and t2.delete_flag = '0'
    </select>
</mapper>
