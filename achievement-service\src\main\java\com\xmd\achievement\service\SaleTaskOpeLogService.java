package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.SaleTaskModel;
import com.xmd.achievement.dao.entity.SaleTaskOpeLogModel;

import java.util.List;

public interface SaleTaskOpeLogService {

    /**
     * 批量保存销售任务操作日志
     * @param taskModels 销售任务
     * @param opeType 任务操作类型
     */
    void saveBatch(List<SaleTaskModel> taskModels, int opeType);

    /**
     * 批量保存销售任务的操作日志
     * @param models 操作日志集合
     */
    void saveBatch(List<SaleTaskOpeLogModel> models);
}
