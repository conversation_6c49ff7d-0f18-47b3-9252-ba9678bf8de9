package com.xmd.achievement.web.entity.response;

import java.io.Serializable;

/**
 * 返回包装体
 *
 * @version 1.0.0
 * @date 2021/05/28 下午3:38
 */
public class WebResult<T> implements Serializable {
    private static final long serialVersionUID = -2156057946333564975L;
    private String msg;
    private String code;
    private T data;

    private WebResult() {
    }

    private WebResult(T data) {
        this.code = WebCodeMessageEnum.REQUEST_SUCCESS.getCode();
        this.msg = WebCodeMessageEnum.REQUEST_SUCCESS.getMsg();
        this.data = data;
    }

    private WebResult(T data, String msg) {
        this.code = WebCodeMessageEnum.REQUEST_SUCCESS.getCode();
        this.msg = msg;
        this.data = data;
    }

    private WebResult(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private WebResult(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    private WebResult(WebCodeMessageEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMsg();
        }
    }

    public static <T> WebResult<T> success(T data) {
        return new WebResult<>(data);
    }

    public static <T> WebResult<T> success(T data, String msg) {
        return new WebResult<>(data, msg);
    }

    public static <T> WebResult<T> success() {
        return success(null);
    }

    public static <T> WebResult<T> error(WebCodeMessageEnum cm) {
        return new WebResult<>(cm);
    }

    public static <T> WebResult<T> error(WebCodeMessageEnum cm, String msg) {
        return new WebResult<>(cm.getCode(), msg);
    }

    public static <T> WebResult<T> error(String code, String msg) {
        return new WebResult<>(code, msg);
    }

    public static <T> WebResult<T> error(WebResult<?> resultEntity) {
        return new WebResult<>(resultEntity.getCode(), resultEntity.getMsg());
    }

    public static <T> WebResult<T> error(WebResult<?> resultEntity, T data) {
        return new WebResult<>(resultEntity.getCode(), resultEntity.getMsg(), data);
    }

    public static <T> T parseResult(WebResult<T> resultEntity) {
        if (resultEntity.checkSuccess() && resultEntity.getData() != null) {
            return resultEntity.getData();
        }
        return null;
    }

    public T getData() {
        return this.data;
    }

    public String getMsg() {
        return this.msg;
    }

    public String getCode() {
        return this.code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Boolean checkSuccess() {
        return WebCodeMessageEnum.REQUEST_SUCCESS.getCode().equals(this.code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setData(T data) {
        this.data = data;
    }
}
