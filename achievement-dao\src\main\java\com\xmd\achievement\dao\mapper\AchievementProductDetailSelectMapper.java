package com.xmd.achievement.dao.mapper;

import com.xmd.achievement.dao.entity.AchievementProductDetailSelectModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 业绩商品明细表(查询) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface AchievementProductDetailSelectMapper extends BaseMapper<AchievementProductDetailSelectModel> {
    void batchInsertOrUpdate(@Param("list") List<AchievementProductDetailSelectModel> list);
}
