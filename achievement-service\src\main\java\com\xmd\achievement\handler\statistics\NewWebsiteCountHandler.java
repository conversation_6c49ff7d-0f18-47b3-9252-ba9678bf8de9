package com.xmd.achievement.handler.statistics;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.enums.AchStatus;
import com.xmd.achievement.support.constant.enums.MainSubEnum;
import com.xmd.achievement.support.constant.enums.SiteFlagEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * ●新开网站个数，</br>
 *○统计该商务人员本商务月售卖类型为新开或升级网站且净现金大于0的业绩条数；</br>
 * ○只统付款时生成的业绩流水；</br>
 * ○统计主分单人为主单人的业绩，辅分单人业绩则不统计；</br>
 * <AUTHOR>
 * @date: 2024/12/25 14:45
 * @version: 1.0.0
 * @return {@link }
 */
@Service
public class NewWebsiteCountHandler implements StatisticsHandler {
    @Override
    public void statistics(StatisticsFactInfo factInfo) {
        List<AchievementProductDetailModel> achList = factInfo.getAchList();
        factInfo.setNewWebsiteCount((int) achList.stream().filter(ach -> MainSubEnum.MAIN.getType().equals(ach.getMainSplitPerson())
                && (SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType()) || SaleTypeEnum.UPGRADE.getType().equals(ach.getSaleType()))
                && SiteFlagEnum.YES.getType().equals(ach.getSiteFlag())
                && ach.getNetCash().compareTo(BigDecimal.ZERO) > 0).count());
    }
}