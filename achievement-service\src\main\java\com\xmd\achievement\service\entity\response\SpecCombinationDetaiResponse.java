package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 规格组合明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class SpecCombinationDetaiResponse implements Serializable {
    private static final long serialVersionUID = -9164238856182793076L;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品三级分类名称")
    private String productCategoryName;

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "规格id")
    private Long specId;

    @Schema(description = "规格名称")
    private String specName;

    @Schema(description = "规格分类名称")
    private String specCategoryName;


    @Schema(description = "新开政策性成本")
    private BigDecimal newPolicyCost;

    @Schema(description = "续费政策性成本")
    private BigDecimal renewalPolicyCost;

    @Schema(description = "升级政策性成本")
    private BigDecimal upgradePolicyCost;

    @Schema(description = "另购政策性成本")
    private BigDecimal additionalPolicyCost;
}