package com.xmd.achievement.cache.constant;

import com.xmd.achievement.cache.entity.SortCollectionData;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 缓存公共常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/14 上午9:52
 */
public class CacheConstant {

    /**
     * 缓存前缀
     */
    public final static String CACHE_PREFIX = "CBO:CUST:DATA:FLOW";

    /**
     * 缓存key分割符
     */
    public final static String CACHE_KEY_SEPARATOR = ":";

    /**
     * 缓存空数据（防止缓存击穿）
     */
    public static class CacheNullData {
        /**
         * 字符串
         */
        public final static String STRING = "null";
        /**
         * 字符串
         */
        public final static List<String> LIST = Arrays.stream(new String[]{STRING}).collect(Collectors.toList());
        /**
         * 字符串
         */
        public final static Set<String> SET = Arrays.stream(new String[]{STRING}).collect(Collectors.toSet());
        /**
         * 字符串
         */
        public final static List<SortCollectionData> Z_SET = Arrays.stream(new SortCollectionData[]{new SortCollectionData(STRING, null)}).collect(Collectors.toList());
        /**
         * 字符串
         */
        public final static Map<String, String> HASH = Arrays.stream(new String[][]{{STRING, STRING}}).collect(Collectors.toMap(entry -> entry[0], entry -> entry[1]));

    }

    /**
     * 缓存心跳数据
     */
    public final static String CACHE_HEARTBEAT = "redis-heartbeat";

    /**
     * 缓存有效期
     */
    public static class CacheExpire {
        /**
         * 永久
         */
        public final static long FOREVER = 0;
        /**
         * 1分钟
         */
        public final static long ONE_MINUTES = 60;
        /**
         * 5分钟
         */
        public final static long FIVE_MINUTES = 5 * 60;
        /**
         * 1天
         */
        public final static long ONE_DAYS = 24 * 60 * 60;
        /**
         * 7天
         */
        public final static long SEVEN_DAYS = 7 * 24 * 60 * 60;

        /**
         * 30天
         */
        public final static long THIRTY_DAYS = 30 * 24 * 60 * 60;
    }
}
