package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.OrganizationDailyReportModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 机构日报 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface IOrganizationDailyReportRepository extends IService<OrganizationDailyReportModel> {

    void batchInsertOrUpdate(List<OrganizationDailyReportModel> list);

    List<OrganizationDailyReportModel> listByTime(Date startTime, Date endTime);

    void deleteByTime(Date startTime);
}
