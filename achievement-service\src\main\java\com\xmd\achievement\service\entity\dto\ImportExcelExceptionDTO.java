package com.xmd.achievement.service.entity.dto;

import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入错误描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImportExcelExceptionDTO {

    @Schema(description = "错误字段名称")
    @ExcelProperty("字段名")
    private String name;

    @Schema(description = "错误描述信息")
    @ExcelProperty("错误信息")
    private String desc;
}
