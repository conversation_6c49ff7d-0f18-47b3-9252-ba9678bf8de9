package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 销售任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
public class QuerySaleTaskListRequest implements
        Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "商务月")
    @NotNull(message = "商务月不能为空")
    private String businessMonth;

    @Schema(description = "分公司任务状态")
    private Integer branchStatus;

    @Schema(description = "审核状态")
    private Integer reviewStatus;

}