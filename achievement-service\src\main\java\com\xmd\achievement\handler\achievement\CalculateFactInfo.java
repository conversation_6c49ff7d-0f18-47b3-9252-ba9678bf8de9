package com.xmd.achievement.handler.achievement;

import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
import com.xmd.achievement.rpc.entity.dto.ProductDiscountRulesListForAchievementResponse;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 计算因子
 * <AUTHOR>
 * @date: 2024/12/20 14:41
 */
@Data
public class CalculateFactInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 当前计算的规格
     */
    private AchievementSpecDetailModel spec;
    /**
     * 当前订单所有规格集合
     */
    private List<AchievementSpecDetailModel> specList;
    /**
     * 1=新开，2=续费，3=升级，4=另购
     */
    private Integer orderSaleType;
    /**
     * 是否拆分订单
     */
    private boolean splitOrder;
    /**
     * 订单类型：1=普通订单，2=折扣订单
     */
    private Integer orderType;
    /**
     * 当前规格对应商品
     */
    private AchievementProductDetailModel product;
    /**
     * 当前订单对应商品折扣规则
     */
    List<ProductDiscountRulesListForAchievementResponse> productDiscountRulesList;
    /**
     * 规格对应政策性成本
     */
    Map<Long, BigDecimal> calculatePolicyCosts;
    /**
     * 当前状态 1=支付完成，2=服务完成
     */
    private Integer currentStatus;
    /**
     * 判断当前订单净现金是否需要设置为0
     * 如果这一单是折扣订单且某个商品的折扣<0提成线，则整单的净现金为0
     */
    private Boolean zeroNetCash;
    /**
     * 判断当前订单中是否包含特殊商品
     * 参考配置文件 special-products
     */
    private List<Long> specialSpecIds;
    /**
     * 判断当前订单是否已经计算过特殊商品的政策性成本
     */
    private Boolean specialRecord;

    private OrderSimpleInfoResponse orderInfo;

    Map<Long, List<Long>> productBySpecIdMap;
}
