package com.xmd.achievement.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.xmd.achievement.service.AchievementFileService;
import com.xmd.achievement.service.IAchievementService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.*;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.CustomerTypeEnum;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.request.RecalculateAchievementRequest;
import com.xmd.achievement.web.entity.request.RecalculateRefundRequest;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.EasyExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品业绩类
 *
 * <AUTHOR>
 */
@Tag(name = "商品业绩查询")
@Slf4j
@RestController
@RequestMapping("achievement")
@RequiredArgsConstructor
public class AchievementProductController {

    private final IAchievementService achievementService;
    private final AchievementFileService achievementFileService;

    @Operation(summary = "商品业绩列表接口")
    @PostMapping("/achievementProductList")
    public WebResult<PageResponse<AchievementProductDetailResponse>> achievementProductList(@RequestBody @Valid AchievementProductPageRequest pageRequest) {

        return WebResult.success(achievementService.achievementProductList(pageRequest));
    }

    @Operation(summary = "导出商品业绩")
    @PostMapping("/exportProductAchievement")
    public void exportProductAchievement(@RequestBody ExcelProductRequest search, HttpServletResponse response) {
        achievementFileService.exportProductAchievement(search, response);
    }

    @Operation(summary = "商品业绩详情")
    @GetMapping("/getAchievementProductDetail")
    public WebResult<AchievementDetailResponse> getAchievementProductDetail(Long achievementId) {
        AchievementDetailResponse achievementDetailResponse = achievementService.getAchievementProductDetail(achievementId);
        return WebResult.success(achievementDetailResponse);
    }

    @Operation(summary = "规格分类业绩详情")
    @GetMapping("/getAchievementCategoryDetail")
    public WebResult<List<AchievementCategoryResponse>> getAchievementCategoryDetail(Long achievementId) {
        List<AchievementCategoryResponse> achievementCategoryDetail = achievementService.getAchievementCategoryDetail(achievementId);
        return WebResult.success(achievementCategoryDetail);
    }

    @Operation(summary = "规格业绩详情")
    @GetMapping("/getAchievementSpecDetail")
    public WebResult<List<AchievementSpecDetailResponse>> getAchievementSpecDetail(Long achievementCategoryId,String orderProductId) {
        List<AchievementSpecDetailResponse> achievementSpecDetail = achievementService.getAchievementSpecDetail(achievementCategoryId,orderProductId);
        return WebResult.success(achievementSpecDetail);
    }

    @Operation(summary = "商务业绩统计查询接口")
    @PostMapping("/businessAchievementList")
    public WebResult<PageResponse<BusinessAchievementResponse>> businessAchievementList(@RequestBody @Valid BusinessSearchRequest pageRequest) {
        PageResponse<BusinessAchievementResponse> businessAchievementResponsePageResponse = achievementService.businessAchievementList(pageRequest);
        return WebResult.success(businessAchievementResponsePageResponse);
    }

    @Operation(summary = "导出商务业绩统计")
    @PostMapping("/exportBusinessAchievement")
    public void exportBusinessAchievement(@RequestBody @Valid BusinessSearchRequest pageRequest, HttpServletResponse response) {
        pageRequest.setPageIndex(1);
        pageRequest.setPageSize(10000);
        PageResponse<BusinessAchievementResponse> pageResponse = achievementService.businessAchievementList(pageRequest);
        List<BusinessAchievementExcel> collect = pageResponse.getList().stream()
                .map(achievementResponse -> BeanUtil.copyProperties(achievementResponse, BusinessAchievementExcel.class))
                .collect(Collectors.toList());
        try {
            EasyExcelUtil.download(response, collect, "商务统计导出", Boolean.TRUE);
        } catch (IOException e) {
            log.error("导出产品业绩失败", e);
        }
    }

    @Operation(summary = "导出商品规格业绩")
    @PostMapping("/exportProductSpecAchievement")
    public void exportProductSpecAchievement(@RequestBody ExcelProductRequest search, HttpServletResponse response) {
        achievementFileService.exportProductSpecAchievement(search, response);
    }

    @Operation(summary = "业绩流水修改接口")
    @PostMapping("/updateAchievement")
    public WebResult<Void> updateAchievement(@RequestBody @Valid AchievementUpdateRequest updateRequest) {
        if (ObjectUtil.isAllEmpty(updateRequest.getStatisticsTime(), updateRequest.getCustomerType(),
                updateRequest.getMainSplitPerson(), updateRequest.getBusinessId(), updateRequest.getBusinessRepresentative(),
                updateRequest.getSpecList())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "必须至少有一项更改");
        }
        CustomerTypeEnum.validate(updateRequest.getCustomerType());
        if (CollUtil.isNotEmpty(updateRequest.getSpecList())) {
            if (updateRequest.getSpecList().stream().anyMatch(t -> ObjectUtil.isNull(t.getId()))) {
                return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, "规格业绩Id不能为空");
            }
        }
        // WebAspect会把手动抛出的异常消息吞了，暂时先这么处理
        try {
            achievementService.updateAchievementAndSpec(updateRequest);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, ((BusinessException) e).getErrorMsg());
            }
            log.error("修改业绩流水异常：", e);
            return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
        }
        return WebResult.success();
    }

    @Operation(summary = "手动导入商品业绩流水")
    @PostMapping("/importAchievement")
    public WebResult<Void> importAchievement(@RequestParam("file") MultipartFile file) {
        try {
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                achievementFileService.excelFlowing(file);
                return WebResult.success();
            } else {
                throw new IllegalArgumentException("只支持 Excel 文件");
            }
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
        } catch (Exception e) {
            log.error("手动导入业绩流水异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
        return WebResult.success();
    }

    @Operation(summary = "校验导入商品业绩文件格式")
    @PostMapping("/importVerifyFile")
    public WebResult<Void> importVerifyFile(@RequestParam("file") MultipartFile file) {
        try {
            if (null == file) {
                throw new IllegalArgumentException("文件不能为空");
            }
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                achievementFileService.verifyFile(file);
                return WebResult.success();
            } else {
                throw new IllegalArgumentException("只支持 Excel 文件");
            }
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
        } catch (Exception e) {
            log.error("校验导入业绩文件格式异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
        return WebResult.success();
    }

    @Operation(summary = "重算订单业绩【excel导入】")
    @PostMapping("/importRecalculateAchievement")
    public WebResult importRecalculateAchievement(@RequestParam("file") MultipartFile file) {
        String[] orderNos = null;
        try {
            if (null == file) {
                throw new IllegalArgumentException("文件不能为空");
            }
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                orderNos = achievementService.recalculateAchievementByExcel(file);
            } else {
                throw new IllegalArgumentException("只支持 Excel 文件");
            }
        } catch (Exception e) {
            log.error("重算订单业绩【excel导入】异常：", e);
            return WebResult.error(WebCodeMessageEnum.RECALCULATE_ACHIEVEMENT_EXCEL_ERROR, e.getMessage());
        }
        return WebResult.success(orderNos);
    }

    @Operation(summary = "重算订单业绩【页面】")
    @PostMapping("/recalculateAchievement")
    public WebResult<Void> recalculateAchievement(@RequestBody @Valid RecalculateAchievementRequest request) {
        try {
            if(request.getOrderNos().length<1){
                throw new BusinessException("订单编号未传入！");
            }
            achievementService.recalculateAchievement(request.getOrderNos(), Lists.newArrayList(request.getOrderNos()).stream().collect(Collectors.joining(",")));
        } catch (Exception e) {
            log.error("重算订单业绩异常：", e);
            return WebResult.error(WebCodeMessageEnum.RECALCULATE_ACHIEVEMENT_PAGE_ERROR, e.getMessage());
        }
        return WebResult.success();
    }

    @Operation(summary = "重算订单明细业绩【excel导入】")
    @PostMapping("/importOrderProduct")
    public WebResult<List<OrderProductResponse>> importOrderProduct(@RequestParam("file") MultipartFile file) {
        try {
            if (null == file) {
                throw new IllegalArgumentException("文件不能为空");
            }
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                return WebResult.success(achievementService.importOrderProduct(file));
            } else {
                throw new IllegalArgumentException("只支持 Excel 文件");
            }
        } catch (Exception e) {
            log.error("重算订单明细业绩【excel导入】异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
    }

    @Operation(summary = "重算订单明细业绩【页面】")
    @PostMapping("/recalculateOrderProductAchievement")
    public WebResult<Void> recalculateOrderProductAchievement(@RequestBody @Valid RecalculateOrderProductRequest request) {
        try {
            achievementService.recalculateOrderProductAchievement(request);
        } catch (Exception e) {
            log.error("重算订单业绩异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
        return WebResult.success();
    }

    @Operation(summary = "手动导入规格业绩流水")
    @PostMapping("/importSpecAchievement")
    public WebResult<Void> importSpecAchievement(@RequestParam("file") MultipartFile file) {
        try {
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                achievementFileService.excelSpecFlowing(file);
                return WebResult.success();
            } else {
                throw new IllegalArgumentException("只支持 Excel 文件");
            }
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
        } catch (Exception e) {
            log.error("手动导入规格业绩流水异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
        return WebResult.success();
    }

    @Operation(summary = "校验导入规格业绩文件格式")
    @PostMapping("/importVerifySpecFile")
    public WebResult<Void> importVerifySpecFile(@RequestParam("file") MultipartFile file) {
        try {
            if (null == file) {
                throw new IllegalArgumentException("文件不能为空");
            }
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                achievementFileService.verifySpecFile(file);
                return WebResult.success();
            } else {
                throw new IllegalArgumentException("只支持 Excel 文件");
            }
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
        } catch (Exception e) {
            log.error("校验导入规格业绩文件格式异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
        return WebResult.success();
    }

    @Operation(summary = "手动导入规格业绩流水退转款")
    @PostMapping("/importSpecAchievementRefund")
    public WebResult<Void> importSpecAchievementRefund(@RequestParam("file") MultipartFile file) {
        try {
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                achievementFileService.excelSpecRefundFlowing(file);
                return WebResult.success();
            } else {
                throw new IllegalArgumentException("只支持 Excel 文件");
            }
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
        } catch (Exception e) {
            log.error("手动导入业绩退转款流水异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
        return WebResult.success();
    }

    @Operation(summary = "校验导入规格业绩退转款文件格式")
    @PostMapping("/importVerifySpecRefundFile")
    public WebResult<Void> importVerifySpecRefundFile(@RequestParam("file") MultipartFile file) {
        try {
            if (null == file) {
                throw new IllegalArgumentException("文件不能为空");
            }
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                achievementFileService.verifySpecRefundFile(file);
                return WebResult.success();
            } else {
                throw new IllegalArgumentException("只支持 Excel 文件");
            }
        } catch (IOException e) {
            log.error("解析Excel表格异常", e);
        } catch (Exception e) {
            log.error("校验导入规格业绩退转款文件格式异常：", e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
        return WebResult.success();
    }


    @Operation(summary = "删除商品业绩列表接口")
    @PostMapping("/deleteAchievement")
    public WebResult<Boolean> deleteAchievement(@RequestBody @Valid DeleteAchievementProductRequest request) {
        return achievementService.deleteAchievement(request);
    }

    // /**
    //  * 导入商品业绩流水
    //  *
    //  * @param file 文件
    //  * @return {@link WebResult }<{@link Void }>
    //  * <AUTHOR>
    //  * @since 1.0
    //  */
    // @Operation(summary = "手动导入商品业绩流水-来源于中企")
    // @PostMapping("/importAchievementFromZq")
    // public WebResult<Void> importAchievementFromZq(@RequestParam("file") MultipartFile file) {
    //     try {
    //         String fileName = file.getOriginalFilename();
    //         if (fileName == null) {
    //             throw new IllegalArgumentException("文件名不能为空");
    //         }
    //         if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
    //             achievementFileService.importKjAchievementFromThird(file);
    //             return WebResult.success();
    //         } else {
    //             throw new IllegalArgumentException("只支持 Excel 文件");
    //         }
    //     } catch (IOException e) {
    //         log.error("解析Excel表格异常", e);
    //     } catch (Exception e) {
    //         log.error("手动导入业绩流水异常：", e);
    //         return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
    //     }
    //     return WebResult.success();
    // }

    @Operation(summary = "重算退款")
    @PostMapping("/recalculateRefund")
    public WebResult<Boolean> recalculateRefund(@RequestBody @Valid RecalculateRefundRequest request) {
        log.info("重算退款请求，业绩ID: {}", request.getAchievementId());

        try {
            Boolean result = achievementService.recalculateRefund(request.getAchievementId());
            return WebResult.success(result);
        } catch (Exception e) {
            log.error("重算退款异常，业绩ID: {}", request.getAchievementId(), e);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, e.getMessage());
        }
    }
}
