package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 规格业绩修改参数
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Schema(description = "规格业绩修改参数")
public class AchievementSpecUpdateRequest implements Serializable {

    private static final long serialVersionUID = 2198701340667260616L;

    /**
     * 商品规格业绩id
     */
    @Schema(description = "商品规格业绩id")
    private Long id;

    /**
     * 净现金
     */
    @Schema(description = "净现金")
    private BigDecimal netCash;

    /**
     * 商代提成业绩
     */
    @Schema(description = "商代提成业绩")
    private BigDecimal agentCommAchv;

    /**
     * 商代实发提成业绩
     */
    @Schema(description = "商代实发提成业绩")
    private BigDecimal agentActCommAchv;

    /**
     * 商代缓发提成业绩
     */
    @Schema(description = "商代缓发提成业绩")
    private BigDecimal agentDefCommAchv;

    /**
     * 部门提成业绩
     */
    @Schema(description = "部门提成业绩")
    private BigDecimal deptCommAchv;

    /**
     * 事业部提成业绩
     */
    @Schema(description = "事业部提成业绩")
    private BigDecimal buCommAchv;

    /**
     * 分公司提成业绩
     */
    @Schema(description = "分公司提成业绩")
    private BigDecimal branchCommAchv;
}
