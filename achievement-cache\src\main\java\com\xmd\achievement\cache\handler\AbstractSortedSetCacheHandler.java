package com.xmd.achievement.cache.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.xmd.achievement.cache.constant.CacheConstant;
import com.xmd.achievement.cache.entity.SortCollectionData;
import com.xmd.achievement.cache.entity.SortedSetData;
import com.xmd.achievement.cache.handler.core.AbstractCacheHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 有序集合缓存处理模板类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 17:18
 */
public abstract class AbstractSortedSetCacheHandler<T> extends AbstractCacheHandler<T, List<SortCollectionData>, List<SortedSetData<T>>> {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(AbstractSortedSetCacheHandler.class);

    /**
     * 升序
     */
    private final static int ASC = 0;
    /**
     * 降序
     */
    private final static int DESC = 1;

    /**
     * 获取排序方式
     *
     * @return int
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    protected int getSortWay() {
        return DESC;
    }

    /**
     * 获取缓存数据
     *
     * @param cacheKey 缓存key
     * @return List<String>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected List<SortCollectionData> getCacheValue(String cacheKey) {
        return getCache().zGet(cacheKey, getSortWay()).stream().map(sortCollectionData -> new SortCollectionData(sortCollectionData.getValue(), sortCollectionData.getWeight())).collect(Collectors.toList());
    }

    /**
     * 设置缓存数据
     *
     * @param cacheKey   缓存key
     * @param cacheValue 缓存值
     * @param expire     缓存有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected void setCacheData(String cacheKey, List<SortCollectionData> cacheValue, long expire) {
        getCache().zSet(cacheKey, cacheValue.stream().map(sortedSetData -> new SortCollectionData(sortedSetData.getValue(), sortedSetData.getWeight())).collect(Collectors.toList()), expire);
    }

    /**
     * 检查是否为空数据
     *
     * @param returnValue 缓存值
     * @return boolean
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected boolean checkNullData(List<SortCollectionData> returnValue) {
        return getNullData().equals(returnValue);
    }

    /**
     * 设置缓存默认数据
     *
     * @return List<SortCollectionData>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected List<SortCollectionData> getNullData() {
        return CacheConstant.CacheNullData.Z_SET;
    }

    /**
     * 缓存数据判空
     *
     * @param sortCollectionDataList 缓存数据
     * @return boolean
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected boolean isEmpty(List<SortCollectionData> sortCollectionDataList) {
        return CollectionUtil.isEmpty(sortCollectionDataList);
    }

    /**
     * 封装缓存value
     *
     * @param customerValue 自定义传入的value
     * @return List<SortCollectionData>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected List<SortCollectionData> packageCacheValue(List<SortedSetData<T>> customerValue) {
        return customerValue.stream().map(this::useToStore).collect(Collectors.toList());
    }

    /**
     * 解析缓存数据
     *
     * @param cacheValue 缓存数据
     * @return List<SortedSetData < T>>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected List<SortedSetData<T>> parseCacheValue(List<SortCollectionData> cacheValue) {
        return cacheValue.stream().map(this::storeToUse).collect(Collectors.toList());
    }

    /**
     * 设置缓存数据
     *
     * @param customerKey   自定义key
     * @param sortedSetData hashKey
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public final void setCacheItem(String customerKey, SortedSetData<T> sortedSetData) {
        getCache().zSetItem(getKey(customerKey), useToStore(sortedSetData));
    }

    /**
     * 获取单个缓存数据
     *
     * @param customerKey 自定义key
     * @param score       分数
     * @return java.util.Set<com.xmd.achievement.cache.entity.SortedSetData < T>>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public final List<SortedSetData<T>> getCacheItem(String customerKey, double score) {
        return getCache().zGetItem(getKey(customerKey), score).stream().map(this::storeToUse).collect(Collectors.toList());
    }

    /**
     * 应用对象转存储对象
     *
     * @param sortedSetData 应用对象
     * @return com.xmd.achievement.cache.entity.SortCollectionData
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    private SortCollectionData useToStore(SortedSetData<T> sortedSetData) {
        return new SortCollectionData(objectToString(sortedSetData.getValue()), sortedSetData.getScore());
    }

    /**
     * 存储对象转应用对象
     *
     * @param sortCollectionData 存储对象
     * @return com.xmd.achievement.cache.entity.SortedSetData<T>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    private SortedSetData<T> storeToUse(SortCollectionData sortCollectionData) {
        return new SortedSetData<T>(stringToObject(sortCollectionData.getValue()), sortCollectionData.getWeight());
    }
}
