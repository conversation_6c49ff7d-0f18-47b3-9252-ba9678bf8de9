package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 第三方业绩表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("third_achievement")
public class ThirdAchievementModel extends BaseModel {
    private static final long serialVersionUID = 1L;
    /**
     * 业绩流水ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * taskId
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 来源：EXCEL-表格 ONLINE实时
     */
    @TableField("third_source")
    private String thirdSource;

    /**
     * task任务类型：ADD-新增 UPDATE-修改 DELETE-删除
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 三方ID
     */
    @TableField("third_id")
    private String thirdId;
    /**
     * 商务月ID
     */
    @TableField("business_month_id")
    private String bizMonthId;
    /**
     * 商务月
     */
    @TableField("business_month")
    private String businessMonth;
    /**
     * 订单明细编号
     */
    @TableField("order_detail_no")
    private String orderDetailNo;
    /**
     * 服务编号
     */
    @TableField("serve_no")
    private String serveNo;
    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;
    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;
    /**
     * 商品类型
     */
    @TableField("product_type")
    private Integer productType;
    /**
     * 业务类型：1新开,2续费,20转款,4另购充值,3310域名转入,3扩容,7升级(ZT升级ZT),5高价赎回,8zadd升级,3115Z+升级ZTSZT,3116Z+升级ZTSZM,6转入,3117NZ+升级ZTSZT,3118	NZ+升级ZTSZM,169大把推续费升级,12DSP升级,18退款,88升级,188门户扩展升级
     */
    @TableField("business_type")
    private Integer businessType;
    /**
     * 是否是网站 0=否 1=是
     */
    @TableField("web_info")
    private Integer webInfo;
    /**
     * 业务类型 "0","有效"
     * "1","失效"
     * "2","已退"
     * "3","已完成"
     */
    @TableField("state")
    private Integer state;
    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;
    /**
     * 订单编号
     */
    @TableField("order_record_code")
    private String orderRecordCode;
    /**
     * 订单来源：1BOSS,18新平台线上,19新平台线下,20新平台电子签单,22新云市场
     */
    @TableField("data_source")
    private Integer dataSource;
    /**
     * 客户ID
     */
    @TableField("cust_id")
    private String custId;
    /**
     * 客户名称
     */
    @TableField("cust_name")
    private String custName;
    /**
     * 客户类型 1老 2新 3非新老
     */
    @TableField("cust_type")
    private Integer custType;
    /**
     * 客户所在省
     */
    @TableField("province_code")
    private String provinceCode;
    /**
     * 客户所在省名字
     */
    @TableField("province_name")
    private String provinceName;
    /**
     * 客户所在城市ID
     */
    @TableField("cust_city")
    private String custCity;
    /**
     * 客户所在城市名字
     */
    @TableField("city_name")
    private String cityName;
    /**
     * 客户所在县区ID
     */
    @TableField("district_code")
    private String districtCode;
    /**
     * 客户所在县区名字
     */
    @TableField("district_name")
    private String districtName;
    /**
     * 客户所在区
     */
    @TableField("customer_region")
    private String customerRegion;
    /**
     * 合同编号
     */
    @TableField("contract_code")
    private String contractCode;
    /**
     * 商务ID
     */
    @TableField("saler_id")
    private String salerId;
    /**
     * 商务代表
     */
    @TableField("saler_name")
    private String salerName;
    /**
     * 主分单人 0副 1主
     */
    @TableField("share_type")
    private Integer shareType;
    /**
     * 公司ID
     */
    @TableField("org_id")
    private String orgId;
    /**
     * 所属区域
     */
    @TableField("area_id")
    private String areaId;
    /**
     * 公司
     */
    @TableField("company")
    private String company;
    /**
     * 事业部ID
     */
    @TableField("bu_id")
    private String buId;
    /**
     * 事业部
     */
    @TableField("division")
    private String division;
    /**
     * 部门ID
     */
    @TableField("dept_id")
    private String deptId;
    /**
     * 部门
     */
    @TableField("department")
    private String department;
    /**
     * 标准价
     */
    @TableField("standard_price")
    private BigDecimal standardPrice;
    /**
     * 应付金额
     */
    @TableField("singing_amount")
    private Double singingAmount;
    /**
     * 实付金额
     */
    @TableField("actual_account")
    private Double actualAccount;
    /**
     * 折扣比例
     */
    @TableField("discount_account")
    private Double discountAccount;
    /**
     * 交付方式: 1-软件交付, 2-服务交付
     */
    @TableField("delivery_method")
    private Integer deliveryMethod;
    /**
     * 订单类型：1=普通订单，2=折扣订单
     */
    @TableField("order_type")
    private Integer orderType;
    /**
     * 签单时间
     */
    @TableField("singing_date")
    private Date singingDate;
    /**
     * 付款时间
     */
    @TableField("to_account_date")
    private Date toAccountDate;
    /**
     * 首年报价
     */
    @TableField("first_standard_account")
    private Double firstStandardAccount;
    /**
     * 首年到账金额
     */
    @TableField("first_actual_account")
    private Double firstActualAccount;
    /**
     * 续费报价
     */
    @TableField("renew_standard_account")
    private Double renewStandardAccount;
    /**
     * 续费到账金额
     */
    @TableField("renew_actual_account")
    private Double renewActualAccount;
    /**
     * 净现金
     */
    @TableField("net_cash_account")
    private Double netCashAccount;
    /**
     * 商代提成业绩
     */
    @TableField("sale_hired_money")
    private Double saleHiredMoney;
    /**
     * 商代实发提成业绩
     */
    @TableField("relay_sale_hired_money")
    private Double relaySaleHiredMoney;
    /**
     * 商代缓发提成业绩
     */
    @TableField("delay_sale_hired_money")
    private Double delaySaleHiredMoney;
    /**
     * 部门提成业绩
     */
    @TableField("manager_hired_money")
    private Double managerHiredMoney;
    /**
     * 事业部提成业绩
     */
    @TableField("current_price")
    private Double currentPrice;
    /**
     * 分司提成业绩
     */
    @TableField("sub_manager_hired_money")
    private Double subManagerHiredMoney;
    /**
     * 服务完成时间
     */
    @TableField("serve_finish_time")
    private Date serveFinishTime;
    /**
     * 数据状态 0正常数据 1异常数据  3删除数据
     */
    @TableField("data_state")
    private Integer dataState;
    /**
     * 业绩生成时间
     */
    @TableField("db_insert_time")
    private Date dbInsertTime;
    /**
     * 创建人ID
     */
    @TableField("creater")
    private String creater;
    /**
     * 创建人名称
     */
    @TableField("create_name")
    private String createName;
    /**
     * 更新时间
     */
    @TableField("db_update_time")
    private Date dbUpdateTime;
    /**
     * 更新人ID
     */
    @TableField("updater")
    private String updater;
    /**
     * 更新人名称
     */
    @TableField("update_name")
    private String updateName;
    /**
     * 任务状态：1-未完成 2-已完成
     */
    @TableField("task_status")
    private Integer taskStatus;
    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;
    /**
     * 失败次数
     */
    @TableField("fail_count")
    private Integer failCount;

    /**
     * 是否展示 0 是，1 否
     */
    @TableField("displayed")
    private Integer displayed;
}