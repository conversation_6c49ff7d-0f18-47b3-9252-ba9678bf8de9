package com.xmd.achievement.dao.repository;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface IMqOrderPaymentInfoRepository extends IService<MqOrderPaymentInfoModel> {

    List<MqOrderPaymentInfoModel> selectListByOrderIdAndInstallment(Long orderId, List<Integer> installmentNumList,Long productId, String serveNo);

    MqOrderPaymentInfoModel selectCheckOne(Long orderId, Long productId, String orderProductId, Integer installment);

    void updateByOrderId(Long orderId,Integer calculateType);

    List<MqOrderPaymentInfoModel> selectByOrderIdAndCalculateType(Long orderId, Integer calculateType);

    List<MqOrderPaymentInfoModel> selectListByOrderId(Long orderId);
}
