package com.xmd.achievement.util.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/18 10:57
 * @version: 1.0.0
 * @return {@link }
 */
@Getter
public enum StatusEnum {
    /**
     * 1 有效
     */
    VALID(1, "有效"),
    /**
     * 2 已完成
     */
    COMPLETED(2, "已完成"),
    /**
     * 3 无效
     */
    NOT_VALID(3, "无效"),
    /**
     * 4 已退
     */
    REFUND(4, "已退"),
    ;

    private final Integer status;
    private final String message;


    StatusEnum(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public static List<Integer> getStatusList() {
        return Arrays.stream(StatusEnum.values()).map(StatusEnum::getStatus).collect(Collectors.toList());
    }

    public Integer getCode() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public static StatusEnum fromCode(int code) {
        for (StatusEnum value : StatusEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("StatusEnum code: " + code);
    }
}
