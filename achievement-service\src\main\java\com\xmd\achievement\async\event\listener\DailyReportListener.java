package com.xmd.achievement.async.event.listener;

import cn.hutool.core.util.ObjectUtil;
import com.xmd.achievement.async.event.ProductAchievementUpdatedEvent;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.handler.achievement.ReportHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 机构日报告监听器
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DailyReportListener {

    private final ReportHandler reportHandler;

    @Transactional
    @TransactionalEventListener
    @Async("achievementUpdateListenerExecutor")
    public void onMonthlyReport(ProductAchievementUpdatedEvent event) {
        AchievementProductDetailModel oldAchievement = event.getOldAchievement();
        AchievementProductDetailModel newAchievement = event.getNewAchievement();
        log.info("机构日报告监听器，监听到{}，事件内容：{}", event.getEVENT_NAME(), event);
        this.updateMonthlyReport(oldAchievement, newAchievement);
    }

    private void updateMonthlyReport(AchievementProductDetailModel oldAchievement, AchievementProductDetailModel newAchievement) {
        if (oldAchievement == null) {
            reportHandler.processDailyReport(newAchievement.getBusinessMonth());
            return;
        }
        if (newAchievement == null) {
            reportHandler.processDailyReport(oldAchievement.getBusinessMonth());
            return;
        }
        if (dataChanged(oldAchievement.getStatisticsTime(), newAchievement.getStatisticsTime())) {
            if (dataChanged(oldAchievement.getBusinessMonth(), newAchievement.getBusinessMonth())) {
                reportHandler.processDailyReport(newAchievement.getBusinessMonth());
                reportHandler.processDailyReport(oldAchievement.getBusinessMonth());
                return;
            }
            reportHandler.processDailyReport(newAchievement.getBusinessMonth());
            return;
        }
        // 客户类型变动，需要重算新客户数
        if (dataChanged(oldAchievement.getCustomerType(), newAchievement.getCustomerType())) {
            reportHandler.processDailyReport(oldAchievement.getBusinessMonth());
            return;
        }
        // 规格业绩有一条修改就需要重算
        if (!ObjectUtil.isAllEmpty(newAchievement.getNetCash(), newAchievement.getAgentCommissionAchievement(),
                newAchievement.getAgentActualCommission(), newAchievement.getAgentDeferredCommission(),
                newAchievement.getDeptCommission(), newAchievement.getDivCommission(), newAchievement.getBranchCommission())) {
            reportHandler.processDailyReport(oldAchievement.getBusinessMonth());
        }
    }

    private boolean dataChanged(Object existing, Object current) {
        return ObjectUtil.isNotEmpty(current) && ObjectUtil.notEqual(existing, current);
    }
}
