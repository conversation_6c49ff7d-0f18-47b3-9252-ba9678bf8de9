package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 商务月冻结异常日志表
 */
@Data
@TableName("freeze_month_error_log")
public class FreezeMonthErrorLogModel  extends BaseModel{
    /** 自增主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 商务月id */
    @TableField("month_id")
    private Long monthId;

    /** 商务月 */
    @TableField("month")
    private String month;

    /** 数据来源系统/模块 */
    @TableField("source_system")
    private String sourceSystem;

    /** 操作对象 */
    @TableField("class_name")
    private String className;

    /** 变更前数据（JSON格式） */
    @TableField("data")
    private String data;

}
