package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 规格组合表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("spec_combination")
public class SpecCombinationModel  extends BaseModel  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 组合id
     */
    @TableField("combination_id")
    private Long combinationId;
    /**
     * 组合名称
     */
    @TableField("combination_name")
    private String combinationName;
    /**
     * 新开政策性成本
     */
    @TableField("new_policy_cost")
    private BigDecimal newPolicyCost;
    /**
     * 续费政策性成本
     */
    @TableField("renewal_policy_cost")
    private BigDecimal renewalPolicyCost;
    /**
     * 升级政策性成本
     */
    @TableField("upgrade_policy_cost")
    private BigDecimal upgradePolicyCost;
    /**
     * 另购政策性成本
     */
    @TableField("additional_policy_cost")
    private BigDecimal additionalPolicyCost;
    /**
     * 状态 1=启用 2=禁用
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 商品规格组合List
     */
    @TableField("spec_combination_list")
    private String specCombinationList;

    /**
     * 商品组合备注
     */
    @TableField("`remark`")
    private String remark;

}