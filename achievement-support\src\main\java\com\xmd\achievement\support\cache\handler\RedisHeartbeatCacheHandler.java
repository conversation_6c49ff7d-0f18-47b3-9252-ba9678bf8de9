package com.xmd.achievement.support.cache.handler;

import com.xmd.achievement.cache.constant.CacheConstant;
import com.xmd.achievement.cache.enumeration.CacheKeyEnum;
import com.xmd.achievement.cache.handler.AbstractStringCacheHandler;
import org.springframework.stereotype.Service;

/**
 * 维持redis心跳
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/6 17:18
 **/
@Service
public class RedisHeartbeatCacheHandler extends AbstractStringCacheHandler<String> {

    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.REDIS_HEARTBEAT;
    }

    @Override
    public String queryDataBySource(String customerKey) {
        return customerKey;
    }

    /**
     * 设置缓存失效时间
     * 方便使用分布式锁，顺带每分钟发送一次redisson的心跳
     *
     * @return long
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    public long getExpire() {
        return CacheConstant.CacheExpire.ONE_MINUTES;
    }

    /**
     * 指定对象类型
     *
     * @return java.lang.Class<T>
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected Class<String> getClazz() {
        return String.class;
    }
}
