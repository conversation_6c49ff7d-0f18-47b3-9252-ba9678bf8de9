package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.SpecCombinationDetailModel;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PageQueryspecCombinationListResponse;
import com.xmd.achievement.service.entity.response.QuerySpecCombinationDetailResponse;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;

import java.util.List;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/19 16:44
 * @version: 1.0.0
 * @return {@link }
 */
public interface ISpecCombinationService {
    /**
     * 保存或修改规格组合信息
     *
     * @param specCombinationDto 规格组合数据传输对象
     * @return 操作结果
     */
    WebResult<Boolean> saveSpecCombination(SpecCombinationRequest specCombinationDto);

    /**
     * 分页查询规格组合列表接口
     *
     * @param request 请求参数
     * @return PageResponse<PageQueryspecCombinationListResponse>
     */
    WebResult<PageResponse<PageQueryspecCombinationListResponse>> pageQuerySpecCombinationList(PageQueryspecCombinationListRequest request);


    /**
     * 查询规格组合详情
     *
     * @param request 请求参数
     * @return WebResult<QuerySpecCombinationDetailResponse>
     */
    WebResult<QuerySpecCombinationDetailResponse> querySpecCombinationDetail(QuerySpecCombinationDetailRequest request);

    /**
     * 编辑规格组合
     *
     * @param request 请求参数
     * @return Boolean
     */
    WebResult<Boolean> updateSpecCombination(UpdateSpecCombinationRequest request);

    /**
     * 删除规格组合
     *
     * @param request 请求参数
     * @return Boolean
     */
    WebResult<Boolean> delSpecCombination(DelSpecCombinationRequest request);


    /**
     * 禁用/启用规格组合
     *
     * @param request 请求参数
     * @return Boolean
     */
    WebResult<Boolean> updateSpecCombinationStatus(UpdateSpecCombinationStatusRequest request);

    Map<Long, List<SpecCombinationDetailModel>> getAllSpecCombinationPrice(Integer orderSaleType);

    Map<Long, List<Long>> getAllCombinationSpecIds();
}
