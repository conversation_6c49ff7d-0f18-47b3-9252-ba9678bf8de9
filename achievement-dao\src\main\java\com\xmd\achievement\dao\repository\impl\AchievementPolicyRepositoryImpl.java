package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.AchievementPolicyModel;
import com.xmd.achievement.dao.mapper.AchievementPolicyMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IAchievementPolicyRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 业绩政策表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@Slf4j
public class AchievementPolicyRepositoryImpl extends ServiceImpl<AchievementPolicyMapper, AchievementPolicyModel> implements IAchievementPolicyRepository {

    @Resource
    private AchievementPolicyMapper achievementPolicyMapper;

}