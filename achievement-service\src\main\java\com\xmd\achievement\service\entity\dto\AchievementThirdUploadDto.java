package com.xmd.achievement.service.entity.dto;

import com.opencsv.bean.CsvBindByName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class AchievementThirdUploadDto implements Serializable {

    private static final long serialVersionUID = -6823907677316917957L;

    /**
     * 三方业绩流水ID
     */
    @CsvBindByName(column = "thirdId")
    private String thirdId;

    /**
     * 商务月
     */
    @CsvBindByName(column = "businessMonth")
    private String businessMonth;

    /**
     * 订单明细编号
     */
    @CsvBindByName(column = "orderDetailNo")
    private String orderDetailNo;

    /**
     * 商品ID
     */
    @CsvBindByName(column = "productId")
    private Long productId;

    /**
     * 商品名称
     */
    @CsvBindByName(column = "productName")
    private String productName;

    /**
     * 商品类型
     */
    @CsvBindByName(column = "productType")
    private String productType;

    /**
     * 业务类型
     */
    @CsvBindByName(column = "businessType")
    private Integer businessType;

    /**
     * 是否网站
     */
    @CsvBindByName(column = "webInfo")
    private Integer webInfo;

    /**
     * 业绩状态
     */
    @CsvBindByName(column = "state")
    private Integer state;

    /**
     * 订单编号
     */
    @CsvBindByName(column = "orderRecordCode")
    private String orderRecordCode;

    /**
     * 客户ID
     */
    @CsvBindByName(column = "custId")
    private String custId;

    /**
     * 客户名称
     */
    @CsvBindByName(column = "custName")
    private String custName;

    /**
     * 客户类型
     */
    @CsvBindByName(column = "custType")
    private Integer custType;

    /**
     * 客户城市
     */
    @CsvBindByName(column = "custCity")
    private String custCity;

    /**
     * 合同编号
     */
    @CsvBindByName(column = "contractCode")
    private String contractCode;

    /**
     * 商务ID
     */
    @CsvBindByName(column = "salerId")
    private String salerId;

    /**
     * 商务代表
     */
    @CsvBindByName(column = "salerName")
    private String salerName;

    /**
     * 主分单人：1=主，2=辅
     */
    @CsvBindByName(column = "shareType")
    private Integer shareType;

    /**
     * 公司ID
     */
    @CsvBindByName(column = "orgId")
    private Long orgId;

    /**
     * 区域ID
     */
    @CsvBindByName(column = "areaId")
    private Long areaId;

    /**
     * 事业部ID
     */
    @CsvBindByName(column = "buId")
    private Long buId;

    /**
     * 部门ID
     */
    @CsvBindByName(column = "deptId")
    private Long deptId;

    /**
     * 标准价（签单金额）
     */
    @CsvBindByName(column = "singingAmount")
    private BigDecimal singingAmount;

    /**
     * 实付金额
     */
    @CsvBindByName(column = "actualAccount")
    private BigDecimal actualAccount;

    /**
     * 折扣比例
     */
    @CsvBindByName(column = "discountAccount")
    private BigDecimal discountAccount;

    /**
     * 签单时间
     */
    @CsvBindByName(column = "singingDate")
    private Date singingDate;

    /**
     * 到账时间
     */
    @CsvBindByName(column = "toAccountDate")
    private Date toAccountDate;

    /**
     * 首年报价
     */
    @CsvBindByName(column = "firstStandardAccount")
    private BigDecimal firstStandardAccount;

    /**
     * 首年到账金额
     */
    @CsvBindByName(column = "firstActualAccount")
    private BigDecimal firstActualAccount;

    /**
     * 续费报价
     */
    @CsvBindByName(column = "renewStandardAccount")
    private BigDecimal renewStandardAccount;

    /**
     * 续费到账金额
     */
    @CsvBindByName(column = "renewActualAccount")
    private BigDecimal renewActualAccount;

    /**
     * 净现金
     */
    @CsvBindByName(column = "netCashAccount")
    private BigDecimal netCashAccount;

    /**
     * 商代提成业绩
     */
    @CsvBindByName(column = "saleHiredMoney")
    private BigDecimal saleHiredMoney;

    /**
     * 商代实发提成
     */
    @CsvBindByName(column = "relaySaleHiredMoney")
    private BigDecimal relaySaleHiredMoney;

    /**
     * 商代缓发提成
     */
    @CsvBindByName(column = "delaySaleHiredMoney")
    private BigDecimal delaySaleHiredMoney;

    /**
     * 部门提成
     */
    @CsvBindByName(column = "managerHiredMoney")
    private BigDecimal managerHiredMoney;

    /**
     * 当前标准价
     */
    @CsvBindByName(column = "currentPrice")
    private BigDecimal currentPrice;

    /**
     * 事业部提成
     */
    @CsvBindByName(column = "subManagerHiredMoney")
    private BigDecimal subManagerHiredMoney;

    /**
     * 数据状态
     */
    @CsvBindByName(column = "dataState")
    private Integer dataState;

    /**
     * 数据插入时间
     */
    @CsvBindByName(column = "dbInsertTime")
    private Date dbInsertTime;

    /**
     * 创建人
     */
    @CsvBindByName(column = "creater")
    private String creater;

    /**
     * 数据更新时间
     */
    @CsvBindByName(column = "dbUpdateTime")
    private Date dbUpdateTime;

    /**
     * 更新人
     */
    @CsvBindByName(column = "updater")
    private String updater;

    /**
     * 修改备注
     */
    @CsvBindByName(column = "remark")
    private String remark;

    /**
     * 业绩流水ID
     */
    @CsvBindByName(column = "achievementId")
    private Long achievementId;

    /**
     * 订单ID
     */
    @CsvBindByName(column = "orderId")
    private Long orderId;
}
