# BSP Achievement System - API接口索引

## API接口概览

基于 Spring Boot + Swagger/Knife4j 的 REST API 设计，所有接口返回统一的 `WebResult<T>` 格式。

## 控制器模块

### 1. 订单管理 (OrderController)
**路径**: `/order`
**标签**: "订单数据处理"

#### 接口列表
- `POST /order/manualOrder` - 手动订单处理
  - 请求体: `ManualOrderRequest`
  - 响应: `List<ManualOrderResponse>`
  
- `POST /order/importManualOrderExcel` - 导入手动订单Excel
  - 参数: `MultipartFile file`
  - 响应: `List<ManualOrderExcelResponse>`

### 2. 政策管理 (PolicyController)
**路径**: `/policy`
**标签**: "POC-业绩政策接口"

#### 接口列表
- `POST /policy/savePolicy` - 添加规格业绩政策配置
  - 请求体: `SavePolicyRequest`
  - 响应: `Boolean`
  
- `POST /policy/queryPolicyList` - 查询规格业绩政策配置
  - 请求体: `QueryPolicyListRequest`
  - 响应: `PolicySpecResponse`

### 3. 业绩黑名单管理 (AchievementBlacklistController)
**路径**: `/achievement-blacklist` (推测)
**功能**: 业绩黑名单的增删改查

### 4. 商务月度管理 (BusinessMonthController)
**路径**: `/business-month` (推测)
**功能**: 商务月度数据管理

### 5. 客户SaaS管理 (CustomerSaasController)
**路径**: `/customer-saas` (推测)
**功能**: 客户SaaS相关业务处理

### 6. 数据修复 (DataRepairController)
**路径**: `/data-repair` (推测)
**功能**: 数据修复和维护接口

### 7. 性能导出 (ExportPerformanceController)
**路径**: `/export-performance` (推测)
**功能**: 性能数据导出功能

### 8. 组织报表 (OrganizationReportController)
**路径**: `/organization-report` (推测)
**功能**: 组织架构相关报表

### 9. 分段管理 (SegmentController)
**路径**: `/segment` (推测)
**功能**: 业务分段管理

### 10. 消息队列数据管理 (MqDataController)
**路径**: `/mq-data` (推测)
**功能**: 消息队列数据处理和监控

## 通用响应格式

### WebResult<T> 结构
```java
{
  "code": "响应码",
  "message": "响应消息",
  "data": "具体数据",
  "success": "是否成功"
}
```

## 请求/响应实体

### 订单相关
- `ManualOrderRequest` - 手动订单请求
- `ManualOrderResponse` - 手动订单响应
- `ManualOrderExcelResponse` - 手动订单Excel响应

### 政策相关
- `SavePolicyRequest` - 保存政策请求
- `QueryPolicyListRequest` - 查询政策列表请求
- `QuerySpecByproductIdRequest` - 根据商品ID查询规格请求
- `PolicySpecResponse` - 政策规格响应
- `PolicySpecDetailResponse` - 政策规格详情响应

## API设计规范

### 1. 路径规范
- 使用小写字母和连字符
- RESTful 风格设计
- 统一前缀路径

### 2. 请求方式
- 查询操作: GET (部分使用POST)
- 创建操作: POST
- 更新操作: PUT
- 删除操作: DELETE

### 3. 参数验证
- 使用 `@Valid` 注解进行参数校验
- 请求体使用 `@RequestBody`
- 文件上传使用 `@RequestParam`

### 4. 文档注解
- 使用 Swagger3 注解 (`@Operation`, `@Tag`)
- 提供接口描述和标签分类

## 外部服务集成

### InnerService 接口
提供与外部系统的集成接口:
- 订单管理系统
- 客户管理系统
- 商品管理系统
- 合同管理系统
- 服务管理系统
- 广告管理系统
- EHR系统
- CRM系统

## 异步处理接口

### 消息队列处理
- 订单支付信息处理
- 订单退款信息处理
- 服务完成时间信息处理

### 定时任务
- 日报生成
- 月报生成
- 数据传输任务