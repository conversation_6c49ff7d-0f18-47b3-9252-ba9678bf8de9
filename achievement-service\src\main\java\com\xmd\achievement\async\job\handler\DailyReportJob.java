package com.xmd.achievement.async.job.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.handler.achievement.ReportHandler;
import com.xmd.achievement.util.date.DateTimeFormatUtil;
import com.xmd.achievement.util.date.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 机构日报job
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class DailyReportJob {

    @Autowired
    private ReportHandler reportHandler;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.DAILY_REPORT_JOB)
    public ReturnT<String> jobHandler(String param) {
        if (ObjectUtil.isNotEmpty(param)) {
            JSONObject jsonObject = JSON.parseObject(param);
            String currentDate = jsonObject.getString("currentDate");
            extracted(currentDate);
        } else {
            extracted(null);
        }

        return ReturnT.SUCCESS;
    }

    private void extracted(String currentDate) {
        log.info("执行DailyReportJob任务Start...");
        try {
            reportHandler.processDailyReport(currentDate);
        } catch (Exception e) {
            log.error("DailyReportJob任务失败,失败原因：", e);
        }
        log.info("执行DailyReportJob任务End...");
    }
}
