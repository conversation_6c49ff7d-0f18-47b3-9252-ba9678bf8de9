package com.xmd.achievement.service.entity.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 业绩黑名单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
public class AchievementBlacklistResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    private Long id;
    /**
     * 黑名单id
     */
    private Long blacklistId;
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 删除标记: 0未删除|1删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 更新人名称
     */
    private String updateUserName;
}