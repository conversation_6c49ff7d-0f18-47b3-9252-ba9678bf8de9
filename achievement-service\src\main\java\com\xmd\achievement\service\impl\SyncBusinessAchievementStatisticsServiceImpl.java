package com.xmd.achievement.service.impl;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IBusinessMonthRepository;
import com.xmd.achievement.handler.statistics.BusinessAchHandler;
import com.xmd.achievement.service.SyncBusinessAchievementStatisticsService;
import com.xmd.achievement.util.enums.BusinessAchievementUpdateTypeEnum;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SyncBusinessAchievementStatisticsServiceImpl implements SyncBusinessAchievementStatisticsService {


    @Resource
    private IBusinessMonthRepository businessMonthRepository;

    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    private BusinessAchHandler businessAchHandler;

    @Override
    @Lock("'syncBusinessAchievementStatistics_' + #employeeId + '_' + #businessMonth")
    public void syncBusinessAchievementStatistics(String employeeId,String businessMonth,Boolean isSyncAll) {
        log.info("SyncBusinessAchievementStatisticsServiceImpl.syncBusinessAchievementStatistics employeeId:{} businessMonth:{}",employeeId,businessMonth);
        List<AchievementProductDetailModel> achList =  null;

        if(null != isSyncAll && isSyncAll){
            //同步全量
            //查询全部业绩
            achList =  achievementProductDetailRepository.selectAchievementProductDetailListByMonth(null,employeeId);
        }else if(StringUtils.isEmpty(employeeId) && StringUtils.isEmpty(businessMonth)){
            String currentMonth = DateUtils.formatDate(new Date(), DateTimeFormatStyleEnum.yyyy_MM.getCode());
            achList =  achievementProductDetailRepository.selectAchievementProductDetailListByMonth(currentMonth,employeeId);

        }else{
            //指定重跑
            if(!StringUtils.isEmpty(businessMonth)){
                achList =  achievementProductDetailRepository.selectAchievementProductDetailListByMonth(businessMonth,employeeId);
            }
        }
        //根据商务月分组
        Map<String, List<AchievementProductDetailModel>> byAchievementMonthMap = achList.stream().filter(ach-> StringUtils.isNotEmpty(ach.getBusinessMonth())).collect(Collectors.groupingBy(AchievementProductDetailModel::getBusinessMonth));

        for (Map.Entry<String, List<AchievementProductDetailModel>> entry : byAchievementMonthMap.entrySet()) {
            List<AchievementProductDetailModel> list = entry.getValue();
            if (StringUtils.isBlank(employeeId)) {
                businessAchHandler.achStatistics(list, null, BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
                continue;
            }
            businessAchHandler.achStatisticsForEmployeeId(list, null, BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
        }
    }
}
