package com.xmd.achievement.dao.mapper;

import com.xmd.achievement.dao.entity.AchievementCategoryDetailSelectModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 业绩规格分类明细表(查询) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface AchievementCategoryDetailSelectMapper extends BaseMapper<AchievementCategoryDetailSelectModel> {
    void batchInsertOrUpdate(@Param("list") List<AchievementCategoryDetailSelectModel> list);
}
