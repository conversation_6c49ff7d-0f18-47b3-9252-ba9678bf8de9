package com.xmd.achievement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.BaseModel;
import com.xmd.achievement.dao.repository.IAchievementSpecDetailRepository;
import com.xmd.achievement.service.IAchievementSpecDetailService;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.util.enums.RevenueNodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 业绩规格明细接口实现
 *
 * <AUTHOR>
 * @date: 2024/12/19 09:57
 */
@Service
public class AchievementSpecDetailServiceImpl implements IAchievementSpecDetailService {
    @Resource
    private IAchievementSpecDetailRepository achievementSpecDetailRepository;

    @Override
    public void save(AchievementSpecDetailModel spec) {
        achievementSpecDetailRepository.save(spec);
    }

    @Override
    public void saveBatch(List<AchievementSpecDetailModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        achievementSpecDetailRepository.saveBatch(models, models.size());
    }

    @Override
    public void updateBatch(List<AchievementSpecDetailModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        achievementSpecDetailRepository.updateBatchById(models, models.size());
    }

    @Override
    public List<AchievementSpecDetailModel> listByAchievementCategoryId(Long achievementCategoryId) {
        if (null == achievementCategoryId) {
            return Collections.emptyList();
        }
        return achievementSpecDetailRepository.list(new LambdaQueryWrapper<AchievementSpecDetailModel>().eq(AchievementSpecDetailModel::getAchievementCategoryId, achievementCategoryId));
    }

    @Override
    public void saveOrUpdateBatch(List<AchievementSpecDetailModel> specList) {
        if (CollectionUtils.isNotEmpty(specList)) {
            achievementSpecDetailRepository.saveOrUpdateBatch(specList, specList.size());
        }
    }

    @Override
    public List<AchievementSpecDetailModel> queryValidSpcList(Long orderId, Long productId,Integer installmentNum,String orderProductId) {
        return achievementSpecDetailRepository.list(
                new LambdaQueryWrapper<AchievementSpecDetailModel>()
                        .eq(AchievementSpecDetailModel::getOrderId, orderId)
                        //.eq(AchievementSpecDetailModel::getProductId, productId)
                        .eq(AchievementSpecDetailModel::getInstallmentNum, installmentNum)
                        .eq(StringUtils.isNotEmpty(orderProductId), AchievementSpecDetailModel::getOrderProductId, orderProductId)
                        .eq(BaseModel::getDeleteFlag, NumberConstants.INTEGER_VALUE_0)
                        .eq(AchievementSpecDetailModel::getRevenueNode, RevenueNodeEnum.PRODUCTION_COMPLETED.getNode()));

    }
}
