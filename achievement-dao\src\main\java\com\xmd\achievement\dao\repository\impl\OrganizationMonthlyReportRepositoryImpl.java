package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xmd.achievement.dao.entity.OrganizationMonthlyReportModel;
import com.xmd.achievement.dao.mapper.OrganizationMonthlyReportMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IOrganizationMonthlyReportRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 机构月报 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Service
@Slf4j
public class OrganizationMonthlyReportRepositoryImpl extends ServiceImpl<OrganizationMonthlyReportMapper,OrganizationMonthlyReportModel> implements IOrganizationMonthlyReportRepository {

@Resource
private OrganizationMonthlyReportMapper organizationMonthlyReportMapper;

    /**
     * 批量插入或者更新月报
     * @param list
     */
    @Override
    public void batchInsertOrUpdate(List<OrganizationMonthlyReportModel> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        organizationMonthlyReportMapper.batchInsertOrUpdate(list);
    }

    @Override
    public void deleteByMonth(String preMonth) {
        LambdaUpdateWrapper<OrganizationMonthlyReportModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrganizationMonthlyReportModel::getBusinessMonth,preMonth);
        organizationMonthlyReportMapper.delete(updateWrapper);
    }
}