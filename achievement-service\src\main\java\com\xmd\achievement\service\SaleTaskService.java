package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.SaleTaskModel;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.OpenSaleTaskResponse;
import com.xmd.achievement.service.entity.response.QuerySaleTaskListResponse;
import com.xmd.achievement.service.entity.response.SaleTaskListInfoResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


/**
 * 销售任务
 */
public interface SaleTaskService {

    /**
     * 销售任务列表查询
     *
     * @param request 页面查询条件
     * @return
     */
    QuerySaleTaskListResponse queryList(QuerySaleTaskListRequest request);

    /**
     * 获取销售任务提交弹出框信息
     * @param businessMonth 商务月
     * @return
     */
    List<QuerySaleTaskListResponse> getPreCommitInfo(String businessMonth);

    /**
     * 提交任务
     *
     * @param tasks 销售任务
     */
    WebResult<Void> taskCommit(SaleTaskCommitRequest tasks);

    /**
     * 撤销任务
     * @param id 主键
     */
    void revoke(Long id);

    /**
     * 任务查看
     *
     * @param id 主键
     * @return
     */
    SaleTaskListInfoResponse view(Long id);

    /**
     * 任务审核
     * @param auditRequest 任务id集合 + 审核信息
     */
    void audit(SaleTaskAuditRequest auditRequest);

    /**
     * 编辑信息
     *
     * @param id 主键
     * @return
     */
    SaleTaskListInfoResponse editInfo(Long id);

    /**
     * 任务编辑
     *
     * @param request 任务列表
     */
    WebResult<Void> edit(SaleTaskEditRequest request);

    /**
     * 根据机构id和商务月获取销售任务
     * @param orgId 机构id
     * @param businessMonth 商务月
     * @return
     */
    SaleTaskModel getByOrgIdAndBusinessMonth(Long orgId, String businessMonth);

    void excelImport(MultipartFile file) throws IOException;

    List<OpenSaleTaskResponse> openSaleTask(OpenSaleTaskRequest request);
}
