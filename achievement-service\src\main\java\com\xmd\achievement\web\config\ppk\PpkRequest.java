package com.xmd.achievement.web.config.ppk;

import lombok.Data;

import java.io.Serializable;

/**
 * RSA请求体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/3/18 3:07 下午
 */
@Data
public class PpkRequest<T> implements Serializable {


    private static final long serialVersionUID = -5185003254259246917L;
    /**
     * 随机串
     */
    private String nonce;

    /**
     * 友商联系方式
     */
    private T data;
}
