package com.xmd.achievement.service.impl;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.MqServeInprogressInfoModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IMqOrderPaymentInfoRepository;
import com.xmd.achievement.dao.repository.IMqServeInprogressInfoRepository;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContextV4;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.InstallmentDetailResponse;
import com.xmd.achievement.rpc.entity.dto.OrderPayDetailResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductResponse;
import com.xmd.achievement.service.MqServeInProgressInfoService;
import com.xmd.achievement.service.entity.dto.ReceiveServeInProgressDto;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.AchievementSourceEnum;
import com.xmd.achievement.support.constant.enums.InstallmentEnum;
import com.xmd.achievement.support.constant.enums.TaskTypeEnum;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.CalculateTypeEnum;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/14:25
 * @since 1.0
 */
@Service
@Slf4j
public class MqServeInProgressInfoServiceImpl implements MqServeInProgressInfoService {
    @Resource
    private IMqOrderPaymentInfoRepository mqOrderPaymentRepository;

    @Resource
    private IMqServeInprogressInfoRepository serveInprogressInfoRepository;

    @Resource
    private CalculateCustomerContextV4 calculateCustomerContextV4;

    @Resource
    private IAchievementProductDetailRepository productDetailRepository;


    @Resource
    private InnerService innerService;

    @Override
    @Lock("'MqServeInProgress'+#progressDto.orderId+#progressDto.productId")
    public boolean saveInfo(ReceiveServeInProgressDto progressDto) {

        OrderSimpleInfoResponse orderSimpleInfoResponse = innerService.getOrderSimpleInfo(progressDto.getOrderId());
        if(null == orderSimpleInfoResponse || CollectionUtils.isEmpty(orderSimpleInfoResponse.getPayDetailResponses())){
            return false;
        }
        Optional<OrderPayDetailResponse> optionalOrderPayDetail = orderSimpleInfoResponse.getPayDetailResponses().stream()
                .filter(e -> e.getStatus().equals(NumberConstants.INTEGER_VALUE_3))
                .findFirst();

        Integer installment ;
        //订单分期状态
        Integer installmentStatus = orderSimpleInfoResponse.getInstallmentStatus();
        if(InstallmentEnum.INSTALLMENT.getCode().equals(installmentStatus) || InstallmentEnum.NOT_INSTALLMENT.getCode().equals(installmentStatus)){
            //不分期
            installment = 1;
        }else{
            installment = 2;
        }
        List<OrderSimpleProductResponse> orderProductIdList = orderSimpleInfoResponse.getProductResponseList();
        //新加了 orderProductId  通过orderProductId匹配
        if(StringUtils.isNoneEmpty(progressDto.getOrderProductId()) && ! "0".equals(progressDto.getOrderProductId())){
            orderProductIdList = orderSimpleInfoResponse.getProductResponseList().stream().filter(o -> o.getOrderProductId().equals(progressDto.getOrderProductId())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(orderProductIdList)){
            log.error("订单明细id错误 orderProductId:{},orderId{}",progressDto.getOrderProductId(),progressDto.getOrderId());
            return false;
        }
        if(CollectionUtils.isEmpty(orderSimpleInfoResponse.getInstallmentDetailResponseList())){
            //幂等校验
            MqOrderPaymentInfoModel model = mqOrderPaymentRepository.selectCheckOne(progressDto.getOrderId(),progressDto.getProductId(),progressDto.getOrderProductId(),installment);

            if (ObjectUtil.isNotEmpty(model)) {
                log.warn("服务中数据信息处理,重复订单不做处理，message:{}", JSONUtil.toJsonStr(progressDto));
                return false;
            }

            MqOrderPaymentInfoModel saveModel = buildMqOrderPaymentInfoModel(progressDto, NumberConstants.INTEGER_VALUE_0, orderSimpleInfoResponse, NumberConstants.INTEGER_VALUE_1, orderProductIdList, optionalOrderPayDetail);
            mqOrderPaymentRepository.save(saveModel);
            return true;
        }

        //已支付的分期集合
        List<InstallmentDetailResponse> installmentPayDetailList = orderSimpleInfoResponse.getInstallmentDetailResponseList().stream().filter(e -> e.getInstallmentPayStatus().equals(NumberConstants.INTEGER_VALUE_3)).collect(Collectors.toList());
        //已支付的分期集合分期数
        List<Integer> installmentNumList = installmentPayDetailList.stream().map(InstallmentDetailResponse::getInstallmentNum).collect(Collectors.toList());
        //已入库分期订单
        List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModelList = mqOrderPaymentRepository.selectListByOrderIdAndInstallment(progressDto.getOrderId(),installmentNumList,progressDto.getProductId(),progressDto.getServeNo());

        List<Integer> notInsertDBInstallmentNum = new ArrayList<>();

        for (Integer installmentNum : installmentNumList) {
            boolean  flag = true;
            for (MqOrderPaymentInfoModel model : mqOrderPaymentInfoModelList) {
                if(installmentNum.equals(model.getInstallmentNum())){
                    flag = false;
                    break;
                }
            }
            if(flag){
                notInsertDBInstallmentNum.add(installmentNum);
            }
        }

        for (Integer num : notInsertDBInstallmentNum) {
            MqOrderPaymentInfoModel saveModel = buildMqOrderPaymentInfoModel(progressDto, num, orderSimpleInfoResponse, installment, orderProductIdList, optionalOrderPayDetail);
            mqOrderPaymentRepository.save(saveModel);
        }

        return true;

    }


    private  MqOrderPaymentInfoModel buildMqOrderPaymentInfoModel(ReceiveServeInProgressDto progressDto, Integer num, OrderSimpleInfoResponse orderSimpleInfoResponse, Integer installment, List<OrderSimpleProductResponse> orderProductIdList, Optional<OrderPayDetailResponse> optionalOrderPayDetail) {
        MqOrderPaymentInfoModel saveModel = new MqOrderPaymentInfoModel();
        BeanUtil.copyProperties(progressDto, saveModel);
        saveModel.setTaskId(IdUtil.getSnowflake().nextId());
        saveModel.setAchievementSource(AchievementSourceEnum.KUAJINFG.getCode());
        saveModel.setTaskType(TaskTypeEnum.ADD.getMsg());
        saveModel.setCustomerId(orderSimpleInfoResponse.getCustomerId());
        saveModel.setCalculateType(CalculateTypeEnum.SERVEINPROGRESS.getCode());
        saveModel.setServeNo(progressDto.getServeNo());
        saveModel.setInstallmentStatus(installment);
        saveModel.setInstallmentNum(num);
        if(!CollectionUtils.isEmpty(orderProductIdList)){
            saveModel.setOrderProductId(orderProductIdList.get(0).getOrderProductCode());
        }
        if (optionalOrderPayDetail.isPresent()) {
            OrderPayDetailResponse o = optionalOrderPayDetail.get();
            saveModel.setPaymentTime(o.getCreateTime());
        } else {
            throw new BusinessException("服务中数据信息处理,未查询到支付时间，roderId:" + progressDto.getOrderId());
        }
        return saveModel;
    }

    @Override
    public List<MqServeInprogressInfoModel> serveDataToPayment() {
        log.info("服务中数据信息处理,开始处理");
        //查询所有数据
        List<MqServeInprogressInfoModel> list = serveInprogressInfoRepository.list();

        List<MqOrderPaymentInfoModel> paymentInfoModels = new ArrayList<>();
        for (MqServeInprogressInfoModel mqServeInprogressInfoModel : list) {
            MqOrderPaymentInfoModel model = new MqOrderPaymentInfoModel();
            model.setTaskId(IdUtil.getSnowflake().nextId());
            model.setTaskType(TaskTypeEnum.ADD.getMsg());
            model.setCalculateType(CalculateTypeEnum.SERVEINPROGRESS.getCode());
            model.setOrderId(mqServeInprogressInfoModel.getOrderId());
            model.setTaskStatus(mqServeInprogressInfoModel.getTaskStatus());
            model.setProductId(mqServeInprogressInfoModel.getProductId());
            model.setServeNo(mqServeInprogressInfoModel.getServeNo());
            model.setFailReason(mqServeInprogressInfoModel.getFailReason());
            model.setFailCount(Integer.valueOf(mqServeInprogressInfoModel.getFailCount()));
            model.setAchievementSource(AchievementSourceEnum.KUAJINFG.getCode());

            List<MqOrderPaymentInfoModel> list2 = mqOrderPaymentRepository.list(
                    new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                            .eq(MqOrderPaymentInfoModel::getOrderId, mqServeInprogressInfoModel.getOrderId()));
            if (!list2.isEmpty()) {
                model.setPaymentTime(list2.get(0).getPaymentTime());
                model.setCustomerId(list2.get(0).getCustomerId());
            }
            model.setCreateTime(mqServeInprogressInfoModel.getCreateTime());
            paymentInfoModels.add(model);
        }

        //转移到支付完成表中，保证创建时间不变
        mqOrderPaymentRepository.saveBatch(paymentInfoModels);
        log.info("服务中数据信息处理,开始处理");
        return Collections.emptyList();
    }
}
