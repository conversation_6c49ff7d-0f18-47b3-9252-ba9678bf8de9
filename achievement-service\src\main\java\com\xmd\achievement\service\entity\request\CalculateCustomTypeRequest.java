package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/13/10:30
 * @since 1.0
 */
@Data
public class CalculateCustomTypeRequest implements Serializable {
    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456789")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "客户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456789")
    @NotBlank(message = "客户ID不能为空")
    private String customerId;
}
