package com.xmd.achievement.async.job.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.handler.achievement.ReportHandler;
import com.xmd.achievement.util.date.DateTimeFormatUtil;
import com.xmd.achievement.util.date.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 机构月报job
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
public class MonthlyReportJob {

    @Autowired
    private ReportHandler reportHandler;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.MONTHLY_REPORT_JOB)
    public ReturnT<String> jobHandler(String param) {
        if (ObjectUtil.isNotEmpty(param)) {
            JSONObject jsonObject = JSON.parseObject(param);
            String currentTime = jsonObject.getString("currentTime");
            Date startDate = DateUtils.stringToDate(currentTime, DateTimeFormatUtil.STANDARD_DATE_TIME.getFormat());
            extracted(startDate);
        } else {
            extracted(null);
        }
        return ReturnT.SUCCESS;
    }

    private void extracted(Date startTime) {
        log.info("执行MonthlyReportJob任务Start...");
        try {
            reportHandler.processMonthlyReport(startTime);
        } catch (Exception e) {
            log.error("MonthlyReportJob任务失败,失败原因：", e);
        }
        log.info("执行MonthlyReportJob任务End...");
    }
}
