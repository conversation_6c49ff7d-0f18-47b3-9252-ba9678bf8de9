package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.SaleTaskAuditModel;
import com.xmd.achievement.dao.mapper.SaleTaskAuditMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.ISaleTaskAuditRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 销售任务审核表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Service
@Slf4j
public class SaleTaskAuditRepositoryImpl extends ServiceImpl<SaleTaskAuditMapper,SaleTaskAuditModel> implements ISaleTaskAuditRepository {

@Resource
private SaleTaskAuditMapper saleTaskAuditMapper;

}