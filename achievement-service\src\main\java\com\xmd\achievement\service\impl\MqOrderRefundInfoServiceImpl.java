package com.xmd.achievement.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.dao.repository.IMqOrderRefundInfoRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.AfterSalesOrderResp;
import com.xmd.achievement.service.IMqOrderRefundInfoService;
import com.xmd.achievement.service.entity.dto.ReceiveOrderRefundDto;
import com.xmd.achievement.support.constant.enums.AfterSalesTypeEnum;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.TaskTypeEnum;
import com.xmd.achievement.support.mq.RocketMqOperate;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


/**
 * 退转款信息处理
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MqOrderRefundInfoServiceImpl implements IMqOrderRefundInfoService {

    private final IMqOrderRefundInfoRepository mqOrderRefundInfoRepository;
    private final InnerService innerService;
    private final RocketMqOperate rocketMqOperate;

    /**
     * 保存信息
     *
     * @param refundDto 退款消息
     * @return boolean
     */
    @Override
    @Lock("'MqOrderRefundInfo-' + #refundDto.messageId")
    public boolean saveInfo(ReceiveOrderRefundDto refundDto) {
        AfterSalesOrderResp afterSalesOrderResp = innerService.queryAfterSalesOrderInfo(refundDto.getAfterSalesOrderNo());

        Long msgCount = mqOrderRefundInfoRepository.lambdaQuery()
                .eq(MqOrderRefundInfoModel::getMessageId, refundDto.getMessageId())
                .eq(MqOrderRefundInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .count();
        if (msgCount > 0) {
            log.warn("订单退款完成数据信息处理,重复订单不做处理，message:{}", JSON.toJSONString(refundDto));
            return false;
        }

        MqOrderRefundInfoModel saveModel = new MqOrderRefundInfoModel();
        saveModel.setTaskId(IdUtil.getSnowflakeNextId());
        saveModel.setTaskType(TaskTypeEnum.ADD.getMsg());
        saveModel.setAftersaleOrderId(afterSalesOrderResp.getAfterSalesOrderId());
        saveModel.setAftersaleOrderNo(afterSalesOrderResp.getAfterSalesOrderNo());
        saveModel.setOrderId(afterSalesOrderResp.getTransferOutOrderId());
        saveModel.setOrderNo(afterSalesOrderResp.getTransferOutOrderNo());
        saveModel.setCustomerId(afterSalesOrderResp.getCustomerId());
        Integer saleType = AfterSalesTypeEnum.getOrderSalesTypeByCode(refundDto.getAfterSalesOrderType());
        saveModel.setSaleType(saleType);
        saveModel.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
        saveModel.setMessageId(refundDto.getMessageId());
        saveModel.setAftersaleStatus(refundDto.getAfterSalesStatus());
        saveModel.setTransferInOrderId(afterSalesOrderResp.getTransferInOrderId());
        saveModel.setTransferInOrderNo(afterSalesOrderResp.getTransferInOrderNo());
        mqOrderRefundInfoRepository.save(saveModel);
        aftersaleCallback(saveModel);
        return true;
    }

    @Override
    public void aftersaleCallback(MqOrderRefundInfoModel refundInfoModel) {
        Map<String, Object> map = new HashMap<>();
        map.put("messageId", Convert.toStr(refundInfoModel.getMessageId()));
        map.put("afterSalesOrderNo", refundInfoModel.getAftersaleOrderNo());
        map.put("systemCode", "achievement");
        AfterSalesTypeEnum afterSalesTypeEnum = AfterSalesTypeEnum.fromOrderSalesType(refundInfoModel.getSaleType());
        map.put("afterSalesOrderType", afterSalesTypeEnum.getCode());
        map.put("afterSalesStatus", refundInfoModel.getAftersaleStatus());
        log.info("退转款回调，参数: {}", JSON.toJSONString(map));
        rocketMqOperate.asyncSendWithRetry(rocketMqOperate.getTopic().getOrderTransferFundCallback(),
                JSON.toJSONString(map));
    }

    /**
     * 通过消息id获取
     *
     * @param messageId 消息id
     * @return {@link MqOrderRefundInfoModel }
     * <AUTHOR>
     * @since 1.0
     */
    @Override
    public MqOrderRefundInfoModel getByMessageId(String messageId) {
        return mqOrderRefundInfoRepository.getByMessageId(messageId);
    }
}
