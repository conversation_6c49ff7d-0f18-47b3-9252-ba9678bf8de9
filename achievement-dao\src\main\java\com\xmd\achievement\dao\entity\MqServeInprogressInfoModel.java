package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("mq_serve_inprogress_info")
public class MqServeInprogressInfoModel extends BaseModel {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 服务编号
     */
    @TableField("serve_no")
    private String serveNo;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 任务状态：1-未完成 2-已完成
     */
    @TableField("task_status")
    private String taskStatus;
    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;
    /**
     * 失败次数
     */
    @TableField("fail_count")
    private String failCount;
}