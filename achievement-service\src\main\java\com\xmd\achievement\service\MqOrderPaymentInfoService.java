package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.ThirdAchievementModel;
import com.xmd.achievement.service.entity.dto.ReceiveOrderPaymentDto;
import com.xmd.achievement.service.entity.request.InfoSaveMqOrderPaymentInfoRequest;
import com.xmd.achievement.service.entity.request.UpdatePaycompleteTestRequest;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/14:26
 * @since 1.0
 */
public interface MqOrderPaymentInfoService {
    /**
     * 保存信息
     *
     * @param paymentDto 请求参数
     */
    boolean saveInfo(ReceiveOrderPaymentDto paymentDto);

    /**
     * 执行成功
     *
     * @param model 请求参数
     */
    void executeSuccess(MqOrderPaymentInfoModel model);

    /**
     * 执行失败
     *
     * @param model,message 请求参数
     */
    void executeFailed(MqOrderPaymentInfoModel model, String message);

    /**
     * 查询需要执行的任务
     *
     * @return List<MqOrderPaymentInfoModel>
     */
    List<MqOrderPaymentInfoModel> queryExcutTask();

    /**
     * 检查任务状态
     *
     * @param orderId 请求参数
     * @return Boolean
     */
    Boolean checkTaskStatus(MqOrderPaymentInfoModel orderId);

    /**
     * 保存MqOrderPaymentInfo信息
     *
     * @param request 请求参数
     * @return Boolean
     */
    Boolean infoSaveMqOrderPaymentInfo(InfoSaveMqOrderPaymentInfoRequest request);

    /**
     * 保存mqOrderPaymentInfoModel信息
     *
     * @param mqOrderPaymentInfoModel 请求参数
     */
    void saveModel(MqOrderPaymentInfoModel mqOrderPaymentInfoModel);

    void saveModels(List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModels);

    void updatePayTime();

    void handelThirdAchievementToPaymentOrder(ThirdAchievementModel model);

    Boolean renewDate();

    Boolean addPayTime();

    void updatePayCompleteTest(@Valid UpdatePaycompleteTestRequest request);

    MqOrderPaymentInfoModel queryExcutTaskByTaskId(Long taskId);

    void repairCalculateCustomerType();

    Boolean repairThirdDataCreateTime();
}
