package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.ReportDownloadLogModel;
import com.xmd.achievement.dao.mapper.ReportDownloadLogMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IReportDownloadLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 日报月报导出记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Service
@Slf4j
public class ReportDownloadLogRepositoryImpl extends ServiceImpl<ReportDownloadLogMapper,ReportDownloadLogModel> implements IReportDownloadLogRepository {

@Resource
private ReportDownloadLogMapper reportDownloadLogMapper;

}