package com.xmd.achievement.handler.achievement;

import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.ItemTypeEnum;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import com.xmd.achievement.support.constant.enums.OrderSpecTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *●首年报价，规格维度，保留3位小数
 * ○新开
 * ■当计费类型为时长时，则首年报价=应付金额*新开标准价/（新开标准价+续费标准价*(购买年限-1)），购买年限=时长的值÷12 ，向上取整；
 * ■当计费时长为非时长类，则首年报价=应付金额；
 * ○续费，
 * ■计费项为时长，首年报价=0；
 * ■计费项为非时长，无此场景，不考虑；
 * ○升级
 * ■当计费类型为时长时，则首年报价=应付金额*新开标准价/（新开标准价+续费标准价*(购买年限-1)），购买年限=时长的值÷12 ，向上取整；
 * ■当计费时长为非时长类，则首年报价=应付金额；
 * ○另购
 * ■当计费类型为时长时，则首年报价=应付金额*新开标准价/（新开标准价+续费标准价*(购买年限-1)），购买年限=时长的值÷12 ，向上取整；
 * ■当计费时长为非时长类，则首年报价=应付金额；
 * <AUTHOR>
 * @date: 2024/12/20 14:42
 */
@Slf4j
@Service
public class SpecCalculateFirstYearQuoteHandler implements CalculateAmountHandler {

    @Override
    public void calculate(CalculateFactInfo factInfo) {
        try {
            AchievementSpecDetailModel spec = factInfo.getSpec();
            OrderSaleTypeEnum saleType = OrderSaleTypeEnum.fromValue(factInfo.getOrderSaleType());
            if (ItemTypeEnum.TIME_LENGTH.getType().equals(spec.getItemType())) {
                switch (saleType) {
                    case OPEN:
                    case UPGRADE:
                    case ANOTHER_BUY:
                        // 首年报价=应付金额*新开标准价/（新开标准价+续费标准价*(购买年限-1)），购买年限=时长的值÷12 ，向上取整；
                        spec.setFirstYearQuote(calculateFirstYearQuote(spec));
                        break;
                    case RENEW:
                    default:
                        spec.setFirstYearQuote(BigDecimal.ZERO);
                        break;
                }
            } else {
                switch (saleType) {
                    case OPEN:
                    case UPGRADE:
                    case ANOTHER_BUY:
                        // 当计费时长为非时长类，则首年报价=应付金额；
                        spec.setFirstYearQuote(spec.getPayableAmount());
                        break;
                    case RENEW:
                    default:
                        spec.setFirstYearQuote(BigDecimal.ZERO);
                        break;
                }
            }
        } finally {
            log.warn("首年报价业绩 规格id:{},参数:{}", factInfo.getSpec().getSpecId(), JSON.toJSONString(factInfo));
        }
    }

    private static BigDecimal calculateFirstYearQuote(AchievementSpecDetailModel spec) {
        if (spec.getPaidAmount().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        // 定义常量
        BigDecimal billingPrice = spec.getBillingPrice();
        if (null == billingPrice) {
            throw new RuntimeException("billingPrice计费单价为空");
        }
        BigDecimal payableAmount = spec.getPayableAmount();
        if (null == payableAmount) {
            throw new RuntimeException("payableAmount应付金额为空");
        }
        BigDecimal renewalPrice = spec.getRenewalPrice() == null ? BigDecimal.ZERO : spec.getRenewalPrice();
        BigDecimal itemNum = new BigDecimal(spec.getItemNum());
        BigDecimal months = new BigDecimal(NumberConstants.INTEGER_VALUE_12);

        // 计算分母
        BigDecimal denominator = billingPrice.add(renewalPrice.multiply(itemNum.divide(months, RoundingMode.UP).subtract(new BigDecimal(NumberConstants.INTEGER_VALUE_1)))).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
        // 最终计算
        if(denominator.compareTo(BigDecimal.ZERO) == 0){
            return BigDecimal.ZERO;
        }
        return payableAmount.multiply(billingPrice).divide(denominator, NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
    }
}
