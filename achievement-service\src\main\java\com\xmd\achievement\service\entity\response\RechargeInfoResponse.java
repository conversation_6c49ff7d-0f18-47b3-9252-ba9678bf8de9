package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/11/10:57
 * @since 1.0
 */
@Data
public class RechargeInfoResponse {

    @Schema(description = "客户id")
    private String customerId;

    @Schema(description = "充值时间")
    private Date rechargeTime;
}
