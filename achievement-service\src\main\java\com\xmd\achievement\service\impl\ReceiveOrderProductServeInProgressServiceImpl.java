//package com.xmd.achievement.service.impl;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.util.IdUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.json.JSONUtil;
//import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
//import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
//import com.xmd.achievement.dao.repository.IMqOrderPaymentInfoRepository;
//import com.xmd.achievement.dao.repository.IMqServeInprogressInfoRepository;
//import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContextV4;
//import com.xmd.achievement.rpc.InnerService;
//import com.xmd.achievement.rpc.entity.dto.InstallmentDetailResponse;
//import com.xmd.achievement.rpc.entity.dto.OrderPayDetailResponse;
//import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
//import com.xmd.achievement.service.ReceiveOrderProductServeInProgressInfoService;
//import com.xmd.achievement.service.entity.dto.ReceiveOrderProductServeInProgressDto;
//import com.xmd.achievement.support.constant.NumberConstants;
//import com.xmd.achievement.support.constant.enums.AchievementSourceEnum;
//import com.xmd.achievement.support.constant.enums.InstallmentEnum;
//import com.xmd.achievement.support.constant.enums.TaskTypeEnum;
//import com.xmd.achievement.support.constant.exceptions.BusinessException;
//import com.xmd.achievement.util.enums.CalculateTypeEnum;
//import com.xmd.achievement.web.annotate.lock.annotation.Lock;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
///**
// * 描述这个类的功能和用途
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2024/12/26/14:25
// * @since 1.0
// */
//@Service
//@Slf4j
//public class ReceiveOrderProductServeInProgressServiceImpl implements ReceiveOrderProductServeInProgressInfoService {
//    @Resource
//    private IMqOrderPaymentInfoRepository mqOrderPaymentRepository;
//
//    @Resource
//    private IMqServeInprogressInfoRepository serveInprogressInfoRepository;
//
//    @Resource
//    private CalculateCustomerContextV4 calculateCustomerContextV4;
//
//    @Resource
//    private IAchievementProductDetailRepository productDetailRepository;
//
//
//    @Resource
//    private InnerService innerService;
//
//    @Override
//    @Lock("'MqServeInProgress'+#progressDto.orderId+#progressDto.productId")
//    public boolean saveInfo(ReceiveOrderProductServeInProgressDto progressDto) {
//
//        OrderSimpleInfoResponse orderSimpleInfoResponse = innerService.getOrderSimpleInfo(progressDto.getOrderId());
//        Optional<OrderPayDetailResponse> optionalOrderPayDetail = orderSimpleInfoResponse.getPayDetailResponses().stream()
//                .filter(e -> e.getStatus().equals(NumberConstants.INTEGER_VALUE_3))
//                .findFirst();
//
//        Integer installment ;
//        //订单分期状态
//        Integer installmentStatus = orderSimpleInfoResponse.getInstallmentStatus();
//        if(InstallmentEnum.INSTALLMENT.getCode().equals(installmentStatus) || InstallmentEnum.NOT_INSTALLMENT.getCode().equals(installmentStatus)){
//            //不分期
//            installment = 1;
//        }else{
//            installment = 2;
//        }
//
//        if(CollectionUtils.isEmpty(orderSimpleInfoResponse.getInstallmentDetailResponseList())){
//            //幂等校验
//            MqOrderPaymentInfoModel model = mqOrderPaymentRepository.selectCheckOne(progressDto.getOrderId(),progressDto.getProductId(),progressDto.getServeNo(),installment);
//
//            if (ObjectUtil.isNotEmpty(model)) {
//                log.warn("服务中数据信息处理,重复订单不做处理，message:{}", JSONUtil.toJsonStr(progressDto));
//                return false;
//            }
//
//            MqOrderPaymentInfoModel saveModel = new MqOrderPaymentInfoModel();
//            BeanUtil.copyProperties(progressDto, saveModel);
//            saveModel.setTaskId(IdUtil.getSnowflake().nextId());
//            saveModel.setAchievementSource(AchievementSourceEnum.KUAJINFG.getCode());
//            saveModel.setTaskType(TaskTypeEnum.ADD.getMsg());
//            saveModel.setCustomerId(orderSimpleInfoResponse.getCustomerId());
//            saveModel.setCalculateType(CalculateTypeEnum.SERVEINPROGRESS.getCode());
//            saveModel.setServeNo(progressDto.getServeNo());
//            saveModel.setInstallmentStatus(NumberConstants.INTEGER_VALUE_1);
//            saveModel.setInstallmentNum(NumberConstants.INTEGER_VALUE_0);
//            saveModel.setOrderProductId(progressDto.getOrderProductId());
//            if (optionalOrderPayDetail.isPresent()) {
//                OrderPayDetailResponse o = optionalOrderPayDetail.get();
//                saveModel.setPaymentTime(o.getCreateTime());
//            } else {
//                throw new BusinessException("服务中数据信息处理,未查询到支付时间，roderId:" + progressDto.getOrderId());
//            }
//            mqOrderPaymentRepository.save(saveModel);
//            return true;
//        }
//
//        //已支付的分期集合
//        List<InstallmentDetailResponse> installmentPayDetailList = orderSimpleInfoResponse.getInstallmentDetailResponseList().stream().filter(e -> e.getInstallmentPayStatus().equals(NumberConstants.INTEGER_VALUE_3)).collect(Collectors.toList());
//        //已支付的分期数集合
//        List<Integer> installmentNumList = installmentPayDetailList.stream().map(InstallmentDetailResponse::getInstallmentNum).collect(Collectors.toList());
//
//
//        //已入库分期订单
//        List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModelList = mqOrderPaymentRepository.selectListByOrderIdAndInstallment(progressDto.getOrderId(),installmentNumList,progressDto.getProductId(),progressDto.getServeNo());
//
//        List<Integer> notInsertDBInstallmentNum = new ArrayList<>();
//
//        for (Integer installmentNum : installmentNumList) {
//            boolean  flag = true;
//            for (MqOrderPaymentInfoModel model : mqOrderPaymentInfoModelList) {
//                if(installmentNum.equals(model.getInstallmentNum())){
//                    flag = false;
//                    break;
//                }
//            }
//            if(flag){
//                notInsertDBInstallmentNum.add(installmentNum);
//            }
//        }
//
//        for (Integer num : notInsertDBInstallmentNum) {
//            MqOrderPaymentInfoModel saveModel = new MqOrderPaymentInfoModel();
//            BeanUtil.copyProperties(progressDto, saveModel);
//            saveModel.setTaskId(IdUtil.getSnowflake().nextId());
//            saveModel.setAchievementSource(AchievementSourceEnum.KUAJINFG.getCode());
//            saveModel.setTaskType(TaskTypeEnum.ADD.getMsg());
//            saveModel.setCustomerId(orderSimpleInfoResponse.getCustomerId());
//            saveModel.setCalculateType(CalculateTypeEnum.SERVEINPROGRESS.getCode());
//            saveModel.setServeNo(progressDto.getServeNo());
//            saveModel.setInstallmentStatus(installment);
//            saveModel.setInstallmentNum(num);
//            saveModel.setOrderProductId(progressDto.getOrderProductId());
//            if (optionalOrderPayDetail.isPresent()) {
//                OrderPayDetailResponse o = optionalOrderPayDetail.get();
//                saveModel.setPaymentTime(o.getCreateTime());
//            } else {
//                throw new BusinessException("服务中数据信息处理,未查询到支付时间，roderId:" + progressDto.getOrderId());
//            }
//            mqOrderPaymentRepository.save(saveModel);
//        }
//        return true;
//
//    }
//
//}
