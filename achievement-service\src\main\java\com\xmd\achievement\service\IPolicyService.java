package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.PolicySpecDetailModel;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PolicySpecDetailResponse;
import com.xmd.achievement.service.entity.response.PolicySpecResponse;
import com.xmd.achievement.web.entity.response.WebResult;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/19 16:23
 * @version: 1.0.0
 * @return {@link }
 */
public interface IPolicyService {
    /**
     * 获取商品业绩政策
     * @param request 请求参数
     * @return WebResult<Boolean>
     */
    WebResult<Boolean> savePolicy(SavePolicyRequest request);

    /**
     * 查询商品业绩政策
     * @param request 请求参数
     * @return WebResult<List < QueryPolicyListResponse>>
     */
    WebResult<PolicySpecResponse> queryPolicyList(QueryPolicyListRequest request);

    /**
     * 获取商品业绩政策
     * @param request 请求参数
     * @return AchievementPolicyModel
     */
    WebResult<List<PolicySpecDetailResponse>> querySpecByProductId(QuerySpecByproductIdRequest request);

    /**
     * 获取商品业绩政策
     * @param request 请求参数
     * @return WebResult<Boolean>
     */
    List<String> checkPolicy(CheckPolicyRequest request);

    /**
     * 通过specId获取业绩配置详情
     * @param specId 请求参数
     * @return PolicySpecDetailModel
     */
    PolicySpecDetailModel getPolicyDetailBySpecId(Long specId);

    /**
     * 获取业绩详情集合
     * @param specIds 请求参数
     * @return List<PolicySpecDetailModel>
     */
    List<PolicySpecDetailModel> getPolicyDetailList(List<Long> specIds);


    /**
     * 根据OrderID获取业绩详情集合
     * @param productId 请求参数
     * @return List<PolicySpecDetailModel>
     */
    List<PolicySpecDetailModel> getPolicyDetailByOrderId(Long productId);

    /**
     * 修复业绩政策历史数据
     * @return Boolean
     */
    Boolean repairPolicyHistoryDate();
}
