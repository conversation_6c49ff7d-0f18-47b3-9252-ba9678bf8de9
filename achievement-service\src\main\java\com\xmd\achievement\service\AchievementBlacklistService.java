package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.AchievementBlacklistModel;
import com.xmd.achievement.service.entity.request.AchievementBlacklistRequest;
import com.xmd.achievement.service.entity.response.AchievementBlacklistVO;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface AchievementBlacklistService {

    /**
     * 黑名单列表查询
     */
    WebResult<PageResponse<AchievementBlacklistVO>> selectBlacklist(@Valid AchievementBlacklistRequest request);

    void importBlacklist(MultipartFile file) throws Exception;

    WebResult<Boolean> deleteBlacklist(@Valid AchievementBlacklistRequest request);
}
