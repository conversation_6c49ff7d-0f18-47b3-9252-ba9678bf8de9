package com.xmd.achievement.handler.achievement;

import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.support.constant.enums.OrderSpecTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 *续费到账金额=实付金额-首年到账金额
 * <AUTHOR>
 * @date: 2024/12/20 14:48
 */
@Slf4j
@Service
public class SpecCalculateRenewIncomeAmountHandler implements CalculateAmountHandler {
    @Override
    public void calculate(CalculateFactInfo factInfo) {
        try {
            AchievementSpecDetailModel spec = factInfo.getSpec();

            spec.setRenewalIncome(spec.getPaidAmount().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : spec.getPaidAmount().subtract(spec.getFirstYearIncome()));
        } finally {
            log.warn("续费到账金额业绩 规格id:{},参数:{}", factInfo.getSpec().getSpecId(), JSON.toJSONString(factInfo));
        }
    }
}
