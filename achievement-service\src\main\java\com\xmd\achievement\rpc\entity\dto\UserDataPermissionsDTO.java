package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 登录人权限校验dto
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/5/23
 */
@Data
public class UserDataPermissionsDTO {


    private String userId;
    /**
     * 机构id
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 数据权限类型:1-无权限 2-本人及公司，3-本部门，4-事业部，5-本公司 ，6所有,7-本人
     */
    private Integer dataPermissionsType;
    /**
     * 是否为总部角色 0否 1是
     */
    private Integer headquarters;
}