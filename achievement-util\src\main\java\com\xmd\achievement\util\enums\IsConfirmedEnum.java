package com.xmd.achievement.util.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/18 11:01
 * @version: 1.0.0
 * @return {@link }
 */
@Getter
public enum IsConfirmedEnum {
    /**
     * 1 是
     */
    YES(1),
    /**
     * 2 否
     */
    NO(2);

    private final Integer isConfirmed;

    IsConfirmedEnum(Integer isConfirmed) {
        this.isConfirmed = isConfirmed;
    }

    public static List<Integer> getIsConfirmedList() {
        return Arrays.stream(IsConfirmedEnum.values()).map(IsConfirmedEnum::getIsConfirmed).collect(Collectors.toList());
    }
}