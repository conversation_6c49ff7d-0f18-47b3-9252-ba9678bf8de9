package com.xmd.achievement.service;

import com.xmd.achievement.service.entity.dto.AchievementUploadDto;
import com.xmd.achievement.service.entity.request.ExcelProductRequest;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 业绩的导入导出服务
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface AchievementFileService {

    /**
     * 校验商品业绩流水导入文件
     *
     * @param file 文件
     */
    void verifyFile(MultipartFile file) throws Exception;

    /**
     * 校验规格业绩流水导入文件
     *
     * @param file 文件
     */
    void verifySpecFile(MultipartFile file) throws Exception;

    /**
     * 校验规格业绩流水退转款导入文件
     *
     * @param file 文件
     */
    void verifySpecRefundFile(MultipartFile file) throws Exception;

    /**
     * 导入商品业绩流水
     *
     * @param file 文件
     */
    void excelFlowing(MultipartFile file) throws Exception;

    /**
     * 导入规格业绩流水
     *
     * @param file 文件
     */
    void excelSpecFlowing(MultipartFile file) throws Exception;

    /**
     * 导入规格业绩流水退转款
     *
     * @param file 文件
     */
    void excelSpecRefundFlowing(MultipartFile file) throws Exception;

    /**
     * 导出商品业绩
     *
     * @param search   查询条件
     * @param response 响应
     */
    void exportProductAchievement(ExcelProductRequest search, HttpServletResponse response);


    /**
     * 导出商品规格业绩
     *
     * @param search   查询条件
     * @param response 响应
     */
    void exportProductSpecAchievement(ExcelProductRequest search, HttpServletResponse response);

    void handleUploadFlowing(List<AchievementUploadDto> uploadList, boolean needHandleSpec);

    /**
     * 导入中企商品业绩，覆盖原数据，数据来源于和中企对比后的数据
     *
     * @param file 文件
     * <AUTHOR>
     * @since 1.0
     */
    void importZqAchievementFromThird(MultipartFile file) throws Exception;

    /**
     * 导入中企商品业绩，覆盖原数据，数据来源于和中企对比后的数据
     *
     * @param file 文件
     * <AUTHOR>
     * @since 1.0
     */
    void importKjAchievementFromThird(MultipartFile file) throws Exception;
}
