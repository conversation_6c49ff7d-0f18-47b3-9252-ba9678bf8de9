package com.xmd.achievement.rpc.entity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AfterSalesItemResp {

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 订单商品明细号
     */
    private String orderProductCode;

    /**
     * 标准价
     */
    private BigDecimal basePrice;

    /**
     * 实付金额
     */
    private BigDecimal paidAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 商品规格详情列表
     */
    private List<AfterSalesItemSpecResp> afterSalesItemSpecResps;

}

