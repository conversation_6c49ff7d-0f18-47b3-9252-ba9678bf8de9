package com.xmd.achievement.service.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/14/14:36
 * @since 1.0
 */
@Data
public class MqServeFinishTimeInfoRequest implements Serializable {
    @Schema(description = "serveNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotEmpty(message = "serveNo不能为空")
    private String serveNo;

    @Schema(description = "orderId", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotEmpty(message = "orderId不能为空")
    private Long orderId;

    @Schema(description = "orderIds", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotEmpty(message = "productId不能为空")
    private Long productId;

    @Schema(description = "serveFinishTime", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01-01 00:00:00")
    @NotEmpty(message = "serveFinishTime不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date serveFinishTime;
}
