package com.xmd.achievement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.dao.repository.IAchievementCategoryDetailRepository;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IAchievementSpecDetailRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.ProductForAchievementResponse;
import com.xmd.achievement.service.ExportPerformanceServcie;
import com.xmd.achievement.service.IBusinessAchievementService;
import com.xmd.achievement.service.entity.request.ExportKuajingAchievementRequest;
import com.xmd.achievement.service.entity.request.ExportPerformancRequest;
import com.xmd.achievement.service.entity.response.ExportKuajingAchievementResponse;
import com.xmd.achievement.service.entity.response.PrformanceInfoResponse;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.AchievementSourceEnum;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.YesOrNotEnum;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;
import com.xmd.achievement.util.enums.MainSplitPersonEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import com.xmd.achievement.util.enums.StatusEnum;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/10/17:22
 * @since 1.0
 */
@Service
@Slf4j
public class ExportPerformanceServcieImpl implements ExportPerformanceServcie {
    @Resource
    IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    IAchievementCategoryDetailRepository achievementCategoryDetailRepository;

    @Resource
    IAchievementSpecDetailRepository achievementSpecDetailRepository;

    @Resource
    IBusinessAchievementService achievementBusinessService;

    @Resource
    InnerService innerService;


    @Override
    public List<PrformanceInfoResponse> export(ExportPerformancRequest request) {
        //商品
        List<AchievementProductDetailModel> productList = achievementProductDetailRepository.list(
                new LambdaQueryWrapper<AchievementProductDetailModel>()
                        .ge(ObjectUtil.isNotEmpty(request.getSignTimeStrat()), AchievementProductDetailModel::getSignedTime, request.getSignTimeStrat())
                        .le(ObjectUtil.isNotEmpty(request.getSignTimeEnd()), AchievementProductDetailModel::getSignedTime, request.getSignTimeEnd())
                        .eq(BaseModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));

        if (ObjectUtil.isEmpty(productList)) {
            return null;
        }

        //分类
        List<Long> achievementIds = productList.stream().map(AchievementProductDetailModel::getAchievementId).collect(Collectors.toList());
        List<AchievementCategoryDetailModel> categoryLists = achievementCategoryDetailRepository.list(
                new LambdaQueryWrapper<AchievementCategoryDetailModel>()
                        .in(AchievementCategoryDetailModel::getAchievementId, achievementIds));
        if (ObjectUtil.isEmpty(categoryLists)) {
            return null;
        }

        Map<Long, List<AchievementCategoryDetailModel>> categoryModelMap = categoryLists.stream()
                .collect(Collectors.groupingBy(AchievementCategoryDetailModel::getAchievementId));

        //规格
        List<Long> achievementCategoryIds = categoryLists.stream().map(AchievementCategoryDetailModel::getAchievementCategoryId).collect(Collectors.toList());
        List<AchievementSpecDetailModel> specDetailLists = achievementSpecDetailRepository.list(
                new LambdaQueryWrapper<AchievementSpecDetailModel>()
                        .in(AchievementSpecDetailModel::getAchievementCategoryId, achievementCategoryIds));
        if (ObjectUtil.isEmpty(specDetailLists)) {
            return null;
        }
        Map<Long, List<AchievementSpecDetailModel>> specDetailModelMap = specDetailLists.stream()
                .collect(Collectors.groupingBy(AchievementSpecDetailModel::getAchievementCategoryId));

        return buildResponse(productList, categoryModelMap, specDetailModelMap);
    }

    @Override
    public List<ExportKuajingAchievementResponse> exportKuajingAchievement(ExportKuajingAchievementRequest request) {
        List<AchievementProductDetailModel> list = achievementProductDetailRepository.list(
                new LambdaQueryWrapper<AchievementProductDetailModel>()
                        .eq(AchievementProductDetailModel::getDeleteFlag, YesOrNotEnum.NO.getCode())
                        .eq(AchievementProductDetailModel::getDisplayed, NumberConstants.INTEGER_VALUE_0)
                        .eq(AchievementProductDetailModel::getAchievementSource, AchievementSourceEnum.KUAJINFG.getCode())
                        .ne(AchievementProductDetailModel::getBusinessId, "96dc187f54a64e89b506b160c8b43531")
                        .between(AchievementProductDetailModel::getStatisticsTime, request.getSignTimeStrat(), request.getSignTimeEnd()));

        List<ProductForAchievementResponse> allProductForAchievementList = innerService.getAllProductForAchievement();

        return buildExportKuajingAchievementResponse(list,allProductForAchievementList);
    }

    private List<ExportKuajingAchievementResponse> buildExportKuajingAchievementResponse(List<AchievementProductDetailModel> list,
            List<ProductForAchievementResponse> allProductForAchievementList) {
        Map<String, ProductForAchievementResponse> productIdByEntityMap = allProductForAchievementList.stream()
                .collect(Collectors.toMap(ProductForAchievementResponse::getProductId, Function.identity()));

        List<ExportKuajingAchievementResponse> responseList = new ArrayList<>();
        for (AchievementProductDetailModel detailModel : list) {
            ExportKuajingAchievementResponse response = new ExportKuajingAchievementResponse();
            response.setId(detailModel.getAchievementId().toString());
            response.setSingingDate(DateUtils.format(detailModel.getSignedTime(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss));
            response.setSingingAmount(detailModel.getPayableAmount());
            response.setActualAccount(detailModel.getPaidAmount());
            response.setTxtCode(detailModel.getContractNo());
            response.setCustId(detailModel.getCustomerId());
            response.setCustName(detailModel.getCustomerName());
            response.setProductId(detailModel.getProductId().toString());
            response.setProductName(detailModel.getProductName());
            //产品类型 0 不是网站 5002  1 是网站 5001
            Integer productType = detailModel.getSiteFlag().equals(NumberConstants.INTEGER_VALUE_1)
                    ? NumberConstants.INTEGER_VALUE_5001
                    : NumberConstants.INTEGER_VALUE_5002;
            response.setProductType(productType);

            if (NumberConstants.INTEGER_VALUE_1.equals(detailModel.getStatus())) {
                response.setStatus(NumberConstants.INTEGER_VALUE_0);
            }
            if (NumberConstants.INTEGER_VALUE_2.equals(detailModel.getStatus())) {
                response.setStatus(NumberConstants.INTEGER_VALUE_3);
            }

            if (NumberConstants.INTEGER_VALUE_1.equals(detailModel.getSaleType())) {
                response.setBusinessType(detailModel.getSaleType());
            }
            if (NumberConstants.INTEGER_VALUE_2.equals(detailModel.getSaleType())) {
                response.setBusinessType(detailModel.getSaleType());
            }
            if (NumberConstants.INTEGER_VALUE_3.equals(detailModel.getSaleType())) {
                response.setBusinessType(88);
            }
            if (NumberConstants.INTEGER_VALUE_4.equals(detailModel.getSaleType())) {
                response.setBusinessType(detailModel.getSaleType());
            }

            if (NumberConstants.INTEGER_VALUE_1.equals(detailModel.getCustomerType())) {
                response.setCustType(NumberConstants.INTEGER_VALUE_2);
            } else if (NumberConstants.INTEGER_VALUE_2.equals(detailModel.getCustomerType())) {
                response.setCustType(NumberConstants.INTEGER_VALUE_1);
            } else {
                response.setCustType(NumberConstants.INTEGER_VALUE_3);
            }
            if (NumberConstants.INTEGER_VALUE_1.equals(detailModel.getMainSplitPerson())) {
                response.setShareType(NumberConstants.INTEGER_VALUE_1);
            } else {
                response.setShareType(NumberConstants.INTEGER_VALUE_0);
            }
            response.setSalerId(detailModel.getBusinessId());
            response.setSaleHiredMoney(detailModel.getAgentCommissionAchievement());
            response.setDelaySaleHiredMoney(detailModel.getAgentDeferredCommission());
            response.setRelaySaleHiredMoney(detailModel.getAgentActualCommission());
            response.setManagerHiredMoney(detailModel.getDeptCommission());
            response.setSubManagerHiredMoney(detailModel.getBranchCommission());

            response.setToAccountDate(DateUtils.format(detailModel.getStatisticsTime(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss));
            response.setIsSalerYj(NumberConstants.INTEGER_VALUE_1);
            response.setIsDeptYj(NumberConstants.INTEGER_VALUE_1);
            response.setIsOrgYj(NumberConstants.INTEGER_VALUE_1);
            response.setIsAreaYj(NumberConstants.INTEGER_VALUE_1);
            //净现金
            response.setProductPrice(detailModel.getNetCash());
            response.setOrderProductId(detailModel.getOrderProductId());
            if (NumberConstants.INTEGER_VALUE_1.equals(detailModel.getStatus())) {
                response.setNodeInfo("支付完成");
            }
            if (NumberConstants.INTEGER_VALUE_2.equals(detailModel.getStatus())) {
                response.setNodeInfo("生产完成");
            }
            //标准价
            response.setStandardPrice(detailModel.getStandardPrice());
            //商品分类
            ProductForAchievementResponse productForAchievementResponse = productIdByEntityMap.get(detailModel.getProductId() + "");
            if(null != productForAchievementResponse){
                response.setLevelOneCategoryName(productForAchievementResponse.getLevelOneCategoryName());
                response.setLevelTwoCategoryName(productForAchievementResponse.getLevelTwoCategoryName());
                response.setLevelThreeCategoryName(productForAchievementResponse.getLevelThreeCategoryName());
            }
            responseList.add(response);
        }
        return responseList;
    }

    private List<PrformanceInfoResponse> buildResponse(List<AchievementProductDetailModel> productList, Map<Long, List<AchievementCategoryDetailModel>> categoryModelMap, Map<Long, List<AchievementSpecDetailModel>> specDetailModelMap) {
        List<PrformanceInfoResponse> responseList = new ArrayList<>();
        for (AchievementProductDetailModel productModel : productList) {

            List<AchievementCategoryDetailModel> achievementCategoryDetailModels = categoryModelMap.get(productModel.getAchievementId());
            for (AchievementCategoryDetailModel categoryDetailModel : achievementCategoryDetailModels) {

                List<AchievementSpecDetailModel> achievementSpecDetailModelList = specDetailModelMap.get(categoryDetailModel.getAchievementCategoryId());
                for (AchievementSpecDetailModel specDetailModel : achievementSpecDetailModelList) {
                    PrformanceInfoResponse response = new PrformanceInfoResponse();
                    //商务月
                    response.setBusinessMonth(productModel.getBusinessMonth());
                    //结算单ID
                    response.setSettlementId(productModel.getOrderProductId());
                    //客户名称
                    response.setCustomerName(productModel.getCustomerName());
                    //客户所在地区
                    response.setCustomerRegion(productModel.getCustomerRegion());
                    //主副分享人
                    response.setSharer(MainSplitPersonEnum.fromCode(productModel.getMainSplitPerson()).getMessage());
                    //分司
                    response.setBranch(productModel.getCompany());
                    //商务部门
                    response.setBusinessDepartment(productModel.getDepartment());
                    //商务名称
                    response.setBusinessName(productModel.getBusinessRepresentative());
                    //商品分类
                    response.setProductCategory(productModel.getProductType());
                    //商品名称
                    response.setProductName(productModel.getProductName());
                    //规格分类
                    response.setSpecificationCategory(categoryDetailModel.getCategoryName());
                    //规格
                    response.setSpecification(specDetailModel.getSpecName());
                    //状态
                    response.setStatus(StatusEnum.fromCode(productModel.getStatus()).getMessage());
                    //业务类型
                    response.setBusinessType(SaleTypeEnum.fromCode(productModel.getSaleType()).getMessage());
                    //到账时间（支付时间）
                    response.setPaymentTime(DateUtils.format(productModel.getPaymentTime(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss));
                    //实际支付金额
                    response.setActualPaymentAmount(specDetailModel.getPaidAmount().toString());
                    //业绩
                    response.setPerformance(specDetailModel.getNetCash().toString());
                    //获取当前商务月对应商务的统计
                    Long businessMonthId = productModel.getBusinessMonthId();
                    String businessId = productModel.getBusinessId();
                    BusinessAchievementModel businessAchievementModel = achievementBusinessService.findByBusinessMonthIdAndBusinessId(businessMonthId, businessId);
                    if (businessAchievementModel != null) {
                        // 职级体系
                        response.setRankSystem(businessAchievementModel.getPosition());
                        // 岗位名称
                        response.setPositionName(businessAchievementModel.getPositionName());
                        // 累计工龄
                        response.setTotalWorkYears(businessAchievementModel.getTenure());
                    }
                    responseList.add(response);
                }
            }
        }
        return responseList;
    }
}
