package com.xmd.achievement.dao.repository.impl;

import com.xmd.achievement.dao.entity.ThirdAchievementModel;
import com.xmd.achievement.dao.mapper.ThirdAchievementMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IThirdAchievementRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 第三方业绩表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@Slf4j
public class MqThirdAchievementRepositoryImpl extends ServiceImpl<ThirdAchievementMapper, ThirdAchievementModel> implements IThirdAchievementRepository {

@Resource
private ThirdAchievementMapper mqThirdAchievementMapper;

}