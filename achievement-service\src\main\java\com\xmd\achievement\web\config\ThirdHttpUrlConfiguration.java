package com.xmd.achievement.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * http调用的url管理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/9 11:53 上午
 */
@Data
@Component
@ConfigurationProperties(prefix = "third-url")
public class ThirdHttpUrlConfiguration {

    /**
     * 管理 bsp-management
     */
    private BspManagement bspManagement;
    private ContractManagement contractManagement;
    private OrderManagement orderManagement;
    private ItemManagement itemManagement;
    private CrmManagement crmManagement;
    private AdManagement adManagement;
    private ServeManagement serveManagement;
    private EhrManagement ehrManagement;
    private RefundOrderManagement refundOrderManagement;

    @Data
    public static class EhrManagement {

        /**
         * ehr地址
         */
        private String empUrl;

        private String orgInfoById;

        private String orgInfoList;

        private String orgFunction;
    }

    @Data
    public static class BspManagement {

        /**
         * 校验登录信息
         */
        private String verifyUserInfo;

        /**
         * 获取用户信息
         */
        private String queryUserInfoDetail;
        /**
         * 获取客户信息
         */
        private String queryCustomerInfo;

        /**
         * 获取跨境分公司列表
         */
        private String pageQueryKjListOrg;

        /**
         * 查询组织树
         */
        private String queryKjOrgTree;

        /**
         * 获取机构和上级信息
         */
        private String queryListOrg;
        /**
         * 根据orgId查询上下级信息
         */
        private String queryLeveRelation;
        /**
         * 查询体系列表
         */
        private String queryOrgFunctionList;

        /**
         * 根据职能id查询职能分类
         */
        private String getOrgFunctionById;

        /**
         * 根据机构id查询机构商务信息
         */
        private String getOrgBusiness;

        
        private String getCustomerLossDateList;

        private String sendWxMessage;

    }

    @Data
    public static class ContractManagement {
        private String queryContractDetail;

    }

    @Data
    public static class OrderManagement {
        private String simpleOrderDetail;
        private String specStatistics;

    }

    @Data
    public static class ItemManagement {
        private String productsForAchievement;
        private String getProductDiscountRulesListForAchievement;
        private String specDetail;
        private String allProductForAchievement;
        private String getProductListForOrder;
    }

    @Data
    public static class CrmManagement {
        private String getCustomerInfoByCondition;
        private String getProtectByCustomerId;
        private String getCustomerLossDateListCrm;
        private String secretKey;
    }

    @Data
    public static class AdManagement {
        private String queryCustomerRechargeTime;
        private String queryCustomerAllRechargeTime;
    }
    @Data
    public static class ServeManagement {
        private String getServeList;
        private String getConfigList;
        private String getServerDetail;
    }

    @Data
    public static class RefundOrderManagement {
        private String queryAfterSalesOrderInfo;
        private String queryAfterSalesOrderDetail;
    }

}
