package com.xmd.achievement.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 特殊商品走特殊的规格政策性成本
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "product-achievement")
public class ProductAchievementConfig {

    //网站商品id
    private String website;
    //网站商品名称
    private String websiteName;
    //广告通商品id
    private String advertisement;
    //广告通商品id
    private String specId;

}
