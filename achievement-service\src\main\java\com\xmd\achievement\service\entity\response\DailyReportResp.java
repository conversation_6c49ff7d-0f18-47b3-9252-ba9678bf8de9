package com.xmd.achievement.service.entity.response;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DailyReportResp implements Serializable {

    private static final long serialVersionUID = 1458386425873231750L;
    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 商务月id
     */
    @ExcelIgnore
    private Long businessMonthId;
    /**
     * 商务月
     */
    @ExcelProperty("商务月")
    private String businessMonth;
    /**
     * 报表日
     */
    @ExcelProperty("报表日")
    private String reportDay;
    /**
     * 机构ID
     */
    @ExcelProperty("机构id")
    private Long organizationId;
    /**
     * 机构名称
     */
    @ExcelProperty("机构名称")
    private String organizationName;

    /**
     * 所属公司机构id
     */
    @ExcelProperty("所属分司机构id")
    private Long companyId;
    /**
     * 所属公司名称
     */
    @ExcelProperty("所属分司")
    private String companyName;
    /**
     * 体系
     */
    @ExcelProperty("体系")
    private String systemCategory;
    /**
     * 市场类别
     */
    @ExcelProperty("市场类别")
    private String marketCategory;
    /**
     * 日签单金额（元）
     */
    @ExcelProperty("日签单金额（元）")
    private BigDecimal dailySigningAmount;
    /**
     * 日净现金（元）
     */
    @ExcelProperty("日净现金（元）")
    private BigDecimal dailyNetCash;
    /**
     * 月总签单金额（元）
     */
    @ExcelProperty("月总签单金额（元）")
    private BigDecimal monthlySigningAmount;
    /**
     * 月净现金（元）
     */
    @ExcelProperty("月净现金（元）")
    private BigDecimal monthlyNetCash;
    /**
     * 基本任务（元）
     */
    @ExcelProperty("基本任务（元）")
    private BigDecimal basicTask;
    /**
     * 任务完成率
     */
    @ExcelProperty("任务完成率")
    private BigDecimal taskCompletionRate;
    /**
     * 网站净现金（元）
     */
    @ExcelProperty("网站净现金（元）")
    private BigDecimal websiteNetCash;
    /**
     * 月新客户数
     */
    @ExcelProperty("月新客户数")
    private Long monthlyNewCustomerCount;
    /**
     * 月网站新开客户数
     */
    @ExcelProperty("月网站新开客户数")
    private Long monthlyWebsiteNewCustomerCount;
    /**
     * 月新增网站数
     */
    @ExcelProperty("月新增网站数")
    private Long monthlyNewWebsiteCount;
    /**
     * 体系id
     */
    @ExcelIgnore
    private Long systemId;

    /**
     * 市场类别id
     */
    @ExcelIgnore
    private Long marketCategoryId;
    /**
     * 创建人id
     */
    @ExcelIgnore
    private String createUserId;
    /**
     * 创建人名称
     */
    @ExcelIgnore
    private String createUserName;
    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date createTime;
    /**
     * 更新人id
     */
    @ExcelIgnore
    private String updateUserId;
    /**
     * 更新人名称
     */
    @ExcelIgnore
    private String updateUserName;
    /**
     * 更新时间
     */
    @ExcelIgnore
    private Date updateTime;

    @ExcelProperty("saas净现金（元）")
    private BigDecimal saasNetCash;
}
