package com.xmd.achievement.handler.statistics;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.enums.*;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * ●网站非续费客户数，</br>
 * ○主分单销售于当前月所生成的首付款且产品类型为网站或者电商商城且业务类型为新开或升级且净现金大于0的业绩条数；</br>
 * ○只统付款时生成的业绩流水；</br>
 * ○统计主分单人为主单人的业绩，辅分单人业绩则不统计；</br>
 * ○去重。
 * <AUTHOR>
 * @date: 2024/12/25 16:10
 * @version: 1.0.0
 * @return {@link }
 */
@Service
public class WebsiteNonRenewalCustomersHandler implements StatisticsHandler {
    @Override
    public void statistics(StatisticsFactInfo factInfo) {
        List<AchievementProductDetailModel> achList = factInfo.getAchList();
        factInfo.setWebsiteNonRenewalCustomers((int) achList.stream().filter(ach -> MainSubEnum.MAIN.getType().equals(ach.getMainSplitPerson())
                && (ach.getSaleType().equals(OrderSaleTypeEnum.OPEN.getType()) || ach.getSaleType().equals(OrderSaleTypeEnum.UPGRADE.getType()))
                && SiteFlagEnum.YES.getType().equals(ach.getSiteFlag())
                && ach.getNetCash().compareTo(BigDecimal.ZERO) > 0).map(AchievementProductDetailModel::getCustomerId).distinct().count());
    }
}