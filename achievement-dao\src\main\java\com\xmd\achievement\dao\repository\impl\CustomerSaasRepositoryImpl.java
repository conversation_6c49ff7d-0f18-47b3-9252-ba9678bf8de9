package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.dto.SearchCustomerSaasDto;
import com.xmd.achievement.dao.entity.CustomerSaasModel;
import com.xmd.achievement.dao.mapper.CustomerSaasMapper;
import com.xmd.achievement.dao.repository.ICustomerSaasRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class CustomerSaasRepositoryImpl extends ServiceImpl<CustomerSaasMapper, CustomerSaasModel> implements ICustomerSaasRepository {
    @Autowired
    private CustomerSaasMapper customerSaasMapper;

    @Override
    public Date getMaxChurnDateByCustomerId(String customerId) {
        return customerSaasMapper.selectMaxChurnDateByCustomerId(customerId);
    }

    @Override
    public Date getMaxChurnDateByCustomerId(String customerId, Date date,Long orderId) {
       return customerSaasMapper.selectMaxChurnDateByCustomerIdWithDate(customerId,date,orderId);
    }  
    

    @Override
    public int countOrderBetween(String customerId, Date start, Date end, Integer orderSource) {
        return customerSaasMapper.countOrderBetween(customerId, start, end, orderSource);
    }

    @Override
    public List<SearchCustomerSaasDto> searchCustomerSaasCount(List<String> businessIds,
            String startDate, String endDate) {
        return customerSaasMapper.searchCustomerSaasCount(businessIds, startDate, endDate);
    }
}
