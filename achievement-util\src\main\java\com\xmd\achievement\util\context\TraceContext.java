package com.xmd.achievement.util.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.xmd.achievement.util.constant.UtilConstant;
import org.slf4j.MDC;

public class TraceContext {

    private static final TransmittableThreadLocal<String> ttl = new TransmittableThreadLocal<>();

    public static void set(String id) {
        ttl.set(id);
        MDC.put(UtilConstant.Mdc.REQUEST_ID_NAME, id);
    }

    public static String get() {
        return ttl.get();
    }

    public static void clear() {
        ttl.remove();
        MDC.remove(UtilConstant.Mdc.REQUEST_ID_NAME);
    }
}
