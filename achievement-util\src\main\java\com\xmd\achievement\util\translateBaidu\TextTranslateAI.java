package com.xmd.achievement.util.translateBaidu;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 百度智能文本翻译工具
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/3/5 10:20 上午
 */
@Slf4j
public class TextTranslateAI {
    /**
     * 百度翻译域名
     */
    private static final String DOMAIN = "https://fanyi.baidu.com";
    /**
     * 检测文本语言url
     */
    private static final String LANGUAGE_DETECT_URL = DOMAIN + "/langdetect";
    /**
     * 翻译url
     */
    private static final String TRANSLATE_URL = DOMAIN + "/ait/text/translate";

    /**
     * 语言
     */
    private static final String LANGUAGE_LIST = "{\"auto\":\"自动检测\",\"zh\":\"中文(简体)\",\"en\":\"英语\",\"jp\":\"日语\",\"th\":\"泰语\",\"spa\":\"西班牙语\",\"ara\":\"阿拉伯语\",\"fra\":\"法语\",\"kor\":\"韩语\",\"ru\":\"俄语\",\"de\":\"德语\",\"pt\":\"葡萄牙语\",\"it\":\"意大利语\",\"el\":\"希腊语\",\"nl\":\"荷兰语\",\"pl\":\"波兰语\",\"fin\":\"芬兰语\",\"cs\":\"捷克语\",\"bul\":\"保加利亚语\",\"dan\":\"丹麦语\",\"est\":\"爱沙尼亚语\",\"hu\":\"匈牙利语\",\"rom\":\"罗马尼亚语\",\"slo\":\"斯洛文尼亚语\",\"swe\":\"瑞典语\",\"vie\":\"越南语\",\"yue\":\"粤语\",\"cht\":\"中文(繁体)\",\"wyw\":\"文言文\",\"afr\":\"南非荷兰语\",\"alb\":\"阿尔巴尼亚语\",\"amh\":\"阿姆哈拉语\",\"arm\":\"亚美尼亚语\",\"asm\":\"阿萨姆语\",\"ast\":\"阿斯图里亚斯语\",\"aze\":\"阿塞拜疆语\",\"baq\":\"巴斯克语\",\"bel\":\"白俄罗斯语\",\"ben\":\"孟加拉语\",\"bos\":\"波斯尼亚语\",\"bur\":\"缅甸语\",\"cat\":\"加泰罗尼亚语\",\"ceb\":\"宿务语\",\"hrv\":\"克罗地亚语\",\"dzo\":\"不丹语\",\"epo\":\"世界语\",\"fao\":\"法罗语\",\"fil\":\"菲律宾语\",\"glg\":\"加利西亚语\",\"geo\":\"格鲁吉亚语\",\"guj\":\"古吉拉特语\",\"hau\":\"豪萨语\",\"heb\":\"希伯来语\",\"hi\":\"印地语\",\"ice\":\"冰岛语\",\"ibo\":\"伊博语\",\"id\":\"印尼语\",\"gle\":\"爱尔兰语\",\"kan\":\"卡纳达语\",\"kli\":\"克林贡语\",\"kur\":\"库尔德语\",\"lao\":\"老挝语\",\"lat\":\"拉丁语\",\"lav\":\"拉脱维亚语\",\"lit\":\"立陶宛语\",\"ltz\":\"卢森堡语\",\"mac\":\"马其顿语\",\"mg\":\"马拉加斯语\",\"may\":\"马来语\",\"mal\":\"马拉雅拉姆语\",\"mlt\":\"马耳他语\",\"mar\":\"马拉地语\",\"mon\":\"蒙古语\",\"nep\":\"尼泊尔语\",\"nno\":\"新挪威语\",\"per\":\"波斯语\",\"srd\":\"萨丁尼亚语\",\"srp\":\"塞尔维亚语\",\"sin\":\"僧伽罗语\",\"sk\":\"斯洛伐克语\",\"som\":\"索马里语\",\"swa\":\"斯瓦希里语\",\"tgl\":\"他加禄语\",\"tgk\":\"塔吉克语\",\"tam\":\"泰米尔语\",\"tat\":\"鞑靼语\",\"tel\":\"泰卢固语\",\"tib\":\"藏语\",\"tr\":\"土耳其语\",\"tuk\":\"土库曼语\",\"ukr\":\"乌克兰语\",\"urd\":\"乌尔都语\",\"oci\":\"奥克语\",\"kir\":\"吉尔吉斯语\",\"pus\":\"普什图语\",\"hkm\":\"高棉语\",\"ht\":\"海地语\",\"nob\":\"书面挪威语\",\"pan\":\"旁遮普语\",\"uig\":\"维吾尔语\",\"arq\":\"阿尔及利亚阿拉伯语\",\"bis\":\"比斯拉马语\",\"frn\":\"加拿大法语\",\"hak\":\"哈卡钦语\",\"hup\":\"胡帕语\",\"ing\":\"印古什语\",\"lag\":\"拉特加莱语\",\"mau\":\"毛里求斯克里奥尔语\",\"mot\":\"黑山语\",\"pot\":\"巴西葡萄牙语\",\"ruy\":\"卢森尼亚语\",\"sec\":\"塞尔维亚-克罗地亚语\",\"sil\":\"西里西亚语\",\"tua\":\"突尼斯阿拉伯语\",\"ach\":\"亚齐语\",\"aka\":\"阿肯语\",\"arg\":\"阿拉贡语\",\"aym\":\"艾玛拉语\",\"bal\":\"俾路支语\",\"bak\":\"巴什基尔语\",\"bem\":\"本巴语\",\"ber\":\"柏柏尔语\",\"bho\":\"博杰普尔语\",\"bli\":\"比林语\",\"bre\":\"布列塔尼语\",\"bui\":\"布里亚特语\",\"che\":\"车臣语\",\"chr\":\"切罗基语\",\"nya\":\"齐切瓦语\",\"chv\":\"楚瓦什语\",\"cor\":\"康瓦尔语\",\"cos\":\"科西嘉语\",\"cre\":\"克里克语\",\"cri\":\"克里米亚鞑靼语\",\"div\":\"迪维希语\",\"eno\":\"古英语\",\"frm\":\"中古法语\",\"fri\":\"弗留利语\",\"ful\":\"富拉尼语\",\"gla\":\"盖尔语\",\"lug\":\"卢干达语\",\"gra\":\"古希腊语\",\"grn\":\"瓜拉尼语\",\"haw\":\"夏威夷语\",\"hil\":\"希利盖农语\",\"ido\":\"伊多语\",\"ina\":\"因特语\",\"iku\":\"伊努克提图特语\",\"jav\":\"爪哇语\",\"kab\":\"卡拜尔语\",\"kal\":\"格陵兰语\",\"kam\":\"卡尔梅克语\",\"kau\":\"卡努里语\",\"kas\":\"克什米尔语\",\"kah\":\"卡舒比语\",\"kin\":\"卢旺达语\",\"kon\":\"刚果语\",\"kok\":\"孔卡尼语\",\"lim\":\"林堡语\",\"lin\":\"林加拉语\",\"loj\":\"逻辑语\",\"log\":\"低地德语\",\"los\":\"下索布语\",\"mai\":\"迈蒂利语\",\"glv\":\"曼克斯语\",\"mao\":\"毛利语\",\"mah\":\"马绍尔语\",\"nbl\":\"南恩德贝莱语\",\"nea\":\"那不勒斯语\",\"nqo\":\"西非书面语\",\"sme\":\"北方萨米语\",\"nor\":\"挪威语\",\"oji\":\"奥吉布瓦语\",\"ori\":\"奥里亚语\",\"orm\":\"奥罗莫语\",\"oss\":\"奥塞梯语\",\"pam\":\"邦板牙语\",\"pap\":\"帕皮阿门托语\",\"ped\":\"北索托语\",\"que\":\"克丘亚语\",\"roh\":\"罗曼什语\",\"ro\":\"罗姆语\",\"sm\":\"萨摩亚语\",\"san\":\"梵语\",\"sco\":\"苏格兰语\",\"sha\":\"掸语\",\"sna\":\"修纳语\",\"snd\":\"信德语\",\"sol\":\"桑海语\",\"sot\":\"南索托语\",\"syr\":\"叙利亚语\",\"tet\":\"德顿语\",\"tir\":\"提格利尼亚语\",\"tso\":\"聪加语\",\"twi\":\"契维语\",\"ups\":\"高地索布语\",\"ven\":\"文达语\",\"wln\":\"瓦隆语\",\"wel\":\"威尔士语\",\"fry\":\"西弗里斯语\",\"wol\":\"沃洛夫语\",\"xho\":\"科萨语\",\"yid\":\"意第绪语\",\"yor\":\"约鲁巴语\",\"zaz\":\"扎扎其语\",\"zul\":\"祖鲁语\",\"moc\":\"蒙古语\",\"sun\":\"巽他语\",\"hmn\":\"苗语\",\"src\":\"塞尔维亚语\"}";
    /**
     * key=语言（中文） value=语言-标识
     */
    private static final Map<String, String> LANGUAGE_ZH_SIGN_MAP = new HashMap<>();
    /**
     * key=语言-标识 value=语言（中文）
     */
    private static final Map<String, String> LANGUAGE_SIGN_ZH_MAP = new HashMap<>();
    //读取超时，单位秒
    private static final int READ_TIME_OUT = 30;
    //写入超时，单位秒
    private static final int WRITE_TIME_OUT = 30;
    //连接超时，单位秒
    private static final int CONNECT_TIME_OUT = 15;

    //HTTP客户端--针对当前工具创建的客户端
    private static volatile OkHttpClient client;

    static {
        //获取HTTP客户端
        if (client == null) {
            synchronized (TextTranslateAI.class) {
                if (client == null) {
                    client = new OkHttpClient.Builder()
                            .readTimeout(READ_TIME_OUT, TimeUnit.SECONDS)
                            .connectTimeout(CONNECT_TIME_OUT, TimeUnit.SECONDS)
                            .writeTimeout(WRITE_TIME_OUT, TimeUnit.SECONDS)
                            .build();
                }
            }
        }
        JSONObject jsonObject = JSONObject.parseObject(LANGUAGE_LIST);
        Map<String, String> map = jsonObject.toJavaObject(Map.class);
        for (Map.Entry<String, String> entrySet : map.entrySet()) {
            String key = entrySet.getKey();
            String value = entrySet.getValue();
            LANGUAGE_ZH_SIGN_MAP.put(value, key);
            LANGUAGE_SIGN_ZH_MAP.put(key, value);

        }
    }

    private TextTranslateAI() {
    }


    /**
     * 检测文本语言
     *
     * @param msg    文本
     * @param cookie
     * @return
     */
    public static Result<String> langDetect(String msg, String cookie) {
        // 创建请求体
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String encode = null;
        try {
            encode = URLEncoder.encode(msg, "UTF-8");
        } catch (UnsupportedEncodingException unsupportedEncodingException) {
            return Result.error(ResultEnum.REQUEST_ERROR, "消息编码失败");
        }
        String requestBody = "query=" + encode;
        RequestBody body = RequestBody.create(requestBody, mediaType);
        // 创建请求对象
        Request.Builder builder = new Request.Builder()
                .url(LANGUAGE_DETECT_URL)
                .method("POST", body)
                .addHeader("Accept", "*/*")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Cache-Control", "no-cache")
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Origin", DOMAIN)
                .addHeader("Pragma", "no-cache")
                .addHeader("Referer", DOMAIN + "/mtpe-individual/multimodal?ext_channel=Aldtype")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36")
                .addHeader("sec-ch-ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("sec-ch-ua-platform", "\"macOS\"");
        if (StringUtils.isNotEmpty(cookie)) {
            builder.addHeader("cookie", cookie);
        }
        Request request = builder.build();
        // 发送请求并处理响应
        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                System.out.println("baidu text langDetect response error:" + response.code());
                log.warn("baidu text langDetect response error:{}", response.code());
                return Result.error(ResultEnum.LAN_DETECT_EXCEPTION);
            }
            String responseBody = response.body().string();
            System.out.println("baidu text langDetect response result: " + responseBody);
            log.info("baidu text langDetect response result:{} ", responseBody);
            JSONObject jsonObject = JSON.parseObject(responseBody);
            Object error = jsonObject.get("error");
            if (Objects.isNull(error) || !Objects.equals(error, 0)) {
                System.out.println("baidu text langDetect response fail:" + responseBody);
                log.warn("baidu text langDetect response fail:{}", responseBody);
                return Result.error(ResultEnum.LAN_DETECT_EXCEPTION);
            }
            String lan = jsonObject.getString("lan");
            return Result.success(lan);
        } catch (Exception e) {
            System.out.println("baidu text langDetect exception: " + e);
            log.error("baidu text langDetect exception: ", e);
            return Result.error(ResultEnum.LAN_DETECT_EXCEPTION);
        }
    }

    /**
     * 检测文本语言
     *
     * @param msg 文本
     * @return
     */
    public static Result<String> langDetect(String msg) {
        return langDetect(msg, null);
    }

    /**
     * 翻译
     *
     * @param msg      文本
     * @param from     文本语言
     * @param to       目标语言
     * @param cookie
     * @param acsToken
     * @return
     */
    public static Result<String> translate(String msg, String from, String to, String cookie, String acsToken) {
        JSONObject object = new JSONObject();
        object.put("query", msg);
        object.put("from", from);
        object.put("to", to);
        object.put("milliTimestamp", System.currentTimeMillis());
        object.put("reference", "");
        object.put("domain", "common");
        object.put("corpusIds", new ArrayList<>());
        //默认值
        String[] qcSettings = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11"};
        object.put("qcSettings", new ArrayList<>(Arrays.asList(qcSettings)));
        String param = object.toJSONString();
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(param, mediaType);
        Request.Builder builder = new Request.Builder()
                .url(TRANSLATE_URL)
                .method("POST", body)
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Cache-Control", "no-cache")
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Type", "application/json")
                .addHeader("Origin", DOMAIN)
                .addHeader("Pragma", "no-cache")
                .addHeader("Referer", DOMAIN + "/mtpe-individual/multimodal?ext_channel=Aldtype")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36")
                //event stream 数据,仅涉及1次调用
                .addHeader("accept", "text/event-stream")
                .addHeader("sec-ch-ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("sec-ch-ua-platform", "\"macOS\"");
        if (StringUtils.isNotEmpty(cookie)) {
            builder.addHeader("cookie", cookie);
        }
        if (StringUtils.isNotEmpty(cookie)) {
            builder.addHeader("Acs-Token", acsToken);
        }
        Request request = builder.build();
        try {
            Response response = client.newCall(request).execute();
            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            StringBuilder result = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                // 处理 event stream 数据
                System.out.println("baidu text translate line:" + line);
                log.info("baidu text translate line:{}", line);
                // 解析实际翻译结果
                if (StringUtils.isEmpty(line) || !line.startsWith("data:")) {
                    continue;
                }
                String json = line.substring(line.indexOf("{"));
                if (StringUtils.isEmpty(json)) {
                    continue;
                }
                JSONObject jsonObject = JSON.parseObject(json);
                Object errno = jsonObject.get("errno");
                if (Objects.isNull(errno) || !Objects.equals(errno, 0)) {
                    continue;
                }
                JSONObject data = jsonObject.getJSONObject("data");
                if (Objects.isNull(data)) {
                    continue;
                }
                if (StringUtils.isEmpty(data.getString("event"))
                        || !"Translating".equals(data.getString("event"))
                        || Objects.isNull(data.get("list")) || CollectionUtil.isEmpty(data.getJSONArray("list"))) {
                    continue;
                }
                JSONArray list = data.getJSONArray("list");
                for (int i = 0; i < list.size(); i++) {
                    JSONObject translateData = list.getJSONObject(i);
                    if (Objects.isNull(translateData) || StringUtils.isEmpty(translateData.getString("dst"))) {
                        continue;
                    }
                    result.append(translateData.getString("dst")).append("\n");
                }
            }
            System.out.println("baidu text translate result:" + result.toString());
            log.info("baidu text translate result:{}", result.toString());
            return Result.success(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("baidu text translate exception: ", e);
            return Result.error(ResultEnum.TXT_TRANSLATE_EXCEPTION);
        }
    }

    public static Result<String> translate(String msg, String from, String to) {
        return translate(msg, from, to, null, null);
    }

    public static Result<String> translate(String message, String to) {
        Result<String> stringResult = langDetect(message);
        if (!stringResult.checkSuccess()) {
            return stringResult;
        }
        Result<String> translate = translate(message, stringResult.getData(), to);
        StringBuilder info = new StringBuilder("=================================================");
        info.append("\n")
                .append("原语言为：" + LANGUAGE_SIGN_ZH_MAP.get(stringResult.getData()) + " 翻译语言为：" + LANGUAGE_SIGN_ZH_MAP.get(to)).append("\n")
                .append("===============原语言===============").append("\n")
                .append(message).append("\n")
                .append("===============翻译后===============").append("\n")
                .append(translate.getData());
        return translate;
    }


    public static void main(String[] args) {
        String message = "Статистика по словам\tПоказов в месяц\n" +
                "введите\t3 230 392\n" +
                "ввести код\t430 333\n" +
                "ввести промокод\t387 551\n" +
                "какие ввели\t342 980\n" +
                "1 1 ввести\t298 117\n" +
                "ввести ru\t246 042\n" +
                "куда вводить\t238 082\n" +
                "введите число\t216 568\n" +
                "введи ответ\t175 324\n" +
                "введите 2\t155 405\n" +
                "где вводить\t149 470\n" +
                "введите год\t142 418\n" +
                "ввести программу\t135 785\n" +
                "win ввести промокод\t131 439\n" +
                "куда вводить промокод\t125 164\n" +
                "3 ввели\t112 321\n" +
                "ввести данные\t111 720\n" +
                "1win ввести промокод 1 win advancement ru\t110 050\n" +
                "введут ли\t108 851\n" +
                "введите пароль\t99 573\n" +
                "ввести написал\t99 439\n" +
                "4 ввести\t92 815\n" +
                "где ввести промокод\t85 644\n" +
                "введите 5\t83 701\n" +
                "введено выведено\t81 305\n" +
                "выводить ввести\t81 200\n" +
                "введено найдено\t78 075\n" +
                "нужно ввести\t78 069\n" +
                "определить ввести\t78 054\n" +
                "написать программа ввести\t77 942\n" +
                "россия ввела\t77 873\n" +
                "введите номер\t76 676\n" +
                "программа введите число\t74 319\n" +
                "activate ввести\t73 643\n" +
                "ввел новый\t72 730\n" +
                "куда вводить коды\t71 546\n" +
                "введите поле\t71 440\n" +
                "activate ввести код\t69 340\n" +
                "введите значение\t69 061\n" +
                "пользователь вводит\t66 427\n" +
                "ввести пол\t64 593\n" +
                "сколько ввести\t63 063\n" +
                "ввести код +с телевизора\t60 539\n" +
                "ввести цифры\t59 166\n" +
                "win куда вводить промокод\t57 679\n" +
                "+в каком году ввели\t56 357\n" +
                "введите первые\t56 284\n" +
                "введите сумму\t53 130\n" +
                "введите ответ +в поле\t52 352\n" +
                "целый ввести\t51 157";
        String to = "zh";
        Result<String> stringResult = langDetect(message);
        if (!stringResult.checkSuccess()) {
            System.out.println(stringResult.getMsg());
            return;
        }
        Result<String> translate = translate(message, stringResult.getData(), to);
        if (!translate.checkSuccess()) {
            System.out.println(translate.getMsg());
            return;
        }
        System.out.println("=================================================");
        System.out.println("原语言为：" + LANGUAGE_SIGN_ZH_MAP.get(stringResult.getData()) + " 翻译语言为：" + LANGUAGE_SIGN_ZH_MAP.get(to));
        System.out.println();
        System.out.println("===============原语言===============");
        System.out.println();
        System.out.println(message);
        System.out.println("===============翻译后===============");
        System.out.println();
        System.out.println(translate.getData());
    }

}
