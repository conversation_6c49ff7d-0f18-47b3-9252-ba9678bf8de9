package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.SaasTabModel;
import com.xmd.achievement.service.entity.request.SaasTabAddRequest;
import com.xmd.achievement.service.entity.request.SaasTabPageRequest;
import com.xmd.achievement.service.entity.request.SaasTabUpdateRequest;
import com.xmd.achievement.service.entity.response.SaasTabResponse;
import com.xmd.achievement.web.entity.base.PageResponse;

/**
 * saas标签表服务接口
 */
public interface ISaasTabService {

    /**
     * 分页查询saas标签列表
     *
     * @param request 分页查询参数
     * @return 分页结果
     */
    PageResponse<SaasTabResponse> pageQuery(SaasTabPageRequest request);

    /**
     * 新增saas标签
     *
     * @param request 新增参数
     * @return 是否新增成功
     */
    boolean add(SaasTabAddRequest request);

    /**
     * 修改saas标签
     *
     * @param request 修改参数
     * @return 是否修改成功
     */
    boolean update(SaasTabUpdateRequest request);

    /**
     * 删除saas标签
     *
     * @param id saas标签ID
     * @return 是否删除成功
     */
    boolean delete(Long id);

    boolean checkIsSaas(Long productId);

    boolean checkIsSaasKuaJing(Long productId);

    boolean checkIsSaasZhongQi(Long productId);

    boolean batchRefresh();
}