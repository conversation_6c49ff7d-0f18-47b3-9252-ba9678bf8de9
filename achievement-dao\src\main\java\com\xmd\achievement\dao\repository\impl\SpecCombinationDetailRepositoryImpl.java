package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.SpecCombinationDetailModel;
import com.xmd.achievement.dao.mapper.SpecCombinationDetailMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.ISpecCombinationDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 规格组合明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@Slf4j
public class SpecCombinationDetailRepositoryImpl extends ServiceImpl<SpecCombinationDetailMapper, SpecCombinationDetailModel> implements ISpecCombinationDetailRepository {

    @Resource
    private SpecCombinationDetailMapper specCombinationDetailMapper;

    /**
     * 通过组合id集合查询规则详情列表
     * @param combinationIdList
     * @return
     */
    @Override
    public List<SpecCombinationDetailModel> selectSpecCombinationDetailList(List<Long> combinationIdList) {
        LambdaQueryWrapper<SpecCombinationDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SpecCombinationDetailModel::getCombinationId, combinationIdList);
        queryWrapper.eq(SpecCombinationDetailModel::getDeleteFlag,0);
        return specCombinationDetailMapper.selectList(queryWrapper);
    }
}