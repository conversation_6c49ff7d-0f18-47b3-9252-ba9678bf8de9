package com.xmd.achievement.web.interceptor;

import cn.hutool.core.lang.UUID;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 日志拦截器
 *
 * @version 1.0.0
 * @date 2021/10/25 下午5:19
 */
@Component
@Slf4j
public class LogInterceptor implements HandlerInterceptor {

    /**
     * 请求唯一ID名称
     */
    public final static String REQUEST_ID_NAME = "traceId";

    /**
     * 请求开始时间名称
     */
    public final static String START_TIME = "startTime";

    /**
     * 放入MDC的属性列表
     */
    public final static List<String> MDC_NAME_LIST = new ArrayList<>();


    static {
        MDC_NAME_LIST.add(REQUEST_ID_NAME);
        MDC_NAME_LIST.add(START_TIME);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        MDC.put(REQUEST_ID_NAME, UUID.fastUUID().toString(true));
        MDC.put(START_TIME, String.valueOf(System.currentTimeMillis()));
        return true;
    }


    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        MDC.clear();
    }
}
