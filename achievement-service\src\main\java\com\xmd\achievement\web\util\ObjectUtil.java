package com.xmd.achievement.web.util;

import java.io.*;
import java.util.List;

/**
 * 对象操作工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class ObjectUtil {

    /**
     * 对list进行深拷贝
     * @return
     * @param <T>
     * @throws IOException
     * @throws ClassNotFoundException
     */
    public static <T> List<T> deepCopy(List<T> original) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(original);
            oos.flush();
            oos.close();

            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bis);

            return (List<T>) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            throw new RuntimeException("深拷贝失败，对象可能未正确实现 Serializable", e);
        }
    }

    /**
     * 深拷贝对象（对象及其内部引用的对象都会被复制）
     * 要求：对象及其所有引用的对象必须实现 Serializable 接口
     */
    public static <T extends Serializable> T deepCopy(T object) {
        try {
            // 写入字节流
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(object);
            oos.flush();
            oos.close();

            // 从字节流读取副本
            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bis);

            return (T) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            throw new RuntimeException("深拷贝失败，对象可能未正确实现 Serializable", e);
        }
    }
}
