package com.xmd.achievement.handler.statistics;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xmd.achievement.dao.entity.AchievementSegmentModel;
import com.xmd.achievement.dao.entity.PositionModel;
import com.xmd.achievement.dao.entity.SeniorityModel;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.UserInfoDetailResp;
import com.xmd.achievement.service.ISegmentService;
import com.xmd.achievement.service.ISeniorityService;
import com.xmd.achievement.service.PositionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * 职级/在岗时长/司龄处理器
 * <AUTHOR>
 * @date: 2024/12/25 11:19
 */
@Service
@Slf4j
public class BusinessHandler implements StatisticsHandler {
    @Resource
    private InnerService innerService;
    @Resource
    private PositionService positionService;
    @Resource
    private ISeniorityService seniorityService;

    @Override
    public void statistics(StatisticsFactInfo factInfo) {
        UserInfoDetailResp business = innerService.getUserInfoDetail(factInfo.getBusinessId());
        if (ObjectUtil.isEmpty(business)) {
            log.info("商务id：{}所属机构不存在", factInfo.getBusinessId());
            return;
        }
        Integer jobGradeId = business.getJobGradeId();
        Integer passFlag = business.getPassFlag();
        String position = business.getPosition();
        //职级
        factInfo.setPosition(position);
        //处理职级类型
        PositionModel positionType = positionService.getByJobGradeIdAndPassFlag(jobGradeId, passFlag);
        factInfo.setPositionType(positionType);
        //处理在岗时长
        Date enterDate = business.getEnterDate() != null ? business.getEnterDate() : new Date();
        long tenure = LocalDateTimeUtil.between(LocalDateTimeUtil.of(enterDate.toInstant()), LocalDateTimeUtil.of(new Date()), ChronoUnit.MONTHS);
        factInfo.setTenure((int) tenure);
        //处理司龄细分
        SeniorityModel seniority = seniorityService.getSeniority(factInfo.getTenure());
        factInfo.setSeniority(seniority);
    }
}
