package com.xmd.achievement.util.encrypt.crypto.keygen;

/**
 * A StringKeyGenerator that generates hex-encoded String keys. Delegates to a
 * {@link BytesKeyGenerator} for the actual key generation.
 *
 * <AUTHOR>
 */
final class HexEncodingStringKeyGenerator implements StringKeyGenerator {

    private final BytesKeyGenerator keyGenerator;

    HexEncodingStringKeyGenerator(BytesKeyGenerator keyGenerator) {
        this.keyGenerator = keyGenerator;
    }

    @Override
    public String generateKey() {
        return new String(Hex.encode(keyGenerator.generateKey()));
    }

}

