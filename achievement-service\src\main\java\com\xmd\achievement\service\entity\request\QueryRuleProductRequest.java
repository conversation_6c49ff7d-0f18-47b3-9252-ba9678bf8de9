package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/10/09:57
 * @since 1.0
 */
@Data
@Schema(description = "查询规则下商品信息")
public class QueryRuleProductRequest implements Serializable {
    @Schema(description = "规则编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "规则编号不能为空")
    private String ruleCode;
}
