package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/02/08/16:45
 * @since 1.0
 */
@Getter
public enum ThirdSourceEnum {
    /**
     * 表格
     */
    EXCEL(1, "EXCEL"),
    /**
     * 线上实时
     */
    ONLINE(2, "ONLINE"),
    ;

    private final Integer code;
    private final String msg;

    ThirdSourceEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

}
