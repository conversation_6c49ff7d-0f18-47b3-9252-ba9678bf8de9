package com.xmd.achievement.rpc.entity.base;

import lombok.Data;

import java.io.Serializable;

/**
 * spider 加密入参
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/8/15
 */
@Data
public class SpiderEncryptCommonDto<T> implements Serializable {

    private static final long serialVersionUID = -5765532031248498008L;

    /**
     * 随机串
     */
    private String nonce;

    /**
     * 时间戳
     */
    private Long timeStamp;

    /**
     * 数据
     */
    private T data;
}
