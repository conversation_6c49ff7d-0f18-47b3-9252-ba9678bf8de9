package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.MqServeInprogressInfoModel;
import com.xmd.achievement.service.entity.dto.ReceiveServeInProgressDto;

import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/14:25
 * @since 1.0
 */
public interface MqServeInProgressInfoService {
    /**
     * 保存信息
     *
     * @param progressDto 请求参数
     */
    boolean saveInfo(ReceiveServeInProgressDto progressDto);

    List<MqServeInprogressInfoModel> serveDataToPayment();

}
