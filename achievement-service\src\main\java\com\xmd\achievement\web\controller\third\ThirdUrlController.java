package com.xmd.achievement.web.controller.third;

import com.xmd.achievement.service.IAchievementService;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.ICustomerSaasService;
import com.xmd.achievement.service.SaleTaskService;
import com.xmd.achievement.service.entity.request.OpenAchievementRequest;
import com.xmd.achievement.service.entity.request.OpenBusinessMonthRequest;
import com.xmd.achievement.service.entity.request.OpenSaleTaskRequest;
import com.xmd.achievement.service.entity.response.OpenAchievementResponse;
import com.xmd.achievement.service.entity.response.OpenBusinessesMonthResponse;
import com.xmd.achievement.service.entity.response.OpenSaleTaskResponse;
import com.xmd.achievement.web.entity.response.WebResult;

import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 三方开放接口汇总
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Tag(name = "三方开放接口汇总")
@Slf4j
@RestController
@RequestMapping("api")
public class ThirdUrlController {

    @Resource
    private IBusinessMonthService businessMonthService;
    @Resource
    private IAchievementService achievementService;
    @Autowired
    private SaleTaskService saleTaskService;
    @Resource
    private ICustomerSaasService customerSaasService;

    @Operation(summary = "商务月查询开放接口")
    @PostMapping("/businessMonth/openBusinessMonth")
    public WebResult<OpenBusinessesMonthResponse> openBusinessMonth(@RequestBody OpenBusinessMonthRequest currentDate) {
        return WebResult.success(businessMonthService.openBusinessesMonth(currentDate));
    }

    @Operation(summary = "业绩查询开放接口")
    @PostMapping("/achievement/openAchievement")
    public WebResult<List<OpenAchievementResponse>> openAchievement(@RequestBody OpenAchievementRequest request) {
        return WebResult.success(achievementService.openAchievementList(request));
    }

    @Operation(summary = "销售任务开放接口")
    @PostMapping("/saleTask/openSaleTask")
    public WebResult<List<OpenSaleTaskResponse>> openSaleTask(@RequestBody OpenSaleTaskRequest request) {
        return WebResult.success(saleTaskService.openSaleTask(request));
    }

    @ApiOperation("重算客户SaaS历史数据")
    @PostMapping("/customerSaas/recalculateHistory")
    public void recalculateHistory() {
        customerSaasService.recalculateHistory();
    }
}
