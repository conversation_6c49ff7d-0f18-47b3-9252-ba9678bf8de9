package com.xmd.achievement.support.constant.exceptions;


/**
 * 业务异常
 *
 * <AUTHOR>
 * @date: 2024/3/14 2:08 下午
 * @version: 1.0.0
 * @return:
 */
public class BusinessException extends RuntimeException {

    public String errorMsg;

    public BusinessException(String errorMsg) {
        super(errorMsg);
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

}
