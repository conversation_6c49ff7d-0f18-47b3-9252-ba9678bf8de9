package com.xmd.achievement.handler.achievement;

import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.support.constant.enums.OrderSpecTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 *计算续费报价，规格维度，续费报价=应付金额-首年报价；
 * <AUTHOR>
 * @date: 2024/12/20 14:45
 */
@Slf4j
@Service
public class SpecCalculateRenewQuoteAmountHandler implements CalculateAmountHandler {
    @Override
    public void calculate(CalculateFactInfo factInfo) {
        try {
            AchievementSpecDetailModel spec = factInfo.getSpec();
            BigDecimal firstYearQuote = spec.getFirstYearQuote();
            if (null == firstYearQuote) {
                throw new RuntimeException("firstYearQuote首年报价为空");
            }
            BigDecimal paidAmount = spec.getPaidAmount();
            if (null == paidAmount) {
                throw new RuntimeException("paidAmount应付金额为空");
            }
            BigDecimal amount = spec.getPayableAmount();
            if (null == amount) {
                throw new RuntimeException("payableAmount应付金额为空");
            }
            spec.setRenewalQuote(paidAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : amount.subtract(firstYearQuote));
        } finally {
            log.warn("计算续费业绩 规格id:{},参数:{}", factInfo.getSpec().getSpecId(), JSON.toJSONString(factInfo));
        }
    }
}
