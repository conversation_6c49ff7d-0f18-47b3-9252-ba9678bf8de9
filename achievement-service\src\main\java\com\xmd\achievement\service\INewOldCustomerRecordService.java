package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.NewOldCustomerRecordModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date: 2024/12/27 11:00
 */
public interface INewOldCustomerRecordService {
    void save(NewOldCustomerRecordModel record);

    List<NewOldCustomerRecordModel> getListByMonthIdAndEmployeeId(Long businessMonthId, String employeeId);

    void updateCustomer(NewOldCustomerRecordModel record, Integer customerType, Integer currentCustomerType);

    void deleteCustomer(Long businessMonthId, String employeeId, String customerId, Integer currentCustomerType);

    void insertCustomer(NewOldCustomerRecordModel record);
}
