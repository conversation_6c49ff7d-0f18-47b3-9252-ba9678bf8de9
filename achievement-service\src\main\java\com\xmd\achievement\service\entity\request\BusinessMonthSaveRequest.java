package com.xmd.achievement.service.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商务月表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@Schema(description = "商务月保存请求参数")
public class BusinessMonthSaveRequest implements Serializable {
    private static final long serialVersionUID = -8428618915473495735L;
    /**
     * 月半日期
     */
    @NotNull
    @Schema(description = "月半日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date midDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    /**
     * 商务月
     */
    @Schema(description = "商务月")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "商务月必须是 YYYY-MM 格式")
    private String month;
    /**
     * 开始日期
     */
    @Schema(description = "商务月开始")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

}