package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 销售任务提交参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
public class SaleTaskCommitRequest implements
        Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "商务月")
    @NotBlank(message = "商务月不能为空")
    private String businessMonth;

    @Schema(description = "销售任务集合")
    @NotNull(message = "任务不能为空")
    @Size(message = "任务不能为空")
    @Valid
    private List<SaleTaskEditRequest> tasks;



}