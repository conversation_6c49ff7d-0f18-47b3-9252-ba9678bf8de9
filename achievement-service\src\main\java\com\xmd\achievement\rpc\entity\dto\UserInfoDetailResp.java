package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 登录人权限校验dto
 * <AUTHOR>
 * @date 2024/5/23
 * @version 1.0.0
 */
@Data
public class UserInfoDetailResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "账号状态：1: 正常, 2: 冻结")
    private Integer accountStatus;

    @Schema(description = "员工状态 0 在职 1离职")
    private Integer status;

    @Schema(description = "职位")
    private String position;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "联系人手机号码")
    private String mobile;

    @Schema(description = "联系人邮箱")
    private String email;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "职级id")
    private Integer jobGradeId;

    @Schema(description = "是否转正")
    private Integer passFlag;

    @Schema(description = "入职时间")
    private Date enterDate;

    @Schema(description = "离职时间")
    private Date dismissDate;

    @Schema(description = "所属机构信息")
    private OrgPathInfoDTO orgPathInfoDTO;

}