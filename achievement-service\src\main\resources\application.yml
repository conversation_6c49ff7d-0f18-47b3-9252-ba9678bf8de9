server:
  port: 8016
  servlet:
    # 应用的访问路径
    context-path: /achievement-web

spring:
  application:
    name: bsp-achievement
  profiles:
    active: dev
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  mvc:
    format:
      date: yyyy-MM-dd HH:mm:ss
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 3
      min-idle: 3
      max-active: 50
      max-wait: 5000
      keep-alive: false
      remove-abandoned: true
      async-init: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 61000
      max-evictable-idle-time-millis: 25200000
      phy-timeout-millis: 25200000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 100
      max-open-prepared-statements: 100
      filters: config,wall,stat
      connection-properties: druid.stat.slowSqlMillis=1000;druid.stat.logSlowSql=true;config.decrypt=false;
      use-global-data-source-stat: true
      use-unfair-lock: true
      stat-view-servlet:
        enabled: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  redis:
    database: 0
    timeout: 5000
    cluster:
      max-redirects: 6
    lettuce:
      pool:
        max-idle: 8
        min-idle: 0
        max-active: 1000
        max-wait: 15000
        time-between-eviction-runs: 1000

mybatis-plus:
  mapper-locations: classpath:mapper/*Mapper.xml
  type-aliases-package: com.xmd.achievement.dao.entity


rocketmq:
  # 生产者配置
  producer:
    # 消息最大长度 默认1024*64(4M)
    maxMessageSize: 65536
    # 发送消息超时时间,默认3000
    send-message-timeout: 3000
    # 发送消息失败重试次数
    retryTimesWhenSendFailed: 3

xxl:
  job:
    executor:
      author: admin
      oneTimesJob:
        routeStrategy: FAILOVER
        blockStrategy: SERIAL_EXECUTION
        failRetryCount: 1

openapi:
  title: 业务支撑平台 API Docs
  description: 跨境业务支撑平台-业绩
  terms-of-service: https://xxx
  contact-name: xxx
  contact-url: https://xxx.io
  contact-email: <EMAIL>
  version: 1.0
  external-description: xxx
  external-url: https://xxx

sale-task:
  headquarters-name: 中企跨境城市运营中心
  headquarters-org-id: 3950
