package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 拉取任务-订单视图类型标记枚举
 * @date 2024/11/18 14:40
 * @since 1.0
 */
@Getter
public enum OrderViewStatusEnum {

    /**
     * 生成失败
     */
    ORDER_CREATE_FAIL(0, "生成失败"),
    /**
     * 待审核
     */
    WAITING_AUDIT(1, "待审核"),
    /**
     * 待推送
     */
    WAITING_PUSH(2, "待推送"),
    /**
     * 待确认
     */
    WAITING_CONFIRM(3, "待确认"),
    /**
     * 合同审核待发起
     */
    WAITING_START_CONTRACT_AUDIT(4, "合同审核待发起"),
    /**
     * 合同审核中
     */
    CONTRACT_AUDITING(5, "合同审核中"),
    /**
     * 已确认待支付
     */
    CONFIRM_WAITING_PAY(6, "已确认待支付"),
    /**
     * 已完成
     */
    FINISHED(7, "已完成"),
    /**
     * 取消
     */
    CANCEL(8, "取消"),

    PARTIAL_PAYMENT(11,"部分支付"),

    REFUND_OPERATE_CONFIRM(1,"confirm");

    private final Integer code;
    private final String message;

    OrderViewStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }


    //通过code取枚举
    public static OrderViewStatusEnum getApplyStatusByCode(Integer code) {
        for (OrderViewStatusEnum taskTypeEnum : OrderViewStatusEnum.values()) {
            if (taskTypeEnum.getCode() == (code)) {
                return taskTypeEnum;
            }
        }
        throw new EnumConstantNotPresentException(OrderViewStatusEnum.class, String.valueOf(code));
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
