package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * saas标签修改请求参数
 */
@Data
public class SaasTabUpdateRequest {

    @NotNull(message = "saas标签ID不能为空")
    @Schema(description = "saas标签ID", example = "1")
    private Long id;

    @NotNull(message = "是否SAAS产品不能为空")
    @Schema(description = "是否SAAS产品：0-非SAAS产品，1-SAAS产品", example = "1")
    private Integer isSaas;
} 