package com.xmd.achievement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xmd.achievement.dao.entity.NewOldCustomerRecordModel;
import com.xmd.achievement.dao.repository.INewOldCustomerRecordRepository;
import com.xmd.achievement.service.INewOldCustomerRecordService;
import com.xmd.achievement.util.enums.CustomerTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date: 2024/12/27 11:00
 */
@Service
public class NewOldCustomerRecordServiceImpl implements INewOldCustomerRecordService {
    @Resource
    private INewOldCustomerRecordRepository newOldCustomerRecordRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(NewOldCustomerRecordModel record) {
//        String newCustomerId = record.getNewCustomerId();
//        if (StringUtils.isNotBlank(newCustomerId)) {
//            long count = newOldCustomerRecordRepository.count(new LambdaQueryWrapper<NewOldCustomerRecordModel>()
//                    .eq(NewOldCustomerRecordModel::getNewCustomerId, newCustomerId)
//                    .eq(NewOldCustomerRecordModel::getBusinessMonthId, record.getBusinessMonthId())
//                    .eq(NewOldCustomerRecordModel::getEmployeeId, record.getEmployeeId()));
//            if (count > 0) {
//                return;
//            }
//        }
//        String oldCustomerId = record.getOldCustomerId();
//        if (StringUtils.isNotBlank(oldCustomerId)) {
//            long count = newOldCustomerRecordRepository.count(new LambdaQueryWrapper<NewOldCustomerRecordModel>()
//                    .eq(NewOldCustomerRecordModel::getOldCustomerId, oldCustomerId)
//                    .eq(NewOldCustomerRecordModel::getBusinessMonthId, record.getBusinessMonthId())
//                    .eq(NewOldCustomerRecordModel::getEmployeeId, record.getEmployeeId()));
//            if (count > 0) {
//                return;
//            }
//        }
        newOldCustomerRecordRepository.save(record);
    }

    @Override
    public List<NewOldCustomerRecordModel> getListByMonthIdAndEmployeeId(Long businessMonthId, String employeeId) {
        return null == businessMonthId || StringUtils.isEmpty(employeeId) ? Collections.emptyList()
                : newOldCustomerRecordRepository.list(new LambdaQueryWrapper<NewOldCustomerRecordModel>().eq(NewOldCustomerRecordModel::getBusinessMonthId, businessMonthId).eq(NewOldCustomerRecordModel::getEmployeeId, employeeId));
    }

    @Override
    public void updateCustomer(NewOldCustomerRecordModel record, Integer customerType, Integer currentCustomerType) {
        // 查询出第一条记录更新
        LambdaQueryWrapper<NewOldCustomerRecordModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NewOldCustomerRecordModel::getBusinessMonthId, record.getBusinessMonthId());
        queryWrapper.eq(NewOldCustomerRecordModel::getEmployeeId, record.getEmployeeId());
        if (CustomerTypeEnum.NEW.getCustomerType().equals(currentCustomerType)) {
            queryWrapper.eq(NewOldCustomerRecordModel::getNewCustomerId, record.getNewCustomerId());
        } else if (CustomerTypeEnum.OLD.getCustomerType().equals(currentCustomerType)) {
            queryWrapper.eq(NewOldCustomerRecordModel::getOldCustomerId, record.getOldCustomerId());
        }
        NewOldCustomerRecordModel customerRecordModel = newOldCustomerRecordRepository.list(queryWrapper).stream().findFirst().orElseThrow(() -> new RuntimeException("新老客户记录列表为空，商务id：" + record.getEmployeeId() + "商务月id：" + record.getBusinessMonthId() + "客户类型：" + currentCustomerType));
        LambdaUpdateWrapper<NewOldCustomerRecordModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(NewOldCustomerRecordModel::getId, customerRecordModel.getId());
        if (CustomerTypeEnum.OLD.getCustomerType().equals(customerType)) {
            updateWrapper.set(NewOldCustomerRecordModel::getOldCustomerId, record.getOldCustomerId());
            updateWrapper.set(NewOldCustomerRecordModel::getNewCustomerId, "");
        } else if (CustomerTypeEnum.NEW.getCustomerType().equals(customerType)){
            updateWrapper.set(NewOldCustomerRecordModel::getNewCustomerId, record.getNewCustomerId());
            updateWrapper.set(NewOldCustomerRecordModel::getOldCustomerId, "");
        }
        newOldCustomerRecordRepository.update(updateWrapper);
    }

    @Override
    public void deleteCustomer(Long businessMonthId, String employeeId, String customerId, Integer currentCustomerType) {
        // 查询出第一条记录删除
        LambdaQueryWrapper<NewOldCustomerRecordModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NewOldCustomerRecordModel::getBusinessMonthId, businessMonthId);
        queryWrapper.eq(NewOldCustomerRecordModel::getEmployeeId, employeeId);
        if (CustomerTypeEnum.NEW.getCustomerType().equals(currentCustomerType)) {
            queryWrapper.eq(NewOldCustomerRecordModel::getNewCustomerId, customerId);
        } else if (CustomerTypeEnum.OLD.getCustomerType().equals(currentCustomerType)) {
            queryWrapper.eq(NewOldCustomerRecordModel::getOldCustomerId, customerId);
        }
        List<NewOldCustomerRecordModel> list = newOldCustomerRecordRepository.list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        NewOldCustomerRecordModel customerRecordModel = list.stream().findFirst().orElseThrow(() -> new RuntimeException("新老客户记录列表为空，商务id：" + customerId + "商务月id：" + businessMonthId + "客户类型：" + currentCustomerType));
        newOldCustomerRecordRepository.removeById(customerRecordModel.getId());
    }

    @Override
    public void insertCustomer(NewOldCustomerRecordModel record) {
        newOldCustomerRecordRepository.save(record);
    }
}
