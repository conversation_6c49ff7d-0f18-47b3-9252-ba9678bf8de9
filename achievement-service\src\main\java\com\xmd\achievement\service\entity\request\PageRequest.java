package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分页参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/11/11:45
 * @since 1.0
 */
@Data
public class PageRequest implements Serializable {
    @Schema(description = "第几页", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "第几页不能为空")
    private int pageIndex;

    @Schema(description = "每页多少条", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @NotNull(message = "每页多少条不能为空")
    private int pageSize;
}