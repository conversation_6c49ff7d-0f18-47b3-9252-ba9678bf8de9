package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * saas标签表实体类
 */
@Data
@TableName("saas_tab")
public class SaasTabModel extends BaseModel {

     /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * saas标签id
     */
    @TableField("saas_tab_id")
    private Long saasTabId;

    /**
     * 关联id
     */
    @TableField("association_id")
    private Long associationId;

    /**
     * 关联名称
     */
    @TableField("association_name")
    private String associationName;

    /**
     * 是否SAAS产品：0-非SAAS产品，1-SAAS产品
     */
    @TableField("is_saas")
    private Integer isSaas;

    /**
     * 0=中企 ,1=跨境
     */
    @TableField("saas_type")
    private Integer saasType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
} 