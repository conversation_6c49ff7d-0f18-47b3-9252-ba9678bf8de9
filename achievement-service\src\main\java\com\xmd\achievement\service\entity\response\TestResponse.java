package com.xmd.achievement.service.entity.response;

import lombok.Data;

/**
 * 访客信息响应
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/6/27 11:26 上午
 */
@Data
public class TestResponse {
    /**
     * 企业ID
     */
    private String tid;

    /**
     * 访客ID
     */
    private String vid;

    /**
     * 访客code
     */
    private String code;

    /**
     * 访客昵称
     */
    private String nickName;

    /**
     * 名称
     */
    private String name;

    /**
     * 头像
     */
    private String headIcon;

    /**
     * 访客的ip地址
     */
    private String ip;

    /**
     * 渠道 1.IM 2.facebook 3.ins 4.whatsapp
     */
    private Integer channelType;
}
