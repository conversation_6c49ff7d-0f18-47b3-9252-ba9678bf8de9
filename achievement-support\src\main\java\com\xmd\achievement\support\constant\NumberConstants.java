package com.xmd.achievement.support.constant;

import java.math.BigDecimal;

/**
 * 数字常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/6/13
 */
public class NumberConstants {
    /**
     * jwt有效期 1小时
     */
    public static final Long JWT_TTL = 3600000L;


    /**
     * int 值 -1
     */
    public static final Integer INTEGER_VALUE_NEGATIVE_1 = -1;
    /**
     * int 值 0
     */
    public static final Integer INTEGER_VALUE_0 = 0;

    /**
     * int 值 1
     */
    public static final Integer INTEGER_VALUE_1 = 1;
    /**
     * int 值 2
     */
    public static final Integer INTEGER_VALUE_2 = 2;

    /**
     * int 值 3
     */
    public static final Integer INTEGER_VALUE_3 = 3;
    /**
     * int 值 5
     */
    public static final Integer INTEGER_VALUE_5 = 5;
    public static final Integer INTEGER_VALUE_6 = 6;
    public static final Integer INTEGER_VALUE_7 = 7;
    public static final Integer INTEGER_VALUE_8 = 8;
    public static final Integer INTEGER_VALUE_10 = 10;
    public static final Integer INTEGER_VALUE_50 = 50;
    public static final Integer INTEGER_VALUE_90 = 90;
    public static final Integer INTEGER_VALUE_88 = 88;
    public static final Integer INTEGER_VALUE_5001 = 5001;
    public static final Integer INTEGER_VALUE_5002 = 5002;

    public static final Integer INTEGER_VALUE_4 = 4;

    public static final Long LONG_VALUE_0 = 0L;

    public static final Long LONG_VALUE_1 = 1L;

    /**
     * int 值 11
     */
    public static final Long INTEGER_VALUE_11 = 11L;

    /**
     * int 值 15
     */
    public static final Long INTEGER_VALUE_15 = 15L;

    /**
     * int 值 3
     */
    public static final Long INTEGER_VALUE_30 = 30L;

    /**
     * int 值 1000
     */
    public static final Long INTEGER_VALUE_1000 = 1000L;
    /**
     * int 值 1000
     */
    public static final Long INTEGER_VALUE_50000 = 50000L;
    /**
     * int 值 100
     */
    public static final Long INTEGER_VALUE_100 = 100L;
    /**
     * 9分钟超时
     */
    public static final Long ORDER_PAYMENT_TIME_OUT = 9 * 60L + 30;

    public static final Double SCORE_INCREMENT = 1.0D;
    public static final BigDecimal OIL_PAY_COMPARE = new BigDecimal("0.75");

    /**
     * 最小支付金额
     */
    public static final Double MINIMUM_PAYMENT_BALANCE = 0.01D;

    public static final String MINIMUM_PAYMENT_BALANCE_STR = "0.01";
    public static final String FORMATTER_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";


    /**
     * 空字符串
     */
    public static final String EMPTY_STRING = "";
    /**
     * 空字符串
     */
    public static final String POS_ID_DEFAULT = "POS99";
    /**
     * 动态券字符
     */
    public static final String DYNAMIC_COUPON_SYMBOL = "动态";
    /**
     * 生成验证码的基础数据
     */
    public static final String CHAR_CODE_STR = "0123456789";
    /**
     * null 值
     */
    public static final String NULL_VALUE = null;
    /**
     * utf-8编码
     */
    public static final String CHAR_SET_UTF_8 = "UTF-8";
    /**
     * gb编码
     */
    public static final String CHAR_SET_GBK_2312 = "gb2312";
    /**
     * gb编码
     */
    public static final String CHAR_SET_GBK = "GBK";
    /**
     * 折扣前缀
     */
    public static final String REBATE_FLAG = "REBATE";
    /**
     * 折扣前缀
     */
    public static final String VOUCHER_PREFIX = "VOUCHER_PREFIX";
    /**
     * ==
     */
    public static final String EQUAL_SYMBOL = "=";
    /**
     * ;
     */
    public static final String SEMICOLON_SYMBOL = ";";
    /**
     * ,
     */
    public static final String COMMA_SYMBOL = ",";

    public static final String COLON_SYMBOL = ":";
    /**
     * ==
     */
    public static final String MINUS_SYMBOL = "-";
    public static final String POUND_SYMBOL = "#";
    /**
     * 斜线
     */
    public static final String OBLIQUE_LINE = "/";
    /**
     * &
     */
    public static final String AND_SYMBOL = "&";
    /**
     * 中文顿号
     */
    public static final String SUDDEN_SYMBOL = "、";
    /**
     * &
     */
    public static final String FULL_STOP_SYMBOL = ".";
    /**
     * ?
     */
    public static final String W_SYMBOL = "?";
    /**
     * +
     */
    public static final String P_SYMBOL = "+";


    /**
     * 常见问题 默认分页
     */
    public static final Integer PAGE_SIZE = 20;

    public static final Double DOUBLE_VALUE_0 = 0D;
    public static final Integer INTEGER_VALUE_180 = 180;
    public static final Integer INTEGER_VALUE_3000 = 3000;
    public static final Integer INTEGER_VALUE_12 = 12;
    public static final Double DOUBLE_VALUE_0_5 = 0.5D;

    public static final String STR_ZERO = "0";
}
