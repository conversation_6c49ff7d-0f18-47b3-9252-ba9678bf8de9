package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单简单商品规格信息
 * <AUTHOR>
 * @date 2024年11月11日 下午1:43
 */
@Data

public class OrderSimpleProductSpecResponse implements Serializable {

    private static final long serialVersionUID = -4768007783129872420L;
    /**
     * 订单商品规格id
     */

    private Long orderProductSpecId;

    /**
     * 订单商品规格编号
     */

    private String orderProductSpecCode;

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 订单商品明细id
     */

    private String orderProductId;

    /**
     * 商品规格id
     */

    private Long productSpecId;

    /**
     * 商品id
     */

    private Long productId;

    /**
     * 规格名称
     */

    private String specName;

    /**
     * 规格总价
     */

    private BigDecimal specTotalAmount;

    /**
     * 折扣优惠金额
     */

    private BigDecimal discountAmount;

    /**
     * 应付金额
     */

    private BigDecimal payableAmount;

    /**
     * 订单规格类型：1=普通，2=赠品
     */

    private Integer orderSpecType;

    /**
     * 实付金额
     */

    private BigDecimal paidAmount;


    /**
     * 删除标记: 0-未删除|1-删除
     */

    private Integer deleteFlag;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 创建人id
     */

    private String createUserId;

    /**
     * 创建人名称
     */

    private String createUserName;

    /**
     * 更新时间
     */

    private Date updateTime;

    /**
     * 更新人id
     */

    private String updateUserId;

    /**
     * 更新人名称
     */

    private String updateUserName;
}
