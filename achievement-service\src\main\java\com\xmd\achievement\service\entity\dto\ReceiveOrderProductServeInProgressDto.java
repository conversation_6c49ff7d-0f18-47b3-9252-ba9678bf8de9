package com.xmd.achievement.service.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/26/17:28
 * @since 1.0
 */
@Data
public class ReceiveOrderProductServeInProgressDto {
    public int acceptSource;

    public long orderId;

    public String orderProductId;

    public long productId;

    public long scheduleId;

    public List<ReceiveOrderProductServeItemDto> serveItemList;

    public String serveNo;

}
