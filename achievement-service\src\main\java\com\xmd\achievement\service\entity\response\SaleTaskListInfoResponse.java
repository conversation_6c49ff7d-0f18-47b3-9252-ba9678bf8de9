package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 销售任务查看
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
public class SaleTaskListInfoResponse implements
        Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "机构ID")
    private Long orgId;

    @Schema(description = "机构名称")
    private String orgName;

    @Schema(description = "商务月")
    private String businessMonth;

    @Schema(description = "基本任务（元）")
    private BigDecimal basicTask;

    @Schema(description = "任务状态")
    private Integer taskStatus;

    @Schema(description = "最新审核时间")
    private Date latestAuditTime;

    @Schema(description = "最新审核意见")
    private Integer latestAuditResult;

    @Schema(description = "最新审核备注")
    private String latestAuditRemark;
}