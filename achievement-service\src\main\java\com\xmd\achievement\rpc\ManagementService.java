package com.xmd.achievement.rpc;


import com.xmd.achievement.rpc.entity.dto.OrgInfoDTO;
import com.xmd.achievement.rpc.entity.dto.UserLoginInfoDTO;
import com.xmd.achievement.web.entity.response.WebResult;

import java.util.List;
import java.util.Map;

public interface ManagementService {
    WebResult<UserLoginInfoDTO> getLoginUserInfo(Map<String, String> map);

    /**
     * 获取中企跨境所有分公司
     *
     * @return
     */
    List<OrgInfoDTO> getAllBranchOffice();
}
