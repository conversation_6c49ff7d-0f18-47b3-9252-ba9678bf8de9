package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.entity.FreezeMonthErrorLogModel;
import com.xmd.achievement.dao.mapper.FreezeMonthErrorLogMapper;
import com.xmd.achievement.dao.repository.IFreezeMonthErrorLogRepository;
import org.springframework.stereotype.Service;

/**
 * 商务月冻结异常日志表 服务实现类
 */
@Service
public class FreezeMonthErrorLogRepositoryImpl extends ServiceImpl<FreezeMonthErrorLogMapper, FreezeMonthErrorLogModel> implements IFreezeMonthErrorLogRepository {
}
