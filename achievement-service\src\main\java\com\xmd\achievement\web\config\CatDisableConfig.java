package com.xmd.achievement.web.config;

import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 关闭CAT
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Configuration
public class CatDisableConfig {

    @Value("${spring.profiles.active}")
    private String active;

    @PostConstruct
    private void defaultConfig() {
        if ("dev".equals(active) || "pre".equals(active)) {
            Cat.disable();
            log.error("{}环境,Cat已禁用!!!", active);
        }
    }
}
