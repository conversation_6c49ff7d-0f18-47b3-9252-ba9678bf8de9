package com.xmd.achievement.async.job.handler;

import com.alibaba.fastjson.JSON;
import com.xmd.achievement.async.constant.ServiceConstant;
import com.xmd.achievement.async.job.entity.JobParam;
import com.xmd.achievement.service.SyncBusinessAchievementStatisticsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 商务业绩统计job
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/4/14 9:40 上午
 */
@Slf4j
@Component
public class SyncBusinessAchievementStatisticsJob {

    @Resource
    private SyncBusinessAchievementStatisticsService syncBusinessAchievementStatisticsService;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SYNC_BUSINESS_ACHIEVEMENT_STATISTICS_JOB)
    public ReturnT<String> syncBusinessAchievementStatistics(String param) {
        XxlJobLogger.log("syncBusinessAchievementStatistics param :{}",param);
        try {
            log.info("SyncBusinessAchievementStatisticsJob开始 param:{}",param);
            JobParam jobParam = JSON.parseObject(param, JobParam.class);
            String employeeId = null;
            String businessMonth = null;
            Boolean syncAll = false;
            if(null != jobParam){
                employeeId = jobParam.getEmployeeId();
                businessMonth = jobParam.getBusinessMonth();
                syncAll = jobParam.getSyncAll();
            }
            XxlJobLogger.log("syncBusinessAchievementStatistics employeeId :{}，businessMonth：{} ,syncAll :{}",employeeId,businessMonth,syncAll);
            syncBusinessAchievementStatisticsService.syncBusinessAchievementStatistics(employeeId,businessMonth,syncAll);
        } catch (Exception e) {
            log.error("SyncBusinessAchievementStatisticsJob任务失败，,失败原因：" , e);
            XxlJobLogger.log("SyncBusinessAchievementStatisticsJob error :",e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


}
