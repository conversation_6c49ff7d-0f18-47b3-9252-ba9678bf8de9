<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.BusinessMonthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.BusinessMonthModel">
        <id column="id" property="id" />
        <result column="month_id" property="monthId" />
        <result column="month" property="month" />
        <result column="start_date" property="startDate" />
        <result column="mid_date" property="midDate" />
        <result column="end_date" property="endDate" />
        <result column="is_freeze" property="isFreeze" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, month_id, `month`, start_date, mid_date, end_date, is_freeze, delete_flag, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

</mapper>
