package com.xmd.achievement.web.util;


import cn.hutool.core.collection.CollUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.enums.CellDataTypeEnum;
import cn.idev.excel.event.AnalysisEventListener;
import cn.idev.excel.metadata.Head;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;

/**
 * @Author: wangpeng
 * @Date: 2024/3/23 14:34
 * @Description: excel工具类
 */
public class EasyExcelUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(EasyExcelUtil.class);
    public static HashMap<String, String> hashMap = new HashMap<>();

    public static <T> List<T> read(String filePath, final Class<?> clazz) {
        File f = new File(filePath);
        try (FileInputStream fis = new FileInputStream(f)) {
            return read(fis, clazz, UUID.randomUUID().toString());
        } catch (FileNotFoundException e) {
            LOGGER.error("文件{}不存在", filePath, e);
        } catch (IOException e) {
            LOGGER.error("文件读取出错", e);
        }
        return null;
    }

    public static <T> List<T> read(InputStream inputStream, final Class<?> clazz, String uuid) {
        if (inputStream == null) {
            LOGGER.error("文件流为null");
            return null;
        }
        // 有个很重要的点 DataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去

        DataListener<T> listener = new DataListener<>(clazz, uuid);
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        EasyExcel.read(inputStream, clazz, listener).sheet().doRead();
        return listener.getRows();
    }

    public static void write(String outFile, List<?> list) {
        Class<?> clazz = list.get(0).getClass();
        EasyExcel.write(outFile, clazz).sheet().doWrite(list);
    }

    public static void write(String outFile, List<?> list, String sheetName) {
        Class<?> clazz = list.get(0).getClass();
        EasyExcel.write(outFile, clazz).sheet(sheetName).doWrite(list);
    }

    public static void write(OutputStream outputStream, List<?> list, String sheetName) {
        Class<?> clazz = list.get(0).getClass();
        // sheetName为sheet的名字，默认写第一个sheet
        EasyExcel.write(outputStream, clazz).sheet(sheetName).doWrite(list);
    }

    /**
     * 文件下载（失败了会返回一个有部分数据的Excel），用于直接把excel返回到浏览器下载
     */
    public static void download(HttpServletResponse response, List<?> list, String sheetName, Boolean expose) throws IOException {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Class<?> clazz = list.get(0).getClass();
//        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(sheetName, "UTF-8").replaceAll("\\+", "%20");
        if (expose) {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Expose-Headers", "filename,code,msg,flag");
            response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
            response.setHeader("filename", fileName + ".xlsx");
            response.setHeader("code", "200");
            response.setHeader("msg", "成功");
            response.setHeader("flag", "1");
        }
        response.setHeader("Content-Disposition",  "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
        EasyExcel.write(response.getOutputStream(), clazz).head(clazz).sheet("Sheet1").registerWriteHandler(new AutoHeadColumnWidthStyleStrategy()).doWrite(list);
    }

    public static void dynamicExcludeExport(HttpServletResponse response, List<?> list, String sheetName, Boolean expose, Set<String> excludeColumns) throws IOException {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Class<?> clazz = list.get(0).getClass();
//        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(sheetName, "UTF-8").replaceAll("\\+", "%20");
        if (expose) {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Expose-Headers", "filename,code,msg,flag");
            response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
            response.setHeader("filename", fileName + ".xlsx");
            response.setHeader("code", "200");
            response.setHeader("msg", "成功");
            response.setHeader("flag", "1");
        }
        response.setHeader("Content-Disposition",  "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
        if (CollUtil.isEmpty(excludeColumns)) {
            EasyExcel.write(response.getOutputStream(), clazz).sheet("Sheet1").registerWriteHandler(new AutoHeadColumnWidthStyleStrategy()).doWrite(list);
        } else {
            EasyExcel.write(response.getOutputStream(), clazz).excludeColumnFieldNames(excludeColumns).sheet("Sheet1").registerWriteHandler(new AutoHeadColumnWidthStyleStrategy()).doWrite(list);
        }
    }
}

class DataListener<T> extends AnalysisEventListener<T> {
    EasyExcelValidator easyExcelValidator = new EasyExcelValidator();
    private static final Logger LOGGER = LoggerFactory.getLogger(DataListener.class);

    private final List<T> rows = new ArrayList<>();

    Class<?> dataListenerClazz;
    String dataListenerUuid;

    public DataListener(Class<?> clazz, String uuid) {
        dataListenerClazz = clazz;
        dataListenerUuid = uuid;
    }


    @Override
    public void invoke(T t, AnalysisContext analysisContext) {
        rows.add(t);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        LOGGER.info("解析完成！读取{}行", rows.size());
    }

    public List<T> getRows() {
        return rows;
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        Boolean isNull = easyExcelValidator.getIsNull();
        return Boolean.TRUE.equals(isNull);
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        List excelTitle = easyExcelValidator.getExcelTitle(headMap, context);
        EasyExcelValidator.validatorSimpleExcelHeads(dataListenerClazz.getDeclaredFields(), excelTitle, 2, dataListenerUuid);
    }

}


class AutoHeadColumnWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {

    private static final int MAX_COLUMN_WIDTH = 255;
    private Map<Integer, Map<Integer, Integer>> CACHE = new HashMap(8);
    // 保底宽度
    private static final int COLUMN_WIDTH = 20;

    public AutoHeadColumnWidthStyleStrategy() {
    }

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell,
                                  Head head, Integer relativeRowIndex, Boolean isHead) {
        Boolean needSetWidth = isHead || !CollectionUtils.isEmpty(cellDataList);
        if (needSetWidth) {
            Map<Integer, Integer> maxColumnWidthMap = CACHE.computeIfAbsent(writeSheetHolder.getSheetNo(), k -> new HashMap<>());

            Integer columnWidth = this.dataLength(cellDataList, cell, isHead);
            if (columnWidth >= 0) {
                if (columnWidth > MAX_COLUMN_WIDTH) {
                    columnWidth = MAX_COLUMN_WIDTH;
                }
                Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
                if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
                    maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
                    writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
                } else {
                    // 设置表头宽度
                    writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), maxColumnWidth * 256);
                }
            }
        }
    }

    private Integer dataLength(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
        if (isHead) {
            return cell.getStringCellValue().getBytes().length;
        }
        WriteCellData<?> cellData = cellDataList.get(0);
        CellDataTypeEnum type = cellData.getType();
        if (type == null) {
            return -1;
        }
        switch (type) {
            case STRING:
                return cellData.getStringValue().getBytes().length;
            case BOOLEAN:
                return cellData.getBooleanValue().toString().getBytes().length;
            case NUMBER:
                return cellData.getNumberValue().toString().getBytes().length;
            default:
                return -1;
        }
    }

}

@Slf4j
class EasyExcelValidator {

    // 校验规则信息
    private static Boolean isNull;

    // 返回的校验规则信息
    public Boolean getIsNull() {
        return isNull;
    }

    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     */
    public EasyExcelValidator() {
        // 有个很重要的点 DemoDataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
        isNull = Boolean.TRUE;
    }

    List keyList = new ArrayList<>();

    /**
     * 获取表头(一行）
     *
     * @param headMap
     * @param context
     * @return
     */
    public List getExcelTitle(Map<Integer, String> headMap, AnalysisContext context) {
        //合并表头
        //遍历获取第一行存入keyList
        Set<Integer> integerSet = headMap.keySet();
        for (int i1 = 0; i1 < integerSet.size(); i1++) {
            keyList.add(headMap.get(i1));
        }
        return keyList;
    }


    /**
     * 验证单表单表头
     *
     * @param fields           实体的字段列表
     * @param titleList        表头字段列表
     * @param num              需要验证的字段个数
     * @param dataListenerUuid
     */
    public static void validatorSimpleExcelHeads(Field[] fields, List titleList, Integer num, String dataListenerUuid) {
        //判断需要验证的表头个数是否等于需要验证的字段个数
        if (num == titleList.size()) {
            // 遍历字段进行判断
            for (Field field : fields) {
                // 获取当前字段上的ExcelProperty注解信息
                ExcelProperty fieldAnnotation = field.getAnnotation(ExcelProperty.class);
                // 判断当前字段上是否存在ExcelProperty注解
                if (fieldAnnotation != null) {
                    // 存在ExcelProperty注解则根据注解的index索引到表头中获取对应的表头名
                    Object tile = titleList.get(fieldAnnotation.index());
                    // 判断表头是否为空或是否和当前字段设置的表头名不相同
                    if (tile == null || StringUtils.isBlank(tile.toString()) || !tile.toString().equals(fieldAnnotation.value()[0])) {
                        EasyExcelUtil.hashMap.put(dataListenerUuid, String.format("文件表头：%s不正确,请按照模板上传！", tile));
                        isNull = false;
                    }
                }
            }
        } else {
            isNull = false;
        }
    }

}
