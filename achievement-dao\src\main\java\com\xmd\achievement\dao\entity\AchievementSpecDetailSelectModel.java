package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 业绩规格明细表(查询)
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
@TableName("achievement_spec_detail_select")
public class AchievementSpecDetailSelectModel implements

        Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 业绩规格id
     */
    @TableField("achievement_spec_id")
    private Long achievementSpecId;
    /**
     * 业绩规格分类id
     */
    @TableField("achievement_category_id")
    private Long achievementCategoryId;
    /**
     * 规格ID
     */
    @TableField("spec_id")
    private Long specId;
    /**
     * 规格名称
     */
    @TableField("spec_name")
    private String specName;
    /**
     * 规格项类型 1=数量，2=时长，3=自定义
     */
    @TableField("item_type")
    private Integer itemType;
    /**
     * 订单规格类型：1=普通，2=赠品
     */
    @TableField("order_spec_type")
    private Integer orderSpecType;
    /**
     * 计费单位
     */
    @TableField("item_unit")
    private String itemUnit;
    /**
     * 计费个数
     */
    @TableField("item_num")
    private Integer itemNum;
    /**
     * 服务项编码
     */
    @TableField("serve_item_no")
    private String serveItemNo;
    /**
     * 计费价格(新开价格)
     */
    @TableField("billing_price")
    private BigDecimal billingPrice;
    /**
     * 续费价格
     */
    @TableField("renewal_price")
    private BigDecimal renewalPrice;
    /**
     * 标准价
     */
    @TableField("standard_price")
    private BigDecimal standardPrice;
    /**
     * 应付金额
     */
    @TableField("payable_amount")
    private BigDecimal payableAmount;
    /**
     * 实付金额
     */
    @TableField("paid_amount")
    private BigDecimal paidAmount;
    /**
     * 首年报价
     */
    @TableField("first_year_quote")
    private BigDecimal firstYearQuote;
    /**
     * 首年到账金额
     */
    @TableField("first_year_income")
    private BigDecimal firstYearIncome;
    /**
     * 续费报价
     */
    @TableField("renewal_quote")
    private BigDecimal renewalQuote;
    /**
     * 续费到账金额
     */
    @TableField("renewal_income")
    private BigDecimal renewalIncome;
    /**
     * 净现金
     */
    @TableField("net_cash")
    private BigDecimal netCash;
    /**
     * 商代提成业绩
     */
    @TableField("agent_comm_achv")
    private BigDecimal agentCommAchv;
    /**
     * 商代实发提成业绩
     */
    @TableField("agent_act_comm_achv")
    private BigDecimal agentActCommAchv;
    /**
     * 商代缓发提成业绩
     */
    @TableField("agent_def_comm_achv")
    private BigDecimal agentDefCommAchv;
    /**
     * 部门提成业绩
     */
    @TableField("dept_comm_achv")
    private BigDecimal deptCommAchv;
    /**
     * 事业部提成业绩
     */
    @TableField("bu_comm_achv")
    private BigDecimal buCommAchv;
    /**
     * 分公司提成业绩
     */
    @TableField("branch_comm_achv")
    private BigDecimal branchCommAchv;
    /**
     * 删除标记: 0: 未删除, 1: 删除
     */
    @TableField("delete_flag")
    private Integer deleteFlag;
    /**
     * 业绩生成时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;
    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 更新人id
     */
    @TableField("update_user_id")
    private String updateUserId;
    /**
     * 更新人名称
     */
    @TableField("update_user_name")
    private String updateUserName;
    /**
     * 业绩计收节点 1=支付完成，2=生产完成
     */
    @TableField("revenue_node")
    private Integer revenueNode;
    @TableField("order_id")
    private Long orderId;
    @TableField("product_id")
    private Long productId;
    @TableField("product_category_id")
    private Long productCategoryId;
    /**
     * 业绩状态 1=有效，2=已完成
     */
    @TableField("`status`")
    private Integer status;
    /**
     * 主分单人 1=主，2=辅
     */
    @TableField("main_split_person")
    private Integer mainSplitPerson;
    /**
     * 数据变更类型 1 - 正常 2 - 手动新增 3 - 手动修改
     */
    @TableField("data_change_type")
    private Integer dataChangeType;
    /**
     * 分期期数
     */
    @TableField("installment_num")
    private Integer installmentNum;
    /**
     * 订单明细id
     */
    @TableField("order_product_id")
    private String orderProductId;

    @TableField("is_saas")
    private Integer isSaas;
}