package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 商品业绩查询
 *
 * <AUTHOR>
 * @date: 2024/12/19 15:15
 * @version: 1.0.0
 * @return {@link }
 */
@Data
@Schema(description = "商品业绩列表查询参数")
public class DeleteAchievementProductRequest extends PageRequest {

    /**
     * 业绩id集合
     */
    @Schema(description = "业绩id集合")
    private List<Long> achivevementIdList;



}
