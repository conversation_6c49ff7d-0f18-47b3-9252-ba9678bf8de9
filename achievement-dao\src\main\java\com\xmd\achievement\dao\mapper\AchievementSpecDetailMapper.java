package com.xmd.achievement.dao.mapper;

import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 业绩规格明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface AchievementSpecDetailMapper extends BaseMapper<AchievementSpecDetailModel> {

    /**
     * 根据商品业绩Id查询对应所有规格业绩流水
     *
     * @param achievementId 商品业绩Id
     * @return {@link List }<{@link AchievementSpecDetailModel }>
     * <AUTHOR>
     * @since 1.0
     */
    List<AchievementSpecDetailModel> listByAchievementId(Long achievementId);
}
