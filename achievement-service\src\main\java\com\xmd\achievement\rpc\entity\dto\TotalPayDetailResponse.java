package com.xmd.achievement.rpc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 总支付详情响应
 * 
 * <AUTHOR> Generated
 * @date 2025-01-30
 */
@Data
@Schema(description = "总支付详情响应")
public class TotalPayDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 订单支付详情id
     */
    @Schema(description = "订单支付详情id")
    private String orderPayDetailId;

    /**
     * 支付类型
     */
    @Schema(description = "支付类型")
    private Integer payType;

    /**
     * 支付类型来源
     */
    @Schema(description = "支付类型来源")
    private Integer payTypeSource;

    /**
     * 交易id
     */
    @Schema(description = "交易id")
    private Long transactionId;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    /**
     * 支付用户id
     */
    @Schema(description = "支付用户id")
    private String payUserId;

    /**
     * 支付用户名称
     */
    @Schema(description = "支付用户名称")
    private String payUserName;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}
