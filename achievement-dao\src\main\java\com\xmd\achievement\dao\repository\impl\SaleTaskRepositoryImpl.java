package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.SaleTaskModel;
import com.xmd.achievement.dao.mapper.SaleTaskMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.ISaleTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 销售任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Service
@Slf4j
public class SaleTaskRepositoryImpl extends ServiceImpl<SaleTaskMapper,SaleTaskModel> implements ISaleTaskRepository {

@Resource
private SaleTaskMapper saleTaskMapper;

    @Override
    public SaleTaskModel selectSaleTaskByOrgId(Long orgId , String businessMonth) {
        LambdaQueryWrapper<SaleTaskModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaleTaskModel::getDeleteFlag, 0);
        queryWrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
        queryWrapper.eq(SaleTaskModel::getOrgId, orgId);
        queryWrapper.in(SaleTaskModel::getTaskStatus, 5, 7);
        return saleTaskMapper.selectOne(queryWrapper);
    }

    @Override
    public List<SaleTaskModel> selectSaleTaskByOrgIds(List<Long> orgIds, String businessMonth) {
        LambdaQueryWrapper<SaleTaskModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaleTaskModel::getDeleteFlag, 0);
        queryWrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
        queryWrapper.in(SaleTaskModel::getOrgId, orgIds);
        return saleTaskMapper.selectList(queryWrapper);
    }

    @Override
    public SaleTaskModel selectByOrgId(Long orgId, String businessMonth) {
        LambdaQueryWrapper<SaleTaskModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaleTaskModel::getDeleteFlag, 0);
        queryWrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
        queryWrapper.eq(SaleTaskModel::getOrgId, orgId);
        return saleTaskMapper.selectOne(queryWrapper);
    }
}