package com.xmd.achievement.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * saas新客表
 */
@Data
@TableName("customer_saas")
public class CustomerSaasModel extends BaseModel {
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 客户id */
    @TableField("customer_id")
    private String customerId;

    /** 客户名称 */
    @TableField("customer_name")
    private String customerName;

    /** 订单id */
    @TableField("order_id")
    private Long orderId;

    /** 订单编号 */
    @TableField("order_no")
    private String orderNo;

    /** 订单明细id */
    @TableField("order_product_id")
    private String orderProductId;

    @TableField("order_product_code")
    private String orderProductCode;

    /** 服务编号 */
    @TableField("serve_no")
    private String serveNo;

    /** 商品id */
    @TableField("product_id")
    private Long productId;

    /** 商品名称 */
    @TableField("product_name")
    private String productName;

    @TableField("third_id")
    private String thirdId;

    /** 商务ID */
    @TableField("business_id")
    private String businessId;

    /** 商务代表 */
    @TableField("business_representative")
    private String businessRepresentative;

    /** 商务月id */
    @TableField("month_id")
    private Long monthId;

    /** 商务月 */
    @TableField("month")
    private String month;

    /** 是否新客: 0: 新客, 1: 老客 */
    @TableField("saas_status")
    private Integer saasStatus;

    /** 当前商品服务是否流失: 0: 未流失, 1: 流失 */
    @TableField("churn_status")
    private Integer churnStatus;

    /** 这笔订单商品流失日期 */
    @TableField(value = "churn_date")
    private Date churnDate;

    /** 订单来源: 1-跨境 2-中小 */
    @TableField("order_source")
    private Integer orderSource;

    /** 状态 0=有效，1=失效，2=退款 */
    @TableField("status")
    private Integer status;

    /** 订单创建时间 */
    @TableField("order_payment_time")
    private Date orderPaymentTime;
}
