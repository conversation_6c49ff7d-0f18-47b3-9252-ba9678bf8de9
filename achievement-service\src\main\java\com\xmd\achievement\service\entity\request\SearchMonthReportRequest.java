package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "批量机构和月份冻结状态请求")
@Data
public class SearchMonthReportRequest {
    @NotEmpty(message = "机构ID列表不能为空")
    @Schema(description = "机构ID列表")
    private List<Long> orgIds;

    @NotEmpty(message = "月份列表不能为空")
    @Schema(description = "月份字符串列表")
    private List<String> months;
}
