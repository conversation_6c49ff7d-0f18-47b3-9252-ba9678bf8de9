package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.SpecCombinationDetailModel;
import com.xmd.achievement.dao.entity.SpecCombinationModel;
import com.xmd.achievement.dao.repository.ISpecCombinationDetailRepository;
import com.xmd.achievement.dao.repository.ISpecCombinationRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.ProductListForAchievementResponse;
import com.xmd.achievement.service.ISpecCombinationService;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.PageQueryspecCombinationListResponse;
import com.xmd.achievement.service.entity.response.QuerySpecCombinationDetailResponse;
import com.xmd.achievement.service.entity.response.SpecCombinationDetaiResponse;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.CombinationStatusEnum;
import com.xmd.achievement.support.constant.enums.YesOrNotEnum;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date: 2024/12/19 16:52
 * @version: 1.0.0
 * @return {@link }
 */
@Slf4j
@Service
public class SpecCombinationServiceImpl implements ISpecCombinationService {
    @Resource
    private ISpecCombinationDetailRepository specCombinationDetailRepository;

    @Resource
    private ISpecCombinationRepository specCombinationRepository;

    @Resource
    private InnerService innerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebResult<Boolean> saveSpecCombination(SpecCombinationRequest request) {
        //校验检查名称
        List<SpecCombinationModel> list = specCombinationRepository.list(
                new LambdaQueryWrapper<SpecCombinationModel>()
                        .eq(SpecCombinationModel::getCombinationName, request.getCombinationName())
                        .eq(SpecCombinationModel::getStatus, CombinationStatusEnum.ENABLE.getCode()));
        if (ObjectUtil.isNotEmpty(list)) {
            return WebResult.error(WebCodeMessageEnum.PRODUCT_COMBINATION_NAME_EXIST);
        }

        //检查是否重复配置了规格组合
        String specIds = request.getSpecDetailList().stream()
                .sorted(Comparator.comparing(SpecCombinationDetailRequest::getSpecId))
                .map(SpecCombinationDetailRequest::getSpecId)
                .map(String::valueOf)
                .collect(Collectors.joining("-"));
        List<SpecCombinationModel> combinationModel = specCombinationRepository.list(
                new LambdaQueryWrapper<SpecCombinationModel>()
                        .eq(SpecCombinationModel::getSpecCombinationList, specIds)
                        .eq(SpecCombinationModel::getStatus, CombinationStatusEnum.ENABLE.getCode()));

        if (ObjectUtil.isNotEmpty(combinationModel)) {
            return WebResult.error(WebCodeMessageEnum.PRODUCT_COMBINATION_EXIST);
        }

        //保存组合信息
        SpecCombinationModel specCombinationModel = new SpecCombinationModel();
        specCombinationModel.setCombinationId(IdUtil.getSnowflake().nextId());
        specCombinationModel.setSpecCombinationList(specIds);
        BeanUtil.copyProperties(request, specCombinationModel);
        //todo 暂时设置默认值-1
        specCombinationModel.setAdditionalPolicyCost(BigDecimal.valueOf(NumberConstants.INTEGER_VALUE_NEGATIVE_1));
        specCombinationModel.setNewPolicyCost(BigDecimal.valueOf(NumberConstants.INTEGER_VALUE_NEGATIVE_1));
        specCombinationModel.setRenewalPolicyCost(BigDecimal.valueOf(NumberConstants.INTEGER_VALUE_NEGATIVE_1));
        specCombinationModel.setUpgradePolicyCost(BigDecimal.valueOf(NumberConstants.INTEGER_VALUE_NEGATIVE_1));
        specCombinationRepository.save(specCombinationModel);

        //保存规格详情信息
        List<SpecCombinationDetailModel> modelList = request.getSpecDetailList().stream().map(e -> {
            SpecCombinationDetailModel model = new SpecCombinationDetailModel();
            BeanUtil.copyProperties(e, model);
            model.setCombinationId(specCombinationModel.getCombinationId());
            return model;
        }).collect(Collectors.toList());
        specCombinationDetailRepository.saveBatch(modelList, modelList.size());
        return WebResult.success(true);
    }

    @Override
    public WebResult<PageResponse<PageQueryspecCombinationListResponse>> pageQuerySpecCombinationList(PageQueryspecCombinationListRequest request) {
        Page<SpecCombinationModel> page = new Page<>(request.getPageIndex(), request.getPageSize());
        Page<SpecCombinationModel> result = specCombinationRepository.page(
                page,
                new LambdaQueryWrapper<SpecCombinationModel>()
                        .eq(ObjectUtil.isNotEmpty(request.getCombinationId()), SpecCombinationModel::getCombinationId, request.getCombinationId())
                        .like(ObjectUtil.isNotEmpty(request.getCombinationName()), SpecCombinationModel::getCombinationName, request.getCombinationName())
                        .eq(ObjectUtil.isNotEmpty(request.getStatus()), SpecCombinationModel::getStatus, request.getStatus())
                        .eq(SpecCombinationModel::getDeleteFlag, YesOrNotEnum.NO.getCode())
                        .orderByDesc(SpecCombinationModel::getCreateTime));
        PageResponse<PageQueryspecCombinationListResponse> pageResponse = new PageResponse<>(result.getTotal(), result.getCurrent(), request.getPageSize());
        if (ObjectUtil.isEmpty(result.getRecords())) {
            return WebResult.success(pageResponse);
        }

        //查询商品规格信息
        List<Long> combinationDetailIds = result.getRecords().stream().map(SpecCombinationModel::getCombinationId).collect(Collectors.toList());
        List<SpecCombinationDetailModel> list = specCombinationDetailRepository.list(
                new LambdaQueryWrapper<SpecCombinationDetailModel>()
                        .in(SpecCombinationDetailModel::getCombinationId, combinationDetailIds));

        List<Long> specIds = list.stream().map(SpecCombinationDetailModel::getSpecId).collect(Collectors.toList());
        Map<Long, ProductListForAchievementResponse> specIdMap = new HashMap<>();
        Map<Long, List<SpecCombinationDetailModel>> combinationDetailIdMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(specIds)) {
            List<ProductListForAchievementResponse> specDetails = innerService.getProductListForAchievement(specIds, null);
            specIdMap = specDetails.stream()
                    .collect(Collectors.toMap(
                            ProductListForAchievementResponse::getSpecId,
                            response -> response,
                            (existing, replacement) -> existing));
            combinationDetailIdMap = list.stream().collect(Collectors.groupingBy(SpecCombinationDetailModel::getCombinationId));
        }

        //组装信息
        Map<Long, List<SpecCombinationDetailModel>> finalCombinationDetailIdMap = combinationDetailIdMap;
        Map<Long, ProductListForAchievementResponse> finalSpecIdMap = specIdMap;
        List<PageQueryspecCombinationListResponse> records = result.getRecords().stream().map(e -> {
            PageQueryspecCombinationListResponse response = new PageQueryspecCombinationListResponse();
            BeanUtil.copyProperties(e, response);
            List<SpecCombinationDetaiResponse> specDetailList = new ArrayList<>();
            List<SpecCombinationDetailModel> modelList = finalCombinationDetailIdMap.get(e.getCombinationId());
            for (SpecCombinationDetailModel specCombinationDetailModel : modelList) {
                SpecCombinationDetaiResponse specCombinationDetaiResponse = new SpecCombinationDetaiResponse();
                ProductListForAchievementResponse productListForAchievementResponse = finalSpecIdMap.get(specCombinationDetailModel.getSpecId());
                if(Objects.isNull(productListForAchievementResponse)){
                    log.error("分页查询规格组合列表接口,finalSpecIdMap集合未找到 specId:{},finalSpecIdMap:{} ", specCombinationDetailModel.getSpecId(), JSON.toJSONString(finalSpecIdMap));
                    continue;
                }
                specCombinationDetaiResponse.setProductName(productListForAchievementResponse.getProductName());
                specCombinationDetaiResponse.setProductCategoryName(productListForAchievementResponse.getSpecCategoryName());
                specCombinationDetaiResponse.setProductId(productListForAchievementResponse.getProductId());
                specCombinationDetaiResponse.setSpecId(productListForAchievementResponse.getSpecId());
                specCombinationDetaiResponse.setSpecName(productListForAchievementResponse.getSpecName());
                specCombinationDetaiResponse.setSpecCategoryName(productListForAchievementResponse.getSpecCategoryName());
                specCombinationDetaiResponse.setNewPolicyCost(specCombinationDetailModel.getNewPolicyCost());
                specCombinationDetaiResponse.setRenewalPolicyCost(specCombinationDetailModel.getRenewalPolicyCost());
                specCombinationDetaiResponse.setAdditionalPolicyCost(specCombinationDetailModel.getAdditionalPolicyCost());
                specCombinationDetaiResponse.setUpgradePolicyCost(specCombinationDetailModel.getUpgradePolicyCost());
                specDetailList.add(specCombinationDetaiResponse);
            }
            response.setSpecDetailList(specDetailList);
            return response;
        }).collect(Collectors.toList());
        pageResponse.setList(records);
        return WebResult.success(pageResponse);
    }

    @Override
    public WebResult<QuerySpecCombinationDetailResponse> querySpecCombinationDetail(QuerySpecCombinationDetailRequest request) {
        SpecCombinationModel specCombinationModel = specCombinationRepository.getOne(
                new LambdaQueryWrapper<SpecCombinationModel>()
                        .eq(SpecCombinationModel::getCombinationId, request.getCombinationId()));
        if (ObjectUtil.isEmpty(specCombinationModel)) {
            return WebResult.success(null);
        }
        QuerySpecCombinationDetailResponse response = new QuerySpecCombinationDetailResponse();
        BeanUtil.copyProperties(specCombinationModel, response);

        //查询商品规格信息
        List<SpecCombinationDetailModel> specCombinationDetailModelList = specCombinationDetailRepository.list(
                new LambdaQueryWrapper<SpecCombinationDetailModel>()
                        .eq(SpecCombinationDetailModel::getCombinationId, specCombinationModel.getCombinationId()));

        List<Long> specIds = specCombinationDetailModelList.stream().map(SpecCombinationDetailModel::getSpecId).collect(Collectors.toList());
        Map<Long, ProductListForAchievementResponse> specIdMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(specIds)) {
            List<ProductListForAchievementResponse> specDetails = innerService.getProductListForAchievement(specIds, null);
            specIdMap = specDetails.stream()
                    .collect(Collectors.toMap(
                            ProductListForAchievementResponse::getSpecId,
                            item -> item,
                            (existing, replacement) -> existing));
        }

        //组装详情
        Map<Long, ProductListForAchievementResponse> finalSpecIdMap = specIdMap;
        List<SpecCombinationDetaiResponse> specDetailList = new ArrayList<>();
        for (SpecCombinationDetailModel specCombinationDetailModel : specCombinationDetailModelList) {
            SpecCombinationDetaiResponse specCombinationDetaiResponse = new SpecCombinationDetaiResponse();
            ProductListForAchievementResponse productListForAchievementResponse = finalSpecIdMap.get(specCombinationDetailModel.getSpecId());
            specCombinationDetaiResponse.setProductName(productListForAchievementResponse.getProductName());
            specCombinationDetaiResponse.setProductCategoryName(productListForAchievementResponse.getSpecCategoryName());
            specCombinationDetaiResponse.setProductId(productListForAchievementResponse.getProductId());
            specCombinationDetaiResponse.setSpecId(productListForAchievementResponse.getSpecId());
            specCombinationDetaiResponse.setSpecName(productListForAchievementResponse.getSpecName());
            specCombinationDetaiResponse.setSpecCategoryName(productListForAchievementResponse.getSpecCategoryName());
            specCombinationDetaiResponse.setNewPolicyCost(specCombinationDetailModel.getNewPolicyCost());
            specCombinationDetaiResponse.setRenewalPolicyCost(specCombinationDetailModel.getRenewalPolicyCost());
            specCombinationDetaiResponse.setAdditionalPolicyCost(specCombinationDetailModel.getAdditionalPolicyCost());
            specCombinationDetaiResponse.setUpgradePolicyCost(specCombinationDetailModel.getUpgradePolicyCost());
            specDetailList.add(specCombinationDetaiResponse);
        }
        response.setSpecDetailList(specDetailList);
        return WebResult.success(response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebResult<Boolean> updateSpecCombination(UpdateSpecCombinationRequest request) {
        SpecCombinationModel specCombinationModel = specCombinationRepository.getOne(
                new LambdaQueryWrapper<SpecCombinationModel>()
                        .eq(SpecCombinationModel::getCombinationId, request.getCombinationId()));
        //校验检查名称
        List<SpecCombinationModel> list = specCombinationRepository.list(
                new LambdaQueryWrapper<SpecCombinationModel>()
                        .eq(SpecCombinationModel::getCombinationName, request.getCombinationName())
                        .ne(SpecCombinationModel::getCombinationId, request.getCombinationId())
                        .eq(SpecCombinationModel::getStatus, CombinationStatusEnum.ENABLE.getCode()));

        if (ObjectUtil.isNotEmpty(list)) {
            return WebResult.error(WebCodeMessageEnum.PRODUCT_COMBINATION_NAME_EXIST);
        }

        //检查是否重复配置了规格组合
        String specIds = request.getSpecDetailList().stream()
                .sorted(Comparator.comparing(SpecCombinationDetailRequest::getSpecId))
                .map(SpecCombinationDetailRequest::getSpecId)
                .map(String::valueOf)
                .collect(Collectors.joining("-"));
        List<SpecCombinationModel> combinationModel = specCombinationRepository.list(
                new LambdaQueryWrapper<SpecCombinationModel>()
                        .eq(SpecCombinationModel::getSpecCombinationList, specIds)
                        .ne(SpecCombinationModel::getCombinationId, request.getCombinationId())
                        .eq(SpecCombinationModel::getStatus, CombinationStatusEnum.ENABLE.getCode()));

        if (ObjectUtil.isNotEmpty(combinationModel)) {
            return WebResult.error(WebCodeMessageEnum.PRODUCT_COMBINATION_EXIST);
        }

        //修改组合信息
        SpecCombinationModel updateModel = new SpecCombinationModel();
        BeanUtil.copyProperties(request, updateModel);
        updateModel.setSpecCombinationList(specIds);
        updateModel.setId(specCombinationModel.getId());

        //todo 暂时设置默认值-1
        updateModel.setAdditionalPolicyCost(BigDecimal.valueOf(NumberConstants.INTEGER_VALUE_NEGATIVE_1));
        updateModel.setNewPolicyCost(BigDecimal.valueOf(NumberConstants.INTEGER_VALUE_NEGATIVE_1));
        updateModel.setRenewalPolicyCost(BigDecimal.valueOf(NumberConstants.INTEGER_VALUE_NEGATIVE_1));
        updateModel.setUpgradePolicyCost(BigDecimal.valueOf(NumberConstants.INTEGER_VALUE_NEGATIVE_1));
        specCombinationRepository.updateById(updateModel);

        //删除规格详情信息
        specCombinationDetailRepository.remove(
                new LambdaUpdateWrapper<SpecCombinationDetailModel>()
                        .eq(SpecCombinationDetailModel::getCombinationId, request.getCombinationId()));

        //保存规格详情信息
        List<SpecCombinationDetailModel> modelList = request.getSpecDetailList().stream().map(e -> {
            SpecCombinationDetailModel model = new SpecCombinationDetailModel();
            BeanUtil.copyProperties(e, model);
            model.setCombinationId(specCombinationModel.getCombinationId());
            model.setSpecId(e.getSpecId());
            model.setProductId(e.getProductId());
            return model;
        }).collect(Collectors.toList());
        specCombinationDetailRepository.saveBatch(modelList, modelList.size());
        return WebResult.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebResult<Boolean> delSpecCombination(DelSpecCombinationRequest request) {
        //删除规格详情信息
        specCombinationRepository.update(
                new LambdaUpdateWrapper<SpecCombinationModel>()
                        .eq(SpecCombinationModel::getCombinationId, request.getCombinationId())
                        .set(SpecCombinationModel::getDeleteFlag, YesOrNotEnum.YES.getCode()));
        //删除规格详情信息
        specCombinationDetailRepository.update(
                new LambdaUpdateWrapper<SpecCombinationDetailModel>()
                        .eq(SpecCombinationDetailModel::getCombinationId, request.getCombinationId())
                        .set(SpecCombinationDetailModel::getDeleteFlag, YesOrNotEnum.YES.getCode()));
        return WebResult.success(true);
    }

    @Override
    public WebResult<Boolean> updateSpecCombinationStatus(UpdateSpecCombinationStatusRequest request) {
        //启用操作进行检查
        if (CombinationStatusEnum.ENABLE.getCode().equals(request.getStatus())) {
            SpecCombinationModel specCombinationDetail = specCombinationRepository.getOne(
                    new LambdaQueryWrapper<SpecCombinationModel>()
                            .eq(SpecCombinationModel::getCombinationId, request.getCombinationId()));
            //校验检查名称
            List<SpecCombinationModel> list = specCombinationRepository.list(
                    new LambdaQueryWrapper<SpecCombinationModel>()
                            .eq(SpecCombinationModel::getCombinationName, specCombinationDetail.getCombinationName())
                            .ne(SpecCombinationModel::getCombinationId, specCombinationDetail.getCombinationId())
                            .eq(SpecCombinationModel::getStatus, CombinationStatusEnum.ENABLE.getCode()));

            if (ObjectUtil.isNotEmpty(list)) {
                return WebResult.error(WebCodeMessageEnum.PRODUCT_COMBINATION_NAME_EXIST);
            }

            //检查是否重复配置了规格组合
            List<SpecCombinationDetailModel> specDetailList = specCombinationDetailRepository.list(
                    new LambdaQueryWrapper<SpecCombinationDetailModel>()
                            .eq(SpecCombinationDetailModel::getCombinationId, specCombinationDetail.getCombinationId()));

            String specIds = specDetailList.stream()
                    .sorted(Comparator.comparing(SpecCombinationDetailModel::getSpecId))
                    .map(SpecCombinationDetailModel::getSpecId)
                    .map(String::valueOf)
                    .collect(Collectors.joining("-"));
            List<SpecCombinationModel> combinationModel = specCombinationRepository.list(
                    new LambdaQueryWrapper<SpecCombinationModel>()
                            .eq(SpecCombinationModel::getSpecCombinationList, specIds)
                            .ne(SpecCombinationModel::getCombinationId, specCombinationDetail.getCombinationId())
                            .eq(SpecCombinationModel::getStatus, CombinationStatusEnum.ENABLE.getCode()));

            if (ObjectUtil.isNotEmpty(combinationModel)) {
                return WebResult.error(WebCodeMessageEnum.PRODUCT_COMBINATION_EXIST);
            }
        }
        specCombinationRepository.update(
                new LambdaUpdateWrapper<SpecCombinationModel>()
                        .eq(SpecCombinationModel::getCombinationId, request.getCombinationId())
                        .set(SpecCombinationModel::getStatus, request.getStatus()));

        return WebResult.success(true);
    }


    @Override
    public Map<Long, List<SpecCombinationDetailModel>> getAllSpecCombinationPrice(Integer orderSaleType) {
        List<SpecCombinationModel> list = specCombinationRepository.list(new LambdaQueryWrapper<SpecCombinationModel>().eq(SpecCombinationModel::getStatus, NumberConstants.INTEGER_VALUE_1));
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        List<Long> combinationIdList = list.stream().map(SpecCombinationModel::getCombinationId).collect(Collectors.toList());

        //通过组合id集合查询规则详情列表
        List<SpecCombinationDetailModel> specCombinationDetailModelList = specCombinationDetailRepository.selectSpecCombinationDetailList(combinationIdList);
        if(CollectionUtils.isEmpty(specCombinationDetailModelList)){
            return null;
        }
        //根据组合id分组
        Map<Long, List<SpecCombinationDetailModel>> combinationIdAndDetailMap = specCombinationDetailModelList.stream().collect(Collectors.groupingBy(SpecCombinationDetailModel::getCombinationId));
        return combinationIdAndDetailMap;

//        return list.stream().collect(Collectors.toMap(SpecCombinationModel::getCombinationId, v -> {
//            switch (OrderSaleTypeEnum.fromValue(orderSaleType)) {
//                case OPEN:
//                    return v.getNewPolicyCost();
//                case RENEW:
//                    return v.getRenewalPolicyCost();
//                case UPGRADE:
//                    return v.getUpgradePolicyCost();
//                case ANOTHER_BUY:
//                default:
//                    return v.getAdditionalPolicyCost();
//            }
//        }));
        // return null;
    }

    @Override
    public Map<Long, List<Long>> getAllCombinationSpecIds() {
        List<SpecCombinationModel> list = specCombinationRepository.list(new LambdaQueryWrapper<SpecCombinationModel>().eq(SpecCombinationModel::getStatus, NumberConstants.INTEGER_VALUE_1));
        HashMap<Long, List<Long>> result = new HashMap<>();
        list.forEach((SpecCombinationModel v) -> {
            Long combinationId = v.getCombinationId();
            result.put(combinationId, specCombinationDetailRepository.list(new LambdaQueryWrapper<SpecCombinationDetailModel>().eq(SpecCombinationDetailModel::getCombinationId, combinationId))
                    .stream().map(SpecCombinationDetailModel::getSpecId).collect(Collectors.toList()));
        });
        return result;
    }
}
