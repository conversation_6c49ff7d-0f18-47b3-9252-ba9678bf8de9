package com.xmd.achievement.util.encrypt.crypto.keygen;

/**
 * Key generator that simply returns the same key every time.
 *
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
final class SharedKeyGenerator implements BytesKeyGenerator {

    private byte[] sharedKey;

    SharedKeyGenerator(byte[] sharedKey) {
        this.sharedKey = sharedKey;
    }

    @Override
    public int getKeyLength() {
        return sharedKey.length;
    }

    @Override
    public byte[] generateKey() {
        return sharedKey;
    }

}
