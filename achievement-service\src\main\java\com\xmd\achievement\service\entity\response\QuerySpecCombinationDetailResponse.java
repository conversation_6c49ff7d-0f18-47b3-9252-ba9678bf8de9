package com.xmd.achievement.service.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24/15:37
 * @since 1.0
 */
@Data
public class QuerySpecCombinationDetailResponse implements Serializable {
    @Schema(description = "组合ID")
    private String combinationId;

    @Schema(description = "组合名称")
    private String combinationName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "状态 1=启用 2=禁用")
    private Integer status;

    @Schema(description = "规格明细")
    private List<SpecCombinationDetaiResponse> specDetailList;
}
