package com.xmd.achievement.web.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 重算退款请求参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "重算退款请求参数")
public class RecalculateRefundRequest {

    /**
     * 业绩ID
     */
    @Schema(description = "业绩ID", required = true)
    @NotNull(message = "业绩ID不能为空")
    private Long achievementId;
}
