<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.AchievementCalculateParamLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.AchievementCalculateParamLogModel">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="achievement_param" property="achievementParam"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , order_id, achievement_param, create_time, update_time, remark
    </sql>

    <insert id="insertOrUpdate">
        INSERT INTO achievement_calculate_param_log (achievement_param, create_time, update_time, remark, order_id)
        VALUES (#{achievementParam},
                NOW(),
                NOW(),
                #{remark},
                #{orderId}) ON DUPLICATE KEY
        UPDATE
            achievement_param =
        VALUES (achievement_param), update_time = NOW(), remark =
        VALUES (remark)
    </insert>

</mapper>
