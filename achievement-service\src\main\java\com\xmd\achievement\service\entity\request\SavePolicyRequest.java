package com.xmd.achievement.service.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25/17:45
 * @since 1.0
 */
@Data
public class SavePolicyRequest implements Serializable {

    private static final long serialVersionUID = -7910245307546302903L;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @Schema(description = "商品Code")
    private String productCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    @Schema(description = "商品规则编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ruleCode;

    @Schema(description = "商品规则编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String oldRuleCode;

    @Schema(description = "政策明细", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "政策明细不能为空")
    private List<PolicySpecDetailRequest> policySpecDetailRequests;

}
