package com.xmd.achievement.handler.calculateCustomer;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.CustomerType;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ○老客户</br>
 * ■从该客户成为新客户的那一笔订单付款时间起，至该客户与公司所有服务结束时间超过180内，创建订单，则该客户为老客户;</br>
 * ■从第1笔订单付款时间计算，90天内所有业务类型为新开的订单累计实付金额＜3000元，超过90天，该客户成为老客户;</br>
 *
 * <AUTHOR>
 * @date: 2024/12/18 13:40
 */
@Service
public class OldCustomer extends CustomerCalculateTemplate implements ICustomerCalculate {
    @Override
    public boolean calculateCustomerType(List<AchievementProductDetailModel> aches) {
        if (CollectionUtils.isEmpty(aches)) {
            return false;
        }
        return newCustomerAfter180DaysHavePaid(aches) || calculateTotalPaidAmountLast90Days4Old(aches).compareTo(new BigDecimal(NumberConstants.INTEGER_VALUE_3000)) < 0 && between90And180Days(aches);
    }

    BigDecimal calculateTotalPaidAmountLast90Days4Old(List<AchievementProductDetailModel> aches) {
        Date minTime = getMinTime(aches);
        Date inner90DaysTime = calculateDate(minTime, NumberConstants.INTEGER_VALUE_90);

        return aches.stream()
                .filter(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType())
                        && (ach.getPaymentTime().after(minTime) || ach.getPaymentTime().equals(minTime))
                        && ach.getPaymentTime().before(inner90DaysTime))
                .map(AchievementProductDetailModel::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    //判断当前业绩的最大支付时间大于90天小于180天
    public boolean between90And180Days(List<AchievementProductDetailModel> aches) {
        Date minTime = getMinTime(aches);
        Date inner90DaysTime = calculateDate(minTime, NumberConstants.INTEGER_VALUE_90);
        Optional<Date> maxPaymentTimeOptional = aches.stream().filter(ach -> SaleTypeEnum.NEW_OPEN.getType().equals(ach.getSaleType()))
                .map(AchievementProductDetailModel::getPaymentTime).max(Comparator.naturalOrder());
        if (!maxPaymentTimeOptional.isPresent()) {
            return false;
        }
        boolean existServeFinishTime = aches.stream()
                .filter(ach -> ach.getSaleType().equals(SaleTypeEnum.NEW_OPEN.getType()))
                .map(AchievementProductDetailModel::getServeFinishTime)
                .anyMatch(Objects::nonNull);
        if (existServeFinishTime) {
            Date maxServeFinishTime = aches.stream()
                    .filter(ach -> ach.getSaleType().equals(SaleTypeEnum.NEW_OPEN.getType()))
                    .map(AchievementProductDetailModel::getServeFinishTime).filter(Objects::nonNull).max(Comparator.naturalOrder()).get();
            Date after180DaysTime = calculateDate(maxServeFinishTime, NumberConstants.INTEGER_VALUE_180);
            return maxPaymentTimeOptional.get().before(after180DaysTime);
        } else {
            return maxPaymentTimeOptional.get().after(inner90DaysTime);
        }
    }

    /**
     * 新客户180天后有新订单</br>
     * 释：</br>
     * 1. 从该客户成为新客户的那一笔订单付款时间起，至该客户与公司所有服务结束时间超过180内，创建订单，则该客户为老客户;
     *
     * @param aches 业绩集合
     * @return {@link }
     * <AUTHOR>
     * @date: 2024/12/18 16:40
     * @version: 1.0.0
     */
    private boolean newCustomerAfter180DaysHavePaid(List<AchievementProductDetailModel> aches) {
        //标记新客户业绩
        Optional<AchievementProductDetailModel> newCustomerAchOptional = aches.stream()
                .filter(ach -> ach.getSaleType().equals(SaleTypeEnum.NEW_OPEN.getType()) && CustomerType.NEW.getType().equals(ach.getCustomerType()))
                .max(Comparator.comparing(AchievementProductDetailModel::getPaymentTime));
        if (!newCustomerAchOptional.isPresent()) {
            return false;
        }
        //新客户付款时间
        AchievementProductDetailModel newCustomerTypeAch = newCustomerAchOptional.get();

        //在最新一笔新客户业绩的支付时间为起点 ，到最新支付时间的所有服务时
        Set<Date> serviceFinishTimes = aches.stream()
                .filter(ach -> ach.getPaymentTime().after(newCustomerTypeAch.getPaymentTime()) || ach.getPaymentTime().equals(newCustomerTypeAch.getPaymentTime()))
                .map(AchievementProductDetailModel::getServeFinishTime)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        if (newCustomerTypeAch.getServeFinishTime() != null) {
            serviceFinishTimes.add(newCustomerTypeAch.getServeFinishTime());
        }

        Date lastServiceFinishTime = serviceFinishTimes.isEmpty() ? null : serviceFinishTimes.stream().max(Comparator.naturalOrder()).orElse(null);
        if (null == lastServiceFinishTime) {
            return true;
        }

        // 计算最晚服务完成时间后180天的时间
        Date finishTimeAfter180Days = calculateDate(lastServiceFinishTime, NumberConstants.INTEGER_VALUE_180);
        Date maxPaymentTime = aches.stream().map(AchievementProductDetailModel::getPaymentTime).max(Comparator.naturalOrder()).get();
        return maxPaymentTime.after(newCustomerTypeAch.getPaymentTime()) && maxPaymentTime.before(finishTimeAfter180Days);
    }

}
