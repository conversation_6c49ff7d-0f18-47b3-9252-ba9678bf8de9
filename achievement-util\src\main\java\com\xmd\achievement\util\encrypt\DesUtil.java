package com.xmd.achievement.util.encrypt;

import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * des加解密工具类
 *
 * <AUTHOR>
 * @version *******
 * @date 2021/05/17 10:48 上午
 */
public class DesUtil {
    /**
     * 默认加密对象
     **/
    private final static String DEFAULT_ENCRYPT_OBJECT = "DES";
    /**
     * 默认key
     */
    protected final static String KEY = "ScAKC0XhadTHT3Al0QIDAQAB";

    /**
     * @param data 加密数据
     * @param key  加密key
     *             des加密
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/17 10:51 上午
     * @version *******
     **/
    protected static String encrypt(String data, String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        try {
            // DES算法要求有一个可信任的随机数源
            SecureRandom sr = new SecureRandom();
            DESKeySpec deskey = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));
            // 创建一个密匙工厂，然后用它把DESKeySpec转换成一个SecretKey对象
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DEFAULT_ENCRYPT_OBJECT);
            SecretKey secretKey = keyFactory.generateSecret(deskey);
            // 加密对象
            Cipher cipher = Cipher.getInstance(DEFAULT_ENCRYPT_OBJECT);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, sr);
            // 加密，并把字节数组编码成字符串
            return Base64.getEncoder().encodeToString(cipher.doFinal(data.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            throw new RuntimeException("加密错误，错误信息：", e);
        }
    }

    /**
     * @param cryptData 解密数据
     * @param key       解密key
     *                  des解密
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/05/17 10:52 上午
     * @version *******
     **/
    protected static String decrypt(String cryptData, String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        try {
            // DES算法要求有一个可信任的随机数源
            SecureRandom sr = new SecureRandom();
            DESKeySpec deskey = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));
            // 创建一个密匙工厂，然后用它把DESKeySpec转换成一个SecretKey对象
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DEFAULT_ENCRYPT_OBJECT);
            SecretKey secretKey = keyFactory.generateSecret(deskey);
            // 解密对象
            Cipher cipher = Cipher.getInstance(DEFAULT_ENCRYPT_OBJECT);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, sr);
            // 把字符串解码为字节数组，并解密
            return new String(cipher.doFinal(Base64.getDecoder().decode(cryptData)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("解密错误，错误信息：", e);
        }
    }
}
