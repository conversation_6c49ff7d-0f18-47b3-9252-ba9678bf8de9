<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.MqServeInprogressInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xmd.achievement.dao.entity.MqServeInprogressInfoModel">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="serve_no" property="serveNo"/>
        <result column="order_id" property="orderId"/>
        <result column="product_id" property="productId"/>
        <result column="task_status" property="taskStatus"/>
        <result column="fail_reason" property="failReason"/>
        <result column="fail_count" property="failCount"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , task_id, serve_no, order_id, product_id, task_status, fail_reason, fail_count, delete_flag, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

</mapper>
