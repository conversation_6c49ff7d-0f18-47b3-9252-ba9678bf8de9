package com.xmd.achievement.web.annotate;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限注解
 * 需要权限验证才能请求的接口配置上此注解
 *
 * <AUTHOR>
 * @version 1.0.0
 */
//限制只能使用在方法和注解声明的类中
@Target({ElementType.TYPE, ElementType.METHOD})
//定义注解的生命周期为运行时阶段，即在程序运行时通过反射等手段获取到注解的相关信息来做相应处理
@Retention(value = RetentionPolicy.RUNTIME)
public @interface Permission {
    /**
     * 功能码
     */
    String code();
}
