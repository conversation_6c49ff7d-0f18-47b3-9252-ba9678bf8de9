package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

@Getter
public enum OrganizationTypeEnum {

    /**
     * 机构类型1总部；2区域；3分公司；4事业部；5商务组
     *
     */
    /**
     * 总部
     */
    HEADER(1, "总部"),
    /**
     * 区域
     */
    REGION(2, "区域"),
    /**
     * 分公司
     */
    BRANCH_COMPANY(3, "分公司"),
    /**
     * 事业部
     */
    DIVISION(4, "事业部"),
    /**
     * 商务组(部门)
     */
    DEPT(5, "商务组");

    private final Integer code;
    private final String msg;

    OrganizationTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
