package com.xmd.achievement.service;

import com.xmd.achievement.dao.entity.SaleTaskAuditModel;
import com.xmd.achievement.dao.entity.SaleTaskModel;
import com.xmd.achievement.service.entity.request.SaleTaskAuditRequest;

import java.util.List;

public interface SaleTaskAuditService {

    /**
     * 根据任务id获取最新的审核
     * @param taskId 任务业务ID
     * @return
     */
    SaleTaskAuditModel getLatestAuditByTaskId(Long taskId);

    /**
     * 销售任务审核
     * @param saleTaskModels 销售任务集合
     * @param auditRequest 审核结果及内容
     */
    void saleTaskAudit(List<SaleTaskModel> saleTaskModels, SaleTaskAuditRequest auditRequest);
}
