package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.SpecCombinationModel;
import com.xmd.achievement.dao.mapper.SpecCombinationMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.ISpecCombinationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 规格组合表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@Slf4j
public class SpecCombinationRepositoryImpl extends ServiceImpl<SpecCombinationMapper, SpecCombinationModel> implements ISpecCombinationRepository {

    @Resource
    private SpecCombinationMapper specCombinationMapper;

    @Override
    public List<SpecCombinationModel> selectAllSpecCombination() {
        LambdaQueryWrapper<SpecCombinationModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpecCombinationModel::getDeleteFlag,0);
        return this.list(queryWrapper);
    }

}