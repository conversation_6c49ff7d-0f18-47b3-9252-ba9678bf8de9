package com.xmd.achievement.support.constant.enums;

import lombok.Getter;

@Getter
public enum SaasTypeEnum {
     
    ZHONGQI(0, "中企"),
    
    KUAJING(1, "跨境");

    private final Integer code;
    private final String msg;

    SaasTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    
    /**
     * 根据code获取对应的枚举值
     *
     * @param code 编码
     * @return 枚举值
     */
    public static SaasTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SaasTypeEnum value : SaasTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
